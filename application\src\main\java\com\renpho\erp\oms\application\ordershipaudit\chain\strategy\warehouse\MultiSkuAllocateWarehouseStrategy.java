package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.service.OrderInventoryQueryService;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.AutoShipAuditConfig;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.karma.web.servlet.mvc.context.StringUtils;

/**
 * 多品多件-分仓策略
 * <AUTHOR>
 */
@Component
public class MultiSkuAllocateWarehouseStrategy extends AbstractAllocateWarehouseTemplate {

	protected MultiSkuAllocateWarehouseStrategy(WarehouseClient warehouseClient, OrderInventoryQueryService orderInventoryQueryService) {
		super(warehouseClient, orderInventoryQueryService);
	}

	@Override
	public Set<SkuCombinationType> getSkuCombinationTypes() {
		return Set.of(SkuCombinationType.MULTI_SKU);
	}

	@Override
	protected List<String> getOrderedPriorityWarehouseCodes(SkuCombinationType skuCombinationType,
			List<AutoShipAuditConfig> autoShipAuditConfigs) {
		// 订单所有sku的规则是否都包含多品多件
		if (!autoShipAuditConfigs.stream().allMatch(AutoShipAuditConfig::isContainMultiSkuType)) {
			// 存在sku规则不包含多品多件
			return List.of();
		}
		// 获取订单所有sku规则第一优先级的仓库编码集
		Set<String> warehouseCodes = autoShipAuditConfigs.stream()
			.map(AutoShipAuditConfig::getFirstPriorityWarehouseCode)
			.collect(Collectors.toSet());
		// 存在规则没有配置仓库 或 订单的所有SKU的优先级为1的仓库不相同
		if (warehouseCodes.stream().anyMatch(StringUtils::isBlank) || warehouseCodes.size() != 1) {
			return List.of();
		}
		// 订单所有sku规则第一优先级的仓库编码
		String warehouseCode = warehouseCodes.iterator().next();
		return List.of(warehouseCode);
	}

}
