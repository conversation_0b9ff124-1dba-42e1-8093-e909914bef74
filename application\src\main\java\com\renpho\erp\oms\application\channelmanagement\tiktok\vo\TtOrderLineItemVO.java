package com.renpho.erp.oms.application.channelmanagement.tiktok.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TtOrderLineItemVO {
	/**
	 * Tiktok商品id
	 */
	private String itemId;

	/**
	 * sku图片
	 */
	private String skuImage;

	/**
	 * sku id
	 */
	private String skuId;

	/**
	 * 卖家sku
	 */
	private String sellerSku;

	/**
	 * 是否为礼品 0:否 1:是
	 */
	private Boolean isGift;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 原始价格
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal originalPrice;

	/**
	 * 平台折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal platformDiscount;

	/**
	 * 卖家折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal sellerDiscount;

	/**
	 * 税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal tax;

	/**
	 * 追踪号码
	 */
	private String trackingNumber;

	/**
	 * 运输供应商名称
	 */
	private String shippingProviderName;
}
