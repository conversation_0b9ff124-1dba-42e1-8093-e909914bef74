package com.renpho.erp.oms.infrastructure.persistence.receiptOrder.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.oms.domain.b2b.order.B2bReceiptOrderStatusEnum;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderAmountMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderAmountPO;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.mapper.B2bReceiptOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po.B2bReceiptOrderPO;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/23 14:55
 */
@Repository
@RequiredArgsConstructor
public class B2bReceiptOrderPoService extends ServiceImpl<B2bReceiptOrderMapper, B2bReceiptOrderPO> {

    private final B2bOrderAmountMapper b2bOrderAmountMapper;

    /**
     * 确认收款单
     *
     * @param b2bReceiptOrderPO
     */
    @Transactional(rollbackFor = Exception.class)
    public void confirmReceiptOrder(B2bReceiptOrderPO b2bReceiptOrderPO) {
        //修改基础信息
        this.getBaseMapper().updateById(b2bReceiptOrderPO);
        //计算订单已收款金额
        calOrderReceiptAmount(b2bReceiptOrderPO.getB2bOrderId());
    }


    /**
     * 计算订单已收款金额
     * @param b2bOrderId
     */
    public void calOrderReceiptAmount(Long b2bOrderId) {
        B2bOrderAmountPO b2bOrderAmountPO = b2bOrderAmountMapper.selectOne(Wrappers.<B2bOrderAmountPO>lambdaQuery()
                .eq(B2bOrderAmountPO::getOrderId, b2bOrderId)
                .eq(AlterationPO::getDeleted, 0)
                .last(" limit 1"));
        if (b2bOrderAmountPO == null) {
            throw new BusinessException(String.format("未找到订单id:%s金额信息", b2bOrderId));
        }
        //订单总金额
        BigDecimal totalAmount = b2bOrderAmountPO.getTotalAmount();
        //已收款金额
        BigDecimal orderReceiptAmount = baseMapper.selectList(Wrappers.<B2bReceiptOrderPO>lambdaQuery()
                        .eq(B2bReceiptOrderPO::getB2bOrderId, b2bOrderId)
                        .eq(B2bReceiptOrderPO::getStatus, B2bReceiptOrderStatusEnum.CONFIRMED.getValue())
                        .eq(B2bReceiptOrderPO::getIsDeleted, 0))
                .stream()
                .filter(x -> x.getReceiptAmount() != null)
                .map(B2bReceiptOrderPO::getReceiptAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //计算订单已收款的占比，0.2代表20%
        BigDecimal orderReceiptRate = null;
        if (totalAmount == null || Objects.equals(totalAmount, BigDecimal.ZERO)) {
            //总金额为空或者为0
            orderReceiptRate = BigDecimal.ZERO;
        } else {
            orderReceiptRate = orderReceiptAmount.divide(totalAmount, 4, RoundingMode.HALF_UP);
        }
        //修改b2b的已收款和占比
        b2bOrderAmountMapper.update(null, Wrappers.<B2bOrderAmountPO>lambdaUpdate()
                .eq(B2bOrderAmountPO::getOrderId, b2bOrderId)
                .set(B2bOrderAmountPO::getReceiptAmount, orderReceiptAmount)
                .set(B2bOrderAmountPO::getReceiptRate, orderReceiptRate)
                .set(AlterationPO::getUpdateBy, SecurityUtils.getUserId()));

    }

    /**
     * 取消收款单
     *
     * @param b2BReceiptOrderPO
     */
    public boolean cancelReceiptOrder(B2bReceiptOrderPO b2BReceiptOrderPO) {
        return baseMapper.update(null, Wrappers.<B2bReceiptOrderPO>lambdaUpdate()
                .eq(B2bReceiptOrderPO::getId, b2BReceiptOrderPO.getId())
                .set(B2bReceiptOrderPO::getStatus, B2bReceiptOrderStatusEnum.CANCELLED.getValue())
                .set(B2bReceiptOrderPO::getUpdateBy, SecurityUtils.getUserId())) > 0;
    }
}
