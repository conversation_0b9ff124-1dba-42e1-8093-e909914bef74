package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import com.renpho.erp.apiproxy.walmart.model.ShopAccount;
import com.renpho.erp.apiproxy.walmart.model.items.GetAllItemsRequest;
import com.renpho.erp.apiproxy.walmart.model.items.GetAllItemsResponse;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.WalmartClient;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 沃尔玛Listing拉取器
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class WalmartListingPuller extends AbstractListingPullerTemplate {

	private final WalmartClient walmartClient;

	@Override
	public String getSalesChannelCode() {
		return ChannelCode.WMT_CHANNEL_CODE;
	}

	@Override
	protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
		if (storeAuthorization == null || listingSearchQuery == null) {
			return null;
		}
		// 构建 api 接口 授权
		ShopAccount shopAccount = this.buildShopAccount(storeAuthorization);
		// 构建分页参数
		GetAllItemsRequest request = this.buildGetAllItemsRequest(listingSearchQuery);
		if (listingSearchQuery.getSearchAfter() == null) {
			return null;
		}
		// 调用 沃尔玛 api 获取listing
		GetAllItemsResponse response = walmartClient.getWalmartItems(shopAccount, request);
		if (response == null || CollectionUtils.isEmpty(response.getItemResponse())) {
			return null;
		}
		List<GetAllItemsResponse.Item> items = response.getItemResponse();
		// 转换为ListingSource
		List<SalesChannelListingSource> listingSourceList = items.stream()
			.map(e -> this.transToListingSource(e, storeAuthorization))
			.toList();
		// 构建Listing分批拉取结果
		return this.buildListingSearchDTO(listingSourceList, response);
	}

	/**
	 * 构建分页参数
	 * @param listingSearchQuery Listing分批拉取查询参数
	 * @return GetAllItemsRequest
	 */
	private @NotNull GetAllItemsRequest buildGetAllItemsRequest(ListingSearchQuery listingSearchQuery) {
		GetAllItemsRequest request = new GetAllItemsRequest();
		request.setNextCursor(listingSearchQuery.getSearchAfter());
		request.setShowDuplicateItemInfo(true);
		return request;
	}

	/**
	 * 构建Listing分批拉取结果
	 * @param listingSourceList Listing
	 * @param response 沃尔玛item结果
	 * @return ListingSearchDTO
	 */
	private @NotNull ListingSearchDTO buildListingSearchDTO(List<SalesChannelListingSource> listingSourceList,
			GetAllItemsResponse response) {
		ListingSearchDTO lsd = new ListingSearchDTO();
		lsd.setListingSourceList(listingSourceList);
		lsd.setSearchAfter(response.getNextCursor());
		return lsd;
	}

	/**
	 * 构建分批拉取查询参数
	 *
	 * @return SearchAfterQuery
	 */
	@Override
	protected @NotNull ListingSearchQuery buildSearchAfterQuery() {
		ListingSearchQuery first = new ListingSearchQuery();
		first.setSearchAfter("*");
		return first;
	}

	/**
	 * 转换为ListingSource
	 * @param item 沃尔玛item
	 * @param auth 店铺授权信息
	 * @return SalesChannelListingSource
	 */
	private SalesChannelListingSource transToListingSource(GetAllItemsResponse.Item item, StoreAuthorizationDTO auth) {
		// 构建销售渠道原始Listing
		SalesChannelListingSource source = super.buildListingSource(auth);
		source.setListingId(item.getSku());
		source.setContent(JSONKit.toJSONString(item));
		return source;
	}

	/**
	 * 构建店铺账号
	 * @param auth 店铺授权信息
	 * @return ShopAccount
	 */
	private ShopAccount buildShopAccount(StoreAuthorizationDTO auth) {
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSiteCode(auth.getStoreAuthorization().getSiteCode());
		shopAccount.setShopCode(auth.getStoreAuthorization().getAuthorization().getWmtShopId());
		return shopAccount;
	}

}
