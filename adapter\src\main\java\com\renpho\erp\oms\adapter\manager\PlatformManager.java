package com.renpho.erp.oms.adapter.manager;

import com.renpho.erp.oms.application.platform.service.PlatformService;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class PlatformManager {
	@Resource
	private Map<String, PlatformService> platformServiceMap;

	public PlatformService getService(String serviceName) {
		PlatformService platformService = platformServiceMap.get(serviceName);
		if (platformService != null) {
			return platformService;
		}
		else {
			throw new BusinessException(I18nMessageKit.getMessage("UNSUPPORT"));
		}
	}

}
