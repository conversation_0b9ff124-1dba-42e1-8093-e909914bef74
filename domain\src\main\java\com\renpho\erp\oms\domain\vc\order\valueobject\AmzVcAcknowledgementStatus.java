package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.time.LocalDateTime;
import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC确认状态值对象 基于Amazon SP-API OrderItemStatusAcknowledgementStatus模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcAcknowledgementStatus {

	/**
	 * 确认状态 对应API字段：confirmationStatus 可能的值：ACCEPTED, PARTIALLY_ACCEPTED, REJECTED,
	 * UNCONFIRMED
	 */
	private String confirmationStatus;

	/**
	 * 接受数量 对应API字段：acceptedQuantity
	 */
	private AmzVcQuantity acceptedQuantity;

	/**
	 * 拒绝数量 对应API字段：rejectedQuantity
	 */
	private AmzVcQuantity rejectedQuantity;

	/**
	 * 确认日期 对应API字段：acknowledgementDate
	 */
	private LocalDateTime acknowledgementDate;

	/**
	 * 确认状态详情列表 对应API字段：acknowledgementStatusDetails 商品数量确认的详细信息
	 */
	private List<AmzVcAcknowledgementStatusDetails> acknowledgementStatusDetails;

}
