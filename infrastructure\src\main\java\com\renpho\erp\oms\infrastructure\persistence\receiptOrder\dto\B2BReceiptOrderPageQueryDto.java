package com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto;

import com.renpho.karma.dto.PageQuery;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class B2BReceiptOrderPageQueryDto extends PageQuery {
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;
    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;
    /**
     * 支付时间开始
     */
    private LocalDateTime payTimeStart;
    /**
     * 支付时间结束
     */
    private LocalDateTime payTimeEnd;
    /**
     * 接收时间开始
     */
    private LocalDateTime receiveTimeStart;
    /**
     * 接收时间结束
     */
    private LocalDateTime receiveTimeEnd;
    /**
     * 自定义收款单号
     */
    private List<String> receiptOrderNos;

    /**
     * 银行参考号
     */
    private List<String> bankReferNos;

    /**
     * 收款所属的订单号
     */
    private List<String> b2bOrderNos;
    /**
     * 银行账号id
     */
    private Integer bankAccountId;
    /**
     * 状态
     */
    private Integer status;

    private boolean exportFlag;
}
