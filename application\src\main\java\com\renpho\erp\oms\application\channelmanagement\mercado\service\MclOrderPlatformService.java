package com.renpho.erp.oms.application.channelmanagement.mercado.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclOrderLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderVO;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrder;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderRepository;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclShipmentRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.mercado.mapper.MclOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.mercado.po.MclOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service(ChannelCode.MCL_CHANNEL_CODE)
@Slf4j
public class MclOrderPlatformService extends AbstractDefaultPlatfomService {
	private final MclOrderRepository mclOrderRepository;
	private final PlatformOrderConvertor platformOrderConvertor;
	private final MclOrderLogConvertor mclOrderLogConvertor;
	private final MclShipmentRepository mclShipmentRepository;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private MclOrderMapper mclOrderMapper;

	@Override
	protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<MclOrderPage> page = mclOrderRepository.page(platformPageQuery);
		List<MclOrderPage> mclOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(mclOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}
		List<PlatformOrderVO> records = mclOrderPageList.stream().map(mclOrderPage -> {
			return platformOrderConvertor.mercadoOrderToVO(mclOrderPage);
		}).collect(Collectors.toList());

		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		MclOrder mclOrder = mclOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(mclOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		MclOrderVO mclOrderVO = mclOrderLogConvertor.toVO(mclOrder);
		// 查询店铺
		mclOrderVO.setStoreName(getStoreName(mclOrder.getStoreId()));
		// 查询运单信息
		MclShipment mclShipment = mclShipmentRepository.getByUniqueIndex(mclOrderVO.getShippingId(), mclOrderVO.getMclUserId());
		if (Objects.nonNull(mclShipment)) {
			// 填充运单信息
			MclOrderAppConvertor.fillShipment(mclOrderVO, mclShipment);
		}
		return R.success(mclOrderVO);
	}

	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> monitorPlatformOrderDtos = mclOrderMapper
			.selectList(Wrappers.<MclOrderPO> lambdaQuery()
				.select(MclOrderPO::getMercadoOrderId, MclOrderPO::getLastMpdsSyncOrderTime, MclOrderPO::getStoreId)
				.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, MclOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
				.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, MclOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
			.stream()
			.map(x -> monitorPlatformOrderConvertor.mercadoOrderToDto(x))
			.collect(Collectors.toList());
		return monitorPlatformOrderDtos;
	}
}
