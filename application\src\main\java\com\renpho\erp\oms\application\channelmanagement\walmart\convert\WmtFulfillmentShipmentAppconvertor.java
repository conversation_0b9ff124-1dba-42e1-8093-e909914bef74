package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillmentShipment;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.karma.json.JSONKit;
import org.apache.commons.compress.utils.Lists;

import java.util.List;
import java.util.Objects;

public class WmtFulfillmentShipmentAppconvertor {

    public static List<WmtFulfillmentShipment> toDomainList(List<GetFulfillmentOrdersStatusResponse.Shipment> shipmentList) {
        List<WmtFulfillmentShipment> wmtFulfillmentShipmentList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(shipmentList)) {
            shipmentList.forEach(shipment -> {
                wmtFulfillmentShipmentList.add(toDomain(shipment));
            });
        }
        return wmtFulfillmentShipmentList;
    }

    public static WmtFulfillmentShipment toDomain(GetFulfillmentOrdersStatusResponse.Shipment shipment) {
        if (Objects.nonNull(shipment)) {
            WmtFulfillmentShipment wmtFulfillmentShipment = new WmtFulfillmentShipment();
            wmtFulfillmentShipment.setStatus(shipment.getStatus());
            wmtFulfillmentShipment.setStatusDescription(shipment.getStatusDescription());
            wmtFulfillmentShipment.setPurchaseOrderId(shipment.getPurchaseOrderId());
            wmtFulfillmentShipment.setScac(shipment.getScac());
            wmtFulfillmentShipment.setTrackingNo(shipment.getTrackingNo());
            wmtFulfillmentShipment.setExternalTrackingUrl(shipment.getExternalTrackingURL());
            wmtFulfillmentShipment.setShipmentNo(shipment.getShipmentNo());
            wmtFulfillmentShipment.setActualShipmentDate(DateUtil.parse(shipment.getActualShipmentDate()));
            wmtFulfillmentShipment.setPackageAsn(shipment.getPackageASN());
            wmtFulfillmentShipment.setCarrierDescription(shipment.getCarrierDescription());
            wmtFulfillmentShipment.setCarrierServiceCode(shipment.getCarrierServiceCode());
            wmtFulfillmentShipment.setPackageId(shipment.getPackageId());
            wmtFulfillmentShipment.setLastModified(DateUtil.parse(shipment.getLastModified()));
            wmtFulfillmentShipment.setShipmentLines(CollectionUtil.isNotEmpty(shipment.getShipmentLines()) ? JSONKit.toJSONString(shipment.getShipmentLines()) : null);
            wmtFulfillmentShipment.setShipmentDates(CollectionUtil.isNotEmpty(shipment.getShipmentDates()) ? JSONKit.toJSONString(shipment.getShipmentDates()) : null);
            return wmtFulfillmentShipment;
        }
        return null;

    }

}
