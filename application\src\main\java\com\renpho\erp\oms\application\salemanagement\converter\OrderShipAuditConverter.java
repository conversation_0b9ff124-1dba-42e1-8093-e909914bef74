package com.renpho.erp.oms.application.salemanagement.converter;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderItemFulfillment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.application.ordershipaudit.cmd.OrderShipAuditCmd;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.domain.salemanagement.OrderShipAudit;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface OrderShipAuditConverter {

    OrderShipAuditDTO convertShipAuditDto(OrderShipAuditCmd cmd);

    OrderShipAudit convertShipAudit(OrderShipAuditDTO orderShipAudit);

    default OrderShipAuditCmd convertToCmd(SaleOrderAggRoot saleOrderAggRoot) {
        OrderShipAuditCmd orderShipAuditCmd = new OrderShipAuditCmd();
        orderShipAuditCmd.setOrderId(saleOrderAggRoot.getId().getId());
        orderShipAuditCmd.setFulfillmentServiceType(FulfillmentServiceType.OFFLINE.getValue());
        SaleOrderItemFulfillment itemFulfillment = saleOrderAggRoot.getItems().get(0).getItemFulfillment();
        orderShipAuditCmd.setWarehouseId(itemFulfillment.getWarehouseId());
        orderShipAuditCmd.setWarehouseCode(itemFulfillment.getWarehouseCode());
        orderShipAuditCmd.setItems(saleOrderAggRoot.getItems().stream().map(saleOrderItem -> {
            OrderShipAuditCmd.Item item = new OrderShipAuditCmd.Item();
            item.setItemId(saleOrderItem.getId().getId());
            return item;
        }).collect(Collectors.toList()));
        return orderShipAuditCmd;
    }
}
