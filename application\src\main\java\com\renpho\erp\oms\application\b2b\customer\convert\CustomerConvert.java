package com.renpho.erp.oms.application.b2b.customer.convert;

import java.util.*;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import org.springframework.context.i18n.LocaleContextHolder;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.renpho.erp.oms.application.b2b.customer.command.*;
import com.renpho.erp.oms.application.b2b.customer.vo.*;
import com.renpho.erp.oms.domain.b2b.customer.model.*;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.*;
import com.renpho.erp.pds.client.common.LanguageParent;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;

/**
 * 客户对象转换器
 */
@Mapper
public interface CustomerConvert {
	CustomerConvert INSTANCE = Mappers.getMapper(CustomerConvert.class);

	/**
	 * 转换联系人命令到领域对象
	 */
	CustomerContactInfo toCustomerContactInfo(CustomerContactCommand command);

	/**
	 * 转换发票信息命令到领域对象
	 */
	CustomerInvoiceInfo toCustomerInvoiceInfo(CustomerInvoiceCommand command);

	/**
	 * 转换收货信息命令到领域对象
	 */
	CustomerShippingInfo toCustomerShippingInfo(CustomerShippingCommand command);

	/**
	 * 转换附件信息命令到领域对象
	 */
	CustomerAttachmentInfo toCustomerAttachmentInfo(CustomerAttachmentCommand command);

	/**
	 * 转换销售公司和协议信息命令到领域对象
	 */
	CustomerSalesCompanyTermsInfo toCustomerSalesCompanyTermsInfo(CustomerSalesCompanyTermsCommand command);

	/**
	 * 转换保险授信信息命令到领域对象
	 */
	CustomerInsuranceCreditInfo toCustomerInsuranceCreditInfo(CustomerInsuranceCreditCommand command);

	/**
	 * 转换联系人命令列表到领域对象列表
	 */
	List<CustomerContactInfo> toCustomerContactInfoList(List<CustomerContactCommand> commands);

	/**
	 * 转换发票信息命令列表到领域对象列表
	 */
	List<CustomerInvoiceInfo> toCustomerInvoiceInfoList(List<CustomerInvoiceCommand> commands);

	/**
	 * 转换收货信息命令列表到领域对象列表
	 */
	List<CustomerShippingInfo> toCustomerShippingInfoList(List<CustomerShippingCommand> commands);

	/**
	 * 转换附件信息命令列表到领域对象列表
	 */
	List<CustomerAttachmentInfo> toCustomerAttachmentInfoList(List<CustomerAttachmentCommand> commands);

	/**
	 * 领域聚合根转为VO
	 */
	CustomerVO toCustomerVO(CustomerAggRoot customerAggRoot);

	/**
	 * 领域联系人对象转为VO
	 */
	CustomerContactVO toCustomerContactVO(CustomerContactInfo contactInfo);

	/**
	 * 领域发票信息对象转为VO
	 */
	CustomerInvoiceVO toCustomerInvoiceVO(CustomerInvoiceInfo invoiceInfo);

	/**
	 * 领域收货信息对象转为VO
	 */
	CustomerShippingVO toCustomerShippingVO(CustomerShippingInfo shippingInfo);

	/**
	 * 领域附件信息对象转为VO
	 */
	CustomerAttachmentVO toCustomerAttachmentVO(CustomerAttachmentInfo attachmentInfo);

	/**
	 * 领域销售公司和协议信息对象转为VO
	 */
	CustomerSalesCompanyTermsVO toCustomerSalesCompanyTermsVO(CustomerSalesCompanyTermsInfo termsInfo);

	/**
	 * 领域保险授信信息对象转为VO
	 */
	CustomerInsuranceCreditVO toCustomerInsuranceCreditVO(CustomerInsuranceCreditInfo creditInfo);

	/**
	 * 领域联系人对象列表转为VO列表
	 */
	List<CustomerContactVO> toCustomerContactVOList(List<CustomerContactInfo> contactInfos);

	/**
	 * 领域发票信息对象列表转为VO列表
	 */
	List<CustomerInvoiceVO> toCustomerInvoiceVOList(List<CustomerInvoiceInfo> invoiceInfos);

	/**
	 * 领域收货信息对象列表转为VO列表
	 */
	List<CustomerShippingVO> toCustomerShippingVOList(List<CustomerShippingInfo> shippingInfos);

	/**
	 * 领域附件信息对象列表转为VO列表
	 */
	List<CustomerAttachmentVO> toCustomerAttachmentVOList(List<CustomerAttachmentInfo> attachmentInfos);

	/**
	 * PO转为VO
	 */
	CustomerDetailVO toCustomerVO(CustomerInfoPO po);

	/**
	 * PO转为VO
	 */
	CustomerContactVO toContactVO(CustomerContactInfoPO po);

	/**
	 * PO转为VO
	 */
	List<CustomerContactVO> toContactVOList(List<CustomerContactInfoPO> poList);

	/**
	 * PO转为VO
	 */
	CustomerInvoiceVO toInvoiceVO(CustomerInvoiceInfoPO po);

	/**
	 * PO转为VO
	 */
	List<CustomerInvoiceVO> toInvoiceVO(List<CustomerInvoiceInfoPO> poList);

	/**
	 * PO转为VO
	 */
	CustomerShippingVO toShippingVO(CustomerShippingInfoPO po);

	/**
	 * PO转为VO
	 */
	List<CustomerShippingVO> toShippingVOList(List<CustomerShippingInfoPO> poList);

	/**
	 * PO转为VO
	 */
	CustomerSalesCompanyTermsVO toSalesCompanyTermsVO(CustomerSalesCompanyTermsInfoPO po);

	@Mapping(target = "id", source = "customerAggRoot.id")
	@Mapping(target = "customerCompanyName", source = "customerAggRoot.customerCompanyName")
	@Mapping(target = "country", expression = "java(getCountry(customerAggRoot.getCountry(), countryMap))")
	@Mapping(target = "primaryContactInfo", expression = "java(getPrimaryContactInfo(customerAggRoot.getContactInfos()))")
	@Mapping(target = "countryManagerName", source = "customerAggRoot.countryManagerName")
	@Mapping(target = "countryManagerCode", source = "customerAggRoot.countryManagerCode")
	@Mapping(target = "salesAssistantName", source = "customerAggRoot.salesAssistantName")
	@Mapping(target = "salesAssistantCode", source = "customerAggRoot.salesAssistantCode")
	@Mapping(target = "createTime", source = "customerAggRoot.createTime")
	@Mapping(target = "status", source = "customerAggRoot.status")
	CustomerPageVO toCustomerPageVO(CustomerAggRoot customerAggRoot, Map<String, PdsCountryRegionVo> countryMap);

	default String getCountry(String countryCode, Map<String, PdsCountryRegionVo> countryMap) {
		// 获取当前语言环境
		String language = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");
		PdsCountryRegionVo pdsCountryRegionVo = countryMap.get(countryCode);
		return Optional.ofNullable(pdsCountryRegionVo.getNames())
			.orElse(Collections.emptyList())
			.stream()
			.filter(ln -> language.equals(ln.getLanguage()))
			.findFirst()
			.map(LanguageParent::getName)
			.orElse("");
	}

	/**
	 * 获取主要联系人信息
	 */
	default CustomerPageVO.ContactInfo getPrimaryContactInfo(List<CustomerContactInfo> contactInfos) {
		if (CollectionUtils.isEmpty(contactInfos)) {
			return null;
		}
		// 获取主联系人
		CustomerContactInfo primaryContact = contactInfos.stream()
			.filter(contact -> Constants.PRIMARY_CONTACT_POSITION.equalsIgnoreCase(contact.getContactPosition()) && contact.getIsDefault())
			.findFirst()
			.orElse(null);
		// 如果没有找到则返回null;
		if (primaryContact == null) {
			return null;
		}
		CustomerPageVO.ContactInfo contactInfo = new CustomerPageVO.ContactInfo();
		contactInfo.setContactName(primaryContact.getContactName());
		contactInfo.setContactPhone(primaryContact.getContactPhone());
		contactInfo.setContactEmail(primaryContact.getContactEmail());
		return contactInfo;
	}

	@Mapping(target = "customerId", source = "customerInfoPo.id")
	CustomerSelectVO toCustomerSelectVO(CustomerInfoPO customerInfoPo);
}