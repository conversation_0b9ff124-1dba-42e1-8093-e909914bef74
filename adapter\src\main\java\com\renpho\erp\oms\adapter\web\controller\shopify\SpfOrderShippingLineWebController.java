package com.renpho.erp.oms.adapter.web.controller.shopify;

import com.renpho.erp.oms.application.channelmanagement.shopify.service.SpfOrderShippingLineService;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderShippingLineVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/shopify/**")
@RequestMapping("/shopify/shipping")
@AllArgsConstructor
public class SpfOrderShippingLineWebController {
	private final SpfOrderShippingLineService spfOrderShippingLineService;

	/**
	 * Shopify发货列表
	 * @param orderId
	 * @return R<List<SpfOrderShippingLineVO>>
	 */
	@GetMapping("/list")
	public R<List<SpfOrderShippingLineVO>> listByOrderId(@RequestParam Long orderId) {
		return spfOrderShippingLineService.listByOrderId(orderId);
	}
}
