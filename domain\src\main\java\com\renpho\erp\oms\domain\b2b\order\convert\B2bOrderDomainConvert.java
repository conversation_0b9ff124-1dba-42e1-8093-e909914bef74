package com.renpho.erp.oms.domain.b2b.order.convert;

import org.mapstruct.Mapper;

import com.renpho.erp.oms.domain.b2b.order.cmd.B2bOrderModifyCmd;
import com.renpho.erp.oms.domain.b2b.order.entity.B2bOrderAddress;
import com.renpho.erp.oms.domain.b2b.order.entity.B2bOrderCustomerInvoice;

/**
 * <AUTHOR>
 */
@Mapper
public interface B2bOrderDomainConvert {

	B2bOrderCustomerInvoice convert(B2bOrderModifyCmd.CustomerInvoice invoice);

	B2bOrderAddress convert(B2bOrderModifyCmd.Address address);
}
