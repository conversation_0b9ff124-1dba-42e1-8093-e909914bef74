package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.temu.repository.TemOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * Temu订单监视器
 * <AUTHOR>
 */
@Component
public class TemuOrderMonitor extends AbstractChannelOrderMonitor {

	private final TemOrderRepository temOrderRepository;

	public TemuOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			TemOrderRepository temOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.temOrderRepository = temOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.TEM_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return temOrderRepository.count(storeAuthorization.getTemuShopId(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		return storeAuthorization.getTemuShopId();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 按照temu 店铺id统计
		return storeAuthorizationList.stream()
			.filter(s -> storeNames.contains(s.getStoreName()))
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			// temu 店铺id
			.map(StoreAuthorizationVo.Authorization::getTemuShopId)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.map(temuShopId -> {
				StoreAuthorization storeAuthorization = new StoreAuthorization();
				storeAuthorization.setTemuShopId(temuShopId);
				// 店铺名称
				storeAuthorization.setStoreNames(storeAuthorizationList.stream()
					.filter(s -> Objects.nonNull(s.getAuthorization())
							&& StringUtils.equals(s.getAuthorization().getTemuShopId(), temuShopId))
					.map(StoreAuthorizationVo::getStoreName)
					.collect(Collectors.toSet()));
				return storeAuthorization;
			})
			.toList();

	}
}
