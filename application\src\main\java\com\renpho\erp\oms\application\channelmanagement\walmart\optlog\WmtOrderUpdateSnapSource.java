package com.renpho.erp.oms.application.channelmanagement.walmart.optlog;

import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtOrderService;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLog;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.renpho.erp.oplog.log.SnapshotDatatSource;

import lombok.AllArgsConstructor;

/**
 * *
 */
@Component
@AllArgsConstructor
public class WmtOrderUpdateSnapSource implements SnapshotDatatSource {

	private final WmtOrderService wmtOrderService;

	@Override
	public JSONObject getOldData(Object[] args) {
		// 将对象转换为 Json 字符串
		Long id = ((JSONObject) JSONObject.toJSON(args[0])).getObject("id", Long.class);

		return JSON.parseObject(JSON.toJSONString(wmtOrderService.getLogById(id)));
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		WmtOrderLog wmtOrderLog = wmtOrderService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(wmtOrderLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
