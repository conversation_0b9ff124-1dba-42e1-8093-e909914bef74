package com.renpho.erp.oms.application.vc.order.command;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.util.List;

/**
 * VC订单重新解析SKU命令
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单重新解析SKU命令")
public class VcOrderReparseSkusCmd {

    @NotEmpty(message = "{VC_ORDER_IDS_NOT_EMPTY}")
    @Schema(description = "订单ID列表", required = true)
    private List<Long> orderIds;

}
