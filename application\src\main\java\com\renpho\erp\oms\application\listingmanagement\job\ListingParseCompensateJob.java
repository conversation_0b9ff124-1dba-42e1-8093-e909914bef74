package com.renpho.erp.oms.application.listingmanagement.job;

import com.renpho.erp.oms.application.listingmanagement.dto.ListingCompensateParam;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.ListingParser;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.ListingParserRegister;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourceResolveStatusEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Listing解析-补偿job
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ListingParseCompensateJob {

	private final SalesChannelListingSourceRepository channelListingSourceRepository;
	private final ListingParserRegister listingParserRegister;

	/**
	 * 补偿解析Listing Job
	 */
	@XxlJob("compensateParseListingJob")
	public void compensateParseListingJob() {
		// 通过XXL-JOB参数
		ListingCompensateParam listingCompensateParam = ListingJobHelper.getListingCompensateParam();
		// 销售渠道编码集合
		Set<String> salesChannelCodeSet = listingCompensateParam.getSalesChannelCodeSet();
		// 如果为空，设置默认的销售渠道
		if (CollectionUtils.isEmpty(salesChannelCodeSet)) {
			salesChannelCodeSet = Set.of(ChannelCode.MCL_CHANNEL_CODE, ChannelCode.AMZ_CHANNEL_CODE, ChannelCode.TT_CHANNEL_CODE,
					ChannelCode.WMT_CHANNEL_CODE, ChannelCode.SPF_CHANNEL_CODE, ChannelCode.TEM_CHANNEL_CODE, ChannelCode.EBY_CHANNEL_CODE);
		}
		// 分批查询的条数，如果为空，默认200
		Integer pageSize = Optional.ofNullable(listingCompensateParam.getPageSize()).orElse(200);
		// 补偿解析Listing
		this.compensateParseListing(salesChannelCodeSet, pageSize);
	}

	/**
	 * 补偿解析Listing
	 * @param salesChannelCodeSet 销售渠道编码
	 * @param pageSize 分批查询的条数
	 */
	public void compensateParseListing(Set<String> salesChannelCodeSet, Integer pageSize) {
		// 解析状态：待解析，解析失败
		Set<Integer> resolveStatusSet = Set.of(ListingSourceResolveStatusEnum.WAIT_RESOLVE.getStatus(),
				ListingSourceResolveStatusEnum.RESOLVE_FAIL.getStatus());
		// 循环分批查询（直到无数据）
		Long lastId = null;
		while (true) {
			// 分批查询原始Listing
			List<SalesChannelListingSource> listingSourceList = channelListingSourceRepository
				.findBySalesChannelCodeAndResolveStatus(salesChannelCodeSet, lastId, pageSize, resolveStatusSet);
			// 无数据，结束循环
			if (CollectionUtils.isEmpty(listingSourceList)) {
				break;
			}
			// 相同渠道的相同Listing，存在多条记录，取创建时间最大的那条，需解析
			List<SalesChannelListingSource> distinctListingSourceList = this.getDistinctListingSourceList(listingSourceList);
			// 相同渠道的相同Listing，存在多条记录，非最新的记录，无需解析
			Set<Long> resolveCancelListingSourceIdSet = this.getResolveCancelListingSourceIdSet(distinctListingSourceList, listingSourceList);
			// 更新解析状态为作废
			channelListingSourceRepository.updateResolveStatusToCancel(resolveCancelListingSourceIdSet);
			// 遍历，解析Listing
			distinctListingSourceList.forEach(this::parseListing);
			// 上一页最后一条的id，用于检索下一页
			lastId = listingSourceList.stream()
				.max(Comparator.comparingLong(SalesChannelListingSource::getId))
				.map(SalesChannelListingSource::getId)
				.orElse(null);
		}
	}

	/**
	 * 解析Listing
	 * @param listingSource 原始Listing
	 */
	private void parseListing(SalesChannelListingSource listingSource) {
		// 获取解析器
		ListingParser parser = listingParserRegister.getParser(listingSource.getSalesChannelCode());
		if (Objects.isNull(parser)) {
			XxlJobHelper.log("销售渠道编码：{}，对应的解析器不存在", listingSource.getSalesChannelCode());
			return;
		}
		try {
			// 解析Listing
			parser.parseListing(listingSource);
		}
		catch (Exception e) {
			log.error("销售渠道编码：{}，原始listing主键id:{}，解析失败：", listingSource.getSalesChannelCode(), listingSource.getId(), e);
			XxlJobHelper.log("销售渠道编码：{}，原始listing主键id:{}，解析失败：{}", listingSource.getSalesChannelCode(), listingSource.getId(),
					e.getMessage());
		}
	}

	/**
	 * 获取相同渠道的相同Listing，存在多条记录，创建时间小的记录
	 * @param distinctListingSourceList 相同渠道的相同Listing，存在多条记录，取创建时间最大的那条
	 * @param listingSourceList Listing列表
	 * @return Set
	 */
	private @NotNull Set<Long> getResolveCancelListingSourceIdSet(List<SalesChannelListingSource> distinctListingSourceList,
			List<SalesChannelListingSource> listingSourceList) {
		Set<Long> listingSourceIdSet = distinctListingSourceList.stream().map(SalesChannelListingSource::getId).collect(Collectors.toSet());
		return listingSourceList.stream()
			.map(SalesChannelListingSource::getId)
			.filter(id -> !listingSourceIdSet.contains(id))
			.collect(Collectors.toSet());
	}

	/**
	 * 相同渠道的相同Listing，存在多条记录，取创建时间最大的那条
	 * @param listingSourceList 原始Listing列表
	 * @return List
	 */
	private @NotNull List<SalesChannelListingSource> getDistinctListingSourceList(List<SalesChannelListingSource> listingSourceList) {
		return listingSourceList.stream()
			.collect(Collectors.toMap(s -> s.getSalesChannelId() + "-" + s.getListingId(), Function.identity(),
					(s1, s2) -> s1.getCreateTime().isAfter(s2.getCreateTime()) ? s1 : s2))
			.values()
			.stream()
			.sorted(Comparator.comparing(SalesChannelListingSource::getId))
			.toList();
	}
}
