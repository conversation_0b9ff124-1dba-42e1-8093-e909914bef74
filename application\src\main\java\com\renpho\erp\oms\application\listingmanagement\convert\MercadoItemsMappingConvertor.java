package com.renpho.erp.oms.application.listingmanagement.convert;

import com.renpho.erp.apiproxy.mercado.model.items.GetItemsMappingResponse;
import com.renpho.erp.oms.domain.listingmanagement.model.MercadoGlobalItem;
import com.renpho.erp.oms.domain.listingmanagement.model.MercadoMarketplaceItem;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public final class MercadoItemsMappingConvertor {

	private MercadoItemsMappingConvertor() {
	}

	public static MercadoGlobalItem toMercadoGlobalItem(GetItemsMappingResponse itemsMappingResponse) {
		if (Objects.isNull(itemsMappingResponse)) {
			return null;
		}
		// 全球item
		MercadoGlobalItem mercadoGlobalItem = new MercadoGlobalItem();
		mercadoGlobalItem.setItemId(itemsMappingResponse.getItem_id());
		mercadoGlobalItem.setUserId(itemsMappingResponse.getUser_id());
		mercadoGlobalItem.setSiteId(itemsMappingResponse.getSite_id());
		mercadoGlobalItem.setParentId(itemsMappingResponse.getParent_id());
		mercadoGlobalItem.setDateCreated(itemsMappingResponse.getDate_created());
		// 市场item
		List<GetItemsMappingResponse.MarketplaceItem> mercadoMarketplaceItems = itemsMappingResponse.getMarketplace_items();
		if (CollectionUtils.isNotEmpty(mercadoMarketplaceItems)) {
			List<MercadoMarketplaceItem> marketplaceItemList = new ArrayList<>();
			mercadoMarketplaceItems.forEach(mercadoMarketplaceItem -> {
				MercadoMarketplaceItem marketplaceItem = new MercadoMarketplaceItem();
				marketplaceItem.setMarketItemId(mercadoMarketplaceItem.getItem_id());
				marketplaceItem.setMarketUserId(mercadoMarketplaceItem.getUser_id());
				marketplaceItem.setMarketSiteId(mercadoMarketplaceItem.getSite_id());
				marketplaceItem.setMarketParentId(mercadoMarketplaceItem.getParent_id());
				marketplaceItem.setMarketDateCreated(mercadoMarketplaceItem.getDate_created());
				marketplaceItemList.add(marketplaceItem);
			});
			mercadoGlobalItem.setMarketplaceItemList(marketplaceItemList);
		}
		return mercadoGlobalItem;
	}

}
