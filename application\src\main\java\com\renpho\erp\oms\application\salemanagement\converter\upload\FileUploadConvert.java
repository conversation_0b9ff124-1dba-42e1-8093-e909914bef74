package com.renpho.erp.oms.application.salemanagement.converter.upload;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.oms.application.salemanagement.dto.SaleOrderImportExcel;
import com.renpho.erp.oms.domain.upload.FileUploadDetail;
import com.renpho.erp.oms.domain.upload.FileUploadRecordAggRoot;
import com.renpho.erp.oms.domain.upload.FileUploadType;
import com.renpho.erp.oms.domain.upload.UploadStatus;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.List;
import java.util.stream.Collectors;

public class FileUploadConvert {
    public static FileUploadRecordAggRoot toAgg(String fileName, FileUploadType fileUploadType, List<SaleOrderImportExcel> saleOrderImportExcelList, SalesChannelVo salesChannelVo, String fileUrl) {
        FileUploadRecordAggRoot fileUploadRecordAggRoot = new FileUploadRecordAggRoot();
        fileUploadRecordAggRoot.setId(IdWorker.getId());
        fileUploadRecordAggRoot.setLanguage(LocaleContextHolder.getLocale().toString());
        fileUploadRecordAggRoot.setFileName(fileName);
        fileUploadRecordAggRoot.setFileUrl(fileUrl);
        fileUploadRecordAggRoot.setFileUploadType(fileUploadType);
        fileUploadRecordAggRoot.setChannelCode(salesChannelVo.getChannelCode());
        fileUploadRecordAggRoot.setChannelName(salesChannelVo.getChannelName());
        fileUploadRecordAggRoot.setTotalRows(saleOrderImportExcelList.size());
        fileUploadRecordAggRoot.setUploadStatus(UploadStatus.PARSING);
        fileUploadRecordAggRoot.setFileUploadDetailList(toDeailListAgg(saleOrderImportExcelList));
        return fileUploadRecordAggRoot;
    }

    private static List<FileUploadDetail> toDeailListAgg(List<SaleOrderImportExcel> saleOrderImportExcelList) {
        return saleOrderImportExcelList.stream().map(saleOrderImportExcel -> {
            FileUploadDetail fileUploadDetail = new FileUploadDetail();
            fileUploadDetail.setId(IdWorker.getId());
            fileUploadDetail.setStoreName(saleOrderImportExcel.getStoreName());
            fileUploadDetail.setChannelOrderNo(saleOrderImportExcel.getChannelOrderNo());
            fileUploadDetail.setReferOrderNo(saleOrderImportExcel.getReferOrderNo());
            fileUploadDetail.setOrderIdentification(saleOrderImportExcel.getOrderIdentification());
            fileUploadDetail.setCurrency(saleOrderImportExcel.getCurrency());
            fileUploadDetail.setOrderedTime(saleOrderImportExcel.getOrderedTime());
            fileUploadDetail.setPaidTime(saleOrderImportExcel.getPaidTime());
            fileUploadDetail.setRecipientName(saleOrderImportExcel.getRecipientName());
            fileUploadDetail.setRecipientPhone(saleOrderImportExcel.getRecipientPhone());
            fileUploadDetail.setPostalCode(saleOrderImportExcel.getPostalCode());
            fileUploadDetail.setCountryCode(saleOrderImportExcel.getCountryCode());
            fileUploadDetail.setProvince(saleOrderImportExcel.getProvince());
            fileUploadDetail.setCity(saleOrderImportExcel.getCity());
            fileUploadDetail.setAddress1(saleOrderImportExcel.getAddress1());
            fileUploadDetail.setAddress2(saleOrderImportExcel.getAddress2());
            fileUploadDetail.setEmail(saleOrderImportExcel.getEmail());
            fileUploadDetail.setPsku(saleOrderImportExcel.getPsku());
            fileUploadDetail.setFnSku(saleOrderImportExcel.getFnSku());
            fileUploadDetail.setQuantityShipment(saleOrderImportExcel.getQuantityShipment());
            fileUploadDetail.setProductPrice(saleOrderImportExcel.getProductPrice());
            fileUploadDetail.setProductTax(saleOrderImportExcel.getProductTax());
            fileUploadDetail.setProductDiscount(saleOrderImportExcel.getProductDiscount());
            fileUploadDetail.setProductDiscountTax(saleOrderImportExcel.getProductDiscountTax());
            fileUploadDetail.setFreightAmount(saleOrderImportExcel.getFreightAmount());
            fileUploadDetail.setFreightTax(saleOrderImportExcel.getFreightTax());
            fileUploadDetail.setFreightDiscount(saleOrderImportExcel.getFreightDiscount());
            fileUploadDetail.setFreightDiscountTax(saleOrderImportExcel.getFreightDiscountTax());
            fileUploadDetail.setWarehouseCode(saleOrderImportExcel.getWarehouseCode());
            fileUploadDetail.setWarehouseOrderNo(saleOrderImportExcel.getWarehouseOrderNo());
            fileUploadDetail.setCarrier(saleOrderImportExcel.getCarrier());
            fileUploadDetail.setTrackingNo(saleOrderImportExcel.getTrackingNo());
            fileUploadDetail.setShippedTime(saleOrderImportExcel.getShippedTime());
            fileUploadDetail.setDeliveredTime(saleOrderImportExcel.getDeliveredTime());
            return fileUploadDetail;
        }).collect(Collectors.toList());
    }
}
