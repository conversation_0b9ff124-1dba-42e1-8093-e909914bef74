package com.renpho.erp.oms.domain.vc.order.entity;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import cn.hutool.core.util.ObjectUtil;
import com.renpho.erp.oms.domain.vc.order.valueobject.VcOrderItemAmount;
import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单发票实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderInvoice {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 发票号
	 */
	private String invoiceNo;

	/**
	 * 开票日期
	 */
	private LocalDate invoiceDate;

	/**
	 * 税率
	 */
	private BigDecimal taxRate;

	/**
	 * 发票金额
	 */
	private BigDecimal tax;

	/**
	 * 生成id
	 * @param orderId 订单id
	 */
	public void generateId(Long orderId) {
		this.id = IdWorker.getId();
		this.orderId = orderId;
	}

	/**
	 * 将税平分到每个订单行
	 */
	public void taxCalculate(List<VcOrderItem> orderItemList) {
		// 过滤掉没接单的
		orderItemList = orderItemList.stream()
				.filter(VcOrderItem::isAccept)
				.toList();

		BigDecimal total = BigDecimal.ZERO;
		Map<Long, BigDecimal> priceMap = new HashMap<>();
		for (VcOrderItem vcOrderItem : orderItemList) {
			// 计算商品总价格
			total = total.add(vcOrderItem.getAmount().getProductPrice());
			priceMap.put(vcOrderItem.getId(), vcOrderItem.getAmount().getProductPrice());
		}
		// 摊分剩的数量
		BigDecimal remain = total;


		// 开始摊分
		for (int i = 0; i < orderItemList.size(); i++) {
			VcOrderItem vcOrderItem = orderItemList.get(i);
			if (Objects.equals(i, orderItemList.size() - 1)) {
				vcOrderItem.invoiceTax(remain);
			}

			// 根据商品总价摊分
			BigDecimal currentTax = priceMap.get(vcOrderItem.getId())
					.divide(total, 8, RoundingMode.HALF_UP)
					.multiply(tax)
					.setScale(2, RoundingMode.HALF_UP);
			vcOrderItem.invoiceTax(currentTax);

			remain = remain.subtract(currentTax);
		}
	}
}
