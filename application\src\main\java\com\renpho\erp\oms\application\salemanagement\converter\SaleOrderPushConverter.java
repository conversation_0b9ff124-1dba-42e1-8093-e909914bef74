package com.renpho.erp.oms.application.salemanagement.converter;

import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.salemanagement.dto.OrderItemPushDTO;
import com.renpho.erp.oms.application.salemanagement.dto.OrderPushDTO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderPO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售订单推送转换器
 *
 * <AUTHOR>
 */
public class SaleOrderPushConverter {
	/**
	 * 转换销售订单为订单推送DTO
	 *
	 * @param saleOrder 销售订单
	 * @return 订单推送DTO
	 */
	public static OrderPushDTO convertToOrderPushDTO(SaleOrderPO saleOrder, Map<Integer, CompanyVo> companyVoMap,Map<Long, List<SaleOrderItemPO>> orderId2Item) {
		if (Objects.isNull(saleOrder)) {
			return null;
		}
		OrderPushDTO orderPushDTO = new OrderPushDTO();
		// 店铺信息
		orderPushDTO.setStoreId(saleOrder.getStoreId());
		orderPushDTO.setStoreName(saleOrder.getStoreName());
		// 渠道信息
		orderPushDTO.setChannelCode(saleOrder.getChannelCode());
		orderPushDTO.setChannelName(saleOrder.getChannelName());
		// 订单号相关
		orderPushDTO.setOrderNo(saleOrder.getOrderNo());
		orderPushDTO.setShopOrderNo(saleOrder.getChannelOrderNo());
		// 货主信息（公司信息）
		Integer companyId = saleOrder.getCompanyId();
		orderPushDTO.setOwnerId(companyId);
		Optional.ofNullable(companyVoMap.get(companyId)).map(CompanyVo::getShortName).ifPresent(orderPushDTO::setOwnerShortName);
		// 货币和金额
		orderPushDTO.setCurrency(saleOrder.getCurrency());
		orderPushDTO.setItemAmount(saleOrder.getItemAmount());
		// 发货时间
		orderPushDTO.setDeliveredTime(saleOrder.getDeliveredTime());
		// 订单行信息
		orderPushDTO.setOrderItemList(convertOrderItems(orderId2Item.get(saleOrder.getId())));
		return orderPushDTO;
	}

	/**
	 * 转换销售订单行列表为订单推送DTO列表
	 *
	 * @param saleOrderItems 销售订单行列表
	 * @return 订单推送DTO列表
	 */
	private static List<OrderItemPushDTO> convertOrderItems(List<SaleOrderItemPO> saleOrderItems) {
		if (saleOrderItems == null) {
			return new ArrayList<>();
		}
		// 根据psku分组
		Map<String, List<SaleOrderItemPO>> pskuListMap = saleOrderItems.stream().collect(Collectors.groupingBy(SaleOrderItemPO::getPsku));
		// 转换为OrderItemPushDTO列表
		List<OrderItemPushDTO> orderItemPushDTOList = new ArrayList<>();
		for (Map.Entry<String, List<SaleOrderItemPO>> entry : pskuListMap.entrySet()) {
			OrderItemPushDTO orderItemPushDTO = new OrderItemPushDTO();
			orderItemPushDTO.setPsku(entry.getKey());
			// 计算数量和金额
			List<SaleOrderItemPO> saleOrderItemList = entry.getValue();
			// 数量
			orderItemPushDTO.setQuantity(saleOrderItemList.stream().mapToInt(item -> Optional.ofNullable(item.getQuantityShipment()).orElse(0)).sum());
			// 商品金额，未税、不含折扣、总价
			orderItemPushDTO.setProductPrice(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getProductPrice()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 商品税额，原始的税额
			orderItemPushDTO.setProductTax(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getProductTax()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 商品折扣，卖家折扣，不含平台折扣
			orderItemPushDTO.setProductDiscount(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getProductDiscount()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 折扣税额，商品折扣导致的税额折扣
			orderItemPushDTO.setProductDiscountTax(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getProductDiscountTax()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 运费金额，未税、不含折扣、总价
			orderItemPushDTO.setFreightAmount(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getFreightAmount()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 运费税额，原始的税额
			orderItemPushDTO.setFreightTax(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getFreightTax()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 运费折扣，卖家折扣，不含平台折扣
			orderItemPushDTO.setFreightDiscount(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getFreightDiscount()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 运费折扣税，运费折扣导致的税额折扣
			orderItemPushDTO.setFreightDiscountTax(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getFreightDiscountTax()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			// 总金额（两个金额之和 + 两个税额之和 - 四个折扣之和）
			orderItemPushDTO.setTotalAmount(saleOrderItemList.stream()
					.map(item -> Optional.ofNullable(item.getTotalAmount()).orElse(BigDecimal.ZERO))
					.reduce(BigDecimal.ZERO, BigDecimal::add));
			orderItemPushDTOList.add(orderItemPushDTO);
		}
		return orderItemPushDTOList;
	}

}