package com.renpho.erp.oms.application.ordershipaudit.cmd;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 发货审核创建
 *
 * <AUTHOR>
 */
@Data
public class OrderShipAuditCmd {

	/**
	 * 订单id
	 */
	@NotNull(message = "订单id不能为空")
	private Long orderId;

	/**
	 * 发货服务类型
	 */
	@NotNull(message = "发货服务类型不能为空")
	private Integer fulfillmentServiceType;

	/**
	 * 渠道门店id
	 */
	private Integer channelStoreId;

	/**
	 * 发货仓库id
	 */
	@NotNull(message = "发货仓库id不能为空")
	private Integer warehouseId;

	/**
	 * 发货仓库code
	 */
	@NotEmpty(message = "发货仓库code不能为空")
	private String warehouseCode;

	/**
	 * 三方仓库编码
	 */
	private String thirdPartWarehouseCode;

	/**
	 * 承运商编码，自建仓发货需传（temu，ecom,ebay）
	 */
	private String carrierCode;

	/**
	 * 承运商服务编码
	 */
	private String carrierServiceCode;

	/**
	 * 包裹类型编码（ecom需传值）
	 */
	private String packageTypeCode;

	/**
	 * 运输速度 仅亚马逊 沃尔玛
	 */
	private String shippingSpeedCategory;

	/**
	 * 通知邮箱 亚马逊沃尔玛
	 */
	private String noticeEmail;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * Block AMZL
	 */
	private Boolean blockAmzl;

	/**
	 * Blank boxes
	 */
	private Boolean blankBox;

	/**
	 * 仅京东仓 对应关系来自京东仓文档 1-B2C 2-B2B 3-WarehouseOnly
	 */
	private Integer jdOrderType;

	/**
	 * 包裹长度cm
	 */
	private BigDecimal length;

	/**
	 * 包裹宽度cm
	 */
	private BigDecimal width;

	/**
	 * 包裹高度cm
	 */
	private BigDecimal height;

	/**
	 * 包裹重量kg
	 */
	private BigDecimal weight;

	/**
	 * 物流公司id(目前只有temu在用)
	 */
	private Long shipCompanyId;

	/**
	 * 物流公司服务id(目前只有temu在用)
	 */
	private Long shipChannelId;

	/**
	 * 报价id
	 */
	private String shippingQuoteId;

	/**
	 * 利率id
	 */
	private String rateId;

	/**
	 * 亚马逊JP站点交货开始时间
	 */
	private LocalDateTime deliveryStartTime;

	/**
	 * 亚马逊JP站点交货结束时间
	 */
	private LocalDateTime deliveryEndTime;

	/**
	 * 亚马逊JP站点交货说明
	 */
	private String deliveryInstruction;

	/**
	 * 亚马逊JP站点邻居名字
	 */
	private String neighborName;

	/**
	 * 亚马逊JP站点邻居房号
	 */
	private String neighborRoom;

	@Valid
	@NotEmpty(message = "发货商品行不能为空")
	private List<Item> items;

	/**
	 * 发货审核商品行信息
	 */
	@Data
	public static class Item {

		/**
		 * 订单商品行id
		 */
		@NotNull(message = "订单商品行id不能为空")
		private Long itemId;

		/**
		 * 渠道msku
		 */
		private String channelMsku;

	}

}
