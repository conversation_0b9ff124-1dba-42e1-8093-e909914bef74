package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderItem;
import org.apache.commons.compress.utils.Lists;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.List;
import java.util.Objects;

public class MclOrderItemAppConvertor {
	public static List<MclOrderItem> toDomainList(List<MclOrderDTO.OrderItem> orderItems) {
		List<MclOrderItem> mclOrderItemList = Lists.newArrayList();
		orderItems.forEach(orderItem -> mclOrderItemList.add(toDomain(orderItem)));
		return mclOrderItemList;
	}

	public static MclOrderItem toDomain(MclOrderDTO.OrderItem orderItem) {
		if (Objects.nonNull(orderItem)) {
			MclOrderItem mclOrderItem = new MclOrderItem();
			if (Objects.nonNull(orderItem.getItem())) {
				MclOrderDTO.Item item = orderItem.getItem();
				mclOrderItem.setItemId(item.getId());
				mclOrderItem.setTitle(item.getTitle());
				mclOrderItem.setCategoryId(item.getCategory_id());
				mclOrderItem.setVariationId(item.getVariation_id());
				mclOrderItem.setSellerCustomField(item.getSeller_custom_field());
				mclOrderItem.setVariationAttributes(CollectionUtil.isNotEmpty(item.getVariation_attributes())
						? JsonUtils.toJson(item.getVariation_attributes()) : null);
				mclOrderItem.setWarranty(item.getWarranty());
				mclOrderItem.setItemCondition(item.getCondition());
				mclOrderItem.setSellerSku(item.getSeller_sku());
				mclOrderItem.setParentItemId(item.getParent_item_id());
			}
			mclOrderItem.setQuantity(orderItem.getQuantity());
			mclOrderItem.setUnitPrice(orderItem.getUnit_price());
			mclOrderItem.setFullUnitPrice(orderItem.getFull_unit_price());
			mclOrderItem.setCurrencyId(orderItem.getCurrency_id());
			mclOrderItem.setManufacturingDays(orderItem.getManufacturing_days());
			mclOrderItem.setSaleFee(orderItem.getSale_fee());
			mclOrderItem.setBaseExchangeRate(orderItem.getBase_exchange_rate());
			return mclOrderItem;
		}
		return null;
	}

}
