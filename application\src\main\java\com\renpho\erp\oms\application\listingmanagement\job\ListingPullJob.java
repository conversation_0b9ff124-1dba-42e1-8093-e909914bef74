package com.renpho.erp.oms.application.listingmanagement.job;

import com.renpho.erp.oms.application.listingmanagement.strategy.puller.ListingPuller;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.ListingPullerRegister;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

/**
 * Listing拉取job
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class ListingPullJob {

	private final ListingPullerRegister listingPullerRegister;

	@XxlJob("pullListingJob")
	public void pullListingJob() {
		// 通过XXL-JOB参数获取销售渠道编码
		Set<String> salesChannelCodeSet = ListingJobHelper.getSalesChannelCodeSetByPullParam();
		// 如果为空，设置默认的销售渠道
		if (CollectionUtils.isEmpty(salesChannelCodeSet)) {
			salesChannelCodeSet = Set.of(ChannelCode.MCL_CHANNEL_CODE,
					ChannelCode.TT_CHANNEL_CODE,
					ChannelCode.WMT_CHANNEL_CODE,
					ChannelCode.SPF_CHANNEL_CODE,
					ChannelCode.TEM_CHANNEL_CODE,
					ChannelCode.EBY_CHANNEL_CODE);
		}
		// 逐个平台拉取
		salesChannelCodeSet.forEach(salesChannelCode -> {
			// 根据销售渠道编码获取拉取器
			ListingPuller listingPuller = listingPullerRegister.getPuller(salesChannelCode);
			if (Objects.isNull(listingPuller)) {
				XxlJobHelper.log("销售渠道编码：{}，对应的拉取器不存在", salesChannelCode);
				return;
			}
			try {
				// 拉取Listing
				listingPuller.pullListing();
			}
			catch (Exception e) {
				log.error("销售渠道编码：{}，拉取Listing失败：", salesChannelCode, e);
				XxlJobHelper.log("销售渠道编码：{}，拉取Listing失败：{}", salesChannelCode, e.getMessage());
			}
		});
	}

}
