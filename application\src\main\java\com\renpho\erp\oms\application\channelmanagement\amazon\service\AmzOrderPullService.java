package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.orders.GetOrdersRequest;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.common.service.AbstractDefaultPullOrderService;

import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.domain.config.model.ShopSyncConfig;
import com.renpho.erp.oms.domain.config.repository.ShopSyncConfigRepository;
import com.renpho.erp.oms.infrastructure.common.enums.AmazonApiCodeEnums;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.karma.dto.R;
import io.swagger.client.model.orders.GetOrdersResponse;
import io.swagger.client.model.orders.Order;
import io.swagger.client.model.orders.OrdersList;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.hutool.core.thread.ThreadUtil.sleep;

@Service
@Slf4j
@AllArgsConstructor
public class AmzOrderPullService extends AbstractDefaultPullOrderService {

	private static final Long SLEEP_TIME_MILLISECONDS = 30000L;
	private final AmazonClient amazonClient;
	private final ShopSyncConfigRepository shopSyncConfigRepository;
	private final AmzOrderCreateJsonService amzOrderCreateJsonService;

	protected ShopSyncConfig getConfigDefault(String channelCode, StoreAuthorizationVo storeAuthorization) {
		return shopSyncConfigRepository.getConfigByChannelAndSellerId(channelCode, storeAuthorization.getAuthorization().getAmzSellerId());
	}

	/**
	 * 计算拉单时间范围
	 *
	 * @return Pair<LocalDateTime, LocalDateTime>
	 */
	protected Pair<LocalDateTime, LocalDateTime> calPullTimeRange(ShopSyncConfig shopSyncConfig) {
		return super.defaultcalPullTimeRange(shopSyncConfig);
	}

	/**
	 * @param storeAuthorization 店铺授权集合
	 * @param lastUpdatedAfter 更新时间之后
	 * @param lastUpdatedBefore 更新时间之前
	 * @param orderIds 订单id集合
	 * @param shopSyncConfig 拉取配置
	 */
	protected void doPull(StoreAuthorizationVo storeAuthorization, LocalDateTime lastUpdatedAfter, LocalDateTime lastUpdatedBefore,
			List<String> orderIds, ShopSyncConfig shopSyncConfig) {

		// 构建参数查询
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSellerId(storeAuthorization.getAuthorization().getAmzSellerId());
		String nextToken = null;
		List<Order> amazonOrders = Lists.newArrayList();
		boolean successFlag = true;
		// 循环拉取至nextToken为空
		do {
			GetOrdersRequest getOrdersRequest = new GetOrdersRequest();
			if (CollectionUtil.isNotEmpty(orderIds)) {
				getOrdersRequest.setAmazonOrderIds(orderIds);
			}
			getOrdersRequest.setLastUpdatedAfter(DateUtil.convertToUTC(lastUpdatedAfter));
			getOrdersRequest.setLastUpdatedBefore(DateUtil.convertToUTC(lastUpdatedBefore));
			getOrdersRequest.setNextToken(nextToken);
			getOrdersRequest.setMarketplaceIds(Arrays.asList(storeAuthorization.getAuthorization().getAmzMarketplaceId()));
			R<GetOrdersResponse> ordersResponse = amazonClient.getOrders(shopAccount, getOrdersRequest);
			log.info("查询积加亚马逊订单，返回信息为{}", JsonUtils.toJson(ordersResponse));
			// 限流
			if (AmazonApiCodeEnums.REQUEST_LIMITING.getCode().equals(ordersResponse.getCode())) {
				sleep(SLEEP_TIME_MILLISECONDS, TimeUnit.MILLISECONDS);
				// 暂停后继续拉取
				continue;
			}
			// 失败停止拉取
			if (!ordersResponse.isSuccess()) {
				log.error("查询积加亚马逊订单，失败原因为{}", ordersResponse.getMessage());
				// todo 异常通知
				if (Objects.nonNull(shopSyncConfig)) {
					// 记录日志
					shopSyncConfig.setExcCause(ordersResponse.getMessage());
					shopSyncConfig.setExcType(ordersResponse.getCode());
					shopSyncConfigRepository.update(shopSyncConfig);
					successFlag = false;
					break;
				}
			}
			if (Objects.isNull(ordersResponse.getData())) {
				break;
			}
			OrdersList payload = ordersResponse.getData().getPayload();
			amazonOrders.addAll(payload.getOrders());
			nextToken = payload.getNextToken();
		}
		while (StringUtils.isNotEmpty(nextToken));
		if (!successFlag) {
			return;
		}
		// 入库
		if (CollectionUtil.isNotEmpty(amazonOrders)) {
			amzOrderCreateJsonService.insertOrUpdate(amazonOrders, shopAccount.getSellerId());
		}
		if (Objects.nonNull(shopSyncConfig)) {
			// 记录此次拉取日志
			shopSyncConfig.getConfigContent().setLatestUpdateTime(lastUpdatedBefore);
			shopSyncConfigRepository.update(shopSyncConfig);
		}

	}

}
