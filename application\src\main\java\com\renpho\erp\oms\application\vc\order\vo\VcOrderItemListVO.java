package com.renpho.erp.oms.application.vc.order.vo;

import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * VC订单商品列表VO
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单商品列表")
public class VcOrderItemListVO implements VO {

    @Schema(description = "商品ID")
    private Long id;

    @Schema(description = "订单ID")
    private Long orderId;

    @Schema(description = "采购SKU")
    private String psku;

    @Schema(description = "条码")
    private String barcode;

    @Schema(description = "ASIN")
    private String asin;

    @Schema(description = "下单数量")
    private Integer orderedQuantity;

    @Schema(description = "接受数量")
    private Integer acceptedQuantity;

    @Schema(description = "发货数量")
    private Integer shippedQuantity;

    @Schema(description = "图片ID")
    private String imageId;

    @Schema(description = "图片URL")
    private String imageUrl;
}
