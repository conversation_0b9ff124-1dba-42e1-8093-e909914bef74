package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 亚马逊VC付款方式枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AmzVcPaymentMethod {

	/**
	 * 发票付款
	 */
	INVOICE("Invoice", "发票付款"),

	/**
	 * 寄售付款
	 */
	CONSIGNMENT("Consignment", "寄售付款"),

	/**
	 * 信用卡付款
	 */
	CREDIT_CARD("CreditCard", "信用卡付款"),

	/**
	 * 预付款
	 */
	PREPAID("Prepaid", "预付款");

	private final String value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return AmzVcPaymentMethod
	 */
	public static AmzVcPaymentMethod getByValue(String value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
