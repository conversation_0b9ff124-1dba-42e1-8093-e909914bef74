package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingLine;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;

public class SkuMappingImportExcelConvertor {

    public static SkuMappingAggRoot convertByImportExcleList(OumUserInfoRes oumUserInfoRes, StoreVo storeVo,
                                                             List<SkuMappingImportExcel> skuMappingImportExcelList) {
        SkuMappingImportExcel skuMappingImportExcel = skuMappingImportExcelList.get(0);
        SkuMappingAggRoot skuMappingAggRoot = new SkuMappingAggRoot();
        skuMappingAggRoot.setChannelId(storeVo.getSalesChannelId());
        skuMappingAggRoot.setChannelCode(storeVo.getSalesChannelCode());
        skuMappingAggRoot.setStoreType(storeVo.getStoreType());
        skuMappingAggRoot.setMsku(skuMappingImportExcel.getMsku());
        skuMappingAggRoot.setFulfillmentType(FulfillmentType.enumOf(skuMappingImportExcel.getFulfillmentType()));
        skuMappingAggRoot.setAsin(skuMappingImportExcel.getAsin());
        skuMappingAggRoot.setStoreId(storeVo.getId());
        skuMappingAggRoot.setFbaInventoryCounted(skuMappingImportExcel.getFbaInventoryCounted());
        if (Objects.nonNull(oumUserInfoRes)) {
            skuMappingAggRoot.setOperatorId(oumUserInfoRes.getId());
        }
        skuMappingAggRoot.setSkuMappingLineList(convertByImportExcleList(skuMappingImportExcelList));
        return skuMappingAggRoot;
    }

    public static List<SkuMappingLine> convertByImportExcleList(List<SkuMappingImportExcel> skuMappingImportExcelList) {
        return skuMappingImportExcelList.stream().map(skuMappingImportExcel -> {
            SkuMappingLine skuMappingLine = new SkuMappingLine();
            skuMappingLine.setFnsku(skuMappingImportExcel.getFnSku());
            skuMappingLine.setPsku(skuMappingImportExcel.getPsku());
            skuMappingLine.setFnskuQuantity(skuMappingImportExcel.getFnskuQuantity());
            skuMappingLine.setRetailPrice(skuMappingImportExcel.getRetailPrice());
            return skuMappingLine;
        }).collect(Collectors.toList());
    }

}
