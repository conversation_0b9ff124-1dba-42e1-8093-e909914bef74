<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.renpho.erp</groupId>
        <artifactId>oms-parent</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>oms-adapter</artifactId>
    <name>${project.artifactId}</name>
    <description>接口层、模块</description>

    <dependencies>
        <!-- ==================================== -->
        <!-- 内部依赖 -->
        <!-- ==================================== -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>oms-application</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>erp-operator-log</artifactId>
                    <groupId>com.renpho.erp</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.renpho.erp</groupId>
            <artifactId>erp-operator-log</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>oms-client</artifactId>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
