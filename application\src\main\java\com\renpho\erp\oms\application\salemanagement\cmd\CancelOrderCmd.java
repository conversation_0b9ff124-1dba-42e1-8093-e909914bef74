package com.renpho.erp.oms.application.salemanagement.cmd;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 取消订单cmd
 * <AUTHOR>
 */
@Data
public class CancelOrderCmd {

	/**
	 * 订单id
	 */
	@NotNull(message = "订单id不能为空")
	private Long orderId;

	/**
	 * 取消原因
	 */
	@NotBlank(message = "{SALE_ORDER_CANCEL_REASON_EMPTY_CHECK}")
	private String cancelReason;

}
