package com.renpho.erp.oms.domain.vc.order.repository;

import java.util.List;
import java.util.Set;

import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;

/**
 * <AUTHOR>
 */
public interface AmzVcOrderSourceJsonRepository {

	/**
	 * 查询需要解析的亚马逊vc源订单主键集
	 * @param storeIds 店铺id集
	 * @param vcPurchaseNos 采购单号集
	 * @param size 分批的条数
	 * @return List
	 */
	List<Long> findNeedParseAmzVcOrderIds(Set<Integer> storeIds, Set<String> vcPurchaseNos, Integer size);

	/**
	 * 查询亚马逊订单聚合根
	 * @param id 亚马逊vc源订单主键
	 * @return AmzVcOrderAggRoot
	 */
	AmzVcOrderAggRoot findById(Long id);

	/**
	 * 更新为已解析
	 * @param id 亚马逊vc源订单主键
	 * @param parseOrderStatus 解析状态
	 * @param failCount 失败次数
	 */
	void updateParseStatus(Long id, Integer parseOrderStatus, Integer failCount);
}
