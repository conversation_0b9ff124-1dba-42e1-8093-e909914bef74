package com.renpho.erp.oms.application.channelmanagement.common.service;

import cn.hutool.core.collection.CollectionUtil;

import com.renpho.erp.mdm.client.store.command.StoreAuthorizationQuery;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.common.dto.PullOrerParam;
import com.renpho.erp.oms.application.channelmanagement.common.enums.PullMode;
import com.renpho.erp.oms.domain.config.model.ShopSyncConfig;
import com.renpho.erp.oms.domain.config.model.ShopSyncConfigContent;
import com.renpho.erp.oms.domain.config.repository.ShopSyncConfigRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnums;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.renpho.erp.oms.infrastructure.common.util.MDCUtil;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.util.Pair;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractDefaultPullOrderService implements PullOrderservice {
	@Resource
	private ShopSyncConfigRepository shopSyncConfigRepository;
	@Resource
	private StoreClient storeClient;
	@Resource(name = "pullOrderExecutor")
	private ExecutorService pullOrderExecutor;

	@Override
	public void pullOrder(PullOrerParam pullOrerParam, String channelCode, PullMode pullMode) {
		// 获取店铺及授权信息
		StoreAuthorizationQuery storeAuthorizationQuery = new StoreAuthorizationQuery();
		storeAuthorizationQuery.setChannelCode(channelCode);
		storeAuthorizationQuery.setStoreType(StoreTypeEnums.SC.getValue());
		// 任务自动执行时填充分片参数
		Optional.ofNullable(JobShardContext.getShardInfo()).ifPresent(jobShardInfo -> {
			storeAuthorizationQuery.setShardIndex(jobShardInfo.getShardIndex());
			storeAuthorizationQuery.setShardTotal(jobShardInfo.getShardTotal());
		});

		List<StoreAuthorizationVo> storeAuthorizationList = storeClient.getShardingStoreAuthorizations(storeAuthorizationQuery);
		if (CollectionUtil.isEmpty(storeAuthorizationList)) {
			log.info("查询店铺授权信息为空，任务结束");
			return;
		}

		// 按模式处理
		if (pullMode == PullMode.ASYNC) {
			handlePullOrdersAsync(storeAuthorizationList, pullOrerParam, channelCode);
		}
		else {
			handlePullOrdersSync(storeAuthorizationList, pullOrerParam, channelCode);
		}
	}

	private void handlePullOrdersSync(List<StoreAuthorizationVo> storeAuthorizationList, PullOrerParam pullOrerParam, String channelCode) {
		log.info("Starting synchronous pull order process...");
		for (StoreAuthorizationVo storeAuthorization : storeAuthorizationList) {
			processSingleStore(storeAuthorization, pullOrerParam, channelCode);
		}
	}

	private void handlePullOrdersAsync(List<StoreAuthorizationVo> storeAuthorizationList, PullOrerParam pullOrerParam, String channelCode) {
		log.info("Starting asynchronous pull order process...");
		try {
			List<CompletableFuture<Void>> futures = storeAuthorizationList.stream()
				.map(storeAuthorization -> handleCompletableFuture(storeAuthorization, pullOrerParam, channelCode))
				.collect(Collectors.toList());
			// 等待所有异步任务完成
			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
		}
		catch (Exception e) {
			log.error("异步任务执行异常：", e);
		}
		log.info("All asynchronous pull order tasks completed.");
	}

	private CompletableFuture<Void> handleCompletableFuture(StoreAuthorizationVo storeAuthorization, PullOrerParam pullOrerParam,
			String channelCode) {
		return CompletableFuture
			.runAsync(MDCUtil.wrapWithMDC(() -> processSingleStore(storeAuthorization, pullOrerParam, channelCode)), pullOrderExecutor)
			.handle((result, ex) -> {
				if (ex != null) {
					log.error("拉取店铺{}订单异步任务时出错：", storeAuthorization.getStoreName(), ex);
				}
				return null;
			})
			.thenAccept(o -> {
			});
	}

	private void processSingleStore(StoreAuthorizationVo storeAuthorization, PullOrerParam pullOrerParam, String channelCode) {
		try {
			if (Objects.nonNull(pullOrerParam)) {
				// 亚马逊判断是否有sellerId输入
				if (ChannelCode.AMZ_CHANNEL_CODE.equals(channelCode)) {
					if (CollectionUtil.isNotEmpty(pullOrerParam.getStoreIds())
							&& (pullOrerParam.getSellerIds().contains(storeAuthorization.getAuthorization().getAmzSellerId()))) {
						doPull(storeAuthorization, pullOrerParam.getStartDateUTC(), pullOrerParam.getEndDateUTC(),
								pullOrerParam.getOrderIds(), null);
					}
					else {
						return;
					}
				}
				// 手动输入参数获取
				doPull(storeAuthorization, pullOrerParam.getStartDateUTC(), pullOrerParam.getEndDateUTC(), pullOrerParam.getOrderIds(),
						null);
			}
			else {
				// 定时任务自动获取
				ShopSyncConfig shopSyncConfig = getConfigDefault(channelCode, storeAuthorization);
				if (Objects.nonNull(shopSyncConfig)) {
					Pair<LocalDateTime, LocalDateTime> timeRange = calPullTimeRange(shopSyncConfig);
					doPull(storeAuthorization, timeRange.getFirst(), timeRange.getSecond(), null, shopSyncConfig);
				}
				else {
					log.warn("店铺{}的同步配置为空，跳过拉单", storeAuthorization.getStoreName());
				}
			}
		}
		catch (Exception ex) {
			log.error("拉取店铺{}订单时发生错误: {}", storeAuthorization.getStoreName(), ex.getMessage(), ex);
		}
	}

	protected ShopSyncConfig getConfigDefault(String channelCode, StoreAuthorizationVo storeAuthorization) {
		return shopSyncConfigRepository.getConfigByChannelAndStore(channelCode, storeAuthorization.getId());
	}

	protected abstract Pair<LocalDateTime, LocalDateTime> calPullTimeRange(ShopSyncConfig shopSyncConfig);

	protected abstract void doPull(StoreAuthorizationVo storeAuthorization, LocalDateTime lastUpdateTimeAfter,
			LocalDateTime lastUpdateTimeBefore, List<String> orderIds, ShopSyncConfig shopSyncConfig);

	public Pair<LocalDateTime, LocalDateTime> defaultcalPullTimeRange(ShopSyncConfig shopSyncConfig) {
		// 计算拉取时间
		// 获取当前UTC时间(服务器所在时间为utc时间)
		LocalDateTime now = LocalDateTime.now();
		ShopSyncConfigContent configContent = shopSyncConfig.getConfigContent();
		// 抓取订单开始时间 = 上次抓取订单的结束时间
		LocalDateTime startTime = configContent.getLatestUpdateTime();
		// 抓取订单结束的时间 = 开始时间 + 时间跨度
		LocalDateTime endTime = startTime.plusMinutes(configContent.getLastUpdatedBeforeCurrOffset());
		log.info("Initial startTime: {}", startTime);
		log.info("Initial endTime: {}", endTime);
		// 处理时间交叉

		// 开始时间往前位移
		startTime = startTime.minusMinutes(configContent.getCrossStartTimeOffset());
		log.info("Adjusted startTime with crossStartTimeOffset: {}", startTime);
		// 抓单结束时间大于当前时间
		if (endTime.isAfter(LocalDateTime.now())) {
			log.info("startTime is after endTime, correcting...");
			// 抓单结束时间=当前时间 - 设置结束位移交叉时间
			endTime = now.minusMinutes(configContent.getCrossEndTimeOffset());
			// 如果 -位移时间 之后抓单结束时间小于抓单开始时间的话，抓单结束时间等于抓单开始时间
			if (endTime.isBefore(startTime)) {
				endTime = startTime;
			}
		}
		log.info("Final time range: startTime = {}, endTime = {}", startTime, endTime);
		return Pair.of(startTime, endTime);
	}
}
