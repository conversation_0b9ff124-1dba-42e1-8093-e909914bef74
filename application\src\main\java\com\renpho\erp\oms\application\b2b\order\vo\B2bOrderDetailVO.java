package com.renpho.erp.oms.application.b2b.order.vo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimal4Serializer;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

/**
 * B2B订单详情VO
 */
@Data
public class B2bOrderDetailVO implements VO {

	private Long id;

	/**
	 * 订单基本信息
	 */
	private OrderInfo orderInfo;

	/**
	 * 客户信息
	 */
	private CustomerInfo customerInfo;

	/**
	 * 地址信息
	 */
	private AddressInfo addressInfo;

	/**
	 * 金额信息
	 */
	private AmountInfo amountInfo;

	/**
	 * 发货要求
	 */
	private ShipmentRequirements shipmentRequirements;

	/**
	 * 备注信息
	 */
	private List<NoteInfo> noteInfo;

	/**
	 * 商品信息
	 */
	private List<ProductInfo> products;

	/**
	 * 订单基本信息
	 */
	@Data
	public static class OrderInfo implements VO {
		private Long id;

		/**
		 * 客户id
		 */
		private Long customerId;

		/**
		 * B2B订单号
		 */
		private String orderNo;

		/**
		 * 客户参考号
		 */
		private String referNo;

		/**
		 * 发票号
		 */
		private String invoiceNo;

		/**
		 * 订单状态：1-待处理 2-订单审核中 3-备货中 4-部分发货 5-已发货 6-已完结 7-已取消
		 */
		@Trans(type = TransType.DICTIONARY, key = "ToB_order_status", ref = "orderStatusName")
		private Integer orderStatus;

		/**
		 * 订单状态名称
		 */
		private String orderStatusName;

		/**
		 * 订单创建时间
		 */
		private LocalDateTime createTime;

		/**
		 * 订单最后更新时间
		 */
		private LocalDateTime updateTime;

		/**
		 * 发票时间
		 */
		private LocalDateTime invoiceTime;

		private LocalDateTime shippedTime;
	}

	/**
	 * 客户信息
	 */
	@Data
	public static class CustomerInfo {
		/**
		 * 客户公司名称
		 */
		private String companyName;

		/**
		 * 客户参考号
		 */
		private String referNo;

		/**
		 * 客户所在国家代码
		 */
		private String country;

		/**
		 * 客户所在国家名称
		 */
		private String countryName;

		/**
		 * 销售公司名称
		 */
		private String salesCompanyName;

		/**
		 * 贸易条款编码（如FOB、CIF等）
		 */
		private String incotermsCode;

		/**
		 * 贸易条款名称
		 */
		private String incotermsName;

		/**
		 * 付款条款编码（如T/T、L/C等）
		 */
		private String paymentTermsCode;

		/**
		 * 付款条款名称
		 */
		private String paymentTermsName;

		/**
		 * 海外区域经理姓名
		 */
		private String countryManagerName;

		/**
		 * 销售助理姓名
		 */
		private String salesAssistantName;

		/**
		 * 保险公司名称
		 */
		private String insuranceCompany;

		/**
		 * 投保币种
		 */
		private String insuredCurrency;

		/**
		 * 投保金额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal insuredAmount;

		/**
		 * 未结订单列表（欠款订单）
		 */
		private List<B2bCustomerOrderDebtVO> outstandingOrders;

		/**
		 * 已用额度币种
		 */
		private String usedCreditCurrency;

		/**
		 * 已用额度金额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal usedCreditAmount;

		/**
		 * 已用额度比例（已用额度/投保金额）
		 */
		@JsonSerialize(using = BigDecimal4Serializer.class)
		private BigDecimal usedCreditRatio;
	}

	/**
	 * 地址信息
	 */
	@Data
	public static class AddressInfo {
		/**
		 * 城市
		 */
		private String city;

		/**
		 * 国家代码
		 */
		private String country;

		/**
		 * 国家名称
		 */
		private String countryName;

		/**
		 * 邮政编码
		 */
		private String postalCode;

		/**
		 * 详细地址
		 */
		private String address;

		/**
		 * 收件人姓名
		 */
		private String attnTo;

		/**
		 * 联系电话
		 */
		private String contactNumber;

		/**
		 * 收货方公司
		 */
		private String shippingCompany;

		/**
		 * 备注
		 */
		private String comment;
	}

	/**
	 * 金额信息
	 */
	@Data
	public static class AmountInfo {
		/**
		 * 订单币种
		 */
		private String currency;

		/**
		 * 商品总价（不含税）
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal productPrice;

		/**
		 * 税费总额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal tax;

		/**
		 * 运费
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal shippingFee;

		/**
		 * 订单总金额（含税含运费）
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal totalAmount;

		/**
		 * 已收款金额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal receiptAmount;

		/**
		 * 收款比例（已收款金额/订单总金额）
		 */
		@JsonSerialize(using = BigDecimal4Serializer.class)
		private BigDecimal receiptRate;

		/**
		 * 其他收入
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal otherFees;

		/**
		 * 费用明细列表（推广费、广告费等）
		 */
		private List<FeeDetailInfo> feesDetail;
	}

	/**
	 * 费用明细信息
	 */
	@Data
	public static class FeeDetailInfo implements VO {
		private Long id;

		/**
		 * 费用类型：1-推广费 2-广告费
		 */
		@Trans(type = TransType.DICTIONARY, key = "ToB_fee_type", ref = "feeTypeName")
		private Integer feeType;

		/**
		 * 费用类型名称
		 */
		private String feeTypeName;

		/**
		 * 费用金额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal feeAmount;
	}

	/**
	 * 发货要求
	 */
	@Data
	public static class ShipmentRequirements implements VO {
		private Long id;
		/**
		 * 要求交货日期
		 */
		private LocalDate requestDeliveryDate;

		/**
		 * 提单类型
		 */
		@Trans(type = TransType.DICTIONARY, key = "Tob_blType", ref = "blTypeName")
		private Integer blType;

		/**
		 * 提单类型名称
		 */
		private String blTypeName;

		/**
		 * 是否需要托盘化：1-需要 0-不需要
		 */
		@Trans(type = TransType.DICTIONARY, key = "Tob_palletizationRequired", ref = "palletizationRequiredName")
		private Integer palletizationRequired;

		/**
		 * 托盘化要求名称
		 */
		private String palletizationRequiredName;

		/**
		 * 其他发货要求
		 */
		private String otherRequirements;

		/**
		 * 外箱标签名称
		 */
		private String masterCartonLabelName;
	}

	/**
	 * 备注信息
	 */
	@Data
	public static class NoteInfo {
		/**
		 * 备注内容
		 */
		private String content;

		/**
		 * 创建人ID
		 */
		private Integer createBy;

		/**
		 * 创建人姓名
		 */
		private String creator;

		/**
		 * 创建人工号
		 */
		private String creatorNo;

		/**
		 * 创建时间
		 */
		private LocalDateTime createTime;
	}

	/**
	 * 商品信息
	 */
	@Data
	public static class ProductInfo {
		/**
		 * 采购SKU
		 */
		private String psku;

		/**
		 * 商品条码
		 */
		private String barcode;

		/**
		 * 商品图片ID
		 */
		private String imageId;

		/**
		 * 商品图片URL
		 */
		private String imageUrl;

		/**
		 * 下单数量
		 */
		private Integer orderedQuantity;

		/**
		 * 下单箱数
		 */
		private BigDecimal orderedBoxNumber;

		/**
		 * 已发货数量
		 */
		private Integer shippedQuantity;

		/**
		 * 发货箱数
		 */
		private BigDecimal shippedBoxNumber;

		/**
		 * 单价（不含税）
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal unitPrice;

		/**
		 * 税费
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal tax;

		/**
		 * 小计金额（单价 × 数量 + 税费）
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal subTotal;

		/**
		 * 装箱数量（每箱的psku数量）
		 */
		private Integer numberOfUnitsPerBox;

		/**
		 * 税率
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal taxRate;
	}

}
