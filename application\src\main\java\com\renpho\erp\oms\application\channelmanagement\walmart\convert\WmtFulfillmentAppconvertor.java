package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillment;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.karma.json.JSONKit;

import java.util.Objects;

public class WmtFulfillmentAppconvertor {
    public static WmtFulfillment toDomain(GetFulfillmentOrdersStatusResponse.Order order) {
        if (Objects.nonNull(order)) {
            WmtFulfillment wmtFulfillment = new WmtFulfillment();
            wmtFulfillment.setSellerOrderId(order.getSellerOrderId());
            wmtFulfillment.setOriginSystemOrderId(order.getOriginSystemOrderId());
            wmtFulfillment.setOrderChannelId(order.getOrderChannelId());
            wmtFulfillment.setStatus(order.getStatus());
            wmtFulfillment.setOrderType(order.getOrderType());
            wmtFulfillment.setBuyerInfo(Objects.nonNull(order.getBuyerInfo()) ? JSONKit.toJSONString(order.getBuyerInfo()) : null);
            wmtFulfillment.setOrderDate(DateUtil.parse(order.getOrderDate()));
            wmtFulfillment.setOrderLines(CollectionUtil.isNotEmpty(order.getOrderLines()) ? JSONKit.toJSONString(order.getOrderLines()) : null);
            return wmtFulfillment;
        }
        return null;
    }
}
