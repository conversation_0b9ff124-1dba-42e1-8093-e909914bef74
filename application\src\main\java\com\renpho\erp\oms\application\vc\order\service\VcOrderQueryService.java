package com.renpho.erp.oms.application.vc.order.service;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.renpho.erp.oms.application.vc.order.convert.VcOrderConvertor;
import com.renpho.erp.oms.application.vc.order.vo.*;
import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.enums.VcOrderOperateType;
import com.renpho.erp.oms.domain.vc.order.repository.VcOrderRepository;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderDiMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderDiPO;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;
import com.renpho.karma.cloud.mybatisplus.po.CreationPO;
import jakarta.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.query.VcOrderPageQuery;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderRemarkMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderRemarkPO;
import com.renpho.karma.dto.Paging;

import cn.hutool.core.collection.CollUtil;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;


/**
 * VC订单查询服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderQueryService {

    private final VcOrderMapper vcOrderMapper;
    private final VcOrderItemMapper vcOrderItemMapper;
    private final VcOrderRemarkMapper vcOrderRemarkMapper;
    private final FileClient fileClient;
    private final UserClient userClient;
    private final VcOrderRepository vcOrderRepository;
    private final VcOrderConvertor vcOrderConvertor;
    private final VcOrderDiMapper vcOrderDiMapper;

    /**
     * 获取vc订单的操作信息
     *
     * @param orderId     订单id
     * @param operateType 操作类型 1,接单 2,上传箱唛 3,提交供应链
     */
    public VcOrderOperatorDetailVo operatorDetail(Long orderId, Integer operateType) {
        // 1. 查询聚合根
        VcOrderAggRoot orderAggRoot = vcOrderRepository.findById(orderId);
        // 2. 校验操作权限
        VcOrderOperateType vcOrderOperateType = VcOrderOperateType.enumOf(operateType);
        // 领域模型执行验证逻辑
        orderAggRoot.validateOperationPermission(vcOrderOperateType);
        // 3. 获取图片信息
        Set<String> imageIdSet = orderAggRoot.getItems().stream()
                .filter(e -> Objects.nonNull(e.getProduct()))
                .map(e -> e.getProduct().getImageId())
                .collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);
        // 4. 转换为VO
        return vcOrderConvertor.toVcOrderOperatorDetailVo(orderAggRoot, fileMap);

    }

    /**
     * 获取VC订单备注历史
     *
     * @param orderId 订单ID
     * @return 备注历史列表
     */
    public List<VcOrderRemarkHistoryVO> getRemarkHistory(Long orderId) {
        // 1. 查询备注列表
        List<VcOrderRemarkPO> remarkPOs = vcOrderRemarkMapper.selectList(
                new LambdaQueryWrapper<VcOrderRemarkPO>()
                        .eq(VcOrderRemarkPO::getOrderId, orderId)
                        .orderByDesc(VcOrderRemarkPO::getCreateTime)
        );

        if (CollUtil.isEmpty(remarkPOs)) {
            return Collections.emptyList();
        }

        // 2. 收集创建人ID
        Set<Integer> creatorIds = remarkPOs.stream()
                .map(VcOrderRemarkPO::getCreateBy)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 3. 批量查询创建人信息
        Map<Integer, OumUserInfoRes> creatorMap = userClient.getUserByIds(new ArrayList<>(creatorIds)).stream()
                .collect(Collectors.toMap(OumUserInfoRes::getId, Function.identity()));

        // 4. 映射为VO
        return remarkPOs.stream().map(po -> {
            VcOrderRemarkHistoryVO vo = new VcOrderRemarkHistoryVO();
            vo.setRemark(po.getRemark());
            vo.setCreateTime(po.getCreateTime());
            Optional.ofNullable(creatorMap.get(po.getCreateBy()))
                    .ifPresent(userInfo -> {
                        vo.setCreatorCode(userInfo.getCode());
                        vo.setCreatorName(userInfo.getName());
                    });
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 获取VC订单开票信息
     *
     * @param orderId 订单ID
     * @return 开票信息VO
     */
    public VcOrderInvoiceInfoVO getInvoiceInfo(Long orderId) {
        // 1. 根据ID查询VC订单实体
        VcOrderPO vcOrder = vcOrderMapper.selectById(orderId);
        if (vcOrder == null) {
            // 如果订单不存在，抛出业务异常
            throw new BusinessException("VC_ORDER_NOT_FOUND");
        }

        // todo 后续对接供应链,需校验是否供应链发货了

        // 2. 组装并返回VO
        VcOrderInvoiceInfoVO vo = new VcOrderInvoiceInfoVO();
        vo.setOrderId(vcOrder.getId());
        vo.setStoreId(vcOrder.getStoreId());
        vo.setStoreName(vcOrder.getStoreName());
        vo.setShippingWindowStart(vcOrder.getShippingWindowStart());
        vo.setShippingWindowEnd(vcOrder.getShippingWindowEnd());

        return vo;
    }

    /**
     * VC订单分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @TransMethodResult
    public Paging<VcOrderPageVO> page(VcOrderPageQuery query) {
        // 1. 构建查询条件并分页查询主表（带数据权限控制）
        Page<VcOrderPO> pageParam = new Page<>(query.getPageIndex(), query.getPageSize());
        IPage<VcOrderPO> orderPage = vcOrderMapper.selectPermissionPage(pageParam, query);

        if (CollUtil.isEmpty(orderPage.getRecords())) {
            return Paging.of(Collections.emptyList(), 0, query.getPageSize(), query.getPageIndex());
        }

        // 2. 收集订单ID
        List<Long> orderIds = orderPage.getRecords().stream().map(VcOrderPO::getId).collect(Collectors.toList());

        // 3. 批量查询商品信息
        Map<Long, List<VcOrderItemPO>> orderItemInfoMap = vcOrderItemMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.groupingBy(VcOrderItemPO::getOrderId));

        // 4. 收集图片ID并获取图片信息
        Set<String> imageIdSet = orderItemInfoMap.values()
                .stream()
                .flatMap(List::stream)
                .map(VcOrderItemPO::getImageId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);

        // 5. 组装返回结果
        List<VcOrderPageVO> records = orderPage.getRecords()
                .stream()
                .map(order -> convertToPageVO(order, orderItemInfoMap.get(order.getId()), fileMap))
                .collect(Collectors.toList());

        return Paging.of(records, (int) orderPage.getTotal(), (int) orderPage.getPages(), (int) orderPage.getCurrent());
    }

    /**
     * 转换为分页VO
     */
    private VcOrderPageVO convertToPageVO(VcOrderPO order, List<VcOrderItemPO> orderItems, Map<String, FileDetailResponse> fileMap) {
        VcOrderPageVO vo = new VcOrderPageVO();
        vo.setId(order.getId());
        vo.setOrderNo(order.getOrderNo());
        vo.setVcPurchaseNo(order.getVcPurchaseNo());
        vo.setStoreName(order.getStoreName());
        vo.setBusinessType(order.getBusinessType());
        vo.setShipFrom(order.getShipFrom());
        vo.setShipTo(order.getShipTo());
        vo.setShippingWindowStart(order.getShippingWindowStart());
        vo.setShippingWindowEnd(order.getShippingWindowEnd());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setAcceptedStatus(order.getAcceptedStatus());
        vo.setExceptionReason(order.getExceptionReason());
        vo.setRemark(order.getRemark());
        vo.setCurrency(order.getCurrency());
        vo.setProductPrice(order.getProductPrice());
        vo.setOrderedTime(order.getOrderedTime());
        vo.setAcceptedTime(order.getAcceptedTime());
        vo.setUpdateTime(order.getUpdateTime());

        // 商品信息，参考B2bOrderQueryService的实现
        if (CollUtil.isNotEmpty(orderItems)) {
            VcOrderItemPO firstItem = orderItems.get(0);
            VcOrderItemVO itemVO = new VcOrderItemVO();
            itemVO.setPsku(firstItem.getPsku());
            itemVO.setImageId(firstItem.getImageId());
            itemVO.setAsin(firstItem.getAsin());
            itemVO.setOrderedQuantity(firstItem.getOrderedQuantity());
            itemVO.setShippedQuantity(firstItem.getShippedQuantity());
            itemVO.setReceivedQuantity(firstItem.getReceivedQuantity());
            itemVO.setItemCount(orderItems.size());

            // 设置图片URL
            if (firstItem.getImageId() != null && fileMap.containsKey(firstItem.getImageId())) {
                FileDetailResponse fileDetail = fileMap.get(firstItem.getImageId());
                if (fileDetail != null) {
                    itemVO.setImageUrl(fileDetail.getUrl());
                }
            }

            vo.setItem(itemVO);
        }

        return vo;
    }

    /**
     * 获取VC订单商品列表
     *
     * @param orderId 订单ID
     * @return 商品列表
     */
    @TransMethodResult
    public List<VcOrderItemListVO> getVcOrderItemList(Long orderId) {
        // 查询订单商品信息
        List<VcOrderItemPO> items = vcOrderItemMapper
                .selectList(new LambdaQueryWrapper<VcOrderItemPO>().eq(VcOrderItemPO::getOrderId, orderId).orderByAsc(VcOrderItemPO::getId));

        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 收集图片ID并获取图片信息
        Set<String> imageIdSet = items.stream().map(VcOrderItemPO::getImageId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);

        // 转换为VO对象
        return items.stream().map(item -> convertToItemListVO(item, fileMap)).collect(Collectors.toList());
    }

    /**
     * 转换为商品列表VO对象
     */
    private VcOrderItemListVO convertToItemListVO(VcOrderItemPO item, Map<String, FileDetailResponse> fileMap) {
        VcOrderItemListVO vo = new VcOrderItemListVO();

        vo.setId(item.getId());
        vo.setOrderId(item.getOrderId());
        vo.setPsku(item.getPsku());
        vo.setBarcode(item.getBarcode());
        vo.setAsin(item.getAsin());
        vo.setOrderedQuantity(item.getOrderedQuantity());
        vo.setAcceptedQuantity(item.getAcceptedQuantity());
        vo.setShippedQuantity(item.getShippedQuantity());
        vo.setImageId(item.getImageId());

        // 设置图片URL
        if (item.getImageId() != null && fileMap.containsKey(item.getImageId())) {
            FileDetailResponse fileDetail = fileMap.get(item.getImageId());
            if (fileDetail != null) {
                vo.setImageUrl(fileDetail.getUrl());
            }
        }

        return vo;
    }

    /**
     * VC订单导出
     *
     * @param query    查询参数
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @TransMethodResult
    public void export(VcOrderPageQuery query, HttpServletResponse response) throws IOException {
        HttpUtil.setExportResponseHeader(response, "vcOrder.xlsx");
        @Cleanup
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                .autoCloseStream(true)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0)
                .head(VcOrderExportVO.class)
                .build();

        query.setPageSize(1000);
        query.setPageIndex(1);
        VcOrderQueryService bean = SpringUtil.getBean(this.getClass());
        do {
            List<VcOrderExportVO> records = bean.getExportData(query);
            excelWriter.write(records, writeSheet);
            if (!Objects.equals(records.size(), query.getPageSize())) {
                break;
            }
            query.setPageIndex(query.getPageIndex() + 1);
        } while (true);
    }

    /**
     * 获取导出数据
     *
     * @param query 查询参数
     * @return 导出数据列表
     */
    @TransMethodResult
    public List<VcOrderExportVO> getExportData(VcOrderPageQuery query) {
        // 1. 分页查询订单数据
        Page<VcOrderPO> pageParam = new Page<>(query.getPageIndex(), query.getPageSize(), false);
        IPage<VcOrderPO> orderPage = vcOrderMapper.selectPermissionPage((IPage<VcOrderPO>) pageParam, query);

        if (CollUtil.isEmpty(orderPage.getRecords())) {
            return Collections.emptyList();
        }

        // 2. 收集订单ID
        List<Long> orderIds = orderPage.getRecords().stream().map(VcOrderPO::getId).collect(Collectors.toList());

        // 3. 批量查询商品信息
        Map<Long, List<VcOrderItemPO>> orderItemInfoMap = vcOrderItemMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.groupingBy(VcOrderItemPO::getOrderId));

        // 4. 转换为导出VO
        return orderPage.getRecords()
                .stream()
                .flatMap(order -> {
                    List<VcOrderItemPO> items = orderItemInfoMap.get(order.getId());
                    if (CollUtil.isEmpty(items)) {
                        // 如果没有商品信息，创建一个空的商品记录
                        return List.of(convertToExportVO(order, null)).stream();
                    }
                    // 每个商品一行
                    return items.stream().map(item -> convertToExportVO(order, item));
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换为导出VO
     */
    private VcOrderExportVO convertToExportVO(VcOrderPO order, VcOrderItemPO item) {
        VcOrderExportVO vo = new VcOrderExportVO();

        // 订单信息
        vo.setId(order.getId());
        vo.setOrderNo(order.getOrderNo());
        vo.setVcPurchaseNo(order.getVcPurchaseNo());
        vo.setStoreName(order.getStoreName());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setOrderedTime(order.getOrderedTime());
        vo.setAcceptedTime(order.getAcceptedTime());
        vo.setShipFrom(order.getShipFrom());
        vo.setShipTo(order.getShipTo());
        vo.setShippingWindowStart(order.getShippingWindowStart());
        vo.setShippingWindowEnd(order.getShippingWindowEnd());
        vo.setExceptionReason(order.getExceptionReason());
        vo.setRemark(order.getRemark());
        vo.setCurrency(order.getCurrency());

        // 商品信息
        if (item != null) {
            vo.setPsku(item.getPsku());
            vo.setAsin(item.getAsin());
            vo.setBarcode(item.getBarcode());
            vo.setOrderedQuantity(item.getOrderedQuantity());
            vo.setAcceptedQuantity(item.getAcceptedQuantity());
            vo.setShippedQuantity(item.getShippedQuantity());
            vo.setReceivedQuantity(item.getReceivedQuantity());
            vo.setUnitPrice(item.getUnitPrice());
            vo.setTax(item.getTax());
            vo.setSubTotal(item.getSubTotal());
            // 计算总金额（这里可以根据业务逻辑调整）
            vo.setTotalAmount(order.getProductPrice());
        }

        return vo;
    }

    /**
     * 查询不重复的站点编码列表
     *
     * @return 站点编码列表
     */
    public List<String> getDistinctSiteCodes() {
        return vcOrderMapper.selectDistinctSiteCodes();
    }

    /**
     * 获取订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    @TransMethodResult
    public VcOrderDetailVO getVcOrderDetail(Long orderId) {
        // 1. 查询订单信息
        VcOrderPO vcOrderPO = Optional.ofNullable(vcOrderMapper.selectById(orderId))
                .orElseThrow(() -> new BusinessException("VC_ORDER_NOT_FOUND"));
        // 订单商品
        List<VcOrderItemPO> vcOrderItemPOList = vcOrderItemMapper.selectList(Wrappers.<VcOrderItemPO>lambdaQuery()
                .eq(VcOrderItemPO::getOrderId, orderId)
                .eq(AlterationPO::getDeleted, false));
        // 订单DI
        VcOrderDiPO vcOrderDiPO = vcOrderDiMapper.selectOne(Wrappers.<VcOrderDiPO>lambdaQuery()
                .eq(VcOrderDiPO::getOrderId, orderId)
                .eq(AlterationPO::getDeleted, false));
        // 订单备注
        List<VcOrderRemarkPO> remarks = vcOrderRemarkMapper.selectList(Wrappers.<VcOrderRemarkPO>lambdaQuery()
                .eq(VcOrderRemarkPO::getOrderId, orderId)
                .eq(AlterationPO::getDeleted, false));
        // 2. 获取图片信息
        Set<String> imageIdSet = vcOrderItemPOList.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getImageId()))
                .map(VcOrderItemPO::getImageId)
                .collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);
        // 3. 获取remark的创建人
        Map<Integer, OumUserInfoRes> userInfoMap = Maps.newHashMap();
        if (CollUtil.isNotEmpty(remarks)) {
            List<Integer> userIdList = remarks.stream().map(CreationPO::getCreateBy).collect(Collectors.toList());
            userInfoMap = userClient.getUserByIds(userIdList)
                    .stream()
                    .collect(Collectors.toMap(OumUserInfoRes::getId, Function.identity()));
        }
        // 4. 转换为VO
        return vcOrderConvertor.toVcOrderDetailVO(vcOrderPO, vcOrderItemPOList, vcOrderDiPO, remarks, fileMap, userInfoMap);
    }
}
