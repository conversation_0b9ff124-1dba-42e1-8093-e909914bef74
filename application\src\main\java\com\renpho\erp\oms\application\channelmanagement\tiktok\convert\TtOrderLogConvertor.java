package com.renpho.erp.oms.application.channelmanagement.tiktok.convert;

import com.renpho.erp.oms.application.channelmanagement.tiktok.vo.TtOrderVO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderLog;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrder;
import org.mapstruct.Mapper;

import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface TtOrderLogConvertor {

	TtOrderLog toLog(TtOrder ttOrder);

	@Mapping(target = "state", expression = "java(BaseConvertor.convertState(param.getDistrictInfo()))")
	@Mapping(target = "city", expression = "java(BaseConvertor.convertCity(param.getDistrictInfo()))")
	TtOrderVO toVO(TtOrder param);

}
