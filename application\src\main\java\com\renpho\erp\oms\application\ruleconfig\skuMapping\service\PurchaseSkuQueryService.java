package com.renpho.erp.oms.application.ruleconfig.skuMapping.service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.renpho.erp.oms.application.b2b.order.convert.B2bProductConvert;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.PskuQueryCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.B2bPurchaseSkuVO;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.PurchaseSkuVO;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bProduct;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDetailDTO;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.security.constant.RoleLabelEnum;
import com.renpho.erp.security.util.SecurityUtils;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PurchaseSkuQueryService {

	private final ProductClient productClient;
	private final B2bProductConvert b2bProductConvert;

	/**
	 * 采购sku列表（含数据权限）
	 * @return List
	 */
	public List<PurchaseSkuVO> listPermissionPurchaseSku() {
		// 角色标签
		String[] roleLabels = SecurityUtils.getUserLabels();
		// 超级管理 || 不存在运营标签（运营管理、运营人员），拥有所有采购sku权限
		if (SecurityUtils.isAdmin() || Objects.isNull(roleLabels) || Arrays.stream(roleLabels).noneMatch(RoleLabelEnum::isOperations)) {
			return this.buildPurchaseSkuList(productClient.getPurchaseSkuSet());
		}
		// 采购sku数据权限集（当前用户的）
		return this.buildPurchaseSkuList(productClient.findPskuPermission(SecurityUtils.getUserId()));
	}

	/**
	 * 构建采购sku列表
	 * @param purchaseSkuSet 采购sku集合
	 * @return List
	 */
	private @NotNull List<PurchaseSkuVO> buildPurchaseSkuList(Set<String> purchaseSkuSet) {
		return purchaseSkuSet.stream().map(s -> {
			PurchaseSkuVO purchaseSkuVO = new PurchaseSkuVO();
			purchaseSkuVO.setPurchaseSku(s);
			return purchaseSkuVO;
		}).toList();
	}

	/**
	 * B2B采购sku列表（含数据权限）
	 * @return List
	 */
	public List<B2bPurchaseSkuVO> b2bList(PskuQueryCmd queryCmd) {
		// 采购sku集
		Set<String> purchaseSkuSet = productClient.getPurchaseSkuSet();
		// psku-模糊查询
		String psku = queryCmd.getPsku();
		if (StringUtils.isNotBlank(queryCmd.getPsku())) {
			purchaseSkuSet = purchaseSkuSet.stream().filter(s -> s.contains(psku)).collect(Collectors.toSet());
		}
		// 查询psku明细
		List<PurchaseProductDetailDTO> pskuList = productClient.findPurchaseProductList(purchaseSkuSet);
		// 构建返回VO列表
		return this.buildB2bVOList(pskuList);
	}

	/**
	 * 构建返回VO列表
	 * @param pskuList psku列表
	 * @return List
	 */
	private @NotNull List<B2bPurchaseSkuVO> buildB2bVOList(List<PurchaseProductDetailDTO> pskuList) {
		return pskuList.stream().map(p -> {
			B2bPurchaseSkuVO vo = new B2bPurchaseSkuVO();
			vo.setPsku(p.getPsku());
			// 中文or英文名称，根据语言环境返回
			if ("zh-CN".equals(LocaleContextHolder.getLocale().toLanguageTag())) {
				vo.setPskuName(p.getPskuNameZh());
			}
			else {
				vo.setPskuName(p.getPskuNameEn());
			}
			vo.setNumberOfUnitsPerBox(p.getNumberOfUnitsPerBox());
			return vo;
		}).toList();
	}

	/**
	 * B2B采购sku列表
	 * @param pskus psku集
	 * @return Map，key为psku，value为PurchaseProductDetailDTO
	 */
	public Map<String, PurchaseProductDetailDTO> b2bPurchaseSkuList(Set<String> pskus) {
		// 查询psku明细
		List<PurchaseProductDetailDTO> pskuList = productClient.findPurchaseProductList(pskus);
		// Map，key为psku，value为PurchaseProductDetailDTO
		return pskuList.stream().collect(Collectors.toMap(PurchaseProductDetailDTO::getPsku, Function.identity(), (x1, x2) -> x1));
	}

	/**
	 * B2B采购sku列表
	 * @param pskus psku集
	 * @return Map，key为psku，value为B2bProduct
	 */
	public Map<String, B2bProduct> b2bPurchaseList(Set<String> pskus) {
		// 查询psku明细
		List<PurchaseProductDetailDTO> pskuList = productClient.findPurchaseProductList(pskus);
		// Map，key为psku，value为PurchaseProductDetailDTO
		return pskuList.stream().collect(Collectors.toMap(PurchaseProductDetailDTO::getPsku, b2bProductConvert::convert, (x1, x2) -> x1));
	}

}
