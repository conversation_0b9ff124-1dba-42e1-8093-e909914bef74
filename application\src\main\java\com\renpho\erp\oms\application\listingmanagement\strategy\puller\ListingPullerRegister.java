package com.renpho.erp.oms.application.listingmanagement.strategy.puller;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Listing拉取器注册
 *
 * <AUTHOR>
 */
@Component
public class ListingPullerRegister implements ApplicationContextAware, InitializingBean {

	private ApplicationContext context;

	private static final Map<String, ListingPuller> CACHE = new HashMap<>(1 << 5);

	@Override
	public void setApplicationContext(@NotNull @Nonnull ApplicationContext applicationContext) throws BeansException {
		this.context = applicationContext;
	}

	@Override
	public void afterPropertiesSet() {
		// 从容器获取ListingPuller
		String[] beanNamesForTypes = context.getBeanNamesForType(ListingPuller.class);
		for (String beanNamesForType : beanNamesForTypes) {
			ListingPuller listingPuller = (ListingPuller) context.getBean(beanNamesForType);
			// 销售渠道编码
			String salesChannelCode = listingPuller.getSalesChannelCode();
			// 注册
			this.register(salesChannelCode, listingPuller);
		}
	}

	/**
	 * 注册拉取器
	 * @param salesChannelCode 销售渠道编码
	 * @param listingPuller Listing拉取器
	 */
	private void register(String salesChannelCode, ListingPuller listingPuller) {
		if (CACHE.containsKey(salesChannelCode)) {
			return;
		}
		CACHE.put(salesChannelCode, listingPuller);
	}

	/**
	 * 获取拉取器
	 * @param salesChannelCode 销售渠道编码
	 * @return ListingPuller
	 */
	public ListingPuller getPuller(String salesChannelCode) {
		return CACHE.get(salesChannelCode);
	}
}
