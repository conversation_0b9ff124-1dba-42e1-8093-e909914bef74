package com.renpho.erp.oms.application.salemanagement.converter.fulfillment;

import java.util.List;
import java.util.Set;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.ChannelFulfillment;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;

/**
 * 渠道订单转销售订单策略接口.
 * <AUTHOR>
 * @date 2025/1/5 16:34
 */
public interface ChannelConvertFulfillmentStrategy {

	/**
	 * 获取渠道类型.
	 * @return SaleChannelType
	 */
	SaleChannelType getChannelType();

	/**
	 * 获取发货类型.
	 * @return FulfilmentType
	 */
	FulfillmentType getFulfillmentType();

	/**
	 * 获取店铺列表.
	 *
	 * @param channelType 渠道类型
	 * @param ids 店铺ID集
	 * @return List<StoreDTO>
	 */
	List<StoreDTO> getStores(SaleChannelType channelType, Set<Integer> ids);

	/**
	 * 分批获取渠道订单履约列表.
	 *
	 * @param storeDTO 店铺
	 * @param channelFulfillmentIds 渠道履约id集
	 * @param pageSize 分批查询的条数
	 * @param lastId 上一页返回的标记（最后一条的id），用于检索下一页，查询第一页时，此参数为空
	 * @return List<DefaultChannelOrder>（只返回id）
	 */
	List<ChannelFulfillment> getChannelFulfillmentList(StoreDTO storeDTO, Set<String> channelFulfillmentIds, Integer pageSize, Long lastId);

	/**
	 * 转换销售订单履约信息.
	 * @param channelFulfillment 渠道订单履约信息
	 */
	void convert(ChannelFulfillment channelFulfillment);

}
