package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.service.OrderInventoryQueryService;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.AutoShipAuditConfig;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;

/**
 * 单品【单品单件，单品多件】-分仓策略
 * <AUTHOR>
 */
@Component
public class OneSkuAllocateWarehouseStrategy extends AbstractAllocateWarehouseTemplate {

	protected OneSkuAllocateWarehouseStrategy(WarehouseClient warehouseClient, OrderInventoryQueryService orderInventoryQueryService) {
		super(warehouseClient, orderInventoryQueryService);
	}

	@Override
	public Set<SkuCombinationType> getSkuCombinationTypes() {
		return Set.of(SkuCombinationType.ONE_SKU_ONE_PCS, SkuCombinationType.ONE_SKU_MULTI_PCS);
	}

	@Override
	protected List<String> getOrderedPriorityWarehouseCodes(SkuCombinationType skuCombinationType,
			List<AutoShipAuditConfig> autoShipAuditConfigs) {
		// 自动发货审核，单品只有一个规则
		AutoShipAuditConfig auditConfigAggRoot = autoShipAuditConfigs.get(0);
		// 是否包含sku组合类型
		if (!auditConfigAggRoot.isContainSkuCombinationType(skuCombinationType)) {
			return List.of();
		}
		// 获取有序优先级的仓库编码列表
		return auditConfigAggRoot.getOrderedPriorityWarehouseCodes();
	}
}
