package com.renpho.erp.oms.application.channelmanagement.tiktok.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderLineItem;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

public class TtOrderLineItemAppConvertor {

	public static List<TtOrderLineItem> toDomainList(List<TtOrderDTO.Item> itemList) {
		List<TtOrderLineItem> ttOrderLineItemList = Lists.newArrayList();
		if (CollectionUtil.isNotEmpty(itemList)) {
			itemList.forEach(item -> ttOrderLineItemList.add(toDomain(item)));
		}
		return ttOrderLineItemList;
	}

	public static TtOrderLineItem toDomain(TtOrderDTO.Item item) {
		if (Objects.nonNull(item)) {
			TtOrderLineItem ttOrderLineItem = new TtOrderLineItem();
			ttOrderLineItem.setItemId(item.getId());
			ttOrderLineItem.setSkuId(item.getSku_id());
			if (CollectionUtil.isNotEmpty(item.getCombined_listing_skus())) {
				ttOrderLineItem.setCombinedListingSkus(JsonUtils.toJson(item.getCombined_listing_skus()));
			}
			ttOrderLineItem.setDisplayStatus(item.getDisplay_status());
			ttOrderLineItem.setProductName(item.getProduct_name());
			ttOrderLineItem.setSellerSku(item.getSeller_sku());
			ttOrderLineItem.setSkuImage(item.getSku_image());
			ttOrderLineItem.setSkuName(item.getSku_name());
			ttOrderLineItem.setProductId(item.getProduct_id());
			if (StringUtils.isNotEmpty(item.getSale_price())) {
				ttOrderLineItem.setSalePrice(new BigDecimal(item.getSale_price()));
			}
			if (StringUtils.isNotEmpty(item.getPlatform_discount())) {
				ttOrderLineItem.setPlatformDiscount(new BigDecimal(item.getPlatform_discount()));
			}
			if(StringUtils.isNotEmpty(item.getSeller_discount())){
				ttOrderLineItem.setSellerDiscount(new BigDecimal(item.getSeller_discount()));
			}
			ttOrderLineItem.setSkuType(item.getSku_type());
			ttOrderLineItem.setCancelReason(item.getCancel_reason());
			if (StringUtils.isNotEmpty(item.getOriginal_price())) {
				ttOrderLineItem.setOriginalPrice(new BigDecimal(item.getOriginal_price()));
			}
			ttOrderLineItem.setRtsTime(DateUtil.convertToDateTime(item.getRts_time()));
			ttOrderLineItem.setPackageStatus(item.getPackage_status());
			ttOrderLineItem.setCurrency(item.getCurrency());
			ttOrderLineItem.setShippingProviderName(item.getShipping_provider_name());
			ttOrderLineItem.setCancelUser(item.getCancel_user());
			ttOrderLineItem.setShippingProviderId(item.getShipping_provider_id());
			ttOrderLineItem.setIsGift(item.getIs_gift());
			if (CollectionUtil.isNotEmpty(item.getItem_tax())) {
				ttOrderLineItem.setTaxInfo(JsonUtils.toJson(item.getItem_tax()));
			}
			ttOrderLineItem.setTrackingNumber(item.getTracking_number());
			if (StringUtils.isNotEmpty(item.getRetail_delivery_fee())) {
				ttOrderLineItem.setRetailDeliveryFee(new BigDecimal(item.getRetail_delivery_fee()));
			}
			if (StringUtils.isNotEmpty(item.getBuyer_service_fee())) {
				ttOrderLineItem.setBuyerServiceFee(new BigDecimal(item.getBuyer_service_fee()));
			}
			if (StringUtils.isNotEmpty(item.getSmall_order_fee())) {
				ttOrderLineItem.setSmallOrderFee(new BigDecimal(item.getSmall_order_fee()));
			}
			ttOrderLineItem.setHandlingDurationDays(item.getHandling_duration_days());
			return ttOrderLineItem;
		}
		return null;
	}
}
