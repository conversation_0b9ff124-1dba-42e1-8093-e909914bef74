package com.renpho.erp.oms.adapter.client;

import java.util.List;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.renpho.erp.oms.adapter.client.converter.SalesChannelListingConverter;
import com.renpho.erp.oms.application.listingmanagement.service.SalesChannelListingQueryService;
import com.renpho.erp.oms.client.listing.RemoteSalesChannelListingService;
import com.renpho.erp.oms.client.listing.query.ListingFindByStoreIdAndSkuId;
import com.renpho.erp.oms.client.listing.vo.SalesChannelListingVO;
import com.renpho.erp.oms.infrastructure.persistence.listing.po.SalesChannelListingPO;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.R;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class RemoteSalesChannelListingClient implements RemoteSalesChannelListingService {

	private final SalesChannelListingQueryService salesChannelListingQueryService;
	private final SalesChannelListingConverter salesChannelListingConverter;

	@Inner
	@Override
	public R<List<SalesChannelListingVO>> findByStoreIdAndSkuId(@RequestBody @Valid ListingFindByStoreIdAndSkuId query) {
		List<SalesChannelListingPO> listings = salesChannelListingQueryService.findByStoreIdAndSkuId(query.getStoreId(), query.getSkuId());
		return R.success(salesChannelListingConverter.toVO(listings));
	}
}
