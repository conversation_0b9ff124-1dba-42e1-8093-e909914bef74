package com.renpho.erp.oms.application.returnorder.converter;

import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderRemarkVO;
import com.renpho.erp.oms.domain.returnorder.model.ReturnOrderRemark;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @desc:
 * @time: 2025-03-21 14:24:22
 * @author: <PERSON>na
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface ReturnOrderRemarkVOConvertor {
    ReturnOrderRemarkVO toReturnOrderRemarkVO(ReturnOrderRemark returnOrderRemark);
}
