package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import java.util.*;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.infrastructure.persistence.misc.service.RegionService;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.ecom.model.logistics.CreateShipmentRequest;
import com.renpho.erp.apiproxy.ecom.model.logistics.CreateShipmentResponse;
import com.renpho.erp.apiproxy.ecom.model.logistics.RetrieveShipLabelResponse;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.service.UploadPdfService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderFulfillment;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import com.renpho.erp.oms.infrastructure.config.NacosOrderConfig;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.EcomClient;
import com.renpho.karma.dto.R;

import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * ecom物流策略
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EcomLogisticsStrategy extends AbstractLogisticsStrategy implements InitializingBean {

	private final WarehouseClient warehouseClient;
	private final EcomClient ecomClient;
	private final UploadPdfService uploadPdfService;
	private final NacosOrderConfig nacosOrderConfig;
	private final RegionService regionService;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE);
	}

	@Override
	public void createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		// 仓库信息（包含仓库地址）
		WarehouseVo warehouse = warehouseClient.getByCode(shipAuditAggRoot.getWarehouseCode());
		// 构建请求
		CreateShipmentRequest request = this.buildRequest(shipAuditAggRoot, saleOrderAggRoot, receivingAddress, warehouse);
		R<CreateShipmentResponse> r;
		try {
			// 请求ecom物流下单
			r = ecomClient.createShipment(request);
		}
		catch (Exception e) {
			log.error("ecom物流下单异常：", e);
			this.markFailed(shipAuditAggRoot, logisticsShipmentAggRoot, "ecom物流下单异常：" + e.getMessage());
			return;
		}
		// 处理结果
		this.handleCreateR(shipAuditAggRoot, logisticsShipmentAggRoot, saleOrderAggRoot, r);
	}

	/**
	 * 标记物流下单失败
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param failMsg 失败原因
	 */
	private void markFailed(OrderShipAuditAggRoot shipAuditAggRoot, LogisticsShipmentAggRoot logisticsShipmentAggRoot, String failMsg) {
		// 物流下单标记失败
		logisticsShipmentAggRoot.markAsFailed(failMsg);
		// 发货审核标记失败
		shipAuditAggRoot.createFail(failMsg);
	}

	/**
	 * 处理物流下单结果
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param r 结果
	 */
	private void handleCreateR(OrderShipAuditAggRoot shipAuditAggRoot, LogisticsShipmentAggRoot logisticsShipmentAggRoot,
			SaleOrderAggRoot saleOrderAggRoot, R<CreateShipmentResponse> r) {
		if (!r.isSuccess()) {
			this.markFailed(shipAuditAggRoot, logisticsShipmentAggRoot, "ecom物流下单失败：" + r.getMessage());
			return;
		}
		CreateShipmentResponse data = r.getData();
		if (Objects.isNull(data)) {
			this.markFailed(shipAuditAggRoot, logisticsShipmentAggRoot, "ecom物流下单，返回的data为空");
			return;
		}
		String bookNumber = data.getBookNumber();
		String trackingNumber = data.getTrackingNumber();
		if (StringUtils.isNotBlank(bookNumber) && StringUtils.isNotBlank(trackingNumber)) {
			// 物流下单成功
			logisticsShipmentAggRoot.markWaitDownloadWaybillFile(bookNumber, trackingNumber);
			// 发货审核创建成功
			shipAuditAggRoot.createSuccess(null);
			// 订单填充物流单号（预订号）
			saleOrderAggRoot.updateLogisticsOrderNo(bookNumber);
			return;
		}
		this.markFailed(shipAuditAggRoot, logisticsShipmentAggRoot, "ecom物流下单，返回的预订号或者跟踪号为空");
	}

	/**
	 * 构建物流下单请求
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 订单收货地址
	 * @param warehouse 发货仓信息
	 * @return CreateShipmentRequest
	 */
	private @NotNull CreateShipmentRequest buildRequest(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, WarehouseVo warehouse) {
		CreateShipmentRequest request = new CreateShipmentRequest();
		// 承运商编码
		request.setCarrierCode(shipAuditAggRoot.getCarrierCode());
		// 承运商服务编码
		request.setServiceCode(shipAuditAggRoot.getCarrierServiceCode());
		// 包裹类型编码
		request.setPackageTypeCode(shipAuditAggRoot.getPackageTypeCode());
		// 传当前日期（零时区）
		request.setShipmentDate(new Date());
		// 参考单号：传发货审核号
		request.setShipmentReference(shipAuditAggRoot.getAuditNo());
		// 参考单号2：传店铺单号
		request.setShipmentReference2(saleOrderAggRoot.getChannelOrderNo());
		// 内容描述
		request.setContentDescription("");
		// 签名选项编码
		request.setSignatureOptionCode(null);
		// 发件信息
		request.setSender(this.buildSender(warehouse));
		// 收件信息
		request.setReceiver(this.buildReceiver(receivingAddress));
		// 是否住宅
		request.setResidential(true);
		// 重量单位
		request.setWeightUnit("lb");
		// 尺寸单位
		request.setDimUnit("in");
		// 币种
		request.setCurrency("USD");
		// 报关货币
		request.setCustomsCurrency("USD");
		// 包裹信息
		request.setPieces(this.buildPieces(saleOrderAggRoot.getFulfillment()));
		// 账单信息
		request.setBilling(this.buildBilling());
		return request;
	}

	/**
	 * 构建账单信息
	 *
	 * @return Billing
	 */
	private CreateShipmentRequest.Billing buildBilling() {
		CreateShipmentRequest.Billing billing = new CreateShipmentRequest.Billing();
		// 账单方
		billing.setParty("sender");
		// 账单国家
		billing.setCountry("US");
		return billing;
	}

	/**
	 * 构建包裹信息
	 *
	 * @param fulfillment 订单履约信息
	 * @return List
	 */
	private List<CreateShipmentRequest.Pieces> buildPieces(SaleOrderFulfillment fulfillment) {
		CreateShipmentRequest.Pieces pieces = new CreateShipmentRequest.Pieces();
		// 重量-英镑
		pieces.setWeight(SizeUtil.formatToLbUnit(fulfillment.getWeight()));
		// 长-英尺
		pieces.setLength(SizeUtil.parseSizeToIn(fulfillment.getSize(), 0));
		// 宽-英尺
		pieces.setWidth(SizeUtil.parseSizeToIn(fulfillment.getSize(), 1));
		// 高-英尺
		pieces.setHeight(SizeUtil.parseSizeToIn(fulfillment.getSize(), 2));
		return List.of(pieces);
	}

	/**
	 * 构建收件信息
	 *
	 * @param receivingAddress 订单收货地址
	 * @return Ship
	 */
	private CreateShipmentRequest.Ship buildReceiver(SaleOrderAddressVO receivingAddress) {
		// 收件信息
		CreateShipmentRequest.Ship ship = new CreateShipmentRequest.Ship();
		// 联系人
		ship.setName(receivingAddress.getRecipientName());
		// 收件公司，不能为null，这里传空字符串
		ship.setCompany("");
		// 国家
		ship.setCountry(receivingAddress.getCountryCode());
		// 州、省
		ship.setState(StrUtil.blankToDefault(regionService.getStateCode(receivingAddress.getCountryCode(), receivingAddress.getProvince()), receivingAddress.getProvince()));
		// 城市
		ship.setCity(receivingAddress.getCity());
		// 地址1
		ship.setAddress1(receivingAddress.getAddress1());
		// 地址2，为空时，传空字符串
		ship.setAddress2(Optional.ofNullable(receivingAddress.getAddress2()).orElse(""));
		// 邮编
		ship.setZip(receivingAddress.getPostalCode());
		// 联系电话
		ship.setPhone(receivingAddress.getRecipientPhone());
		// 邮箱
		ship.setEmail(receivingAddress.getEmail());
		return ship;
	}

	/**
	 * 构建发件信息
	 *
	 * @param warehouse 发货仓
	 * @return Ship
	 */
	private CreateShipmentRequest.Ship buildSender(WarehouseVo warehouse) {
		// 发货仓信息
		CreateShipmentRequest.Ship ship = new CreateShipmentRequest.Ship();
		// 联系人
		ship.setName(warehouse.getContactName());
		// 发件公司，不能为null，ups传空字符串会提示Sender Company Name is required，这里统一传船务部
		ship.setCompany(nacosOrderConfig.getEcomSenderCompany());
		// 国家
		ship.setCountry(warehouse.getCountryCode());
		// 州、省
		ship.setState(StrUtil.blankToDefault(regionService.getStateCode(warehouse.getCountryCode(), warehouse.getProvince()), warehouse.getProvince()));
		// 城市
		ship.setCity(warehouse.getCity());
		// 地址
		ship.setAddress1(warehouse.getAddress());
		// 地址
		ship.setAddress2(warehouse.getAddress());
		// 邮编
		ship.setZip(warehouse.getZipCode());
		// 联系电话
		ship.setPhone(warehouse.getContactNumber());
		// 邮箱
		ship.setEmail(warehouse.getContactEmail());
		return ship;
	}

	@Override
	public void doProcessLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		R<RetrieveShipLabelResponse> downLoadResponse = ecomClient.retrieveShipLabel(logisticsShipmentAggRoot.getLogisticsOrderNo(), "PDF");
		if (!downLoadResponse.isSuccess() || Objects.isNull(downLoadResponse.getData())
				|| StringUtils.isNotEmpty(downLoadResponse.getData().getError())) {
			log.error("ecom面单下载失败，物流单号{}，原因{}", logisticsShipmentAggRoot.getLogisticsOrderNo(), downLoadResponse.getMessage());
			throw new BusinessException("eBay面单下载失败");
		}
		String s3Url = uploadPdfService.uploadPdfFromBytes(ArrayUtils.toObject(downLoadResponse.getData().getContent()),
				logisticsShipmentAggRoot.getWaybillFilePlatformUrl());
		if (StringUtils.isEmpty(s3Url)) {
			throw new BusinessException("ecom面单上传失败");
		}

		logisticsShipmentAggRoot.markSuccess(s3Url, null);
	}

	@Override
	public void afterPropertiesSet() {
		// 使用代理对象，不用this，避免事务失效等问题
		Set<FulfillmentServiceType> serviceTypes = this.getFulfillmentServiceType();
		serviceTypes.forEach(serviceType -> LogisticsFactory.register(serviceType, SpringUtil.getBean(this.getClass())));
	}
}
