package com.renpho.erp.oms.application.salemanagement.converter;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.SaleOrderInfoConverterDto;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;

import lombok.extern.slf4j.Slf4j;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Slf4j
@Component
public class SpfChannelConvertSaleOrderStrategy extends AbstractChannelConvertSaleOrderStrategy {

	private final SpfOrderRepository spfOrderRepository;

	public SpfChannelConvertSaleOrderStrategy(SpfOrderRepository spfOrderRepository, SaleOrderRepository saleOrderRepository,
			StoreClient storeClient, SkuMappingRepository skuMappingRepository, ProductClient productClient) {
		super(saleOrderRepository, storeClient, skuMappingRepository, productClient);
		this.spfOrderRepository = spfOrderRepository;
	}

	@Override
	public SaleChannelType getChannelType() {
		return SaleChannelType.SHOPIFY;
	}

	@Override
	public FulfillmentType getFulfillmentType() {
		return FulfillmentType.MERCHANT;
	}

	@Override
	public List<SaleOrderInfoConverterDto> getChannelOrders(StoreDTO storeDTO, Set<String> channelOrderIds, Integer pageSize, Long lastId) {
		return spfOrderRepository.findUnCompletedOrders(storeDTO.getStoreId(), channelOrderIds, pageSize, lastId);
	}

	@Override
	protected Set<String> findSellerSkusByChannelOrderId(String channelOrderNo, Integer storeId) {
		// 查询渠道订单的销售sku集
		return spfOrderRepository.findSellerSkusById(channelOrderNo, storeId);
	}

}
