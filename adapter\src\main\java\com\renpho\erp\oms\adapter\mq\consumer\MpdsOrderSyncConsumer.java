package com.renpho.erp.oms.adapter.mq.consumer;

import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderService;
import com.renpho.erp.oms.application.channelmanagement.ebay.dto.EbyOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.ebay.service.EbyOrderService;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.service.MclOrderService;
import com.renpho.erp.oms.application.channelmanagement.mercado.service.MclShipmentService;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.service.TtOrderService;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtOrderService;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.shopify.service.SpfOrderService;
import com.renpho.erp.oms.application.channelmanagement.temu.dto.TemOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.temu.service.TemOrderService;
import com.renpho.erp.oms.infrastructure.common.constant.MQConstant;
import com.renpho.erp.oms.infrastructure.common.mq.MqMessageHandlerDecorator;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 消费MPDS同步发回来的三方订单信息
 */
@Component
@AllArgsConstructor
@Slf4j
public class MpdsOrderSyncConsumer {
    private final AmzOrderService amzOrderService;
    private final WmtOrderService wmtOrderService;
    private final TtOrderService ttOrderService;
    private final MclOrderService mclOrderService;
    private final MclShipmentService mclShipmentService;
    private final SpfOrderService spfOrderService;
    private final TemOrderService temOrderService;
    private final EbyOrderService ebyOrderService;


    @Bean
    public Consumer<Message<String>> syncMpdsOrder() {
        return MqMessageHandlerDecorator.wrap(msg -> {
            String tag = msg.getHeaders().get("msgTag").toString();
            String message = msg.getPayload();
            log.info("接收到MPDS系统同步至OMS的信息{} ,tag为{}", message, tag);
            switch (tag) {
                // 同步亚马逊
                case MQConstant.TAG.SYNC_AMAZON:
                    AmzOrderPushDTO amzOrderPushDTO = JSONKit.parseObject(message, AmzOrderPushDTO.class);
                    amzOrderService.createOrUpdate(amzOrderPushDTO);
                    break;
                // 同步沃尔玛
                case MQConstant.TAG.SYNC_WALMART:
                    WmtOrderDTO wmtOrderDTO = JSONKit.parseObject(message, WmtOrderDTO.class);
                    wmtOrderService.createOrUpdate(wmtOrderDTO);
                    break;
                // 同步Tiktok
                case MQConstant.TAG.SYNC_TIKTOK:
                    TtOrderDTO ttOrderDTO = JSONKit.parseObject(message, TtOrderDTO.class);
                    ttOrderService.createOrUpdate(ttOrderDTO);
                    break;
                // 同步美客多订单
                case MQConstant.TAG.SYNC_MERCADO:
                    MclOrderDTO mclOrderDTO = JSONKit.parseObject(message, MclOrderDTO.class);
                    mclOrderService.createOrUpdate(mclOrderDTO);
                    break;
                // 同步美客多运单
                case MQConstant.TAG.SYNC_MERCADO_SHIPMENT:
                    MclShipmentDTO mclShipmentDTO = JSONKit.parseObject(message, MclShipmentDTO.class);
                    mclShipmentService.createOrUpdate(mclShipmentDTO);
                    break;
                case MQConstant.TAG.SYNC_SHOPIFY:
                    SpfOrderDTO spfOrderDTO = JSONKit.parseObject(message, SpfOrderDTO.class);
                    spfOrderService.createOrUpdate(spfOrderDTO);
                    break;
                // 同步TEMU
                case MQConstant.TAG.SYNC_TEMU:
                    TemOrderPushDTO temOrderPushDTO = JSONKit.parseObject(message, TemOrderPushDTO.class);
                    temOrderService.createOrUpdate(temOrderPushDTO);
                    break;
                // 同步eBay
                case MQConstant.TAG.SYNC_EBAY:
                    EbyOrderPushDTO ebyOrderPushDTO = JSONKit.parseObject(message, EbyOrderPushDTO.class);
                    ebyOrderService.createOrUpdate(ebyOrderPushDTO);
                    break;
                default:
                    throw new RuntimeException("未知tag");
            }
        });
    }

}
