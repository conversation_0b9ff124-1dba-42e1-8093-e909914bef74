package com.renpho.erp.oms.application.b2b.receiptOrder.vo;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/20 13:54
 */
@Data
public class SearchB2bOrderBaseInfoVO {

    /**
     * B2B订单号
     */
    private String orderNo;

    /**
     * 币种
     */
    private String currency;


    /**
     * 金额合计
     */
    private BigDecimal totalAmount;


    /**
     * 订单已收款金额
     */
    private BigDecimal orderReceiptAmount;

    /**
     * 已收款百分比例(20代表20%)
     */
    private BigDecimal orderReceiptRate;

    /**
     * 银行账号
     */
    private Integer fmsFundAccountCurrencyId;

    /**
     * 银行账号名称
     */
    private String fmsFundAccountCurrencyName;

    /**
     * 付款条款编码
     */
    private String paymentTermsCode;

    /**
     * 付款条款名称
     */
    private String paymentTermsName;


}
