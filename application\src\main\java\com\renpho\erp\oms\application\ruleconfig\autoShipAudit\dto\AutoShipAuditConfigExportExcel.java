package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/19 16:11
 */
@Data
public class AutoShipAuditConfigExportExcel {

    /**
     * 渠道
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.CHANNEL")
    private String channel;

    /**
     * 店铺
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.STORE")
    private String store;

    /**
     * psku
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.PSKU")
    private String psku;

    /***
     * 仓库选择顺序
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.WAREHOUSESELECTORPRIORITY")
    private String warehouseSelectorPriority;

    /**
     * sku组合类型
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.SKUCOMBINATIONTYPE")
    private String skuCombinationType;

    /**
     * 自动发货审核开关
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXPORT_EXCEL.AUTOSWITCH")
    private String autoSwitch;
}

