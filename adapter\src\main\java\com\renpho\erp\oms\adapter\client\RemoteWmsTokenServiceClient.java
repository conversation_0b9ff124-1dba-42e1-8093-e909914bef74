package com.renpho.erp.oms.adapter.client;

import com.renpho.erp.oms.application.wms.WmsTokenService;
import com.renpho.erp.oms.client.wms.RemoteWmsTokenService;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class RemoteWmsTokenServiceClient implements RemoteWmsTokenService {
    @Autowired
    private WmsTokenService wmsTokenService;

    @Inner
    @Override
    public R<String> getToken(String wmsWarehouseCode) {
        return R.success(wmsTokenService.getToken(wmsWarehouseCode));
    }
}
