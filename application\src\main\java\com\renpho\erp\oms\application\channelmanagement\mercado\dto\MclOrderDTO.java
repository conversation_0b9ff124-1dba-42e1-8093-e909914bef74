package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MclOrderDTO {

	/**
	 * 同步时间
	 */
	private String syncTime;
	private String mclUserId;
	private Integer storeId;

	private Long id;
	private String date_created;
	private String date_closed;
	private String last_updated;
	private String manufacturing_ending_date;
	private Feedback feedback;
	private List<Mediation> mediations;
	private String comments;
	private Long pack_id;
	private String pickup_id;
	private OrderRequest order_request;
	private Boolean fulfilled;
	private BigDecimal paid_amount;
	private Coupon coupon;
	private String expiration_date;
	private List<OrderItem> order_items;
	private String currency_id;
	private List<Payment> payments;
	private Id shipping;
	private String status;
	private StatusDetail status_detail;
	private Buyer buyer;
	private Id seller;
	private Taxes taxes;
	private Context context;

	@Data
	public static class Feedback {
		private Purchase purchase;
	}

	@Data
	public static class Purchase {
		private Long id;
	}

	@Data
	public static class OrderRequest {
		@JSONField(name = "return")
		@JsonProperty("return")
		private String returnPolicy;
		@JSONField(name = "change")
		@JsonProperty("change")
		private String changePolicy;
	}

	@Data
	public static class Coupon {
		private String id;
		private BigDecimal amount;
	}

	@Data
	public static class OrderItem {
		private Item item;
		private Integer quantity;
		private BigDecimal unit_price;
		private BigDecimal full_unit_price;
		private String currency_id;
		private Integer manufacturing_days;
		private BigDecimal sale_fee;
		private BigDecimal base_exchange_rate;
	}

	@Data
	public static class Item {
		private String id;
		private String title;
		private String category_id;
		private Long variation_id;
		private String seller_custom_field;
		private List<VariationAttribute> variation_attributes;
		private String warranty;
		private String condition;
		private String seller_sku;
		private String parent_item_id;
	}

	@Data
	public static class VariationAttribute {
		private String id;
		private String name;
		private String valueId;
		private String valueName;
	}

	@Data
	public static class Payment {
		private Long id;
		private Long order_id;
		private Long payer_id;
		private Id collector;
		private Long card_id;
		private String site_id;
		private String reason;
		private String payment_method_id;
		private String currency_id;
		private Integer installments;
		private String issuer_id;
		private AtmTransferReference atm_transfer_reference;
		private String coupon_id;
		private String activation_uri;
		private String operation_type;
		private String payment_type;
		private List<String> available_actions;
		private String status;
		private Integer status_code;
		private String status_detail;
		private BigDecimal transaction_amount;
		private BigDecimal taxes_amount;
		private BigDecimal shipping_cost;
		private BigDecimal coupon_amount;
		private BigDecimal overpaid_amount;
		private BigDecimal total_paid_amount;
		private BigDecimal installment_amount;
		private String deferred_period;
		private String date_approved;
		private String authorization_code;
		private String transaction_order_id;
		private String date_created;
		private String date_last_modified;
	}

	@Data
	public static class AtmTransferReference {
		private String company_id;
		private String transaction_id;
	}

	@Data
	public static class Buyer {
		private Long id;
		private String nickname;
		private String last_name;
		private String first_name;
	}

	@Data
	public static class Id {
		private Long id;
	}

	@Data
	public static class Taxes {
		private String amount;
		private String currency_id;
	}

	@Data
	public static class Context {
		private String channel;
		private String site;
		private List<String> flows;
		private String application;
	}

	@Data
	public static class StatusDetail {
		private String code;
		private String description;
	}

	@Data
	public static class Mediation {
		private Long id;
	}

}
