package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingUpdateCmd;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingLine;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SkuMappingCmdConvertor {
	@Mapping(target = "fulfillmentType",
			expression = "java(com.renpho.erp.oms.domain.salemanagement.FulfillmentType.enumOf(param.getFulfillmentType()))")
	SkuMappingAggRoot toDomain(SkuMappingAddCmd param);

	SkuMappingAggRoot toDomain(SkuMappingUpdateCmd param);

	SkuMappingLine toDomain(SkuMappingAddCmd.SkuMappingLineAddCmd param);
}
