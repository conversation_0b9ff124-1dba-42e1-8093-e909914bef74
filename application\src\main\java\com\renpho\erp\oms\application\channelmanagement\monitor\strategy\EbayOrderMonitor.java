package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.ebay.repository.EbyOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * Ebay订单监视器
 * <AUTHOR>
 */
@Component
public class EbayOrderMonitor extends AbstractChannelOrderMonitor {

	private final EbyOrderRepository ebyOrderRepository;

	public EbayOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			EbyOrderRepository ebyOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.ebyOrderRepository = ebyOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.EBY_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return ebyOrderRepository.count(storeAuthorization.getEbaySellerId(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		return storeAuthorization.getEbaySellerId();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 按照ebay 卖家ID统计
		return storeAuthorizationList.stream()
			.filter(s -> storeNames.contains(s.getStoreName()))
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			// ebay 卖家ID
			.map(StoreAuthorizationVo.Authorization::getEbaySellerId)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.map(ebaySellerId -> {
				StoreAuthorization storeAuthorization = new StoreAuthorization();
				storeAuthorization.setEbaySellerId(ebaySellerId);
				// 店铺名称
				storeAuthorization.setStoreNames(storeAuthorizationList.stream()
					.filter(s -> Objects.nonNull(s.getAuthorization())
							&& StringUtils.equals(s.getAuthorization().getEbaySellerId(), ebaySellerId))
					.map(StoreAuthorizationVo::getStoreName)
					.collect(Collectors.toSet()));
				return storeAuthorization;
			})
			.toList();

	}
}
