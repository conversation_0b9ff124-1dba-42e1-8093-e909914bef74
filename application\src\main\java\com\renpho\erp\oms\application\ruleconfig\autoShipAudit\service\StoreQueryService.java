package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.service;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.security.constant.RoleLabelEnum;
import com.renpho.erp.security.util.SecurityUtils;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/20 16:59
 */
@Service
@AllArgsConstructor
public class StoreQueryService {

	private final StoreClient storeClient;

	/**
	 * 有数据权限的店铺id
	 * @return
	 */
	public Set<Integer> listPermissionStoreIds() {
		// 角色标签
		String[] roleLabels = SecurityUtils.getUserLabels();
		// 超级管理 || 不存在运营标签（运营管理、运营人员），拥有所有店铺权限
		if (SecurityUtils.isAdmin() || Objects.isNull(roleLabels) || Arrays.stream(roleLabels).noneMatch(RoleLabelEnum::isOperations)) {
			return storeClient.queryAllStore().stream()
                    .map(StoreVo::getId)
                    .collect(Collectors.toSet());
		}
		return storeClient.findStorePermission(SecurityUtils.getUserId());
	}
}
