package com.renpho.erp.oms.application.mpdspushordermonitor.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.mpds.client.api.ChannelOrderApi;
import com.renpho.erp.mpds.client.model.OrderSourceFilterPillQueryDto;
import com.renpho.erp.mpds.client.model.OrderSourceFilterPillQueryParam;
import com.renpho.erp.mpds.client.model.OrderSourcePushParam;
import com.renpho.erp.oms.application.mpdspushordermonitor.cmd.MpdsPushOrderMonitorCmd;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.factory.PlatformServiceFactory;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.mercado.po.MclShipmentPO;
import com.renpho.erp.oms.infrastructure.persistence.mercado.repository.MclShipmentRepositoryImpl;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.shenyu.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description mpds推单监控
 * @date 2025/5/8 15:01
 */
@Component
@Slf4j
public class MpdsPushOrderMonitorJob {

	@Autowired
	private ChannelOrderApi channelOrderApi;

	@Autowired
	private PlatformServiceFactory platformServiceFactory;

	@Autowired
	private StoreClient storeClient;

	@Autowired
	private DingTalkClient dingTalkClient;

	// 定义美客多运单类型,额外处理
	private final String mclShipment = "MCLSHIPMENT";

	// 分割的符号
	private final String spiltStr = "###";

	@Autowired
	private MclShipmentRepositoryImpl mclShipmentRepository;

	/**
	 * 环境
	 */
	@Value("${spring.profiles.active}")
	private String env;

	/**
	 * mpds推单监控
	 */
	@XxlJob("mpdsPushOrderMonitorJob")
	public void mpdsPushOrderMonitorJob() {
		String jobParam = XxlJobHelper.getJobParam();
		MpdsPushOrderMonitorCmd mpdsPushOrderMonitorCmd = new MpdsPushOrderMonitorCmd();
		if (StrUtil.isNotEmpty(jobParam)) {
			mpdsPushOrderMonitorCmd = JSONKit.parseObject(jobParam, MpdsPushOrderMonitorCmd.class);
		}
		// 获取监控的渠道
		List<String> monitorChannelTypes = getMonitorChannelTypes(mpdsPushOrderMonitorCmd);
		// 获取开始时间、结束时间
		Pair<LocalDateTime, LocalDateTime> monitorTime = getMonitorTime(mpdsPushOrderMonitorCmd);
		LocalDateTime startLocalDateTime = monitorTime.getKey();
		LocalDateTime endLocalDateTime = monitorTime.getValue();
		XxlJobHelper.log("开始监控{}至{}的订单推送", startLocalDateTime, endLocalDateTime);
		// 每个渠道监控
		for (String monitorChannelType : monitorChannelTypes) {
			try {
				if (StrUtil.equals(monitorChannelType, mclShipment)) {
					// 美客多运单额外处理
					doMclShipmentMonitor(mclShipment, startLocalDateTime, endLocalDateTime, mpdsPushOrderMonitorCmd.isAutoRetryPushOrder());
				} else {
					doSingleChannelTypeMonitor(monitorChannelType, startLocalDateTime, endLocalDateTime, mpdsPushOrderMonitorCmd.isAutoRetryPushOrder());
				}
			} catch (Exception e) {
				XxlJobHelper.log("处理渠道:{}异常，e:{}", monitorChannelType, e.getMessage(), e);
				log.info("处理渠道:{}异常，e:{}", monitorChannelType, e.getMessage(), e);
			}
		}

	}

	/**
	 * 获取监控的渠道，可指定渠道
	 * @param mpdsPushOrderMonitorCmd
	 * @return
	 */
	private List<String> getMonitorChannelTypes(MpdsPushOrderMonitorCmd mpdsPushOrderMonitorCmd) {
		// 默认要监控的渠道
		List<String> monitorChannelTypes = Arrays.asList(SaleChannelType.AMAZON.getValue(), SaleChannelType.WALMART.getValue(),
				SaleChannelType.TIKTOK.getValue(), SaleChannelType.MERCADO.getValue(), SaleChannelType.SHOPIFY.getValue(),
				SaleChannelType.TEMU.getValue(), SaleChannelType.EBAY.getValue(), mclShipment);
		if (CollUtil.isNotEmpty(mpdsPushOrderMonitorCmd.getMonitorChannelTypes())) {
			// 指定渠道
			monitorChannelTypes = mpdsPushOrderMonitorCmd.getMonitorChannelTypes();
		}
		return monitorChannelTypes;
	}

	/**
	 * 处理单个渠道的监控
	 * @param monitorChannelType
	 * @param startLocalDateTime
	 * @param endLocalDateTime
	 */
	private void doSingleChannelTypeMonitor(String monitorChannelType, LocalDateTime startLocalDateTime, LocalDateTime endLocalDateTime, boolean autoRetryOrder) {
		XxlJobHelper.log("渠道{}监控", monitorChannelType);
		// 1.查询mpds已同步的订单
		Map<String, OrderSourceFilterPillQueryDto> mpdsSyncOrderMap = queryMpdsSyncOrder(startLocalDateTime, endLocalDateTime, monitorChannelType)
				.stream()
				.collect(Collectors.toMap(x -> StrUtil.concat(false, x.getPlatOrderId(), spiltStr, String.valueOf(x.getStoreId())),
							x -> x, (v1, v2) -> v1));
		if (CollUtil.isEmpty(mpdsSyncOrderMap)) {
			XxlJobHelper.log("{}这段时间mpds没有同步订单", monitorChannelType);
			return;
		}
		Set<String> mpdsSyncOrderSet = mpdsSyncOrderMap.keySet();
		// 2.查询oms已同步的订单
		Set<String> omsSyncOrderSet = queryOmsSyncOrder(startLocalDateTime, endLocalDateTime, monitorChannelType)
				.stream()
				.map(x -> StrUtil.concat(false, x.getOrderId(), spiltStr, String.valueOf(x.getStoreId())))
				.collect(Collectors.toSet());
		// 3.查看差异(mpds已同步,oms没有数据)
		mpdsSyncOrderSet.removeAll(omsSyncOrderSet);
		if (CollUtil.isEmpty(mpdsSyncOrderSet)) {
			// 没有差异
			return;
		}
		Set<String> noSyncOrderIdList = mpdsSyncOrderSet.stream().map(x -> x.split(spiltStr)[0]).collect(Collectors.toSet());
		XxlJobHelper.log("{},oms没有同步的订单:{}", monitorChannelType, JSONKit.toJSONString(noSyncOrderIdList));
		log.info("{},oms没有同步的订单:{}", monitorChannelType, JSONKit.toJSONString(noSyncOrderIdList));
		// 4.发送钉钉异常通知
		sendChannelOrderAlarmDingTalk(mpdsSyncOrderSet, mpdsSyncOrderMap, monitorChannelType);
		// 5.调用mpds重新推送
		retryPushOrder(noSyncOrderIdList, autoRetryOrder, monitorChannelType);
	}

	/**
	 * 调用mpds重新推送
	 * @param noSyncList
	 * @param autoRetryOrder
	 */
	private void retryPushOrder(Set<String> noSyncList, boolean autoRetryOrder, String monitorChannelType) {
		if (!autoRetryOrder) {
			// 不用重新推送
			return;
		}
		CollUtil.split(noSyncList,500).forEach(tempList->{
			OrderSourcePushParam param = new OrderSourcePushParam();
			param.setChannelCode(monitorChannelType);
			if (StrUtil.equals(monitorChannelType, mclShipment)) {
				param.setShipmentIds(tempList.stream().collect(Collectors.toSet()));
			} else {
				param.setChannelOrderIds(tempList.stream().collect(Collectors.toSet()));
			}
			channelOrderApi.pushOrder(param);
		});

	}

	/**
	 * 美客多监控运单处理
	 * @param monitorChannelType
	 * @param startLocalDateTime
	 * @param endLocalDateTime
	 */
	private void doMclShipmentMonitor(String monitorChannelType, LocalDateTime startLocalDateTime, LocalDateTime endLocalDateTime,
			boolean autoRetryPush) {
		XxlJobHelper.log("渠道{}监控", monitorChannelType);
		// 1.查询mpds已同步的运单
		Map<String, OrderSourceFilterPillQueryDto> mpdsSyncShipmentMap = queryMpdsSyncOrder(startLocalDateTime, endLocalDateTime, monitorChannelType)
				.stream()
				.collect(Collectors.toMap(x -> StrUtil.concat(false, x.getShipmentId(), spiltStr, String.valueOf(x.getStoreId())),
							x -> x, (v1, v2) -> v1));
		if (CollUtil.isEmpty(mpdsSyncShipmentMap)) {
			XxlJobHelper.log("{}这段时间mpds没有同步运单", monitorChannelType);
			return;
		}
		Set<String> mpdsSyncShipmentSet = mpdsSyncShipmentMap.keySet();
		// 2.查询oms已同步的运单
		Set<String> omsSyncShipmentSet = mclShipmentRepository.list(Wrappers.<MclShipmentPO> lambdaQuery()
						.select(MclShipmentPO::getMclShipmentId, MclShipmentPO::getStoreId)
				         //往前5分钟，减少误差
						.ge(MclShipmentPO::getLastMpdsSyncShipmentTime, startLocalDateTime.minusMinutes(5))
						.le(MclShipmentPO::getLastMpdsSyncShipmentTime, endLocalDateTime))
				.stream()
				.map(x -> StrUtil.concat(false, String.valueOf(x.getMclShipmentId()), spiltStr, String.valueOf(x.getStoreId())))
			.collect(Collectors.toSet());
		// 3.查看差异(mpds已同步,oms没有数据)
		mpdsSyncShipmentSet.removeAll(omsSyncShipmentSet);
		if (CollUtil.isEmpty(mpdsSyncShipmentSet)) {
			// 没有差异
			return;
		}
		Set<String> noSynShipmentIds = mpdsSyncShipmentSet.stream().map(x -> x.split(spiltStr)[0]).collect(Collectors.toSet());
		XxlJobHelper.log("oms没有同步的美客多运单:{}", JSONKit.toJSONString(noSynShipmentIds));
		log.info("oms没有同步的美客多运单:{}", JSONKit.toJSONString(noSynShipmentIds));
		// 4.发送钉钉异常通知
		sendMclShipmentAlarmDingTalk(mpdsSyncShipmentSet, mpdsSyncShipmentMap, monitorChannelType);
		// 5. 调用mpds重新推送
		retryPushOrder(noSynShipmentIds, autoRetryPush, monitorChannelType);
	}

	/**
	 * 发送美客多运单异常通知
	 * @param noSyncShipmentSet
	 * @param mpdsSyncShipmentMap
	 * @param monitorChannelType
	 */
	private void sendMclShipmentAlarmDingTalk(Set<String> noSyncShipmentSet, Map<String, OrderSourceFilterPillQueryDto> mpdsSyncShipmentMap,
			String monitorChannelType) {
		// 异常订单by店铺维度发生通知
		Map<Integer, List<OrderSourceFilterPillQueryDto>> errorSyncOrderMap = noSyncShipmentSet.stream()
			.map(x -> mpdsSyncShipmentMap.get(x))
			.collect(Collectors.groupingBy(OrderSourceFilterPillQueryDto::getStoreId));
		Map<Integer, StoreVo> storeInfoMap = getStoreInfoMap(errorSyncOrderMap.keySet());
		errorSyncOrderMap.forEach((storeId, list) -> {
			// 防止发送钉钉的消息体过大，批次发100个异常订单
			CollUtil.split(list, 100).forEach(tempList -> {
				// 通知内容 "OMS未同步订单:渠道-店铺：order1,order2"
				String shipmentIds = tempList.stream().map(OrderSourceFilterPillQueryDto::getShipmentId).collect(Collectors.joining(","));
				String msg = String.format("环境:%s,OMS未同步美客多运单,店铺:%s,运单%s", env,
						storeInfoMap.getOrDefault(storeId, new StoreVo()).getStoreName(), shipmentIds);
				dingTalkClient.sendAlarmMessage(msg);
			});
		});
	}

	/**
	 * 发送渠道订单异常通知
	 * @param noSyncOrderSet
	 * @param mpdsSyncOrderMap
	 */
	private void sendChannelOrderAlarmDingTalk(Set<String> noSyncOrderSet, Map<String, OrderSourceFilterPillQueryDto> mpdsSyncOrderMap,
			String monitorChannelType) {
		// 异常订单by店铺维度发生通知
		Map<Integer, List<OrderSourceFilterPillQueryDto>> errorSyncOrderMap = noSyncOrderSet.stream()
			.map(x -> mpdsSyncOrderMap.get(x))
			.collect(Collectors.groupingBy(OrderSourceFilterPillQueryDto::getStoreId));
		Map<Integer, StoreVo> storeInfoMap = getStoreInfoMap(errorSyncOrderMap.keySet());
		errorSyncOrderMap.forEach((storeId, list) -> {
			// 防止发送钉钉的消息体过大，批次发100个异常订单
			CollUtil.split(list, 100).forEach(tempList -> {
				// 通知内容 "OMS未同步订单:渠道-店铺：order1,order2"
				String orderIds = tempList.stream().map(OrderSourceFilterPillQueryDto::getPlatOrderId).collect(Collectors.joining(","));
				String msg = String.format("环境:%s,OMS未同步渠道订单,渠道:%s,店铺:%s,订单:%s", env, monitorChannelType,
						storeInfoMap.getOrDefault(storeId, new StoreVo()).getStoreName(), orderIds);
				dingTalkClient.sendAlarmMessage(msg);
			});
		});
	}

	/**
	 * 查询oms已同步的订单
	 * @param startLocalDateTime
	 * @param endLocalDateTime
	 * @param monitorChannelType
	 * @return
	 */
	private List<MonitorPlatformOrderDto> queryOmsSyncOrder(LocalDateTime startLocalDateTime, LocalDateTime endLocalDateTime,
			String monitorChannelType) {
		MonitorPlatformOrderQuery query = new MonitorPlatformOrderQuery();
		//查询oms订单往前5分钟，减小时间误差
		query.setStartLastMpdsSyncOrderTime(startLocalDateTime.minusMinutes(5));
		query.setEndLastMpdsSyncOrderTime(endLocalDateTime);
		return platformServiceFactory.getService(monitorChannelType).queryMonitorPlatformOrder(query);
	}

	/**
	 * 查询mpds同步的订单
	 * @param startLocalDateTime
	 * @param endLocalDateTime
	 * @param monitorChannelType
	 * @return
	 */
	private List<OrderSourceFilterPillQueryDto> queryMpdsSyncOrder(LocalDateTime startLocalDateTime, LocalDateTime endLocalDateTime,
			String monitorChannelType) {
		List<OrderSourceFilterPillQueryDto> orderSourceFilterPillQueryDtoList = Lists.newArrayList();
		OrderSourceFilterPillQueryParam queryParam = new OrderSourceFilterPillQueryParam();
		queryParam.setPageSize(500);
		queryParam.setChannelCode(monitorChannelType);
		queryParam.setSyncOmsStatus(1);
		if (StrUtil.equals(SaleChannelType.TEMU.getValue(),monitorChannelType)) {
			queryParam.setStartLastPushOmsTime(DateUtils.localDateTimeToString(startLocalDateTime));
			// 往前3分钟是防止mq消费延迟
			queryParam.setEndLastPushOmsTime(DateUtils.localDateTimeToString(endLocalDateTime.minusMinutes(3)));
		}else {
			queryParam.setStartUpdateDate(DateUtils.localDateTimeToString(startLocalDateTime));
			// 往前3分钟是防止mq消费延迟
			queryParam.setEndUpdateDate(DateUtils.localDateTimeToString(endLocalDateTime.minusMinutes(3)));
		}
		int returnPageSize = 1;
		Integer pageIndex = 0;
		List<OrderSourceFilterPillQueryDto> records = Lists.newArrayList();
		do {
			pageIndex++;
			queryParam.setPageIndex(pageIndex);
			R<Paging<OrderSourceFilterPillQueryDto>> resp = channelOrderApi.queryOrderSourceFilterPill(queryParam);
			if (!resp.isSuccess()) {
				throw new RuntimeException(
						String.format("查询mpds订单出错,channelType%s,param:%s", monitorChannelType, JSONKit.toJSONString(queryParam)));
			}
			returnPageSize = resp.getData().getPageSize();
			records = resp.getData().getRecords();
			orderSourceFilterPillQueryDtoList.addAll(records);
		} while (pageIndex < returnPageSize);

		return orderSourceFilterPillQueryDtoList;
	}

	/**
	 * 获取监控时间 1.指定开始时间，结束时间取对应的时间
	 * 2.无指定开始时间，结束时间，取当前时间-最近lastHour，当前时间（可参数指定lastHour，默认一个小时）
	 * @return
	 */
	private Pair<LocalDateTime, LocalDateTime> getMonitorTime(MpdsPushOrderMonitorCmd mpdsPushOrderMonitorCmd) {
		// 最近多少个小时
		int lastHour = mpdsPushOrderMonitorCmd.getLastHour() == null ? 1 : mpdsPushOrderMonitorCmd.getLastHour();
		// 监控的开始时间
		LocalDateTime startLocalDateTime;
		// 监控的结束时间
		LocalDateTime endLocalDateTime;
		if (StrUtil.isNotEmpty(mpdsPushOrderMonitorCmd.getStartTime()) && StrUtil.isNotEmpty(mpdsPushOrderMonitorCmd.getEndTime())) {
			startLocalDateTime = DateUtils.parseLocalDateTime(mpdsPushOrderMonitorCmd.getStartTime());
			endLocalDateTime = DateUtils.parseLocalDateTime(mpdsPushOrderMonitorCmd.getEndTime());
		} else {
			// 转成yyyy-MM-dd HH:mm:ss格式，因为调用mpds转成这种格式，否则会有毫秒误差
			String nowStr = DateUtils.localDateTimeToString(LocalDateTime.now());
			endLocalDateTime = DateUtils.parseLocalDateTime(nowStr);
			startLocalDateTime = endLocalDateTime.minusHours(lastHour);
		}
		return new Pair<>(startLocalDateTime, endLocalDateTime);
	}

	/**
	 * 获取店铺信息
	 * @param storeIds
	 * @return
	 */
	private Map<Integer, StoreVo> getStoreInfoMap(Set<Integer> storeIds) {
		if (CollUtil.isEmpty(storeIds)) {
			return new HashMap<>();
		}
		return storeClient.getByStoreIds(storeIds).stream().collect(Collectors.toMap(StoreVo::getId, Function.identity()));
	}
}
