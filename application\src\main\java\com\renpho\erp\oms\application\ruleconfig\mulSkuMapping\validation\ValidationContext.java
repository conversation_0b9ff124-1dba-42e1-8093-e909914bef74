package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.validation;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.Getter;

import java.util.Map;
import java.util.Set;

@Getter
public class ValidationContext {
	private final Map<String, StoreVo> storeMap;
	private final Map<String, OumUserInfoRes> operatorMap;
	private final Set<String> purchaseSkuSet;
	private final Set<String> uniqueKeySet;

	public ValidationContext(Map<String, StoreVo> storeMap, Map<String, OumUserInfoRes> operatorMap, Set<String> purchaseSkuSet,
			Set<String> uniqueKeySet) {
		this.storeMap = storeMap;
		this.operatorMap = operatorMap;
		this.purchaseSkuSet = purchaseSkuSet;
		this.uniqueKeySet = uniqueKeySet;
	}

}
