package com.renpho.erp.oms.application.channelmanagement.walmart.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WmtOrderLineStatusLog {
	/**
	 * 商品行号
	 */
	private String lineNumber;

	/**
	 * 卖方SKU
	 */
	private String itemSku;

	/**
	 * 状态 Created Acknowledged Shipped Delivered Cancelled Refund
	 */
	private String status;

	/**
	 * 数量单位
	 */
	private String statusQuantityUnit;

	/**
	 * 数量
	 */
	private String statusQuantityAmount;

	/**
	 * 取消原因
	 */
	private String cancellationReason;

	/**
	 * 发货时间
	 */
	private LocalDateTime trackingInfoShipDateTime;

	/**
	 * 其他发货人
	 */
	private String trackingInfoOtherCarrier;

	/**
	 * 发货人
	 */
	private String trackingInfoCarrier;

	/**
	 * 发货方式编码
	 */
	private String trackingInfoMethodCode;

	/**
	 * 追踪号码
	 */
	private String trackingInfoTrackingNumber;

	/**
	 * 跟踪货件的 URL
	 */
	private String trackingInfoTrackingUrl;

	/**
	 * 使卖家能够在履行期间指定 RC 中心地址json
	 */
	private String returnCenterAddress;
}
