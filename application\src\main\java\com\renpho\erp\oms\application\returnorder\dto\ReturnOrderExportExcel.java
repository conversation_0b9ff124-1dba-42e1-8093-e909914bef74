package com.renpho.erp.oms.application.returnorder.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 退货订单导出
 * @time: 2025-03-21 17:07:21
 * @author: Alina
 */
@Data
public class ReturnOrderExportExcel implements VO {

	/**
	 * 主键，不参与 Excel 处理
	 */
	@ExcelIgnore
	private Long id;
	/**
	 * 退货单号
	 */
	@ExcelProperty(value = "RETURN_ORDER_EXCEL_HEADER.SR")
	private String returnOrderNo;

	/**
	 * 店铺单号
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHOPORDER")
	private String channelOrderNo;
	/**
	 * 店铺名称
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHOP")
	private String storeName;

	/**
	 * 销售渠道名称
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SALES_CHANNEL")
	private String channelName;

	/**
	 * 销售SKU，平台给的销售SKU字段
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.MSKU")
	private String msku;

	/**
	 * 采购SKU，根据映射表关联出的本地PSKU（ERP里面的SKU）
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PSKU")
	private String psku;

	/**
	 * FNSKU，根据映射表关联出的商品条码（库存识别码）
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.FNSKU")
	private String fnSku;

	/**
	 * 退货数量
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.QUANTITY")
	private Integer returnQuantity;

	/**
	 * 退货仓库
	 */
	@ExcelProperty(value = "RETURN_ORDER_EXCEL_HEADER.WAREHOUSE")
	private String warehouseName;

	/**
	 * 承运商
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.CARRIER")
	private String carrierName;

	/**
	 * 跟踪号
	 */
	@ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.TRACKING_NO")
	private String trackingNo;

	/**
	 * 状态，不参与 Excel 处理
	 */
	@ExcelIgnore
	@Trans(type = TransType.DICTIONARY, key = "return_order_status", ref = "returnOrderStatusName")
	private Integer returnOrderStatus;

	/**
	 * 状态名称
	 */
	@ExcelProperty(value = "RETURN_ORDER_EXCEL_HEADER.STATUS")
	private String returnOrderStatusName;

	/**
	 * 收货时间
	 */
	@ExcelProperty(value = "RETURN_ORDER_EXCEL_HEADER.RECEIVE_TIME")
	private LocalDateTime receiveTime;

	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "RETURN_ORDER_EXCEL_HEADER.CREATE_TIME")
	private LocalDateTime createTime;

}
