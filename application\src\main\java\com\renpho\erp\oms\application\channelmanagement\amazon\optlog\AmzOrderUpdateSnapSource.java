package com.renpho.erp.oms.application.channelmanagement.amazon.optlog;

import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderLog;
import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderService;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.renpho.erp.oplog.log.SnapshotDatatSource;

import lombok.AllArgsConstructor;

/**
 **
 */
@Component
@AllArgsConstructor
public class AmzOrderUpdateSnapSource implements SnapshotDatatSource {

	private final AmzOrderService amzOrderService;

	@Override
	public JSONObject getOldData(Object[] args) {
		// 将对象转换为 Json 字符串
		Long id = ((JSONObject) JSONObject.toJSON(args[0])).getObject("id", Long.class);

		return JSON.parseObject(JSON.toJSONString(amzOrderService.getLogById(id)));
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		AmzOrderLog amzOrderLog = amzOrderService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(amzOrderLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
