package com.renpho.erp.oms.application.b2b.receiptOrder.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.fms.client.ap.fundaccount.command.FundAccountClientQuery;
import com.renpho.erp.fms.client.ap.fundaccount.vo.FundAccountClientCurrencyVO;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.AddReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.CancelReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.ConfirmReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.SearchB2bOrderQuery;
import com.renpho.erp.oms.application.b2b.receiptOrder.convert.B2bReceiptOrderConvertor;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.ReceiptOrderDetailVO;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.SearchB2bOrderBaseInfoVO;

import com.renpho.erp.oms.domain.b2b.order.B2bOrderStatusEnum;
import com.renpho.erp.oms.domain.b2b.order.B2bReceiptOrderStatusEnum;
import com.renpho.erp.oms.domain.receiptOrder.model.B2bOrderBaseInfoDto;
import com.renpho.erp.oms.domain.receiptOrder.repository.B2bReceiptOrderRepository;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.fms.FmsFundAccountClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.PaymentTermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po.B2bReceiptOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.service.B2bReceiptOrderPoService;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * <AUTHOR>
 * @description
 * @date 2025/6/20 10:38
 */
@Service
@AllArgsConstructor
public class B2bReceiptOrderService {

    private B2bReceiptOrderRepository b2bReceiptOrderRepository;

    private final CompanyClient companyClient;

    private final PaymentTermsConfigService paymentTermsConfigService;

    private final FmsFundAccountClient fmsFundAccountClient;

    private final B2bReceiptOrderConvertor b2bReceiptOrderConvertor;

    /**
     * 能够添加的订单状态
     */
    private final List<Integer> canAddReceiptOrderStatus = Arrays.asList(B2bOrderStatusEnum.PENDING.getValue(), B2bOrderStatusEnum.IN_REVIEW.getValue(),
            B2bOrderStatusEnum.PREPARING.getValue(), B2bOrderStatusEnum.PARTIALLY_SHIPPED.getValue(), B2bOrderStatusEnum.SHIPPED.getValue());

    private B2bReceiptOrderPoService b2bReceiptOrderPoService;


    /**
     * 查询b2b订单信息，用于回显
     *
     * @param searchB2bOrderQuery
     * @return
     */
    public SearchB2bOrderBaseInfoVO searchB2bOrderBaseInfo(SearchB2bOrderQuery searchB2bOrderQuery) {
        //查询b2b订单基本信息
        B2bOrderBaseInfoDto b2bOrderBaseInfoDto = Optional.ofNullable(b2bReceiptOrderRepository.getB2bOrderBaseInfoDto(searchB2bOrderQuery.getOrderNo()))
                .orElseThrow(() -> new BusinessException(I18nMessageKit.getMessage("ORDER_NOT_EXISTS")));
        //付款条款名称
        String paymentTermsName = paymentTermsConfigService.getPaymentTermsNameByCode(b2bOrderBaseInfoDto.getPaymentTermsCode());
        //获取银行账号
        Optional<FundAccountClientCurrencyVO> bankAccountOptional = getBankAccount(b2bOrderBaseInfoDto.getSalesCompanyId(), b2bOrderBaseInfoDto.getCurrency());
        return b2bReceiptOrderConvertor.toB2bOrderBaseInfoVo(b2bOrderBaseInfoDto, bankAccountOptional, paymentTermsName);

    }

    /**
     * 添加收款单
     *
     * @param cmd
     */
    @Lock4j(name = "oms:receiptOrder:addOrConfirm", keys = {"#cmd.orderNo"})
    public void addReceiptOrder(AddReceiptOrderCmd cmd) {
        //获取订单基础信息
        B2bOrderBaseInfoDto b2bOrderBaseInfoDto = b2bReceiptOrderRepository.getB2bOrderBaseInfoDto(cmd.getOrderNo());
        //校验
        validateAddReceipt(b2bOrderBaseInfoDto);
        //获取银行账号
        Optional<FundAccountClientCurrencyVO> bankAccountOptional = getBankAccount(b2bOrderBaseInfoDto.getSalesCompanyId(), b2bOrderBaseInfoDto.getCurrency());
        //转成po
        B2bReceiptOrderPO b2bReceiptOrderPO = b2bReceiptOrderConvertor.addReceiptOrderCmdToPo(cmd, b2bOrderBaseInfoDto, bankAccountOptional);
        //保存
        b2bReceiptOrderPoService.save(b2bReceiptOrderPO);
    }


    /**
     * 确认收款
     *
     * @param cmd
     */
    @Lock4j(name = "b2b:order:edit", keys = {"#cmd.orderId"})
    public void confirmReceiptOrder(ConfirmReceiptOrderCmd cmd) {
        B2bReceiptOrderPO b2bReceiptOrderPO = b2bReceiptOrderPoService.getById(cmd.getId());
        //校验
        validateConfirmCancelReceiptOrder(b2bReceiptOrderPO);
        //获取中英文银行账号
        FundAccountClientCurrencyVO clientCurrencyVO = Optional.ofNullable(fmsFundAccountClient.getFundAccountCurrencyById(cmd.getFmsFundAccountCurrencyId()))
                .orElseThrow(() -> new BusinessException("未找到银行账号"));
        //转成po
        b2bReceiptOrderConvertor.confirmCmdToPo(b2bReceiptOrderPO, cmd, clientCurrencyVO);
        //确认收款单
        b2bReceiptOrderPoService.confirmReceiptOrder(b2bReceiptOrderPO);
    }


    /**
     * 取消收款单
     *
     * @param cmd
     */
    @Lock4j(name = "oms:receiptOrder:cancel", keys = {"#cmd.id"})
    public void cancelReceiptOrder(CancelReceiptOrderCmd cmd) {
        B2bReceiptOrderPO b2bReceiptOrderPO = b2bReceiptOrderPoService.getById(cmd.getId());
        //校验
        validateConfirmCancelReceiptOrder(b2bReceiptOrderPO);
        //修改为取消
        b2bReceiptOrderPoService.cancelReceiptOrder(b2bReceiptOrderPO);
    }

    /**
     * 校验确认和取消
     *
     * @param b2bReceiptOrderPO
     */
    private void validateConfirmCancelReceiptOrder(B2bReceiptOrderPO b2bReceiptOrderPO) {
        if (b2bReceiptOrderPO == null) {
            throw new BusinessException("DATA_NOT_EXITS");
        }
        if (!Objects.equals(b2bReceiptOrderPO.getStatus(), B2bReceiptOrderStatusEnum.WAIT_CONFIRM.getValue())) {
            throw new BusinessException("收款单状态不是待确认");
        }
    }


    /**
     * 查询确认订单详情
     *
     * @param id
     * @return
     */
    public ReceiptOrderDetailVO queryReceiptOrderDetailVO(Long id) {
        B2bReceiptOrderPO b2bReceiptOrderPO = Optional.ofNullable(b2bReceiptOrderPoService.getById(id))
                .orElseThrow(() -> new BusinessException("DATA_NOT_EXITS"));
        B2bOrderBaseInfoDto b2bOrderBaseInfoDto = b2bReceiptOrderRepository.getB2bOrderBaseInfoDto(b2bReceiptOrderPO.getB2bOrderNo());
        // 付款条款名称
        String paymentTermsName = paymentTermsConfigService.getPaymentTermsNameByCode(b2bOrderBaseInfoDto.getPaymentTermsCode());
        return b2bReceiptOrderConvertor.poToConfirmReceiptOrderDetailVO(b2bReceiptOrderPO, b2bOrderBaseInfoDto, paymentTermsName);
    }


    /**
     * 校验添加的收款单
     *
     * @param b2bOrderBaseInfoDto
     */
    private void validateAddReceipt(B2bOrderBaseInfoDto b2bOrderBaseInfoDto) {
        if (ObjectUtil.isNull(b2bOrderBaseInfoDto)) {
            throw new BusinessException(I18nMessageKit.getMessage("ORDER_NOT_EXISTS"));
        }
        if (!canAddReceiptOrderStatus.contains(b2bOrderBaseInfoDto.getOrderStatus())) {
            throw new BusinessException(I18nMessageKit.getMessage("ADD_RECEIPT_ORDER_B2B_STATUS_CHECK"));
        }
    }


    /**
     * 获取银行账号
     *
     * @param salesCompanyId
     * @param currency
     * @return
     */
    private Optional<FundAccountClientCurrencyVO> getBankAccount(Long salesCompanyId, String currency) {
        if (salesCompanyId == null || StrUtil.isEmpty(currency)) {
            return Optional.empty();
        }
        //查询b2b收款账号
        List<CompanyVo> companyVoList = companyClient.getByIds(Arrays.asList(salesCompanyId.intValue()));
        if (CollUtil.isEmpty(companyVoList) || companyVoList.get(0).getB2BFmsFundAccountId() == null) {
            return Optional.empty();
        }
        Integer fundAccountId = companyVoList.get(0).getB2BFmsFundAccountId();
        //调用FMS获取银行账号币种
        FundAccountClientQuery fundAccountClientQuery = new FundAccountClientQuery();
        fundAccountClientQuery.setFundAccountId(fundAccountId);
        fundAccountClientQuery.setCurrency(currency);
        List<FundAccountClientCurrencyVO> fundAccountCurrencyList = fmsFundAccountClient.getFundAccountCurrency(fundAccountClientQuery);
        if (CollUtil.isEmpty(fundAccountCurrencyList)) {
            return Optional.empty();
        }
        return Optional.of(fundAccountCurrencyList.get(0));
    }
}
