package com.renpho.erp.oms.domain.vc.order.entity;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.enums.RejectReason;
import com.renpho.erp.oms.domain.vc.order.valueobject.SubmitAcknowledgmentItem;
import com.renpho.erp.oms.domain.vc.order.valueobject.VcOrderItemAmount;
import com.renpho.erp.oms.domain.vc.order.valueobject.VcProduct;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单商品实体
 * <AUTHOR>
 */
@Slf4j
@Getter
@Builder(toBuilder = true)
@Entity
public class VcOrderItem {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 渠道订单行号
	 */
	private String channelOrderLineNo;

	/**
	 * 销售SKU
	 */
	private String msku;

	/**
	 * ASIN
	 */
	private String asin;

	/**
	 * 商品信息
	 */
	private VcProduct product;

	/**
	 * 下单数量
	 */
	private Integer orderedQuantity;

	/**
	 * 接受数量
	 */
	private Integer acceptedQuantity;

	/**
	 * 拒绝数量
	 */
	private Integer rejectedQuantity;

	/**
	 * 拒绝原因
	 */
	private RejectReason rejectionReason;

	/**
	 * 发货数量
	 */
	private Integer shippedQuantity;

	/**
	 * 接收数量
	 */
	private Integer receivedQuantity;

	/**
	 * 商品金额信息
	 */
	private VcOrderItemAmount amount;

	/**
	 * 生成id
	 * @param orderId 订单id
	 */
	public void generateId(Long orderId) {
		this.id = IdWorker.getId();
		this.orderId = orderId;
	}

	public void submitAcknowledgmentCheck(SubmitAcknowledgmentItem submitAcknowledgmentItem) {
		// 接单数 + 拒单数 == 下单数
		if (!Objects.equals(submitAcknowledgmentItem.getAcceptedQuantity() + submitAcknowledgmentItem.getRejectedQuantity(),
				this.orderedQuantity)) {
			throw DomainException.of("接单数+拒单数必须等于下单数");
		}
		// 该订单行被据了要有理由
		if (submitAcknowledgmentItem.getAcceptedQuantity() == 0 && submitAcknowledgmentItem.getRejectSon() == null) {
			throw DomainException.of("拒单需要选择理由");
		}
	}

	public void submitAcknowledgment(SubmitAcknowledgmentItem submitAcknowledgmentItem) {
		this.acceptedQuantity = submitAcknowledgmentItem.getAcceptedQuantity();
		this.rejectedQuantity = submitAcknowledgmentItem.getRejectedQuantity();
		this.rejectionReason = submitAcknowledgmentItem.getRejectSon();
	}

	/**
	 * 关联产品信息 符合DDD规范的领域方法
	 * @param product 产品信息
	 */
	public void associateProduct(VcProduct product) {
		this.product = product;
		log.info("VC订单商品关联产品信息成功，商品ID：{}，ASIN：{}，PSKU：{}", this.id, this.asin, product.getPsku());
	}

	/**
	 * 计算金额
	 * @param notAccepted 是否未接单
	 */
	public void calculateAmount(boolean notAccepted) {
		// 未接单
		if (notAccepted) {
			// 取下单数量
			amount.calculateAmount(this.orderedQuantity);
		}
		// 已接单
		else {
			// 取接受数量
			amount.calculateAmount(this.acceptedQuantity);
		}
	}

	public boolean isAccept() {
		return this.acceptedQuantity != null && this.acceptedQuantity > 0;
	}

	/**
	 * 摊分税金额
	 * @param tax 摊分到的税金额
	 */
	public void invoiceTax(BigDecimal tax) {
		amount.invoiceTax(this.acceptedQuantity, tax);
	}

	/**
	 * 获取订单行的币种
	 * @return String
	 */
	public String getCurrency() {
		return Optional.ofNullable(this.amount)
			.map(VcOrderItemAmount::getCurrency)
			.orElseThrow(() -> DomainException.of("currency is null"));
	}

	/**
	 * 更新接受数量
	 * @param acceptedQuantityMap 转换订单商品行-接受数量Map（key为orderItemId,value为acceptedQuantity）
	 */
	public void modifyAcceptedQuantity(Map<Long, Integer> acceptedQuantityMap) {
		// 接受数量
		this.acceptedQuantity = acceptedQuantityMap.get(this.getId());
	}
}
