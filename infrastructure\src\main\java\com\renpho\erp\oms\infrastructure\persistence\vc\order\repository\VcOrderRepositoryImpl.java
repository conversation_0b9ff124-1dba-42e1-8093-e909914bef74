package com.renpho.erp.oms.infrastructure.persistence.vc.order.repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderRemarkCmd;
import com.renpho.erp.oms.domain.vc.order.model.*;
import com.renpho.erp.oms.infrastructure.convert.vc.order.VcOrderPOConvertor;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.*;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.*;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.service.VcOrderItemService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单仓储实现
 *
 * <AUTHOR>
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class VcOrderRepositoryImpl implements VcOrderRepository {

	private final VcOrderMapper vcOrderMapper;
	private final VcOrderItemService vcOrderItemService;
	private final VcOrderItemMapper vcOrderItemMapper;
	private final VcOrderInvoiceMapper vcOrderInvoiceMapper;
	private final VcOrderDiMapper vcOrderDiMapper;
	private final VcOrderRemarkMapper vcOrderRemarkMapper;
	private final VcOrderItemLabelMapper vcOrderItemLabelMapper;
	private final VcOrderPOConvertor vcOrderConverter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void save(VcOrderAggRoot aggRoot) {
		// 生成id和订单号
		aggRoot.generateIdAndOrderNo();
		// 订单
		VcOrderPO order = vcOrderConverter.toPo(aggRoot);
		vcOrderMapper.insert(order);
		// 订单商品
		List<VcOrderItemPO> items = vcOrderConverter.toItemPos(aggRoot.getItems(), aggRoot.getId());
		vcOrderItemService.saveBatch(items);
		// 订单DI信息（如果是直接进口订单）
		if (Objects.nonNull(aggRoot.getDi())) {
			VcOrderDiPO di = vcOrderConverter.toDiPo(aggRoot.getDi());
			vcOrderDiMapper.insert(di);
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void update(VcOrderAggRoot aggRoot) {
		// 订单
		VcOrderPO order = vcOrderConverter.toPo(aggRoot);
		vcOrderMapper.updateById(order);
		// 订单商品
		List<VcOrderItemPO> items = vcOrderConverter.toItemPos(aggRoot.getItems(), aggRoot.getId());
		vcOrderItemService.updateBatchById(items);
	}

	@Override
	public VcOrderAggRoot findById(Long id) {
		VcOrderPO order = vcOrderMapper.selectById(id);
		if (Objects.isNull(order)) {
			throw DomainException.ofKey("VC_ORDER_NOT_FOUND");
		}
		// 查询聚合根信息
		return this.findAggRoot(order);
	}

	/**
	 * 查询聚合根信息
	 *
	 * @param order 订单
	 * @return VcOrderAggRoot
	 */
	private @Nullable VcOrderAggRoot findAggRoot(VcOrderPO order) {
		// 店铺
		VcStore vcStore = VcStore.builder()
			.storeName(order.getStoreName())
			.storeId(order.getStoreId())
			.siteCode(order.getSiteCode())
			.build();
		// 订单商品
		List<VcOrderItemPO> items = vcOrderItemMapper
			.selectList(new LambdaQueryWrapper<VcOrderItemPO>().eq(VcOrderItemPO::getOrderId, order.getId()));
		// 订单发票
		VcOrderInvoicePO invoice = vcOrderInvoiceMapper
			.selectOne(new LambdaQueryWrapper<VcOrderInvoicePO>().eq(VcOrderInvoicePO::getOrderId, order.getId()));
		// 订单DI
		VcOrderDiPO di = vcOrderDiMapper.selectOne(new LambdaQueryWrapper<VcOrderDiPO>().eq(VcOrderDiPO::getOrderId, order.getId()));
		// 订单备注
		List<VcOrderRemarkPO> remarks = vcOrderRemarkMapper
			.selectList(new LambdaQueryWrapper<VcOrderRemarkPO>().eq(VcOrderRemarkPO::getOrderId, order.getId()));
		// 订单箱唛
		List<VcOrderItemLabelPO> itemLabels = vcOrderItemLabelMapper
			.selectList(new LambdaQueryWrapper<VcOrderItemLabelPO>().eq(VcOrderItemLabelPO::getOrderId, order.getId()));
		// 构建转换DTO
		VcOrderPOConvertor.ConverterDTO converterDTO = VcOrderPOConvertor.ConverterDTO.builder()
			.store(vcStore)
			.items(items)
			.invoice(invoice)
			.di(di)
			.remarks(remarks)
			.itemLabels(itemLabels)
			.build();
		return vcOrderConverter.toAggRoot(order, converterDTO);
	}

	@Override
	public Optional<VcOrderAggRoot> findByOrderNo(String orderNo) {
		return Optional.empty();
	}

	@Override
	public List<VcOrderAggRoot> findByStoreId(Integer storeId) {
		return List.of();
	}

	@Override
	public void deleteById(Long id) {

	}

	@Override
	public void saveOrderItemLabels(VcOrderAggRoot order) {
		// 1. 更新订单状态
		VcOrderPO orderPo = new VcOrderPO();
		orderPo.setId(order.getId());
		orderPo.setOrderStatus(order.getOrderStatus().getValue());
		vcOrderMapper.updateById(orderPo);

		// 2. 保存箱唛信息
		for (VcOrderItemLabel label : order.getItemLabels()) {
			VcOrderItemLabelPO itemLabelPo = vcOrderConverter.toVcOrderItemLabelPo(label);
			// 假设有 insertOrUpdate 方法
			vcOrderItemLabelMapper.insert(itemLabelPo);
		}
	}

	@Override
	public Long findIdByVcPurchaseNoAndStoreId(String vcPurchaseNo, Integer storeId) {
		LambdaQueryWrapper<VcOrderPO> qw = new LambdaQueryWrapper<VcOrderPO>().select(VcOrderPO::getId)
			.eq(VcOrderPO::getVcPurchaseNo, vcPurchaseNo)
			.eq(VcOrderPO::getStoreId, storeId)
			.last("limit 1");
		VcOrderPO order = vcOrderMapper.selectOne(qw);
		return Optional.ofNullable(order).map(VcOrderPO::getId).orElse(null);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
	public void submitAcknowledgement(VcOrderAggRoot vcOrderAggRoot) {
		VcOrderPO vcOrderForUpdate = new VcOrderPO();
		vcOrderForUpdate.setId(vcOrderAggRoot.getId());
		vcOrderForUpdate.setAcceptedMethod(vcOrderAggRoot.getAcceptedMethod().getValue());
		vcOrderForUpdate.setAcceptedStatus(vcOrderAggRoot.getAcceptedStatus().getValue());
		vcOrderForUpdate.setAcceptedTime(vcOrderAggRoot.getAcceptedTime());
		vcOrderMapper.updateById(vcOrderForUpdate);

		for (VcOrderItem item : vcOrderAggRoot.getSaveOrUpdateItems()) {
			VcOrderItemPO itemForUpdate = new VcOrderItemPO();
			itemForUpdate.setId(item.getId());
			itemForUpdate.setAcceptedQuantity(item.getAcceptedQuantity());
			itemForUpdate.setRejectedQuantity(item.getRejectedQuantity());
			itemForUpdate.setRejectionReason(Optional.ofNullable(item.getRejectionReason()).map(RejectReason::getValue).orElse(null));
			vcOrderItemMapper.updateById(itemForUpdate);
		}

	}

	@Override
	@Transactional
	public void saveSkuParseResult(VcOrderAggRoot order) {
		if (order == null) {
			log.debug("订单聚合根为空，无法保存SKU解析结果");
			return;
		}

		if (order.getOrderNo() == null || order.getId() == null) {
			log.debug("订单号或订单ID为空，无法保存SKU解析结果，订单号：{}，订单ID：{}", order.getOrderNo(), order.getId());
			return;
		}

		try {
			// 1. 更新订单表的 parse_status 和 exception_reason 字段
			LambdaUpdateWrapper<VcOrderPO> orderUpdateWrapper = new LambdaUpdateWrapper<>();
			orderUpdateWrapper.eq(VcOrderPO::getOrderNo, order.getOrderNo())
				.set(VcOrderPO::getParseStatus, order.getParseStatus() != null ? order.getParseStatus().getValue() : null)
				.set(VcOrderPO::getExceptionReason, order.getExceptionReason())
				.set(VcOrderPO::getUpdateTime, LocalDateTime.now());
			int orderUpdateCount = vcOrderMapper.update(null, orderUpdateWrapper);

			if (orderUpdateCount == 0) {
				log.debug("更新VC订单表失败，订单号：{}，请检查订单是否存在", order.getOrderNo());
			}
			else {
				log.debug("更新VC订单表成功，订单号：{}，解析状态：{}", order.getOrderNo(), order.getParseStatus());
			}

			// 2. 批量更新订单商品表的 product 相关字段（psku, barcode, numberOfUnitsPerBox, imageId）
			int itemUpdateCount = vcOrderItemService.batchUpdateSkuParseResultWithMapper(order.getId(), order.getItems());

			log.debug("保存VC订单SKU解析结果完成，订单号：{}，解析状态：{}，更新商品数：{}", order.getOrderNo(), order.getParseStatus(), itemUpdateCount);
		}
		catch (Exception e) {
			log.error("保存VC订单SKU解析结果异常，订单号：{}", order.getOrderNo(), e);
			throw e; // 重新抛出异常，让事务回滚
		}
	}

	@Override
	public void updateOrderStatus(VcOrderAggRoot order) {
		// 更新订单状态
		VcOrderPO orderPo = new VcOrderPO();
		orderPo.setId(order.getId());
		orderPo.setOrderStatus(order.getOrderStatus().getValue());
		vcOrderMapper.updateById(orderPo);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
	public void remark(VcOrderRemarkCmd cmd) {
		// 1. 更新订单主表的备注字段
		VcOrderPO orderPoForUpdate = new VcOrderPO();
		orderPoForUpdate.setId(cmd.getOrderId());
		orderPoForUpdate.setRemark(cmd.getRemark());
		vcOrderMapper.updateById(orderPoForUpdate);

		// 2. 在备注历史表中插入一条新记录
		VcOrderRemarkPO remarkPO = new VcOrderRemarkPO();
		remarkPO.setOrderId(cmd.getOrderId());
		remarkPO.setRemark(cmd.getRemark());
		vcOrderRemarkMapper.insert(remarkPO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED, propagation = Propagation.REQUIRED)
	public void invoice(VcOrderAggRoot vcOrderAggRoot) {
		VcOrderPO vcOrderPO = new VcOrderPO();
		vcOrderPO.setId(vcOrderAggRoot.getId());
		vcOrderPO.setOrderStatus(vcOrderAggRoot.getOrderStatus().getValue());
		vcOrderPO.setInvoicedTime(vcOrderAggRoot.getInvoicedTime());
		vcOrderMapper.updateById(vcOrderPO);

		VcOrderInvoice invoice = vcOrderAggRoot.getInvoice();
		if (invoice.getId() == null) {
			VcOrderInvoicePO invoicePO = vcOrderConverter.toInvoicePO(invoice);
			vcOrderInvoiceMapper.insert(invoicePO);
		}
		else {
			VcOrderInvoicePO invoicePO = vcOrderConverter.toInvoicePO(invoice);
			vcOrderInvoiceMapper.updateById(invoicePO);
		}

		for (VcOrderItem item : vcOrderAggRoot.getItems()) {
			// 只有接单的才需要开发票
			if (!item.isAccept()) {
				continue;
			}
			VcOrderItemPO itemPO = new VcOrderItemPO();
			itemPO.setId(item.getId());
			itemPO.setTax(item.getAmount().getTax());
			itemPO.setSubTotal(item.getAmount().getSubTotal());
			vcOrderItemMapper.updateById(itemPO);
		}
	}
}