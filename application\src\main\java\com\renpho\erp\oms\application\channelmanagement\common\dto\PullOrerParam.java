package com.renpho.erp.oms.application.channelmanagement.common.dto;


import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class PullOrerParam {
	/**
	 * 卖家id
	 */
	private List<Integer> storeIds;

	/**
	 * sellerId 暂时只给亚马逊使用
	 */
	private List<String> sellerIds;

	/**
	 * 订单id
	 */
	private List<String> orderIds;

	/**
	 * 最后更新时间之后
	 */
	private String lastUpdateTimeAfterStr;

	/**
	 * 最后更新时间之前
	 */
	private String lastUpdateTimeBeforeStr;

	public LocalDateTime getStartDateUTC() {
		if (StringUtils.isEmpty(this.lastUpdateTimeAfterStr)) {
			return null;
		}
		return DateUtil.convertStrToUTC(this.lastUpdateTimeAfterStr);
	}

	public LocalDateTime getEndDateUTC() {
		if (StringUtils.isEmpty(this.lastUpdateTimeBeforeStr)) {
			return null;
		}
		return DateUtil.convertStrToUTC(this.lastUpdateTimeBeforeStr);
	}

}
