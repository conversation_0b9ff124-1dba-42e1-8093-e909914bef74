package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.eccang.yunwms.model.fees.GetCalculateFeeBatchRequest;
import com.renpho.erp.apiproxy.eccang.yunwms.model.fees.GetCalculateFeeBatchResponse;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrder;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.dto.WarehouseConfigDto;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.EccangClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodeLanguagePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceCodeLanguageService;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceConfigService;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import lombok.RequiredArgsConstructor;

/**
 * @desc: 易仓查询发货方式报价
 * @time: 2025-05-19 16:53:15
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class EccangShippingServiceStrategy extends AbstractShippingServiceStrategy {

	private final EccangClient eccangClient;
	private final WarehouseClient warehouseClient;

	private final SaleOrderQueryService saleOrderQueryService;
	private final FulfillmentServiceConfigService fulfillmentServiceConfigService;
	private final FulfillmentServiceCodeLanguageService fulfillmentServiceCodeLanguageService;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.YPL_WAREHOUSE, FulfillmentServiceType.KING_SPARK_WAREHOUSE);
	}

	@Override
	public List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query) {
		List<ShippingServicesVO> shippingServicesVOList = new ArrayList<>();
		// 查询ims客户编码
		WarehouseConfigDto warehouseConfig = warehouseClient.getWarehouseConfig(query.getWarehouseId());
		if (warehouseConfig == null) {
			throw new BusinessException(I18nMessageKit.getMessage("WAREHOUSE_CUSTOMER_CODE_CONFIG_NOT_FOUND"));
		}
		// 查询发货方式配置表
		List<FulfillmentServiceCodePO> fulfillmentServiceCodePoList = fulfillmentServiceConfigService
			.getByCondition(query.getFulfillmentServiceType(), query.getWarehouseId());
		// 获取语言
		String language = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");
		Set<Long> codeIdSet = fulfillmentServiceCodePoList.stream().map(FulfillmentServiceCodePO::getId).collect(Collectors.toSet());
		// 查询多语言表
		Map<Long, FulfillmentServiceCodeLanguagePO> codeLanguageMap = fulfillmentServiceCodeLanguageService.getByCodeIdSet(codeIdSet,
				language);
		// 构建获取运费计算单请求
		GetCalculateFeeBatchRequest getCalculateFeeBatchRequest = buildGetCalculateFeeBatchRequest(query, fulfillmentServiceCodePoList);
		R<GetCalculateFeeBatchResponse> r = eccangClient.getCalculateFeeBatch(warehouseConfig.getCustomerCode(),
				getCalculateFeeBatchRequest);
		if (!r.isSuccess()) {
			throw new BusinessException(r.getMessage());
		}
		// 构建响应
		Map<String, GetCalculateFeeBatchResponse.Fee> feeMap = r.getData()
			.getData()
			.stream()
			.collect(Collectors.toMap(GetCalculateFeeBatchResponse.Fee::getShipping_method, Function.identity(), (k1, k2) -> k2));
		for (FulfillmentServiceCodePO fulfillmentServiceCodePo : fulfillmentServiceCodePoList) {
			ShippingServicesVO shippingServices = new ShippingServicesVO();
			shippingServices.setFulfillmentServiceId(fulfillmentServiceCodePo.getId());
			shippingServices.setCarrier(fulfillmentServiceCodePo.getCarrierName());
			shippingServices.setCarrierServiceCode(fulfillmentServiceCodePo.getCarrierServiceCode());
			shippingServices.setCarrierService(Optional.ofNullable(codeLanguageMap.get(fulfillmentServiceCodePo.getId()))
				.map(FulfillmentServiceCodeLanguagePO::getName)
				.orElse(""));
			if (feeMap.containsKey(fulfillmentServiceCodePo.getCarrierServiceCode())) {
				GetCalculateFeeBatchResponse.Fee fee = feeMap.get(fulfillmentServiceCodePo.getCarrierServiceCode());
				shippingServices.setShippingTime(fee.getInvalid() + "days");
				shippingServices.setCurrency(fee.getCurrency_code());
				// 运费
				String totalFee = fee.getTotalFee();
				if (StringUtils.isNotBlank(totalFee)) {
					shippingServices.setShippingCost(fee.getCurrency_code() + " " + new BigDecimal(fee.getTotalFee()).setScale(2, RoundingMode.HALF_UP));
					shippingServices.setAmount(new BigDecimal(totalFee));
				}

			}
			shippingServicesVOList.add(shippingServices);
		}
		return shippingServicesVOList.stream()
			.sorted(Comparator.comparing(ShippingServicesVO::getAmount, Comparator.nullsLast(Comparator.naturalOrder())))
			.collect(Collectors.toList());
	}

	/**
	 * 构建获取运费计算单请求
	 * @param query 参数
	 * @return GetCalculateFeeBatchRequest 请求
	 */
	private GetCalculateFeeBatchRequest buildGetCalculateFeeBatchRequest(ShippingServicesQuery query,
			List<FulfillmentServiceCodePO> fulfillmentServiceCodePoList) {

		// 查询仓库信息
		WarehouseVo warehouseVo = warehouseClient.getById(Set.of(query.getWarehouseId())).get(query.getWarehouseId());
		// 承运商服务编码列表
		List<String> serviceCodeList = fulfillmentServiceCodePoList.stream()
			.map(FulfillmentServiceCodePO::getCarrierServiceCode)
			.filter(StringUtils::isNotEmpty)
			.toList();
		if (CollectionUtils.isEmpty(serviceCodeList)) {
			throw new BusinessException(I18nMessageKit.getMessage("FULFILLMENT_SERVICE_CODE_NOT_FOUND"));
		}
		// 查询敏感地址信息（从mpds）
		SaleOrder saleOrder = saleOrderRepository.getById(query.getOrderId());
		SaleOrderAddressVO receivingAddress = saleOrderQueryService.getReceivingAddress(saleOrder.getOrderNo(), false, false);
		// 构建请求
		GetCalculateFeeBatchRequest getCalculateFeeBatchRequest = new GetCalculateFeeBatchRequest();
		getCalculateFeeBatchRequest.setWarehouse_code(warehouseVo.getThirdWarehouseCode());
		getCalculateFeeBatchRequest.setCountry_code(receivingAddress.getCountryCode());
		getCalculateFeeBatchRequest.setShipping_method(serviceCodeList);
		getCalculateFeeBatchRequest.setWeight(String.valueOf(query.getWeight()));
		getCalculateFeeBatchRequest.setLength(String.valueOf(query.getLength()));
		getCalculateFeeBatchRequest.setWidth(String.valueOf(query.getWidth()));
		getCalculateFeeBatchRequest.setHeight(String.valueOf(query.getHeight()));
		getCalculateFeeBatchRequest.setState(receivingAddress.getProvince());
		getCalculateFeeBatchRequest.setAddress1(receivingAddress.getAddress1());
		getCalculateFeeBatchRequest.setPostcode(receivingAddress.getPostalCode());
		return getCalculateFeeBatchRequest;
	}
}
