package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.renpho.erp.oms.infrastructure.persistence.amazon.mapper.AmzSellerMtpInfoMapper;
import com.renpho.erp.oms.infrastructure.persistence.amazon.po.AmzSellerMtpInfoPO;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AmzSellerMtpInfoQueryService {

	private final AmzSellerMtpInfoMapper amzSellerMtpInfoMapper;

	/**
	 * 卖家参与市场信息列表
	 * @return List
	 */
	public List<AmzSellerMtpInfoPO> list() {
		return amzSellerMtpInfoMapper.selectList(null);
	}
}
