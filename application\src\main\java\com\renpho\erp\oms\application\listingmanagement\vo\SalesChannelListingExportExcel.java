package com.renpho.erp.oms.application.listingmanagement.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SalesChannelListingExportExcel {

	/**
	 * item编码
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.ITEM")
	private String item;

	/**
	 * 销售SKU
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.MSKU")
	private String msku;

	/**
	 * 采购SKU
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.PSKU")
	private String psku;

	/**
	 * 标题
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.TITLE")
	private String title;

	/**
	 * Item link
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.LINK")
	private String link;

	/**
	 * Item状态
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.STATUS")
	private String status;

	/**
	 * 店铺名称
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.STORE")
	private String storeName;

	/**
	 * 销售渠道名称
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.CHANNEL")
	private String salesChannelName;

	/**
	 * 币种
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.CURRENCY")
	private String currency;

	/**
	 * 价格
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.PRICE")
	private BigDecimal price;

	// /**
	// * 币种
	// */
	// @ExcelProperty(value = "SKU_MAPPING_EXCEL.STORE")
	// private String currency;

	/**
	 * 发布时间
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.PUBLISHEDTIME")
	private LocalDateTime publishedTime;

	/**
	 * Item平台更新时间
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.ITEMUPDATEDTIME")
	private LocalDateTime itemUpdatedTime;

	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "SALES_CHANNEL_LISTING_EXPORT_EXCEL.CREATETIME")
	private LocalDateTime updateTime;

}
