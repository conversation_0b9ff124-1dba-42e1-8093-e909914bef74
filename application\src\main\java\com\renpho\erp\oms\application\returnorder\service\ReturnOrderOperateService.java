package com.renpho.erp.oms.application.returnorder.service;

import java.util.List;
import java.util.stream.Collectors;

import com.renpho.erp.oms.application.returnorder.command.*;
import com.renpho.erp.oms.domain.salemanagement.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.renpho.erp.oms.application.returnorder.converter.ResendOrderAddConvertor;
import com.renpho.erp.oms.application.returnorder.converter.ReturnOrderVoConvertor;
import com.renpho.erp.oms.domain.returnorder.model.ReturnOrderAggRoot;
import com.renpho.erp.oms.domain.returnorder.repository.ReturnOrderRemarkRepository;
import com.renpho.erp.oms.domain.returnorder.repository.ReturnOrderRepository;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: 退货订单操作服务
 * @time: 2025-03-18 15:15:04
 * @author: Alina
 */
@Slf4j
@Component
@AllArgsConstructor
public class ReturnOrderOperateService {

	private final ReturnOrderRepository returnOrderRepository;
	private final ReturnOrderVoConvertor returnOrderVoConvertor;
	private final SaleOrderRepository saleOrderRepository;
	private final ReturnOrderRemarkRepository returnOrderRemarkRepository;

	/**
	 * 新建RAM退货订单
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@Transactional(rollbackFor = Exception.class)
	public R<Long> addReturnOrder(ReturnOrderAddCmd cmd) {
		cmd.setItems(cmd.getItems().stream().filter(item -> item.getReturnQuantity() != null && item.getReturnQuantity() > 0).toList());
		List<Long> orderItemIds = cmd.getItems().stream().map(ReturnOrderItemAddCmd::getOrderItemId).collect(Collectors.toList());
		// 根据店铺单号，销售订单明细id 获取聚合根信息
		SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findByChannelOrderNoAndItemIds(cmd.getStoreId(), cmd.getChannelOrderNo(),orderItemIds);
		// 转换为领域模型
		ReturnOrderAggRoot returnOrderAggRoot = returnOrderVoConvertor.toDomain(cmd);
		// 封装退货订单 (在聚合根中完成逻辑处理)
		returnOrderAggRoot.initializeFromSaleOrder(saleOrderAggRoot);
		// 保存退货订单以及明细
		returnOrderRepository.save(returnOrderAggRoot);
		return R.success();
	}

	/**
	 * 新建RAM补发订单
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@Transactional(rollbackFor = Exception.class)
	public R<Long> addResendOrder(ResendOrderAddCmd cmd) {
		cmd.setItems(cmd.getItems().stream().filter(item -> item.getResendQuantity() != null && item.getResendQuantity() > 0).toList());
		List<Long> orderItemIds = cmd.getItems().stream().map(ResendOrderItemAddCmd::getOrderItemId).collect(Collectors.toList());
		// 根据店铺单号，销售订单明细id 获取聚合根信息
		SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findByChannelOrderNoAndItemIds(cmd.getStoreId(), cmd.getChannelOrderNo(),
				orderItemIds);
		// 组建新的补货订单信息
		SaleOrderAggRoot newSaleOrderAggRoot = ResendOrderAddConvertor.convert(cmd, saleOrderAggRoot);
		// 转换入库
		saleOrderRepository.save(newSaleOrderAggRoot);
		return R.success();
	}

	/**
	 * 手动收货
	 *
	 * @param cmd 参数
	 */
	public void manualReceive(ManualReceiveCmd cmd) {
		// 从仓储中加载 ReturnOrder 聚合根
		ReturnOrderAggRoot returnOrderAggRoot = returnOrderRepository.findById(ReturnOrderAggRoot.OrderId.of(cmd.getReturnOrderId()))
			.orElseThrow(() -> new BusinessException("RETURN_ORDER_NOT_FOUND"));
		// 调用领域对象的方法更新收货信息
		returnOrderAggRoot.updateReceiveInfo(cmd.getCarrierName(), cmd.getTrackingNo(), cmd.getReceiveTime());
		// 持久化聚合根状态
		returnOrderRepository.updateReceiveInfo(returnOrderAggRoot);
	}

	/**
	 * 退货订单取消
	 *
	 * @param cmd 参数
	 */
	@Transactional(rollbackFor = Exception.class)
	public void cancel(CancelReturnOrderCmd cmd) {
		// 从仓储中加载 ReturnOrder 聚合根
		ReturnOrderAggRoot returnOrderAggRoot = returnOrderRepository.findById(ReturnOrderAggRoot.OrderId.of(cmd.getReturnOrderId()))
			.orElseThrow(() -> new BusinessException(I18nMessageKit.getMessage("RETURN_ORDER_NOT_FOUND")));
		// 领域层-取消
		returnOrderAggRoot.cancel(cmd.getCancelReason());
		// 仓储层-取消
		returnOrderRepository.updateCancel(returnOrderAggRoot);
	}

	/**
	 * 退货订单备注
	 *
	 * @param cmd 参数
	 */
	public void remark(RemarkReturnOrderCmd cmd) {
		returnOrderRemarkRepository.remark(cmd.getReturnOrderId(), cmd.getRemark(), OrderRemarkType.NORMAL);
	}
}
