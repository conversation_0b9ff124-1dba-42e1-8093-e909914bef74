package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

/**
 * 客户发票信息视图对象
 */
@Data
public class CustomerInvoiceVO {
    /**
     * ID
     */
    private Long id;

    /**
     * 收件人
     */
    private String attnTo;

    /**
     * 发票公司名称
     */
    private String invoiceCompany;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮编
     */
    private String zipCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 国家
     */
    private String country;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 增值税号
     */
    private String vatNumber;
} 