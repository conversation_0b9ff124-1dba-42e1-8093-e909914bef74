package com.renpho.erp.oms.application.ordershipaudit.channel.amz;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 亚马逊多渠道发货excel模板
 *
 * <AUTHOR>
 * @since 2025/2/28
 */
@Data
public class AmzFbaOrderShipExcelDto {

    @NotBlank(message = "{ORDER_SHIP_AUDIT_STORE_NAME}")
    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String storeName;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_ORDER_NO}")
    @ExcelHeadName(zhCnName = "销售单号", enName = "SO#")
    private String orderNo = "";
    @NotBlank(message = "{ORDER_SHIP_AUDIT_AMZ_STORE_NAME}")
    @ExcelHeadName(zhCnName = "亚马逊发货店铺", enName = "Amazon Shipment Shop")
    private String amazonStoreName;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_AMZ_SHIP_SPEED}")
    @ExcelHeadName(zhCnName = "运输速度", enName = "Shipping Speed")
    private String shipSpeedCategory;
    @ExcelHeadName(zhCnName = "通知邮箱", enName = "Notification Email")
    private String email;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_AMZ_PACKING_REMARK}")
    @ExcelHeadName(zhCnName = "装箱备注", enName = "Packing Remarks")
    private String remark;
    @ExcelHeadName(zhCnName = "只发白盒(是/否)", enName = "Blank Box(Yes/No)")
    private String blankBox;
    @ExcelHeadName(zhCnName = "不使用AMZL(是/否)", enName = "Block AMZL(Yes/No)")
    private String blockAmzl;
    @ExcelHeadName(zhCnName = "交货期间-开始时间", enName = "Delivery Start Time")
    private String deliveryStartTime;
    @ExcelHeadName(zhCnName = "交货期间-结束时间", enName = "Delivery End Time")
    private String deliveryEndTime;
    @ExcelHeadName(zhCnName = "交货说明", enName = "Delivery Instructions")
    private String deliveryInstruction;
    @ExcelHeadName(zhCnName = "邻居名字", enName = "Neighbour's Name")
    private String neighborName;
    @ExcelHeadName(zhCnName = "邻居房号", enName = "House Number")
    private String neighborRoom;
    @ExcelHeadName(zhCnName = "订单MSKU", enName = "Order MSKU")
    private String msku;
    @ExcelHeadName(zhCnName = "订单PSKU", enName = "Order PSKU")
    private String psku;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_AMZ_MSKU}")
    @ExcelHeadName(zhCnName = "亚马逊MSKU", enName = "Amazon MSKU")
    private String channelMsku;

    @ExcelHeadName(zhCnName = "错误信息", enName = "error info")
    private String errorInfo;

    @ExcelIgnore
    private List<String> errorInfoList = new ArrayList<>();

    public void addErrorInfo(String errorInfo) {
        this.errorInfoList.add(errorInfo);
    }

    public void buildErrorInfo() {
        errorInfo = String.join("\n", errorInfoList);
    }

    @AssertTrue(message = "{ORDER_SHIP_AUDIT_AMZ_SHIP_SPEED}")
    public boolean isValidShipSpeedCategory() {
        return StrUtil.equalsAny(shipSpeedCategory, "Standard", "Expedited", "Priority");
    }

    @AssertTrue(message = "{ORDER_SHIP_AUDIT_AMZ_BLANK_BOX}")
    public boolean isValidBlankBox() {
        if (StrUtil.isBlank(blankBox)) {
            return true;
        }
        return StrUtil.equalsAnyIgnoreCase(blankBox, "是", "否", "yes", "no");
    }

    @AssertTrue(message = "{ORDER_SHIP_AUDIT_AMZ_BLOCK_AMZL}")
    public boolean isValidBlockAmzl() {
        if (StrUtil.isBlank(blockAmzl)) {
            return true;
        }
        return StrUtil.equalsAnyIgnoreCase(blockAmzl, "是", "否", "yes", "no");
    }

    public String amzConfigKey() {
        return String.join("_", amazonStoreName, shipSpeedCategory, email, remark, blankBox, blockAmzl);
    }
}

