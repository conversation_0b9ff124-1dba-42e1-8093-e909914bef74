package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command;

import java.util.List;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class MulSkuMappingAddCmd {
	/**
	 * 订单店铺Id
	 */
	@NotNull(message = "{STORE_EMPTY}")
	private Integer orderStoreId;

	/**
	 * 订单渠道Id
	 */
	@NotNull(message = "{CHANNLE_EMPTY}")
	private Integer orderChannelId;

	/**
	 * 订单psku
	 */
	@NotNull(message = "{MUL_SKU_ORDER_PSKU_EMPTY}")
	private String orderPsku;

	/**
	 * 映射行
	 */
	private List<MulSkuMappingLineAddCmd> mulSkuMappingLineAddCmdList;


	@Data
	public static class MulSkuMappingLineAddCmd {
		/**
		 * 多渠道msku
		 */
		private String mulChannelMsku;

		/**
		 * 多渠道类型
		 */
		private Integer mulChannelType;

		/**
		 * 多渠道店铺id
		 */
		private Integer mulChannelStoreId;

		/**
		 * 优先级
		 */
		private Integer priority;

	}

}
