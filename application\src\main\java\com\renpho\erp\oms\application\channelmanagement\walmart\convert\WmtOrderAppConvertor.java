package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrder;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.shenyu.common.utils.DateUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.Objects;

public class WmtOrderAppConvertor {
	public static WmtOrder toDomain(WmtOrderDTO wmtOrderDTO) {
		if (Objects.isNull(wmtOrderDTO)) {
			return null;
		}
		WmtOrder wmtOrder = new WmtOrder();
		wmtOrder.setStoreId(wmtOrderDTO.getStoreId());
		wmtOrder.setShopId(wmtOrderDTO.getShopId());
		wmtOrder.setPurchaseOrderId(wmtOrderDTO.getPurchaseOrderId());
		wmtOrder.setCustomerOrderId(wmtOrderDTO.getCustomerOrderId());
		wmtOrder.setCustomerEmailId(wmtOrderDTO.getCustomerEmailId());
		wmtOrder.setOrderType(wmtOrderDTO.getOrderType());
		wmtOrder.setOriginalCustomerOrderId(wmtOrderDTO.getOriginalCustomerOrderID());
		wmtOrder.setOrderDate(DateUtil.convertToDateTime(wmtOrderDTO.getOrderDate()));
		wmtOrder.setBuyerId(wmtOrderDTO.getBuyerId());
		wmtOrder.setMart(wmtOrderDTO.getMart());
		wmtOrder.setIsGuest(wmtOrderDTO.getIsGuest());
		// 发货信息
		if (Objects.nonNull(wmtOrderDTO.getShippingInfo())) {
			wmtOrder.setShippingInfoPhone(wmtOrderDTO.getShippingInfo().getPhone());
			wmtOrder
				.setShippingInfoEstimatedDeliveryDate(DateUtil.convertToDateTime(wmtOrderDTO.getShippingInfo().getEstimatedDeliveryDate()));
			wmtOrder.setShippingInfoEstimatedShipDate(DateUtil.convertToDateTime(wmtOrderDTO.getShippingInfo().getEstimatedShipDate()));
			wmtOrder.setShippingInfoMethodCode(wmtOrderDTO.getShippingInfo().getMethodCode());
			if (Objects.nonNull(wmtOrderDTO.getShippingInfo().getPostalAddress())) {
				WmtOrderDTO.PostalAddress postalAddress = wmtOrderDTO.getShippingInfo().getPostalAddress();
				wmtOrder.setPostalAddressCity(postalAddress.getCity());
				wmtOrder.setPostalAddressState(postalAddress.getState());
				wmtOrder.setPostalAddressPostalCode(postalAddress.getPostalCode());
				wmtOrder.setPostalAddressCountry(postalAddress.getCountry());
			}
		}
		if (Objects.nonNull(wmtOrderDTO.getPaymentTypes()) && wmtOrderDTO.getPaymentTypes().length > 0) {
			wmtOrder.setPaymentTypes(JsonUtils.toJson(wmtOrderDTO.getPaymentTypes()));
		}
		// 费用信息
		if (Objects.nonNull(wmtOrderDTO.getOrderSummary())) {
			if (Objects.nonNull(wmtOrderDTO.getOrderSummary().getTotalAmount())) {
				WmtOrderDTO.Money totalAmount = wmtOrderDTO.getOrderSummary().getTotalAmount();
				wmtOrder.setOrderTotalAmount(totalAmount.getCurrencyAmount());
				wmtOrder.setOrderTotalCurrencyCode(totalAmount.getCurrencyUnit());
			}
			if (CollectionUtil.isNotEmpty(wmtOrderDTO.getOrderSummary().getOrderSubTotals())) {
				wmtOrder.setOrderSubTotals(JsonUtils.toJson(wmtOrderDTO.getOrderSummary().getOrderSubTotals()));
			}
		}

		if (CollectionUtil.isNotEmpty(wmtOrderDTO.getPickupPersons())) {
			wmtOrder.setOrderPickPersons(JsonUtils.toJson(wmtOrderDTO.getPickupPersons()));
		}
		if (Objects.nonNull(wmtOrderDTO.getShipNode())) {
			WmtOrderDTO.ShipNode shipNode = wmtOrderDTO.getShipNode();
			wmtOrder.setShipNodeType(shipNode.getType());
			wmtOrder.setShipNodeName(shipNode.getName());
			wmtOrder.setShipNodeId(shipNode.getId());
		}

		wmtOrder.setLastMpdsSyncOrderTime(
				StrUtil.isNotEmpty(wmtOrderDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(wmtOrderDTO.getSyncTime()) : null);

		return wmtOrder;
	}
}
