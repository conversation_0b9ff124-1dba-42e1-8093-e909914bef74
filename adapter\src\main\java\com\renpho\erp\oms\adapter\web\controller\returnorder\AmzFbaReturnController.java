package com.renpho.erp.oms.adapter.web.controller.returnorder;

import com.renpho.erp.oms.application.returnorder.service.AmzReturnOrderOperateService;
import com.renpho.erp.oms.application.returnorder.service.AmzReturnOrderQueryService;
import com.renpho.erp.oms.application.returnorder.vo.AmzReturnOrderPageVo;
import com.renpho.erp.oms.domain.returnorder.query.AmzFbaReturnPageQuery;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.mdcThreadPool.MdcThreadPoolTaskExecutor;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 亚马逊退货报告controller
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/amzRefund/order/**")
@RequestMapping("/amzRefund/order")
public class AmzFbaReturnController {
    @Autowired
    private AmzReturnOrderQueryService amzReturnOrderQueryService;
    @Autowired
    private AmzReturnOrderOperateService amzReturnOrderOperateService;
    @Autowired
    private MdcThreadPoolTaskExecutor mdcThreadPoolTaskExecutor;

    /**
     * 分页查询.
     *
     * @param query 分页参数
     */
    @PostMapping("/page")
    @PreAuthorize(PermissionConstant.AmzFbaReturn.PAGE)
    public R<Paging<AmzReturnOrderPageVo>> page(@RequestBody @Valid AmzFbaReturnPageQuery query) {
        return R.success(amzReturnOrderQueryService.pageQuery(query));
    }

    /**
     * 导出
     * @param query 分页参数
     */
    @PostMapping("/export")
    @PreAuthorize(PermissionConstant.AmzFbaReturn.EXPORT)
    public void export(@RequestBody @Valid AmzFbaReturnPageQuery query, HttpServletResponse response) throws IOException {
        HttpUtil.setExportResponseHeader(response, "fba-return-order.xlsx");
        @Cleanup
        ServletOutputStream outputStream = response.getOutputStream();
        amzReturnOrderQueryService.export(query, outputStream);
    }

    /**
     * 重新解析
     */
    @PostMapping("/reParse")
    @PreAuthorize(PermissionConstant.AmzFbaReturn.REPARSE)
    public R<Void> reParse() {
        mdcThreadPoolTaskExecutor.execute(() -> amzReturnOrderOperateService.reparseAll(1000));
        return R.success();
    }
}
