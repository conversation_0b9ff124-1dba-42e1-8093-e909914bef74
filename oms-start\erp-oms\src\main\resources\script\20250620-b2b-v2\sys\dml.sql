-- 1. 主菜单项（页面菜单）
INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params,
 perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'B2B' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "B2B Orders", "language": "en-US"}, {"name": "B2B订单", "language": "zh-CN"}]',
        '',
        'Menu',
        'oms/B2B/b2bOrderList/index',
        'b2bOrderList',
        '',
        'oms:b2bOrder:page',
        3,
        1,
        0,
        0,
        0,
        0,
        now(),
        now());


-- 2. 子权限按钮菜单项
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "列表", "language": "zh-CN"}, {"name": "List", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:page', 1, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "备注", "language": "zh-CN"}, {"name": "Remark", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:remark', 2, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "审核", "language": "zh-CN"}, {"name": "Audit", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:audit', 3, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "取消", "language": "zh-CN"}, {"name": "Cancel", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:cancel', 4, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "导出", "language": "zh-CN"}, {"name": "Export", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:export', 5, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS', '[{"name": "发货审核", "language": "zh-CN"}, {"name": "Shipment Audit", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:b2bOrder:shipmentAudit', 6, 1, 0, 0, 0, 0, now(), now());

INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params,
 perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id
               FROM sys_menu_info
               WHERE menu_type = 'Menu' AND route_address = 'b2bOrderList' AND is_deleted = 0) tmp), 'OMS',
        '[{"name": "Edit", "language": "en-US"}, {"name": "编辑", "language": "zh-CN"}]', '',
        'Button',
        '',
        '',
        '',
        'oms:b2bOrder:edit',
        7,
        1,
        0,
        0,
        0,
        0,
        now(),
        now());

-- 主菜单：B2B订单列表页面
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:page' AND menu_type = 'Menu' AND is_deleted = 0), 'en-US', 'B2B Orders', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:page' AND menu_type = 'Menu' AND is_deleted = 0), 'zh-CN', 'B2B订单', 2, NOW(), 2, NOW(), 0);

-- 子按钮：列表

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:page' AND menu_type = 'Button' AND is_deleted = 0), 'en-US', 'List', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:page' AND menu_type = 'Button' AND is_deleted = 0), 'zh-CN', '列表', 2, NOW(), 2, NOW(), 0);


-- 子按钮：备注
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:remark' AND is_deleted = 0), 'en-US', 'Remark', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:remark' AND is_deleted = 0), 'zh-CN', '备注', 2, NOW(), 2, NOW(), 0);


-- 子按钮：审核
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:audit' AND is_deleted = 0), 'en-US', 'Audit', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:audit' AND is_deleted = 0), 'zh-CN', '审核', 2, NOW(), 2, NOW(), 0);


-- 子按钮：取消
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:cancel' AND is_deleted = 0), 'en-US', 'Cancel', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:cancel' AND is_deleted = 0), 'zh-CN', '取消', 2, NOW(), 2, NOW(), 0);


-- 子按钮：导出
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:export' AND is_deleted = 0), 'en-US', 'Export', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:export' AND is_deleted = 0), 'zh-CN', '导出', 2, NOW(), 2, NOW(), 0);


-- 子按钮：发货审核
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:shipmentAudit' AND is_deleted = 0), 'en-US', 'Shipment Audit', 2, NOW(), 2, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:shipmentAudit' AND is_deleted = 0), 'zh-CN', '发货审核', 2, NOW(), 2, NOW(), 0);

-- 子按钮：编辑
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:edit' AND is_deleted = 0), 'en-US', 'Edit', 0, now(), 0, now(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:edit' AND is_deleted = 0), 'zh-CN', '编辑', 0, now(), 0, now(), 0);

-- 主菜单项：收款单列表页面
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'B2B' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Receipt List", "language": "en-US"}, {"name": "收款单列表", "language": "zh-CN"}]',
        '', 'Menu', 'oms/B2B/receiptList/index', 'receiptList', '',
        'oms:b2BReceiptOrder:page', 5, 1, 0, 0, 0, 0, NOW(), NOW());

-- 子按钮：列表
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE route_address = 'receiptList' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "List", "language": "en-US"}, {"name": "列表", "language": "zh-CN"}]',
        '', 'Button', '', '', '', 'oms:b2BReceiptOrder:page', 1, 1, 0, 0, 0, 0, NOW(), NOW());

-- 子按钮：导出
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE route_address = 'receiptList' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Export", "language": "en-US"}, {"name": "导出", "language": "zh-CN"}]',
        '', 'Button', '', '', '', 'oms:b2BReceiptOrder:export', 2, 1, 0, 0, 0, 0, NOW(), NOW());

-- 子按钮：新增收款
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE route_address = 'receiptList' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Add Receipt", "language": "en-US"}, {"name": "新增收款", "language": "zh-CN"}]',
        '', 'Button', '', '', '', 'oms:b2BReceiptOrder:add', 3, 1, 0, 0, 0, 0, NOW(), NOW());

-- 子按钮：确认收款
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE route_address = 'receiptList' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Confirm Receipt", "language": "en-US"}, {"name": "确认收款", "language": "zh-CN"}]',
        '', 'Button', '', '', '', 'oms:b2BReceiptOrder:confirm', 4, 1, 0, 0, 0, 0, NOW(), NOW());

-- 子按钮：取消
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type,
                           component_path, route_address, route_params, perms, sort, status,
                           is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE route_address = 'receiptList' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Cancel", "language": "en-US"}, {"name": "取消", "language": "zh-CN"}]',
        '', 'Button', '', '', '', 'oms:b2BReceiptOrder:cancel', 5, 1, 0, 0, 0, 0, NOW(), NOW());


-- 页面菜单：收款单列表
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id
         FROM sys_menu_info
         WHERE perms = 'oms:b2BReceiptOrder:page' AND menu_type = 'Menu' AND is_deleted = 0),
        'en-US', 'Receipt List', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id
         FROM sys_menu_info
         WHERE perms = 'oms:b2BReceiptOrder:page' AND menu_type = 'Menu' AND is_deleted = 0),
        'zh-CN', '收款单列表', 2, NOW(), 2, NOW(), 0);

-- 按钮：列表
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id
         FROM sys_menu_info
         WHERE perms = 'oms:b2BReceiptOrder:page' AND menu_type = 'Button' AND is_deleted = 0),
        'en-US', 'List', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id
         FROM sys_menu_info
         WHERE perms = 'oms:b2BReceiptOrder:page' AND menu_type = 'Button' AND is_deleted = 0),
        'zh-CN', '列表', 2, NOW(), 2, NOW(), 0);

-- 按钮：导出
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:export' AND is_deleted = 0),
        'en-US', 'Export', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:export' AND is_deleted = 0),
        'zh-CN', '导出', 2, NOW(), 2, NOW(), 0);

-- 按钮：新增收款
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:add' AND is_deleted = 0),
        'en-US', 'Add Receipt', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:add' AND is_deleted = 0),
        'zh-CN', '新增收款', 2, NOW(), 2, NOW(), 0);

-- 按钮：确认收款
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:confirm' AND is_deleted = 0),
        'en-US', 'Confirm Receipt', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:confirm' AND is_deleted = 0),
        'zh-CN', '确认收款', 2, NOW(), 2, NOW(), 0);

-- 按钮：取消
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:cancel' AND is_deleted = 0),
        'en-US', 'Cancel', 2, NOW(), 2, NOW(), 0);
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2BReceiptOrder:cancel' AND is_deleted = 0),
        'zh-CN', '取消', 2, NOW(), 2, NOW(), 0);

-- 1. 主菜单项（页面菜单）
INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params,
 perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'B2B' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Create B2B Order", "language": "en-US"}, {"name": "新建B2B订单", "language": "zh-CN"}]',
        '',
        'Menu',
        'oms/B2B/createB2BOrder/index',
        'createB2BOrder',
        '',
        '',
        4,
        1,
        0,
        0,
        0,
        0,
        now(),
        now());

-- 2. 按钮权限
INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params,
 perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id
         FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'createB2BOrder' AND is_deleted = 0) tmp),
        'OMS',
        '[{"name": "Create B2B Order", "language": "en-US"}, {"name": "新建B2B订单", "language": "zh-CN"}]',
        '',
        'Button',
        '',
        '',
        '',
        'oms:b2bOrder:create',
        1,
        1,
        0,
        0,
        0,
        0,
        now(),
        now());

-- 3. 主菜单多语言
INSERT INTO sys_menu_language
(menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'createB2BOrder' AND menu_type = 'Menu' AND is_deleted = 0),
        'en-US',
        'Create B2B Order',
        2,
        now(),
        2,
        now(),
        0);

INSERT INTO sys_menu_language
(menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'createB2BOrder' AND menu_type = 'Menu' AND is_deleted = 0),
        'zh-CN',
        '新建B2B订单',
        2,
        now(),
        2,
        now(),
        0);

-- 4. 按钮多语言
INSERT INTO sys_menu_language
(menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:create' AND is_deleted = 0),
        'en-US',
        'Create B2B Order',
        2,
        now(),
        2,
        now(),
        0);

INSERT INTO sys_menu_language
(menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:b2bOrder:create' AND is_deleted = 0),
        'zh-CN',
        '新建B2B订单',
        2,
        now(),
        2,
        now(),
        0);

