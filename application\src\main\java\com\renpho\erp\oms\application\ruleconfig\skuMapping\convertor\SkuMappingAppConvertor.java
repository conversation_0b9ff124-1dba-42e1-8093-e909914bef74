package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingUpdateCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.SkuMappingVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SkuMappingAppConvertor {
    @Mapping(target = "status", expression = "java(param.getStatus().getValue())")
    @Mapping(target = "fulfillmentType", expression = "java(param.getFulfillmentType().getValue())")
    SkuMappingVO toVO(SkuMappingAggRoot param);

    SkuMappingVO.SkuMappingLineVO toVO(SkuMappingLine skuMappingLine);
    SkuMappingAggRoot toDomain(SkuMappingUpdateCmd param);

    SkuMappingLine toDomain(SkuMappingAddCmd.SkuMappingLineAddCmd param);
}
