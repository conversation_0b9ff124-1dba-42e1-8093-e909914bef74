package com.renpho.erp.oms.domain.vc.order.model;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;
import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 亚马逊VC订单详情值对象 基于Amazon SP-API getPurchaseOrder接口的orderDetails字段
 * <AUTHOR>
 */
@Slf4j
@Getter
@Builder
@ValueObject
public class AmzVcOrderDetails {

	/**
	 * 采购订单日期 下达采购订单的日期，必须为ISO-8601日期/时间格式
	 */
	private LocalDateTime purchaseOrderDate;

	/**
	 * 采购订单变更日期 订单下达后Amazon最后一次变更采购订单的日期 此日期将大于'purchaseOrderDate'，表示PO数据在该日期发生了变更
	 * 供应商需要履行更新后的PO，PO变更可能涉及商品数量、发货地点、发货窗口等
	 */
	private LocalDateTime purchaseOrderChangedDate;

	/**
	 * 采购订单状态变更日期 当前采购订单状态发生变更的日期，必须为ISO-8601日期/时间格式
	 */
	private LocalDateTime purchaseOrderStateChangedDate;

	/**
	 * 采购订单类型 RegularOrder: 常规订单 - 一次性采购和付款的订单 ConsignedOrder: 寄售订单 -
	 * 允许接收产品但库存仍属于供应商直到产品被使用 NewProductIntroduction: 新品介绍订单 - 引入新产品的采购订单 RushOrder: 紧急订单
	 * - 需要比标准到达日期更早处理和交付的商品采购
	 */
	private String purchaseOrderType;

	/**
	 * 进口详情 进口订单的进口详细信息
	 */
	private AmzVcImportDetails importDetails;

	/**
	 * 交易代码 如果收件人要求，此字段将包含促销/交易号码 折扣代码行是可选的，用于获得订单商品的价格折扣
	 */
	private String dealCode;

	/**
	 * 付款方式 Invoice: 发票付款 - 企业提交发票以支付从供应商购买的产品和服务 Consignment: 寄售 - 零售商作为寄售人代理寄售方提供的商品
	 * CreditCard: 信用卡付款 Prepaid: 预付款
	 */
	private String paymentMethod;

	/**
	 * 买方信息 买方的姓名、地址和税务详情
	 */
	private AmzVcPartyInfo buyingParty;

	/**
	 * 卖方信息 卖方的姓名、地址和税务详情
	 */
	private AmzVcPartyInfo sellingParty;

	/**
	 * 收货方信息 收货方的姓名、地址和税务详情
	 */
	private AmzVcPartyInfo shipToParty;

	/**
	 * 账单方信息 账单方的姓名、地址和税务详情
	 */
	private AmzVcPartyInfo billToParty;

	/**
	 * 发货窗口 根据ISO8601定义的日期时间间隔，间隔由双连字符(--)分隔
	 */
	private String shipWindow;

	/**
	 * 交货窗口 根据ISO8601定义的日期时间间隔，间隔由双连字符(--)分隔
	 */
	private String deliveryWindow;

	/**
	 * 订单商品列表 对应API字段：items
	 */
	private List<AmzVcOrderItem> items;

	/**
	 * 检查是否为直接进口
	 * @return boolean
	 */
	public boolean isDirectImport() {
		return Objects.nonNull(this.shipWindow);
	}

	/**
	 * 获取卖方ID
	 * @return String 卖方信息ID，如果为null则返回null
	 */
	public String getSellingPartyId() {
		return Optional.ofNullable(this.sellingParty).map(AmzVcPartyInfo::getPartyId).orElse(null);
	}

	/**
	 * 获取买方ID
	 * @return String 买方信息ID，如果为null则返回null
	 */
	public String getBuyingPartyId() {
		return Optional.ofNullable(this.buyingParty).map(AmzVcPartyInfo::getPartyId).orElse(null);
	}

	/**
	 * 获取收货方ID
	 * @return String 收货方信息ID，如果为null则返回null
	 */
	public String getShipToPartyId() {
		return Optional.ofNullable(this.shipToParty).map(AmzVcPartyInfo::getPartyId).orElse(null);
	}

	/**
	 * 获取账单方ID
	 * @return String 账单方信息ID，如果为null则返回null
	 */
	public String getBillToPartyId() {
		return Optional.ofNullable(this.billToParty).map(AmzVcPartyInfo::getPartyId).orElse(null);
	}

	/**
	 * 获取订单商品列表（不可修改）
	 * @return List<AmzVcOrderItem>
	 */
	public List<AmzVcOrderItem> getItems() {
		return items == null ? Collections.emptyList() : Collections.unmodifiableList(items);
	}

	/**
	 * 获取Asin列表
	 * @return String
	 */
	public List<String> getAsinList() {
		if (CollectionUtils.isEmpty(items)) {
			return List.of();
		}
		return items.stream().map(AmzVcOrderItem::getAmazonProductIdentifier).toList();
	}

	/**
	 * 构建发货窗口 优先使用shipWindow（发货窗口），如果不存在则使用deliveryWindow（交货窗口）
	 * @return VcShippingWindow 发货窗口对象，如果都不存在则返回null
	 */
	public VcShippingWindow getShippingWindow() {
		// 优先使用shipWindow（发货窗口）
		if (Objects.nonNull(shipWindow) && !shipWindow.trim().isEmpty()) {
			return parseTimeWindow(shipWindow, "shipWindow");
		}

		// 如果shipWindow不存在，则使用deliveryWindow（交货窗口）
		if (Objects.nonNull(deliveryWindow) && !deliveryWindow.trim().isEmpty()) {
			return parseTimeWindow(deliveryWindow, "deliveryWindow");
		}
		return null;
	}

	/**
	 * 解析时间窗口字符串 根据ISO8601格式解析时间间隔，间隔由双连字符(--)分隔
	 * 格式示例：2023-12-01T10:00:00Z--2023-12-01T18:00:00Z
	 * @param timeWindow 时间窗口字符串
	 * @param windowType 窗口类型（用于日志）
	 * @return VcShippingWindow 解析后的发货窗口对象
	 */
	private VcShippingWindow parseTimeWindow(String timeWindow, String windowType) {
		try {
			// 按双连字符分割时间间隔
			String[] timeParts = timeWindow.split("--");
			if (timeParts.length != 2) {
				log.warn("Invalid {} format: {}, expected format: startTime--endTime", windowType, timeWindow);
				return null;
			}

			// 解析开始时间和结束时间
			LocalDateTime startTime = OffsetDateTime.parse(timeParts[0].trim()).atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
			LocalDateTime endTime = OffsetDateTime.parse(timeParts[1].trim()).atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();

			return VcShippingWindow.builder().startTime(startTime).endTime(endTime).build();

		}
		catch (Exception e) {
			log.warn("Failed to parse {} time window: {}", windowType, timeWindow, e);
			return null;
		}
	}

}
