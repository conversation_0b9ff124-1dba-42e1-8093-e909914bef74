package com.renpho.erp.oms.domain.vc.order.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description vc解析源订单状态枚举
 * @date 2025/7/7 10:57
 */
@Getter
@AllArgsConstructor
public enum VcParseSourceOrderStatus {
	WAIT_PARSE(0, "待解析"), HAS_PARSED(1, "已解析"), FAILED_PARSE(2, "解析失败"), NO_PARSE(3, "无需解析");

	private final Integer value;
	private final String description;

}
