package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.amazon.config.AmzEstimatedPriceConfigProperties;
import com.renpho.erp.oms.application.channelmanagement.amazon.convert.AmzAppOrderConvertor;
import com.renpho.erp.oms.application.channelmanagement.amazon.convert.AmzAppOrderItemConvertor;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderLog;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.amazon.optlog.AmzOrderAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.amazon.optlog.AmzOrderBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.amazon.optlog.AmzOrderUpdateSnapSource;
import com.renpho.erp.oms.application.listingmanagement.service.SalesChannelListingService;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.domain.channelmanagement.amazon.command.query.AmzOrderItemPageQuery;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItem;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderItemRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderRepository;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.infrastructure.common.enums.AmzOrderItemEstimatedPriceStatus;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class AmzOrderService {
    private final AmzAppOrderConvertor amzAppOrderConvertor;
    private final AmzAppOrderItemConvertor amzAppOrderItemConvertor;
    private final AmzOrderRepository amzOrderRepository;
    private final AmzOrderItemRepository amzOrderItemRepository;

    private final SalesChannelListingService salesChannelListingService;

    private final AmzEstimatedPriceConfigProperties amzEstimatedPriceConfigProperties;

    @Lock4j(name = "oms:amazon:sync",
            keys = {"#amzOrderPushDTO.amazonOrderId", "#amzOrderPushDTO.sellerId", "#amzOrderPushDTO.marketplaceId"})
    public void createOrUpdate(AmzOrderPushDTO amzOrderPushDTO) {
        // 转换为领域对象
        AmzOrder amzOrder = amzAppOrderConvertor.toDomain(amzOrderPushDTO);
        List<AmzOrderItem> itemList = amzOrderPushDTO.getAmzOrderItemFilterPIIList()
                .stream()
                .map(amazonOrderItemPushDTO -> amzAppOrderItemConvertor.toDomain(amazonOrderItemPushDTO, amzOrder))
                .collect(Collectors.toList());

        // 查询有没有
        AmzOrder exitsOrder = amzOrderRepository.getByUniqueIndex(amzOrder.getAmazonOrderId(), amzOrder.getSellerId(),
                amzOrder.getMarketplaceId());
        // 更新插入
        if (Objects.nonNull(exitsOrder)) {
            amzOrder.setId(exitsOrder.getId());
            SpringUtil.getBean(AmzOrderService.class).doUpdate(amzOrder, itemList);
        } else {
            SpringUtil.getBean(AmzOrderService.class).doCreate(amzOrder, itemList);
        }
    }

    @Transactional
    @OpLog(snaptSource = AmzOrderAddSnapSource.class, title = "新增亚马逊订单", businessType = BusinessType.INSERT,
            businessModule = AmzOrderBusinessModule.class, systemModule = OmsSystemModule.class)
    public R<Long> doCreate(AmzOrder amzOrder, List<AmzOrderItem> itemList) {
        // 插入订单
        Long orderId = amzOrderRepository.insert(amzOrder);
        // 插入商品
        amzOrderItemRepository.batchInsert(itemList, orderId);
        // 返回主键增加日志
        return R.success(orderId);
    }

    @Transactional
    @OpLog(snaptSource = AmzOrderUpdateSnapSource.class, title = "更新亚马逊订单", businessType = BusinessType.UPDATE,
            businessModule = AmzOrderBusinessModule.class, systemModule = OmsSystemModule.class)
    public R<Long> doUpdate(AmzOrder amzOrder, List<AmzOrderItem> itemList) {
        // 更新订单
        Long orderId = amzOrderRepository.update(amzOrder);
        // 先删除原有商品
        amzOrderItemRepository.deleteByOrderId(amzOrder.getId());
        // 插入商品
        amzOrderItemRepository.batchInsert(itemList, orderId);
        // 返回主键增加日志
        return R.success(orderId);
    }

    public AmzOrderLog getLogById(Long orderId) {
        // 查询订单
        AmzOrder amzOrder = amzOrderRepository.getById(orderId);
        if (Objects.isNull(amzOrder)) {
            return null;
        }
        AmzOrderLog amzOrderLog = amzAppOrderConvertor.toDTO(amzOrder);
        // 查询商品
        List<AmzOrderItem> amzOrderItemList = amzOrderItemRepository.listByOrderId(amzOrder.getId());
        amzOrderLog.setAmzOrderItemLogList(amzOrderItemList.stream().map(amazonOrderItem -> {
            return amzAppOrderItemConvertor.toDTO(amazonOrderItem);
        }).collect(Collectors.toList()));
        return amzOrderLog;
    }

    public void estimatedPrice(Integer storeId) {
        List<AmzOrderItem> amzOrderItemList;
        Integer pageSize = 1000;
        Long maxId = 0L;
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = endTime.minusDays(amzEstimatedPriceConfigProperties.getDay());
        do {
            AmzOrderItemPageQuery amzOrderItemPageQuery = new AmzOrderItemPageQuery();
            amzOrderItemPageQuery.setEstimatedPriceStatus(AmzOrderItemEstimatedPriceStatus.UN_ESTIMATE.getStatus());
            amzOrderItemPageQuery.setMaxId(maxId);
            amzOrderItemPageQuery.setStoreId(storeId);

            amzOrderItemPageQuery.setPageSize(pageSize);
            amzOrderItemList = amzOrderItemRepository.getUnEstimateItem(amzOrderItemPageQuery);
            for (AmzOrderItem amzOrderItem : amzOrderItemList) {
                List<AmzOrderItem> amzOrderItemToEstimatedPriceList = amzOrderItemRepository.loadForEstimatedPrice(
                        storeId, amzOrderItem.getOmsAmzOrderId(), amzOrderItem.getSellerSku(), amzEstimatedPriceConfigProperties.getOrderStatus(), startTime, endTime,
                        amzEstimatedPriceConfigProperties.getOrderNum());

                // 获取不到近多少天的亚马逊订单行的话,用listing预估价格
                if (CollUtil.isEmpty(amzOrderItemToEstimatedPriceList)) {
                    SalesChannelListing salesChannelListing = salesChannelListingService.loadFromCache(storeId, amzOrderItem.getSellerSku(), amzEstimatedPriceConfigProperties.getListingCacheMinutes());
                    if (salesChannelListing == null) {
                        amzOrderItem.setEstimatedPriceStatus(AmzOrderItemEstimatedPriceStatus.ESTIMATED_FAIL.getStatus());
                        amzOrderItemRepository.updateById(amzOrderItem);
                        continue;
                    }
                    amzOrderItem.setItemPriceAmount(salesChannelListing.getPrice().multiply(BigDecimal.valueOf(amzOrderItem.getQuantityOrdered())));
                    amzOrderItem.setEstimatedPriceStatus(AmzOrderItemEstimatedPriceStatus.ESTIMATED.getStatus());
                    amzOrderItemRepository.updateById(amzOrderItem);
                    continue;
                }

                Integer itemNum = 0;
                // 商品价格
                BigDecimal totalItemPrice = BigDecimal.ZERO;
                // 商品税
                BigDecimal totalItemTax = BigDecimal.ZERO;
                // 商品折扣
                BigDecimal totalPromotionDiscountAmount = BigDecimal.ZERO;
                // 商品折扣税
                BigDecimal totalPromotionDiscountTax = BigDecimal.ZERO;

                for (AmzOrderItem orderItem : amzOrderItemToEstimatedPriceList) {
                    itemNum += orderItem.getQuantityOrdered();
                    // 价格部分
                    totalItemPrice = totalItemPrice.add(ObjectUtil.defaultIfNull(orderItem.getItemPriceAmount(), BigDecimal.ZERO));
                    totalItemTax = totalItemTax.add(ObjectUtil.defaultIfNull(orderItem.getItemTaxAmount(), BigDecimal.ZERO));
                    totalPromotionDiscountAmount = totalPromotionDiscountAmount
                            .add(ObjectUtil.defaultIfNull(orderItem.getPromotionDiscountAmount(), BigDecimal.ZERO));
                    totalPromotionDiscountTax = totalPromotionDiscountTax
                            .add(ObjectUtil.defaultIfNull(orderItem.getPromotionDiscountTaxAmount(), BigDecimal.ZERO));
                }
                // 估算商品价格
                BigDecimal estimatedPrice = totalItemPrice.divide(BigDecimal.valueOf(itemNum), 2, RoundingMode.HALF_UP);
                amzOrderItem.setItemPriceAmount(estimatedPrice.multiply(BigDecimal.valueOf(amzOrderItem.getQuantityOrdered())));
                // 估算商品税
                estimatedPrice = totalItemTax.divide(BigDecimal.valueOf(itemNum), 2, RoundingMode.HALF_UP);
                amzOrderItem.setItemTaxAmount(estimatedPrice.multiply(BigDecimal.valueOf(amzOrderItem.getQuantityOrdered())));
                // 估算商品折扣
                estimatedPrice = totalPromotionDiscountAmount.divide(BigDecimal.valueOf(itemNum), 2, RoundingMode.HALF_UP);
                amzOrderItem.setPromotionDiscountAmount(estimatedPrice.multiply(BigDecimal.valueOf(amzOrderItem.getQuantityOrdered())));
                // 估算商品折扣税
                estimatedPrice = totalPromotionDiscountTax.divide(BigDecimal.valueOf(itemNum), 2, RoundingMode.HALF_UP);
                amzOrderItem.setPromotionDiscountTaxAmount(estimatedPrice.multiply(BigDecimal.valueOf(amzOrderItem.getQuantityOrdered())));


                amzOrderItem.setEstimatedPriceStatus(AmzOrderItemEstimatedPriceStatus.ESTIMATED.getStatus());
                amzOrderItemRepository.updateById(amzOrderItem);
            }
            maxId = Optional.ofNullable(CollUtil.getLast(amzOrderItemList)).map(AmzOrderItem::getId).orElse(0L);
        }
        while (Objects.equals(amzOrderItemList.size(), pageSize));
    }

}
