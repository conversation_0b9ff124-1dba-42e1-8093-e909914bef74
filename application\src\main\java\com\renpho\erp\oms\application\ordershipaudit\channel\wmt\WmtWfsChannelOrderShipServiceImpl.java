package com.renpho.erp.oms.application.ordershipaudit.channel.wmt;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.walmart.model.RequestHeader;
import com.renpho.erp.apiproxy.walmart.model.ShopAccount;
import com.renpho.erp.apiproxy.walmart.model.WalmartResponse;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.CreateCustomerOrderRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.CreateCustomerOrderResponse;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractMultiChannelOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.ordershipaudit.ChannelItemFulfillmentInfo;
import com.renpho.erp.oms.domain.ordershipaudit.Email;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditItem;
import com.renpho.erp.oms.domain.ordershipaudit.repository.WfsSalesChannelMappingRepository;
import com.renpho.erp.oms.domain.ordershipaudit.wmt.WfsSalesChannelMapping;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.WalmartClient;
import com.renpho.erp.oms.infrastructure.persistence.misc.service.RegionService;
import com.renpho.karma.dto.R;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 沃尔玛wfs多渠道发货 <a href=
 * "https://g1c55p.axshare.com/#id=wsa0ey&p=%E5%88%9B%E5%BB%BA%E8%AE%A2%E5%8D%95_1&g=1">需求文档</a>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
@Slf4j
@Component
public class WmtWfsChannelOrderShipServiceImpl extends AbstractMultiChannelOrderShipService {

	private final WfsSalesChannelMappingRepository wfsSalesChannelMappingRepository;
	private final WalmartClient walmartClient;
	private final RegionService regionService;

	protected WmtWfsChannelOrderShipServiceImpl(StoreClient storeClient, WfsSalesChannelMappingRepository wfsSalesChannelMappingRepository,
			SaleOrderQueryService saleOrderQueryService, WalmartClient walmartClient, RegionService regionService) {
		super(saleOrderQueryService, storeClient);
		this.wfsSalesChannelMappingRepository = wfsSalesChannelMappingRepository;
		this.walmartClient = walmartClient;
		this.regionService = regionService;
	}

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.WFS_MULTI_CHANNEL);
	}

	/**
	 * 创建出库单
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	@Override
	public void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, StoreAuthorizationVo storeAuthorizationVo) {
		CreateCustomerOrderRequest createCustomerOrderRequest;
		try {
			createCustomerOrderRequest = getCreateCustomerOrderRequest(orderShipAuditAggRoot, saleOrderAggRoot, receivingAddress);
		}
		catch (Exception e) {
			log.error("组装沃尔玛创建wfs出库单参数失败, 发货审核id是{}", orderShipAuditAggRoot.getId(), e);
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}

		R<CreateCustomerOrderResponse> customerOrderResponse;
		try {
			customerOrderResponse = walmartClient.createCustomerOrder(getShopAccount(storeAuthorizationVo), createCustomerOrderRequest);
		}
		catch (Exception e) {
			log.error("调用沃尔玛创建wfs出库单发生错误", e);
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}
		handleResponse(customerOrderResponse, orderShipAuditAggRoot);
	}

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, StoreAuthorizationVo storeAuthorizationVo) {
		GetFulfillmentOrdersStatusRequest getFulfillmentOrdersStatusRequest = new GetFulfillmentOrdersStatusRequest();
		getFulfillmentOrdersStatusRequest.setOrderNumber(orderShipAuditAggRoot.getChannelStoreOrderId());

		R<GetFulfillmentOrdersStatusResponse> fulfillmentOrdersStatusResult = null;
		try {
			fulfillmentOrdersStatusResult = walmartClient.getFulfillmentOrdersStatus(getShopAccount(storeAuthorizationVo),
					getFulfillmentOrdersStatusRequest);
		}
		catch (Exception e) {
			log.error("调用沃尔玛同步wfs出库单履约信息发生错误", e);
			orderShipAuditAggRoot.syncFail(e.getMessage());
			return;
		}
		if (!fulfillmentOrdersStatusResult.isSuccess()) {
			/*
			 * 创建沃尔玛多渠道销售单失败,销售sku沃尔玛那边没有等错误 根据订单号搜索不到订单 { "success": false, "code":
			 * "404.OS_SERVICE.100", "message": "Order does not exist", "data": {
			 * "status": "FAIL", "header": null, "payload": null, "errors": [ { "code":
			 * "404.OS_SERVICE.100", "field": null, "description": "Order does not exist",
			 * "info": "HTTP/1.1 404 Not Found", "severity": "ERROR", "category":
			 * "APPLICATION" } ] }, "attached": null }
			 */
			boolean createFailFlag = StrUtil.equalsIgnoreCase("404.OS_SERVICE.100", fulfillmentOrdersStatusResult.getCode())
					&& StrUtil.equalsIgnoreCase("Order does not exist", fulfillmentOrdersStatusResult.getMessage());
			if (createFailFlag) {
				orderShipAuditAggRoot.createFail("Walmart系统返回错误：" + fulfillmentOrdersStatusResult.getMessage());
				return;
			}
			orderShipAuditAggRoot.syncFail(fulfillmentOrdersStatusResult.getMessage());
			return;
		}
		GetFulfillmentOrdersStatusResponse fulfillmentOrdersStatusResponse = fulfillmentOrdersStatusResult.getData();
		List<GetFulfillmentOrdersStatusResponse.Order> orders = fulfillmentOrdersStatusResponse.getPayload();
		if (CollUtil.isEmpty(orders)) {
			orderShipAuditAggRoot.syncFail("调用getFulfillmentOrdersStatus获取不到数据");
			return;
		}
		GetFulfillmentOrdersStatusResponse.Order order = orders.get(0);
		List<GetFulfillmentOrdersStatusResponse.Shipment> shipments = order.getShipments();
		for (GetFulfillmentOrdersStatusResponse.Shipment shipment : ObjectUtil.defaultIfNull(shipments,
				Collections.<GetFulfillmentOrdersStatusResponse.Shipment> emptyList())) {
			LocalDateTime estimatedArrivalDate = null;
			for (GetFulfillmentOrdersStatusResponse.ShipmentDate shipmentDate : shipment.getShipmentDates()) {
				if (StrUtil.equalsIgnoreCase(shipmentDate.getDateTypeId(), "DELIVERY")) {
					estimatedArrivalDate = DateUtil.parseISOStr(shipmentDate.getExpectedDate());
					break;
				}
			}

			ChannelItemFulfillmentInfo channelItemFulfillmentInfo = ChannelItemFulfillmentInfo.builder()
				.trackingNo(shipment.getTrackingNo())
				.carrierName(shipment.getScac())
				.trackingUrl(shipment.getExternalTrackingURL())
				.estimatedArrivalDate(estimatedArrivalDate)
				.shippingDate(DateUtil.parseISOStr(shipment.getActualShipmentDate()))
				.build();
			for (GetFulfillmentOrdersStatusResponse.ShipmentLine shipmentLine : shipment.getShipmentLines()) {
				orderShipAuditAggRoot.markShipping(shipmentLine.getShipmentLineNo(), null, shipmentLine.getQuantity().getMeasurementValue(),
						channelItemFulfillmentInfo);
			}
		}

		orderShipAuditAggRoot.calculateStatus();
	}

	private CreateCustomerOrderRequest getCreateCustomerOrderRequest(OrderShipAuditAggRoot orderShipAuditAggRoot,
			SaleOrderAggRoot saleOrderAggRoot, SaleOrderAddressVO receivingAddress) {
		CreateCustomerOrderRequest createCustomerOrderRequest = new CreateCustomerOrderRequest();

		// 设置头
		createCustomerOrderRequest.setHeader(generateHeader());

		WfsSalesChannelMapping wfsSalesChannelMapping = wfsSalesChannelMappingRepository
			.getBySaleChannelAndStoreId(saleOrderAggRoot.getStore().getChannelCode(), orderShipAuditAggRoot.getChannelStoreId());
		if (wfsSalesChannelMapping == null) {
			throw DomainException.ofKey("WFS_SALE_CHANNEL_MAPPING_NOT_FOUND");
		}

		CreateCustomerOrderRequest.Body body = new CreateCustomerOrderRequest.Body();
		// 沃尔玛映射渠道id
		body.setOrderChannelId(wfsSalesChannelMapping.getOrderChannelId());
		// 订单号
		body.setSellerOrderId(orderShipAuditAggRoot.getAuditNo());
		// 下单时间
		body.setOrderPlacedTime(DateUtil.convertToMsUTC(saleOrderAggRoot.getOrderedTime()));
		// 无需确认
		body.setNeedsConfirmation(Boolean.FALSE);
		// 是否允许部分发货
		body.setPartialFulfillments(Boolean.FALSE);
		// 订单行信息
		body.setOrderItems(fillItems(orderShipAuditAggRoot, saleOrderAggRoot.getCurrency()));
		// 收件人信息
		this.buildAddress(receivingAddress, body, orderShipAuditAggRoot, saleOrderAggRoot);

		createCustomerOrderRequest.setPayload(body);
		return createCustomerOrderRequest;
	}

	/**
     * <a href="https://developer.walmart.com/us-marketplace/reference/createfulfillment">wfs文档</a>
	 * 构建wfs头参数
     */
	private RequestHeader generateHeader() {
		RequestHeader.HeaderAttribute headerAttribute = new RequestHeader.HeaderAttribute();
		// 来自walmart文档, 这些值是从walmart文档上获取的
		headerAttribute.setBuId("0");
		headerAttribute.setMartId("202");
		RequestHeader requestHeader = new RequestHeader();
		requestHeader.setHeaderAttributes(headerAttribute);
		return requestHeader;
	}

	private void handleResponse(R<CreateCustomerOrderResponse> customerOrderResponse, OrderShipAuditAggRoot orderShipAuditAggRoot) {
		// 调用代理服务失败
		if (!customerOrderResponse.isSuccess()) {
			orderShipAuditAggRoot.createFail(customerOrderResponse.getCode() + "___" + customerOrderResponse.getMessage());
			return;
		}

		// 调用沃尔玛创建成功
		CreateCustomerOrderResponse responseData = customerOrderResponse.getData();
		if (StrUtil.equalsIgnoreCase(responseData.getStatus(), "ACCEPTED")) {
			// 沃尔玛的requestId作为平台订单号,由平台返回
			orderShipAuditAggRoot.createSuccess(responseData.getPayload().getRequestId());
			return;
		}

		// 调用沃尔玛失败
		List<WalmartResponse.Error> errors = responseData.getErrors();
		String errorInfo = errors.stream()
			.map(e -> e.getCode() + "___" + e.getInfo() + "___" + e.getDescription())
			.collect(Collectors.joining("\n"));
		orderShipAuditAggRoot.createFail(errorInfo);

	}

	private ShopAccount getShopAccount(StoreAuthorizationVo storeAuthorizationVo) {
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());
		shopAccount.setShopCode(storeAuthorizationVo.getAuthorization().getWmtShopId());
		return shopAccount;
	}

	private List<CreateCustomerOrderRequest.OrderItem> fillItems(OrderShipAuditAggRoot orderShipAuditAggRoot, String currency) {
		List<CreateCustomerOrderRequest.OrderItem> orderItems = new ArrayList<>();
		for (OrderShipAuditItem item : orderShipAuditAggRoot.getItems()) {
			CreateCustomerOrderRequest.OrderItem orderItem = new CreateCustomerOrderRequest.OrderItem();
			// 配送类型
			orderItem.setFulfillmentType("DELIVERY");
			// 订单行号
			orderItem.setSellerLineId(item.getChannelStoreOrderItemId());
			// sku信息
			CreateCustomerOrderRequest.ItemDetail itemDetail = new CreateCustomerOrderRequest.ItemDetail();
			itemDetail.setSku(item.getChannelMsku());
			orderItem.setItemDetail(itemDetail);
			// 数量信息
			CreateCustomerOrderRequest.Qty qty = new CreateCustomerOrderRequest.Qty();
			qty.setUnitOfMeasure("EACH");
			qty.setMeasurementValue(item.getQuantity());
			orderItem.setQty(qty);
			// 发货速度
			orderItem.setShippingMethod(orderShipAuditAggRoot.getWfsShippingSpeedCategory().getValue());

			// 费用货币信息
			CreateCustomerOrderRequest.ChargeDetail chargeDetail = new CreateCustomerOrderRequest.ChargeDetail();
			chargeDetail.setChargeCategory("PRODUCT");
			chargeDetail.setChargeName("Sale Price");
			CreateCustomerOrderRequest.ChargePerUnit chargePerUnit = new CreateCustomerOrderRequest.ChargePerUnit();
			chargePerUnit.setUnit(currency);
			chargePerUnit.setCurrencyAmount(0);
			chargeDetail.setChargePerUnit(chargePerUnit);

			CreateCustomerOrderRequest.TaxPerLine taxPerLine = new CreateCustomerOrderRequest.TaxPerLine();
			taxPerLine.setCurrencyAmount(0);
			taxPerLine.setUnit(currency);
			CreateCustomerOrderRequest.TaxDetail taxDetail = new CreateCustomerOrderRequest.TaxDetail();
			taxDetail.setTaxPerLine(taxPerLine);
			chargeDetail.setTaxDetails(taxDetail);

			orderItem.setChargeDetails(ListUtil.toList(chargeDetail));

			orderItems.add(orderItem);
		}
		return orderItems;
	}

	private void buildAddress(SaleOrderAddressVO receivingAddress, CreateCustomerOrderRequest.Body body,
			OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot) {
		// region 配置姓名联系电话
		CreateCustomerOrderRequest.Contact contact = new CreateCustomerOrderRequest.Contact();
		List<String> nameSplit = StrUtil.split(receivingAddress.getRecipientName(), " ", true, true);
		if (nameSplit.size() >= 2) {
			CreateCustomerOrderRequest.Name name = new CreateCustomerOrderRequest.Name();
			name.setFirstName(nameSplit.get(0));
			name.setLastName(nameSplit.get(1));
			contact.setName(name);

		}
		else {
			CreateCustomerOrderRequest.Name name = new CreateCustomerOrderRequest.Name();
			name.setFirstName(receivingAddress.getRecipientName());
			name.setLastName(receivingAddress.getRecipientName());
			contact.setName(name);
		}

		String phone = handlePhone(saleOrderAggRoot.getChannelCode(), receivingAddress.getRecipientPhone(),
				saleOrderAggRoot.getChannelCode());
		contact.setPhone(phone);
		contact.setEmail(Optional.ofNullable(orderShipAuditAggRoot.getEmail()).map(Email::getEmailUrl).orElse(""));
		// endregion

		// region 配置地址信息
		CreateCustomerOrderRequest.Address address = new CreateCustomerOrderRequest.Address();
		address.setLine1(receivingAddress.getAddress1());
		address.setCity(receivingAddress.getCity());
		address.setState(receivingAddress.getProvince());
		// 州需要传简称,不然报错
		address
			.setState(StrUtil.blankToDefault(regionService.getStateCode(receivingAddress.getCountryCode(), receivingAddress.getProvince()),
					receivingAddress.getProvince()));

		address.setCountry(receivingAddress.getCountryCode());
		address.setZip(receivingAddress.getPostalCode());
		address.setAddressType("RESIDENTIAL");
		// endregion

		CreateCustomerOrderRequest.Customer customer = new CreateCustomerOrderRequest.Customer();
		customer.setContact(contact);
		body.setCustomer(customer);

		CreateCustomerOrderRequest.ShippingTo shippingTo = new CreateCustomerOrderRequest.ShippingTo();
		shippingTo.setContact(contact);
		shippingTo.setAddress(address);
		for (CreateCustomerOrderRequest.OrderItem orderItem : body.getOrderItems()) {
			orderItem.setShippingTo(shippingTo);
		}

	}

	private String handlePhone(String countryCode, String phone, String saleChannel) {
		// 只处理美国的号码
		if (!"us".equalsIgnoreCase(countryCode)) {
			return phone;
		}
		phone = StrUtil.trimStart(phone);
		return switch (saleChannel) {
			// (+1)7142664698 --> 7142664698
			case ChannelCode.TT_CHANNEL_CODE -> StrUtil.removePrefix(phone, "(+1)");
			// temu 13145574151-2225 --> 3145574151
			// ebay 16093349945 --> 6093349945
			case ChannelCode.TEM_CHANNEL_CODE, ChannelCode.EBY_CHANNEL_CODE -> {
				phone = StrUtil.subBefore(phone, "-", true);
				// 11位长度的话把1去掉,1是国家前缀
				boolean flag = StrUtil.startWith(phone, "1") && Objects.equals(StrUtil.length(phone), 11);
				if (!flag) {
					yield phone;
				}
				yield StrUtil.subAfter(phone, '1', false);
			}
			// +16316014484 --> 6316014484
			case ChannelCode.SPF_CHANNEL_CODE -> {
				// 前面的+1是国家前缀
				boolean flag = StrUtil.startWith(phone, "+1") && Objects.equals(StrUtil.length(phone), 12);
				if (!flag) {
					yield phone;
				}
				yield StrUtil.subAfter(phone, "+1", false);
			}
			default -> phone;
		};
	}
}
