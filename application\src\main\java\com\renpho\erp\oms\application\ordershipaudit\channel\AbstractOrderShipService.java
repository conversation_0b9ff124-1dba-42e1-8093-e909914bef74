package com.renpho.erp.oms.application.ordershipaudit.channel;

import java.util.List;

import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象-发货 应用服务
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractOrderShipService implements ChannelOrderShipService {

	private final SaleOrderQueryService saleOrderQueryService;

	protected AbstractOrderShipService(SaleOrderQueryService saleOrderQueryService) {
		this.saleOrderQueryService = saleOrderQueryService;
	}

	@Override
	public void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot) {
		try {
			SaleOrderAddressVO receivingAddress = null;
			// 是否需要订单收货地址信息
			if (this.isNeedAddress(orderShipAuditAggRoot.getFulfillmentServiceType())) {
				// 查询敏感地址信息（从mpds）
				receivingAddress = saleOrderQueryService.getReceivingAddress(saleOrderAggRoot.getOrderNo(),
						this.isNeedEmail(orderShipAuditAggRoot.getFulfillmentServiceType()), false);
			}
			// 创建履约单（仓储物流）
			this.createFulfillmentOrder(orderShipAuditAggRoot, saleOrderAggRoot, receivingAddress);
		}
		catch (Exception e) {
			log.error("创建履约单（仓储物流）异常：", e);
			orderShipAuditAggRoot.createFail(e.getMessage());
		}
	}

	/**
	 * 创建履约单（仓储物流）
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 */
	protected abstract void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress);

	/**
	 * 是否需要订单收货地址信息
	 * @return boolean
	 */
	protected boolean isNeedAddress(FulfillmentServiceType fulfillmentServiceType) {
		// 不需要订单收货地址信息的发货服务类型
		List<FulfillmentServiceType> noNeedAddressType = List.of(FulfillmentServiceType.TEMU_ONLINE_LABEL,
				FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE);
		return !noNeedAddressType.contains(fulfillmentServiceType);
	}

	/**
	 * 是否需要邮箱信息
	 * @return boolean
	 */
	protected boolean isNeedEmail(FulfillmentServiceType fulfillmentServiceType) {
		// 需要邮箱的发货服务类型
		List<FulfillmentServiceType> needEmailType = List.of(FulfillmentServiceType.WFS_MULTI_CHANNEL,
				FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE);
		return needEmailType.contains(fulfillmentServiceType);
	}
}
