package com.renpho.erp.oms.application.salemanagement.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.oms.infrastructure.common.excel.TimeZoneLocalDateTimeStringConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class SaleOrderImportExcel {

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHOP")
    private String storeName;


    /**
     * 店铺单号
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHOPORDER")
    private String channelOrderNo;


    /**
     * 参考单号
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.REFER")
    private String referOrderNo;


    /**
     * 样品订单标识
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.ORDER_TAGS")
    private String orderIdentification;

    /**
     * 币种
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.CURRENCY")
    private String currency;

    /**
     * 下单时间
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.ORDERED_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime orderedTime;

    /**
     * 付款时间
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PAID_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime paidTime;

    /**
     * 收件人姓名
     */
    @ExcelProperty(value = "RECIPIENT_NAME.PAID_TIME")
    private String recipientName;

    /**
     * 收件人手机号
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.RECIPIENT_PHONE")
    private String recipientPhone;

    /**
     * 邮政编码
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.POSTAL_CODE")
    private String postalCode;


    /**
     * 邮箱
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.EMAIL")
    private String email;

    /**
     * 国家或地区
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.COUNTRY_OR_REGION")
    private String countryCode;

    /**
     * 省份/州
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.STATE")
    private String province;

    /**
     * 城市
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.CITY")
    private String city;

    /**
     * 详细地址1
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.ADDRESS1")
    private String address1;

    /**
     * 详细地址2
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.ADDRESS2")
    private String address2;


    /**
     * 采购SKU，根据映射表关联出的本地PSKU（ERP里面的SKU）
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PSKU")
    private String psku;

    /**
     * FNSKU，根据映射表关联出的商品条码（库存识别码）
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.FNSKU")
    private String fnSku;

    /**
     * 应发数量（内部psku的应发货数量）
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.QUANTITY")
    private String quantityShipment;


    /**
     * 商品金额，未税、不含折扣、总价
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PRODUCT_PRINCIPAL")
    private BigDecimal productPrice;

    /**
     * 商品税额，原始的税额
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PRODUCT_TAX")
    private BigDecimal productTax;

    /**
     * 商品折扣，卖家折扣，不含平台折扣
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PRODUCT_DISCOUNT")
    private BigDecimal productDiscount;

    /**
     * 折扣税额，商品折扣导致的税额折扣
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.PRODUCT_TAX_DISCOUNT")
    private BigDecimal productDiscountTax;

    /**
     * 运费金额，未税、不含折扣、总价
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHIPPING_CHARGE")
    private BigDecimal freightAmount;

    /**
     * 运费税额，原始的税额
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHIPPING_TAX")
    private BigDecimal freightTax;

    /**
     * 运费折扣，卖家折扣，不含平台折扣
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHIPPING_DISCOUNT")
    private BigDecimal freightDiscount;

    /**
     * 运费折扣税，运费折扣导致的税额折扣
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHIPPING_TAX_DISCOUNT")
    private BigDecimal freightDiscountTax;

    /**
     * 发货仓库
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.WAREHOUSE")
    private String warehouseCode;

    /**
     * 仓库单号
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.WAREHOUSE_ORDER")
    private String warehouseOrderNo;

    /**
     * 承运商
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.CARRIER")
    private String carrier;

    /**
     * 跟踪号
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.TRACKING_NO")
    private String trackingNo;

    /**
     * 发货时间，第一次发货的时间
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.SHIPPED_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime shippedTime;

    /**
     * 签收时间，最后一次签收的时间
     */
    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.DELIVERED_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime deliveredTime;


    @ExcelProperty(value = "SALE_ORDER_EXCEL_HEADER.ERRORMESSAGE")
    private String erroMessage;
}


