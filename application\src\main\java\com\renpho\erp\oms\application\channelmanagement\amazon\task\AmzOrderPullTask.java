package com.renpho.erp.oms.application.channelmanagement.amazon.task;

import cn.hutool.core.collection.CollectionUtil;

import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderPullService;
import com.renpho.erp.oms.application.channelmanagement.common.dto.PullOrerParam;
import com.renpho.erp.oms.application.channelmanagement.common.enums.PullMode;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Component;

/**
 * 亚马逊拉取订单定时任务
 */
@Component
@AllArgsConstructor
public class AmzOrderPullTask {
	private final AmzOrderPullService amzOrderPullService;

	@XxlJob("AmzOrderPullTask")
	public void amzOrderPullTask() throws Exception {
		// 获取参数
		String param = XxlJobHelper.getJobParam();
		if (StringUtils.isNotEmpty(param)) {
			XxlJobHelper.log("亚马逊主动拉取订单参数: {}", param);
			// 构建参数
			PullOrerParam pullOrerParam = JsonUtils.jsonToObject(param, PullOrerParam.class);
			if (CollectionUtil.isEmpty(pullOrerParam.getSellerIds())) {
				XxlJobHelper.log("手动拉取输入卖家参数为空");
				return;
			}
			boolean timeRange = StringUtils.isNotEmpty(pullOrerParam.getLastUpdateTimeAfterStr())
					&& StringUtils.isNotEmpty(pullOrerParam.getLastUpdateTimeBeforeStr());
			if (!timeRange && CollectionUtil.isEmpty(pullOrerParam.getOrderIds())) {
				XxlJobHelper.log("手动拉取输入时间范围或卖家信息为空");
				return;
			}

			amzOrderPullService.pullOrder(pullOrerParam, ChannelCode.AMZ_CHANNEL_CODE, PullMode.ASYNC);

		}
		else {
			// 获取分片参数
			Integer shardIndex = XxlJobHelper.getShardIndex(); // 当前分片
			Integer shardTotal = XxlJobHelper.getShardTotal(); // 总分片数

			// 将分片参数存入ThreadLocal
			JobShardContext.JobShardInfo shardInfo = new JobShardContext.JobShardInfo();
			shardInfo.setShardIndex(shardIndex);
			shardInfo.setShardTotal(shardTotal);
			JobShardContext.setShardInfo(shardInfo);
			try {
				amzOrderPullService.pullOrder(null, ChannelCode.AMZ_CHANNEL_CODE, PullMode.ASYNC);

			}
			finally {
				// 清除ThreadLocal中的分片信息
				JobShardContext.clear();
			}
		}

	}

}
