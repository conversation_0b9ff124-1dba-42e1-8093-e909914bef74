package com.renpho.erp.oms.domain.vc.order.converter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;

import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.model.*;

/**
 * VC订单修改-转换者
 * <AUTHOR>
 */
public class VcOrderModifyConverter {

	private VcOrderModifyConverter() {
	}

	/**
	 * 生成新的VC聚合根
	 *
	 * @param amzVcOrder 亚马逊VC订单
	 * @param oldVcOrder DB的VC订单
	 * @return VcOrderAggRoot 新的VC聚合根
	 */
	public static VcOrderAggRoot convertOrder(AmzVcOrder amzVcOrder, VcOrderAggRoot oldVcOrder) {
		// 亚马逊订单详情
		AmzVcOrderDetails orderDetails = Optional.ofNullable(amzVcOrder.getOrderDetails())
			.orElseThrow(() -> DomainException.of("Amz orderDetails is null"));
		// 生成新的订单商品列表
		List<VcOrderItem> newItems = buildItems(amzVcOrder, oldVcOrder.getItems());
		// 生成新的VC聚合根
		return VcOrderAggRoot.builder()
			// 订单id
			.id(oldVcOrder.getId())
			// 订单状态：此处设为DB的旧值，计算状态时需用到（计算后会新赋值）
			.orderStatus(oldVcOrder.getOrderStatus())
			// 接单子状态：此处设为DB的旧值，计算状态时需用到（计算后会新赋值）
			.acceptedStatus(oldVcOrder.getAcceptedStatus())
			// 业务类型：此处设为DB的旧值，计算状态时需用到
			.businessType(oldVcOrder.getBusinessType())
			// 接单时间
			.acceptedTime(Optional.ofNullable(oldVcOrder.getAcceptedTime()).orElse(amzVcOrder.getAcknowledgementDate()))
			// 交货窗口
			.shippingWindow(orderDetails.getShippingWindow())
			// 订单商品列表
			.items(newItems)
			.build();
	}

	/**
	 * 转换订单商品行-新的接受数量Map
	 * @param amzVcOrder 亚马逊VC订单
	 * @param oldVcOrder 旧的VC聚合根
	 * @return Map，key为orderItemId,value为acceptedQuantity
	 */
	public static @NotNull Map<Long, Integer> convertAcceptedItems(AmzVcOrder amzVcOrder, VcOrderAggRoot oldVcOrder) {
		// 新的接受数量Map，key为orderItemId,value为acceptedQuantity
		return oldVcOrder.getItems()
			.stream()
			.collect(Collectors.toMap(VcOrderItem::getId, i -> amzVcOrder.getAcceptedQuantity(i.getChannelOrderLineNo())));

	}

	/**
	 * 构建订单商品列表
	 *
	 * @param amzVcOrder Amazon VC订单商品列表
	 * @return List<VcOrderItem> DB-VC订单商品列表
	 * @throws DomainException 当商品列表为空时抛出异常
	 */
	private static List<VcOrderItem> buildItems(AmzVcOrder amzVcOrder, List<VcOrderItem> orderItems) {
		return orderItems.stream()
			.map(i -> VcOrderItem.builder()
				// 订单商品行id
				.id(i.getId())
				// 下单数量，此处设值，为了后续重新计算金额
				.orderedQuantity(i.getOrderedQuantity())
				// 接受数量，此处设为DB的旧值，计算订单状态时会使用旧值，算完状态后再赋为新值
				.acceptedQuantity(i.getAcceptedQuantity())
				// 拒绝数量
				.rejectedQuantity(amzVcOrder.getRejectedQuantity(i.getChannelOrderLineNo()))
				// 接收数量
				.receivedQuantity(amzVcOrder.getReceivedQuantity(i.getChannelOrderLineNo()))
				// 商品金额信息
				.amount(buildItemAmount(amzVcOrder, i.getChannelOrderLineNo()))
				.build())
			.filter(Objects::nonNull)
			.toList();
	}

	/**
	 * 构建订单商品金额信息
	 *
	 * @param amzVcOrder Amazon VC订单
	 * @param itemSequenceNumber 亚马逊VC订单行号
	 * @return VcOrderItemAmount 商品金额对象
	 */
	private static VcOrderItemAmount buildItemAmount(AmzVcOrder amzVcOrder, String itemSequenceNumber) {
		return VcOrderItemAmount.builder()
			// 货币，无需修改
			// 单价：取netCost中的amount作为单价
			.unitPrice(amzVcOrder.getNetCostAmount(itemSequenceNumber))
			// 税额和小计暂不设置，后续业务流程中计算
			.build();
	}
}
