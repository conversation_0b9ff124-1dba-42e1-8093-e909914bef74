package com.renpho.erp.oms.domain.vc.order.converter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;

import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.entity.VcOrderItem;
import com.renpho.erp.oms.domain.vc.order.valueobject.AmzVcOrderDetails;
import com.renpho.erp.oms.domain.vc.order.valueobject.VcOrderItemAmount;

/**
 * VC订单修改-转换者
 * <AUTHOR>
 */
public class VcOrderModifyConverter {

	private VcOrderModifyConverter() {
	}

	/**
	 * 生成新的VC聚合根
	 *
	 * @param amzVcOrder 亚马逊VC订单
	 * @param oldVcOrder DB的VC订单
	 * @return VcOrderAggRoot 新的VC聚合根
	 */
	public static VcOrderAggRoot convertOrder(AmzVcOrderAggRoot amzVcOrder, VcOrderAggRoot oldVcOrder) {
		// 亚马逊订单详情
		AmzVcOrderDetails orderDetails = Optional.ofNullable(amzVcOrder.getOrderDetails())
			.orElseThrow(() -> DomainException.of("Amz orderDetails is null"));
		// 生成新的订单商品列表
		List<VcOrderItem> newItems = buildItems(amzVcOrder, oldVcOrder.getItems());
		// 生成新的VC聚合根
		return VcOrderAggRoot.builder()
			// 订单id
			.id(oldVcOrder.getId())
			// 订单状态：此处设为DB的旧值，计算状态时-会重新赋值
			.orderStatus(oldVcOrder.getOrderStatus())
			// 交货窗口
			.shippingWindow(orderDetails.getShippingWindow())
			// 订单商品列表
			.items(newItems)
			.build();
	}

	/**
	 * 转换订单商品行-接受数量Map
	 * @param amzVcOrder 亚马逊VC订单
	 * @param newVcOrder 新的VC聚合根
	 * @return Map，key为orderItemId,value为acceptedQuantity
	 */
	public static @NotNull Map<Long, Integer> convertAcceptedItems(AmzVcOrderAggRoot amzVcOrder, VcOrderAggRoot newVcOrder) {
		// 接受数量Map，key为orderItemId,value为acceptedQuantity
		return newVcOrder.getItems()
			.stream()
			.collect(Collectors.toMap(VcOrderItem::getId, i -> amzVcOrder.getAcceptedQuantity(i.getChannelOrderLineNo())));

	}

	/**
	 * 构建订单商品列表
	 *
	 * @param amzVcOrder Amazon VC订单商品列表
	 * @return List<VcOrderItem> DB-VC订单商品列表
	 * @throws DomainException 当商品列表为空时抛出异常
	 */
	private static List<VcOrderItem> buildItems(AmzVcOrderAggRoot amzVcOrder, List<VcOrderItem> orderItems) {
		return orderItems.stream()
			.map(i -> VcOrderItem.builder()
				// 订单商品行id
				.id(i.getId())
				// vc订单id
				.orderId(i.getOrderId())
				// 渠道订单行号：Amazon商品行号
				.channelOrderLineNo(i.getChannelOrderLineNo())
				// 接受数量，此处设为DB的旧值，计算订单状态时会使用旧值，算完状态后再赋为新值
				.acceptedQuantity(i.getAcceptedQuantity())
				// 拒绝数量
				.rejectedQuantity(amzVcOrder.getRejectedQuantity(i.getChannelOrderLineNo()))
				// 接收数量
				.receivedQuantity(amzVcOrder.getReceivedQuantity(i.getChannelOrderLineNo()))
				// 商品金额信息
				.amount(buildItemAmount(amzVcOrder, i.getChannelOrderLineNo()))
				.build())
			.filter(Objects::nonNull)
			.toList();
	}

	/**
	 * 构建订单商品金额信息
	 *
	 * @param amzVcOrder Amazon VC订单
	 * @param itemSequenceNumber 亚马逊VC订单行号
	 * @return VcOrderItemAmount 商品金额对象
	 */
	private static VcOrderItemAmount buildItemAmount(AmzVcOrderAggRoot amzVcOrder, String itemSequenceNumber) {
		return VcOrderItemAmount.builder()
			// 货币，无需修改
			// 单价：取netCost中的amount作为单价
			.unitPrice(amzVcOrder.getNetCostAmount(itemSequenceNumber))
			// 税额和小计暂不设置，后续业务流程中计算
			.build();
	}
}
