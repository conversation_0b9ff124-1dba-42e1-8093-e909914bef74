package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 客户联系人命令
 */
@Data
public class CustomerContactCommand {

    private Long id;

    /**
     * 联系人姓名
     */
    @NotEmpty(message = "CUSTOMER_CREATE_CONTACT_NAME_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_CREATE_CONTACT_NAME_TOO_LONG")
    private String contactName;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @Pattern( regexp = "^$|^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
            message = "SALE_ORDER_CREATE_EMAIL_PATTERN_ERROR")
    private String contactEmail;

    /**
     * 联系人职位
     */
    @NotEmpty(message = "CUSTOMER_CREATE_CONTACT_POSITION_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_CREATE_CONTACT_POSITION_TOO_LONG")
    private String contactPosition;

    @Size(max = 1024, message = "CUSTOMER_CREATE_REMARK_TOO_LONG")
    private String remark;

    /**
     * 是否默认
     */
    @NotNull(message = "CUSTOMER_CREATE_CONTACT_IS_DEFAULT_REQUIRE")
    private Boolean isDefault;

} 