package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

/**
 * @desc: 销售公司
 * @time: 2025-04-30 11:32:31
 * @author: <PERSON>na
 */
@Data
public class SalesCompanyVO {
	/**
	 * 公司ID
	 */
	private Integer id;

	/**
	 * 公司名称
	 */
	private String companyName;

	/**
	 * 公司简称
	 */
	private String shortName;

	/**
	 * 注册国家/地区, 字典: registered_region
	 */
	private String registeredCountry;

	/**
	 * 注册国家/地区值
	 */
	private String registeredCountryName;

	/**
	 * 状态，0=InActive，1=Active
	 */
	private Integer status;
}
