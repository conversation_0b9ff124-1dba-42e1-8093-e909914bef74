package com.renpho.erp.oms.application.returnorder.vo;

import lombok.Data;

/**
 * @desc: 退货订单明细行
 * @time: 2025-03-20 10:55:08
 * @author: Alina
 */
@Data
public class ReturnOrderItemVO {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * item编码，平台给的标识当前商品的ID
	 */
	private String item;

	/**
	 * 退货订单主键
	 */
	private String returnOrderId;

	/**
	 * 销售SKU，平台给的销售SKU字段
	 */
	private String msku;

	/**
	 * 采购SKU，根据映射表关联出的本地PSKU（ERP里面的SKU）
	 */
	private String psku;

	/**
	 * FNSKU，根据映射表关联出的商品条码（库存识别码）
	 */
	private String fnSku;

	/**
	 * 应发数量（内部psku的应发货数量）
	 */
	private Integer quantityShipment;

	/**
	 * 退货数量
	 */
	private Integer returnQuantity;

	/**
	 * 图片ID，来自PDS，用于换取临时访问地址
	 */
	private String imageId;

	/**
	 * 图片url，临时访问地址
	 */
	private String imageUrl;

}
