package com.renpho.erp.oms.application.salemanagement.converter.fulfillment;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.convert.fulfillment.ChannelFulfillmentConvertServiceFactory;

/**
 * 渠道履约转换器工厂类
 * <AUTHOR>
 */
public class ChannelFulfillmentConverterFactory {

	private static final Map<ChannelFulfillmentConvertServiceFactory.StrategyKey, ChannelConvertFulfillmentStrategy> STRATEGY_MAP = new HashMap<>(
			16);

	/**
	 * 获取
	 * @param channelType 入参
	 * @return ChannelConvertFulfillmentStrategy
	 */
	public static ChannelConvertFulfillmentStrategy get(SaleChannelType channelType, FulfillmentType fulfillmentType) {
		Assert.notNull(channelType, "获取渠道履约转换器，渠道类型不能为空");
		Assert.notNull(fulfillmentType, "获取渠道履约转换器，发货类型不能为空");
		ChannelFulfillmentConvertServiceFactory.StrategyKey strategyKey = ChannelFulfillmentConvertServiceFactory.StrategyKey
			.newOf(channelType, fulfillmentType);
		if (STRATEGY_MAP.containsKey(strategyKey)) {
			return STRATEGY_MAP.get(strategyKey);
		}
		throw new RuntimeException(MessageFormat.format("获取渠道履约转换器，找不到 {0}-{1} 转换器", channelType.getName(), fulfillmentType.getName()));
	}

	/**
	 * 注册.
	 * @param channelType 渠道类型
	 * @param channelConvertFulfillmentStrategy 策略实体
	 */
	public static void register(SaleChannelType channelType, FulfillmentType fulfillmentType,
			ChannelConvertFulfillmentStrategy channelConvertFulfillmentStrategy) {
		Assert.notNull(channelType, "");
		Assert.notNull(fulfillmentType, "");
		Assert.notNull(channelConvertFulfillmentStrategy, "");
		ChannelFulfillmentConvertServiceFactory.StrategyKey strategyKey = ChannelFulfillmentConvertServiceFactory.StrategyKey
			.newOf(channelType, fulfillmentType);
		STRATEGY_MAP.put(strategyKey, channelConvertFulfillmentStrategy);
	}

}
