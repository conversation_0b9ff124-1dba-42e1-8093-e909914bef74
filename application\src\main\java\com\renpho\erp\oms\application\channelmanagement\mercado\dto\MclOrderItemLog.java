package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class MclOrderItemLog {
	/**
	 * 美客多商品id
	 */
	private String itemId;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 类目id
	 */
	private String categoryId;

	/**
	 * 变化id
	 */
	private Long variationId;

	/**
	 * 卖家自定义属性
	 */
	private String sellerCustomField;

	/**
	 * 变化属性列表json
	 */
	private String variationAttributes;

	/**
	 * 保修单
	 */
	private String warranty;

	/**
	 * 状况
	 */
	private String itemCondition;

	/**
	 * seller SKU
	 */
	private String sellerSku;

	/**
	 * 父类商品id
	 */
	private String parentItemId;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 全单价
	 */
	private BigDecimal fullUnitPrice;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 制造天数
	 */
	private Integer manufacturingDays;

	/**
	 * 销售金额
	 */
	private BigDecimal saleFee;

	/**
	 * 基准汇率
	 */
	private BigDecimal baseExchangeRate;
}
