package com.renpho.erp.oms.application.platform.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PlatformOrderVO {
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	private Integer storeId;

	@JsonIgnore
	private String sellerId;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * 订单状态
	 */
	private String orderStatus;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 销售渠道
	 */
	private String salesChannel;

	private String channelCode;

	private String orderItemId;

	/**
	 * 商品sku
	 */
	private String sellerSku;
	/**
	 * 商品数量
	 */
	private Integer itemQuality;

	/**
	 * 订单创建时间
	 */
	private LocalDateTime orderCreateTime;

	/**
	 * 订单更新时间
	 */
	private LocalDateTime orderUpdateTime;

	/**
	 * 系统创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 系统更新时间
	 */
	private LocalDateTime updateTime;
}
