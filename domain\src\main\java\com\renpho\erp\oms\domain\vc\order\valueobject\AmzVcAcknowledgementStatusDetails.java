package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.time.LocalDateTime;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC确认状态详情值对象
 * 基于Amazon SP-API acknowledgementStatusDetails模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcAcknowledgementStatusDetails {

	/**
	 * 确认日期
	 * 对应API字段：acknowledgementDate
	 * 供应商确认行项目的日期，必须采用ISO-8601日期/时间格式
	 */
	private LocalDateTime acknowledgementDate;

	/**
	 * 接受数量详情
	 * 对应API字段：acceptedQuantity
	 * 订购数量的详细信息
	 */
	private AmzVcQuantityDetail acceptedQuantity;

	/**
	 * 拒绝数量详情
	 * 对应API字段：rejectedQuantity
	 * 订购数量的详细信息
	 */
	private AmzVcQuantityDetail rejectedQuantity;

}
