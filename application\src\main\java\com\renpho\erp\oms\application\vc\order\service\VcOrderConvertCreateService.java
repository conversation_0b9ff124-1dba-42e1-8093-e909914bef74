package com.renpho.erp.oms.application.vc.order.service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.VcSkuMappingVO;
import com.renpho.erp.oms.domain.vc.order.VcOrderCreateDomainService;
import com.renpho.erp.oms.domain.vc.order.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.converter.VcOrderCreateConverter;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.model.VcProduct;
import com.renpho.erp.oms.domain.vc.order.model.VcStore;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDetailDTO;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;

import lombok.RequiredArgsConstructor;

/**
 * VC订单转换创建-应用服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class VcOrderConvertCreateService {

	private final VcOrderCreateDomainService vcOrderCreateDomainService;
	private final VcOrderRepository vcOrderRepository;
	private final SkuMappingRepository skuMappingRepository;
	private final StoreClient storeClient;
	private final ProductClient productClient;

	/**
	 * 转换-创建VC订单
	 * @param amzVcOrder 亚马逊VC订单
	 */
	public void create(AmzVcOrder amzVcOrder) {
		// 查询店铺信息
		StoreDTO storeDTO = storeClient.getByStoreId(amzVcOrder.getStoreId());
		// 查询SKU映射及商品信息
		Map<String, VcProduct> productMap = this.findProduct(amzVcOrder);
		// 转换生成VC订单
		VcOrderAggRoot vcOrder = vcOrderCreateDomainService.convertAndCreate(amzVcOrder, this.buildValueObject(storeDTO, productMap));
		// 仓储保存
		vcOrderRepository.save(vcOrder);
	}

	/**
	 * 查询SKU映射及商品信息
	 * @param amzVcOrder 亚马逊VC订单
	 * @return Map<String, VcProduct> ASIN到商品信息的映射
	 */
	private Map<String, VcProduct> findProduct(AmzVcOrder amzVcOrder) {
		// 查询SKU映射信息
		List<VcSkuMappingVO> skuMappingList = skuMappingRepository.listVcSkuMappingByStoreIdAndAsinList(amzVcOrder.getStoreId(),
				amzVcOrder.getAsinList());
		// 提取psku集合
		Set<String> pskuSet = skuMappingList.stream()
			.map(VcSkuMappingVO::getPsku)
			.filter(StringUtils::isNotBlank)
			.collect(Collectors.toSet());
		// 查询采购skuMap,key为psku
		Map<String, PurchaseProductDetailDTO> pskuToProductMap = productClient.findPurchaseProductMap(pskuSet);
		// 构建ASIN到VcProduct的映射，key为ASIN
		return this.buildAsinToProductMap(skuMappingList, pskuToProductMap);
	}

	/**
	 * 构建ASIN到VcProduct的映射
	 * @param skuMappingList SKU映射信息
	 * @param pskuToProductMap 采购skuMap,key为psku
	 * @return Map，key为ASIN
	 */
	private @NotNull Map<String, VcProduct> buildAsinToProductMap(List<VcSkuMappingVO> skuMappingList,
			Map<String, PurchaseProductDetailDTO> pskuToProductMap) {
		Map<String, VcProduct> asinToProductMap = new HashMap<>();
		for (VcSkuMappingVO skuMapping : skuMappingList) {
			String asin = skuMapping.getAsin();
			String psku = skuMapping.getPsku();
			if (StringUtils.isBlank(asin) || StringUtils.isBlank(psku)) {
				continue;
			}
			PurchaseProductDetailDTO productDetail = pskuToProductMap.get(psku);
			if (productDetail != null) {
				// 转换为VcProduct
				VcProduct vcProduct = convertToVcProduct(skuMapping, productDetail);
				asinToProductMap.put(asin, vcProduct);
			}
		}
		return asinToProductMap;
	}

	/**
	 * 转换SKU映射和商品详情为VcProduct
	 * @param skuMapping SKU映射信息
	 * @param productDetail 商品详情
	 * @return VcProduct
	 */
	private VcProduct convertToVcProduct(VcSkuMappingVO skuMapping, PurchaseProductDetailDTO productDetail) {
		return VcProduct.builder()
			.psku(skuMapping.getPsku())
			.barcode(skuMapping.getFnsku())
			.numberOfUnitsPerBox(productDetail.getNumberOfUnitsPerBox())
			.imageId(productDetail.getImageId())
			.build();
	}

	/**
	 * 构建领域服务所需的值对象 将应用层的DTO对象转换为领域服务所需的值对象
	 * @param storeDTO 店铺信息DTO
	 * @param productMap ASIN到商品信息的映射
	 * @return VcOrderCreateConverter.ValueObject 领域服务值对象
	 */
	private VcOrderCreateConverter.ValueObject buildValueObject(StoreDTO storeDTO, Map<String, VcProduct> productMap) {
		return VcOrderCreateConverter.ValueObject.builder().store(this.buildStore(storeDTO)).asinToProductMap(productMap).build();
	}

	/**
	 * 构建店铺值对象 将店铺DTO转换为领域层的VcStore值对象
	 * @param storeDTO 店铺DTO，包含店铺的基本信息和配置
	 * @return VcStore 店铺值对象，如果输入为null则返回null
	 */
	private VcStore buildStore(StoreDTO storeDTO) {
		return VcStore.builder()
			// 店铺ID
			.storeId(storeDTO.getStoreId())
			// 店铺名称
			.storeName(storeDTO.getStoreName())
			// 站点编码
			.siteCode(storeDTO.getSiteCode())
			.build();
	}
}
