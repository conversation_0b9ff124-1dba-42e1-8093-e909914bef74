package com.renpho.erp.oms.application.ruleconfig.skuMapping.optlog;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.SkuMappingService;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.SkuMappingVO;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.renpho.erp.oplog.log.SnapshotDatatSource;

import lombok.AllArgsConstructor;

/**
 *
 * @since 2024/9/24
 */
@Component
@AllArgsConstructor
public class SkuMappingAddSnapSource implements SnapshotDatatSource {

	private final SkuMappingService skuMappingService;

	@Override
	public JSONObject getOldData(Object[] args) {
		return new JSONObject();
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		SkuMappingVO skuMappingVO = skuMappingService.getById(result.getObject("data", Long.class)).getData();
		return JSON.parseObject(JSON.toJSONString(skuMappingVO));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
