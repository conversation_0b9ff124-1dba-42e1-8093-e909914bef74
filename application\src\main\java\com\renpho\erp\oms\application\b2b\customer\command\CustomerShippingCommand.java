package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 客户收货信息命令
 */
@Data
public class CustomerShippingCommand {

    private Long id;

    /**
     * 收件人
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_ATTN_TO_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_SHIPPING_ATTN_TO_TOO_LONG")
    private String attnTo;

    /**
     * 收货公司名称
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_COMPANY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_SHIPPING_COMPANY_TOO_LONG")
    private String shippingCompany;

    /**
     * 地址
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_ADDRESS_REQUIRE")
    @Size(max = 255, message = "CUSTOMER_SHIPPING_ADDRESS_TOO_LONG")
    private String address;

    /**
     * 邮编
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_ZIP_CODE_REQUIRE")
    @Size(max = 20, message = "CUSTOMER_SHIPPING_ZIP_CODE_TOO_LONG")
    private String zipCode;

    /**
     * 城市
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_CITY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_SHIPPING_CITY_TOO_LONG")
    private String city;

    /**
     * 国家
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_COUNTRY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_SHIPPING_COUNTRY_TOO_LONG")
    private String country;

    /**
     * 联系电话
     */
    @NotEmpty(message = "CUSTOMER_SHIPPING_CONTACT_NUMBER_REQUIRE")
    @Size(max = 50, message = "CUSTOMER_SHIPPING_CONTACT_NUMBER_TOO_LONG")
    private String contactNumber;
} 