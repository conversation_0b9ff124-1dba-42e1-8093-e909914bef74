package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单发票表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order_invoice")
public class VcOrderInvoicePO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 发票号
	 */
	private String invoiceNo;

	/**
	 * 开票日期
	 */
	private LocalDate invoiceDate;

	/**
	 * 税率，0.2代表20%
	 */
	private BigDecimal taxRate;

	/**
	 * 发票金额
	 */
	private BigDecimal tax;

}
