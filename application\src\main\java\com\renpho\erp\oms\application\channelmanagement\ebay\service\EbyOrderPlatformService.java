package com.renpho.erp.oms.application.channelmanagement.ebay.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.channelmanagement.ebay.convert.EbyOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.ebay.convert.EbyOrderLineItemAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayCancelRequestVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayOrderItemVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayPaymentDetailVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbyOrderVO;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrder;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.ebay.repository.EbyOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.ebay.mapper.EbyOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.ebay.po.EbyOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @desc:
 * @time: 2025-04-11 11:12:18
 * @author: Alina
 */
@AllArgsConstructor
@Service(ChannelCode.EBY_CHANNEL_CODE)
@Slf4j
public class EbyOrderPlatformService extends AbstractDefaultPlatfomService {
	private final EbyOrderRepository ebyOrderRepository;
	private final PlatformOrderConvertor platformOrderConvertor;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private final EbyOrderMapper ebyOrderMapper;

	@Override
	protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<EbyOrderPage> page = ebyOrderRepository.page(platformPageQuery);
		List<EbyOrderPage> ebyOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(ebyOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}
		List<PlatformOrderVO> records = ebyOrderPageList.stream().map(platformOrderConvertor::ebayOrderToVO).collect(Collectors.toList());
		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		EbyOrder ebyOrder = ebyOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(ebyOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		EbyOrderVO ebyOrderVO = EbyOrderAppConvertor.toVO(ebyOrder);
		// 查询店铺
		ebyOrderVO.setStoreName(getStoreName(ebyOrder.getStoreId()));
		return R.success(ebyOrderVO);
	}

	/**
	 * Ebay 订单行
	 * @param orderId 订单id
	 * @return R<List<temOrderItemService>>
	 */
	public R<List<EbayOrderItemVO>> listItemByOrderId(Long orderId) {
		EbyOrderAggRoot ebyOrderAggRoot = ebyOrderRepository.getById(orderId);
		if (Objects.isNull(ebyOrderAggRoot)) {
			throw new BusinessException("DATA_NOT_EXITS");
		}
		return R.success(ebyOrderAggRoot.getEbyOrderLineItemList().stream().map(EbyOrderLineItemAppConvertor::toVO).collect(Collectors.toList()));
	}

	/**
	 * Ebay 付款明细
	 * @param orderId 订单id
	 * @return List<EbayPaymentDetailVO>
	 */
	public List<EbayPaymentDetailVO> listPaymentDetailByOrderId(Long orderId) {
		EbyOrderAggRoot ebyOrderAggRoot = ebyOrderRepository.getById(orderId);
		if (Objects.isNull(ebyOrderAggRoot)) {
			throw new BusinessException("DATA_NOT_EXITS");
		}
		return EbyOrderLineItemAppConvertor.toPaymentDetailVO(ebyOrderAggRoot);
	}

	/**
	 * Ebay 取消状态列表
	 * @param orderId 订单id
	 * @return List<EbayCancelRequestVO>
	 */
	public List<EbayCancelRequestVO> listCancelRequestByOrderId(Long orderId) {
		EbyOrderAggRoot ebyOrderAggRoot = ebyOrderRepository.getById(orderId);
		if (Objects.isNull(ebyOrderAggRoot)) {
			throw new BusinessException("DATA_NOT_EXITS");
		}
		return EbyOrderLineItemAppConvertor.toCancelRequestVO(ebyOrderAggRoot);
	}

	/**
	 * 查询监控的平台订单
	 * @param monitorPlatformOrderQuery
	 * @return
	 */
	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> monitorPlatformOrderDtos = ebyOrderMapper.selectList(Wrappers.<EbyOrderPO>lambdaQuery()
						.select(EbyOrderPO::getEbyOrderId, EbyOrderPO::getLastMpdsSyncOrderTime, EbyOrderPO::getStoreId)
						.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, EbyOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
						.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, EbyOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
				.stream()
				.map(x -> monitorPlatformOrderConvertor.ebayOrderToDto(x))
				.collect(Collectors.toList());
		return monitorPlatformOrderDtos;
	}
}
