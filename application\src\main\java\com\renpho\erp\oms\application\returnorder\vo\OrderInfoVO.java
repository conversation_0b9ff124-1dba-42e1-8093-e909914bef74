package com.renpho.erp.oms.application.returnorder.vo;

import lombok.Data;

import java.util.List;

/**
 * @desc: 根据店铺Id，店铺单号获取销售订单详情 出参
 * @time: 2025-03-20 13:58:23
 * @author: <PERSON><PERSON>
 */
@Data
public class OrderInfoVO {

	/**
	 * 渠道编码
	 */
	private String channelCode;

	/**
	 * 销售渠道名称
	 */
	private String channelName;

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 店铺单号（渠道单号），各平台的唯一单号，在平台维度为（店铺+店铺单号）唯一，在OMS不唯一（因为拆单）
	 */
	private String channelOrderNo;

	/**
	 * 最新一条发货审核的发货仓库id （如果存在的话）
	 */
	private Integer warehouseId;

	/**
	 * 订单明细行
	 */
	List<OrderItemInfoVO> items;

}
