package com.renpho.erp.oms.application.listingmanagement.job;

import com.renpho.erp.oms.application.listingmanagement.dto.ListingCompensateParam;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingPullParam;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;

import java.util.Objects;
import java.util.Set;

/**
 * Listing Job帮助器
 * <AUTHOR>
 */
public final class ListingJobHelper {

	private ListingJobHelper() {
	}

	/**
	 * JOB参数获取销售渠道编码
	 * @return Set
	 */
	static Set<String> getSalesChannelCodeSetByPullParam() {
		// 获取参数
		String param = XxlJobHelper.getJobParam();
		// 销售渠道编码集合
		Set<String> salesChannelCodeSet = null;
		if (Objects.nonNull(param)) {
			XxlJobHelper.log("Listing拉取参数: {}", param);
			ListingPullParam listingPullParam = JSONKit.parseObject(param, ListingPullParam.class);
			// 销售渠道编码集合入参
			salesChannelCodeSet = listingPullParam.getSalesChannelCodeSet();
		}
		return salesChannelCodeSet;
	}

	/**
	 * 获取JOB-Listing补偿参数
	 * @return Set
	 */
	static ListingCompensateParam getListingCompensateParam() {
		// 获取参数
		String param = XxlJobHelper.getJobParam();
		if (Objects.nonNull(param)) {
			XxlJobHelper.log("Listing拉取参数: {}", param);
			return JSONKit.parseObject(param, ListingCompensateParam.class);
		}
		return new ListingCompensateParam();
	}

}
