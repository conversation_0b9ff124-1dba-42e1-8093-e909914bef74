package com.renpho.erp.oms.infrastructure.persistence.receiptOrder.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.oms.domain.receiptOrder.model.B2bOrderBaseInfoDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageQueryDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po.B2bReceiptOrderPO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/20 10:28
 */
public interface B2bReceiptOrderMapper extends BaseMapper<B2bReceiptOrderPO> {


    /**
     * 获取b2b订单基础信息
     * @param orderNo
     * @return
     */
    B2bOrderBaseInfoDto getB2bOrderBaseInfoDto(@Param(value = "orderNo") String orderNo);

    Page<B2BReceiptOrderPageDto> page(@Param("queryDto") B2BReceiptOrderPageQueryDto queryDto, Page<B2BReceiptOrderPageDto> page);
}
