package com.renpho.erp.oms.application.channelmanagement.monitor;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

/**
 * 渠道订单监视器-工厂
 * <AUTHOR>
 */
public class ChannelOrderMonitorFactory {

	private ChannelOrderMonitorFactory() {
	}

	private static final Map<String, ChannelOrderMonitor> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 获取
	 * @param channelCode 渠道编码
	 * @return ChannelConvertSaleOrderStrategy
	 */
	public static ChannelOrderMonitor get(String channelCode) {
		Assert.notNull(channelCode, "获取渠道订单监视器，渠道编码不能为空");
		if (STRATEGY_MAP.containsKey(channelCode)) {
			return STRATEGY_MAP.get(channelCode);
		}
		throw new RuntimeException(MessageFormat.format("获取渠道订单监视器，找不到 {0} ", channelCode));
	}

	/**
	 * 注册.
	 * @param channelCode 渠道编码
	 */
	public static void register(String channelCode, ChannelOrderMonitor channelOrderMonitor) {
		Assert.notNull(channelCode, "注册渠道订单监视器，渠道编码不能为空");
		STRATEGY_MAP.put(channelCode, channelOrderMonitor);
	}
}
