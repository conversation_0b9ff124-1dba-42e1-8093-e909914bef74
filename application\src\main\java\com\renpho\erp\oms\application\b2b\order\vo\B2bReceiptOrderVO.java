package com.renpho.erp.oms.application.b2b.order.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * B2B收款单VO
 */
@Data
public class B2bReceiptOrderVO {

    /**
     * 银行参考号
     */
    private String bankReferNo;

    /**
     * 客户付款时间
     */
    private LocalDateTime customerPaidTime;

    /**
     * 收款时间
     */
    private LocalDateTime receiptTime;

    /**
     * 客户付款金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal customerPaidAmount;

    /**
     * 收款金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal receiptAmount;

    /**
     * 费用金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal feesAmount;

    /**
     * 状态 1-待确认,2-已确认,3-已取消
     */
    private Integer status;

    /**
     * 账号名称（根据语言环境返回中文或英文）
     */
    private String accountName;
}
