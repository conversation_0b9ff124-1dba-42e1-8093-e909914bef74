package com.renpho.erp.oms.adapter.web.controller.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
public class SkuMappingExcelListener extends AnalysisEventListener<SkuMappingImportExcel> {
    private final List<SkuMappingImportExcel> skuMappingImportExcels = new ArrayList<>();
    /**
     * 中文表头
     */
    public static final Map<Integer, String> importCNHeadMap = new HashMap<>();

    /**
     * 英文表头
     *
     * @param data
     * @param context
     */
    public static final Map<Integer, String> importUSHeadMap = new HashMap<>();

    static {
        importCNHeadMap.put(0, "店铺");
        importCNHeadMap.put(1, "销售SKU");
        importCNHeadMap.put(2, "FNSKU");
        importCNHeadMap.put(3, "ASIN");
        importCNHeadMap.put(4, "采购SKU");
        importCNHeadMap.put(5, "运营工号");
        importCNHeadMap.put(6, "发货方式（1平台发货 2自发货）");
        importCNHeadMap.put(7, "条形码商品数量");
        importCNHeadMap.put(8, "零售单价");
        importCNHeadMap.put(9, "统计FBA库存？（1是 0否）");

        importUSHeadMap.put(0, "Store");
        importUSHeadMap.put(1, "SellerSKU");
        importUSHeadMap.put(2, "FNSKU");
        importUSHeadMap.put(3, "ASIN");
        importUSHeadMap.put(4, "PurchaseSKU");
        importUSHeadMap.put(5, "OperatorNo");
        importUSHeadMap.put(6, "FulfillmentType (1 platform FulfillmentType 2 self FulfillmentType)");
        importUSHeadMap.put(7, "Fnskuquantity");
        importUSHeadMap.put(8, "Retailprice");
        importUSHeadMap.put(9, "Count FBA inventory? (1 yes 0 no)");
    }


    @Override
    public void invoke(SkuMappingImportExcel data, AnalysisContext context) {
        skuMappingImportExcels.add(data);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (headMap.equals(importCNHeadMap)) {
            // 国际化转换
            ExcelUtil.buildUpdateHeadAgain(context, importCNHeadMap, SkuMappingImportExcel.class);
        } else if (headMap.equals(importUSHeadMap)) {
            ExcelUtil.buildUpdateHeadAgain(context, importUSHeadMap, SkuMappingImportExcel.class);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("HEAD_NOT_MATCH"));
        }

    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("销售sku映射导入excel解析数据为", JsonUtils.toJson(skuMappingImportExcels));

    }
}
