package com.renpho.erp.oms.application.b2b.order.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * B2B客户订单欠款信息VO
 */
@Data
public class B2bCustomerOrderDebtVO {

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 转换后的订单总金额（按投保币种）
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal convertedTotalAmount;

    /**
     * 转换后的收款金额（按投保币种）
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal convertedReceiptAmount;

    /**
     * 欠款金额（订单总金额 - 收款金额）
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal debtAmount;
}
