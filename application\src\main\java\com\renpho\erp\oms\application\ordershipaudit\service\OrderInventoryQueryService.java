package com.renpho.erp.oms.application.ordershipaudit.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import com.renpho.erp.ims.client.feign.inventory.command.RemoteInventoryQueryCommand;
import com.renpho.erp.ims.client.feign.warehouse.vo.InventoryVo;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderItem;
import com.renpho.erp.oms.domain.salemanagement.Store;
import com.renpho.erp.oms.infrastructure.feign.ims.InventoryClient;

import lombok.RequiredArgsConstructor;

/**
 * 订单库存查询 应用服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class OrderInventoryQueryService {

	private final InventoryClient inventoryClient;

	/**
	 * 查询订单所有sku的库存是否充足
	 * @param orderAggRoot 订单聚合根
	 * @return boolean true代表库存充足
	 */
	public boolean queryInventory(SaleOrderAggRoot orderAggRoot, Integer warehouseId) {
		// 构建库存查询参数
		RemoteInventoryQueryCommand command = this.buildInventoryQueryCommand(orderAggRoot, warehouseId);
		// 查询ims库存
		List<InventoryVo> inventoryList = inventoryClient.queryInventory(command);
		// 检查订单所有sku的库存是否充足
		return this.checkInventory(orderAggRoot, inventoryList);
	}

	/**
	 * 检查订单所有sku的库存是否充足
	 * @param orderAggRoot 订单聚合根
	 * @param inventoryList 库存列表
	 * @return boolean
	 */
	private boolean checkInventory(SaleOrderAggRoot orderAggRoot, List<InventoryVo> inventoryList) {
		// 无库存
		if (CollectionUtils.isEmpty(inventoryList)) {
			return false;
		}
		// 订单行列表（非延保商品）
		List<SaleOrderItem> notExtendWarrantyItems = orderAggRoot.getNotExtendWarrantyItems();
		// 构建订单skuMap，key为PSKU+FNSKU，value为应发数量，（PSKU+FNSKU重复的需合并数量）
		Map<String, Integer> orderSkuMap = notExtendWarrantyItems.stream()
			.collect(Collectors.toMap(this::getSkuUniqueValue, SaleOrderItem::getQuantityShipment, Integer::sum));
		// 构建库存skuMap
		Map<String, Integer> skuInventoryMap = inventoryList.stream()
			.collect(Collectors.toMap(this::getSkuUniqueValue, InventoryVo::getAvailableQty, Integer::sum));
		// 检查订单所有sku的库存是否充足
		return orderSkuMap.entrySet().stream().allMatch(entry -> {
			// 库存在库可用数量
			Integer availableQty = skuInventoryMap.get(entry.getKey());
			// 检查库存是否充足（SKU的库存数量 >= 应发数量）
			return Objects.nonNull(availableQty) && availableQty >= entry.getValue();
		});
	}

	/**
	 * 构建库存查询参数
	 * @param orderAggRoot 订单聚合根
	 * @param warehouseId 仓库id
	 * @return RemoteInventoryQueryCommand
	 */
	private RemoteInventoryQueryCommand buildInventoryQueryCommand(SaleOrderAggRoot orderAggRoot, Integer warehouseId) {
		// 库存查询参数
		RemoteInventoryQueryCommand command = new RemoteInventoryQueryCommand();
		// 订单店铺信息
		Optional<Store> store = Optional.ofNullable(orderAggRoot.getStore());
		// 店铺id
		command.setStoreId(store.map(Store::getStoreId).orElseThrow(() -> DomainException.of("店铺id为空")));
		// 货主id
		command.setOwnerId(store.map(Store::getCompanyId).orElseThrow(() -> DomainException.of("货主id为空")));
		// 仓库id
		command.setWarehouseId(warehouseId);
		// 是否查共享库存，true
		command.setCheckShared(true);
		// sku列表（非延保商品）
		command.setSkuInfo(this.buildSkuList(orderAggRoot.getNotExtendWarrantyItems()));
		return command;
	}

	/**
	 * 构建sku列表（非延保商品）
	 * @param notExtendWarrantyItems 订单行列表（非延保商品）
	 * @return List
	 */
	private List<RemoteInventoryQueryCommand.SkuInfo> buildSkuList(List<SaleOrderItem> notExtendWarrantyItems) {
		// 构建sku列表（PSKU+FNSKU重复的，合并）
		return notExtendWarrantyItems.stream()
			.collect(Collectors.toMap(this::getSkuUniqueValue, this::buildSkuList, (s1, s2) -> s1))
			.values()
			.stream()
			.toList();
	}

	/**
	 * 构建sku信息
	 * @param item 订单行
	 * @return SkuInfo
	 */
	private @NotNull RemoteInventoryQueryCommand.SkuInfo buildSkuList(SaleOrderItem item) {
		RemoteInventoryQueryCommand.SkuInfo skuInfo = new RemoteInventoryQueryCommand.SkuInfo();
		skuInfo.setPsku(item.getPsku());
		skuInfo.setFnSku(item.getFnsku());
		return skuInfo;
	}

	/**
	 * 获取sku唯一值（PSKU+FNSKU）
	 * @param item 订单行
	 * @return String
	 */
	private @NotNull String getSkuUniqueValue(SaleOrderItem item) {
		return item.getPsku() + "-" + item.getFnsku();
	}

	/**
	 * 获取sku唯一值（PSKU+FNSKU）
	 * @param inventory 库存
	 * @return String
	 */
	private @NotNull String getSkuUniqueValue(InventoryVo inventory) {
		return inventory.getPSku() + "-" + inventory.getFnsku();
	}

}
