package com.renpho.erp.oms.application.b2b.order.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.renpho.erp.oms.domain.b2b.order.B2bOrderAggRoot;
import com.renpho.erp.oms.domain.b2b.order.entity.B2bOrderAmount;
import com.renpho.erp.oms.domain.b2b.order.entity.B2bOrderCustomer;
import com.renpho.erp.oms.domain.b2b.order.repository.B2bOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.service.B2bOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.bpm.api.dto.KeyAndValueDto;
import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.oms.application.b2b.order.convert.B2bOrderAuditFormConvert;
import com.renpho.erp.oms.application.b2b.order.dto.OrderAuditFormDto;
import com.renpho.erp.oms.application.b2b.order.vo.B2bCustomerOrderDebtVO;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderStatusEnum;
import com.renpho.erp.oms.domain.b2b.order.B2bShipmentReviewStatusEnum;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.bmp.BmpProcessClient;
import com.renpho.erp.oms.infrastructure.form.B2bOrderAuditForm;
import com.renpho.erp.oms.infrastructure.form.B2bOrderShipmentAuditForm;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.CustomerInsuranceCreditInfoPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.CustomerInsuranceCreditInfoService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * B2B订单审核服务
 * <AUTHOR> Assistant
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class B2bOrderAuditService {

	private final B2bOrderService b2bOrderService;
	private final CustomerInsuranceCreditInfoService customerInsuranceCreditInfoService;
	private final B2bOrderQueryService b2bOrderQueryService;
	private final BmpProcessClient bmpProcessClient;
	private final B2bOrderAuditFormConvert b2bOrderAuditFormConvert;
	private final B2bOrderRepository b2bOrderRepository;

	/**
	 * 提交订单审核
	 * @param orderId 订单ID
	 */
	@Lock4j(name = Constants.B2B_ORDER_OPERATE, keys = "#orderId", acquireTimeout = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void submitAudit(Long orderId) {
		// 1. 通过仓储获取订单聚合根(包含审核所需的完整信息)
		B2bOrderAggRoot orderAggRoot = b2bOrderRepository.findOrderAuditInfoById(orderId);
		// 2. 校验是否可以提交审核
		orderAggRoot.checkCanSubmitAudit();
		// 3. 组装审批要素 B2bOrderAuditForm
		OrderAuditFormDto auditFormDto = buildOrderAuditFormDto(orderAggRoot);
		B2bOrderAuditForm auditForm = b2bOrderAuditFormConvert.toB2bOrderAuditForm(auditFormDto);
		// 4. 创建BPM审批流程
		String processInstanceId = bmpProcessClient.startProcessInstance(auditForm, orderId.toString());
		log.info("成功创建BPM审批流程，订单ID: {}, 流程实例ID: {}", orderId, processInstanceId);
		// 5. 创建成功则将订单状态修改为审核中
		b2bOrderService.updateOrderStatus(orderId, B2bOrderStatusEnum.IN_REVIEW);
		log.info("订单审核提交成功，订单ID: {}, 订单号: {}", orderId, orderAggRoot.getOrderNo());
	}

	/**
	 * 计算已用额度
	 */
	private BigDecimal calculateUsedCreditAmount(Long customerId, String insuredCurrency) {
		// 如果投保币种为空，直接抛出异常
		if (insuredCurrency == null || insuredCurrency.trim().isEmpty()) {
			throw new BusinessException("INSURED_CURRENCY_EMPTY");
		}

		try {
			return b2bOrderQueryService.getCustomerOrderDebts(customerId, insuredCurrency)
				.stream()
				.map(B2bCustomerOrderDebtVO::getDebtAmount)
				.filter(Objects::nonNull)
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		}
		catch (Exception e) {
			log.error("计算已用额度失败，客户ID: {}", customerId, e);
			throw new BusinessException("CALCULATE_USED_CREDIT_AMOUNT_ERROR");
		}
	}

	/**
	 * 处理审批回调结果
	 * @param processResultDto 审批结果DTO
	 */
	@Lock4j(name = Constants.B2B_ORDER_OPERATE, keys = "#processResultDto.bizId")
	public void processOrderAuditResult(ProcessResultDto processResultDto) {
		log.info("开始处理B2B订单审批回调结果: {}", processResultDto);

		// 1. 取ProcessResultDto中的bizId,string类型，转成Long，查询B2bOrderPO,不存在直接报错结束
		B2bOrderPO order = getAndValidateOrder(processResultDto);
		Long orderId = order.getId();

		// 2. 判断当前订单状态是否为审核中，不是的话直接结束
		if (!B2bOrderStatusEnum.IN_REVIEW.getValue().equals(order.getOrderStatus())) {
			log.warn("订单状态不是审核中，无需处理审批结果。订单ID: {}, 当前状态: {}", orderId, order.getOrderStatus());
			return;
		}

		// 3. 取ProcessResultDto中的Integer result字段， 1 为已通过 ，2为已拒绝 4为已撤回
		Integer result = processResultDto.getResult();

		B2bOrderStatusEnum newStatus;
		String resultDesc = switch (result) {
			case 1 -> {
				// 备货中
				newStatus = B2bOrderStatusEnum.PREPARING;
				yield "审批通过";
			}
			case 2 -> {
				// 处理中（待处理）
				newStatus = B2bOrderStatusEnum.PENDING;
				yield "审批拒绝";
			}
			case 4 -> {
				// 处理中（待处理）
				newStatus = B2bOrderStatusEnum.PENDING;
				yield "审批撤回";
			}
			default -> {
				log.error("未知的审批结果类型: {}, 订单ID: {}", result, orderId);
				throw new BusinessException("BMP_PROCESS_RESULT_UNKNOWN");
			}
		};

		// 4. 更新订单状态
		order.setOrderStatus(newStatus.getValue());
		order.setRefuseReason(processResultDto.getRefusedDesc());
		boolean updateCount = b2bOrderService.updateById(order);
		if (updateCount) {
			log.info("B2B订单审批结果处理成功。订单ID: {}, 订单号: {}, 审批结果: {}, 新状态: {}", orderId, order.getOrderNo(), resultDesc, newStatus.getDesc());
		} else {
			log.error("B2B订单状态更新失败。订单ID: {}", orderId);
			throw new BusinessException("B2B_ORDER_STATUS_UPDATE_FAILED");
		}
	}

	/**
	 * 提交订单发货审核
	 * @param orderId 订单ID
	 */
	@Lock4j(name = Constants.B2B_ORDER_OPERATE, keys = "#orderId")
	public void submitShipmentAudit(Long orderId) {
		// 1. 通过仓储获取订单聚合根(包含审核所需的完整信息)
		B2bOrderAggRoot orderAggRoot = b2bOrderRepository.findOrderAuditInfoById(orderId);
		// 2. 校验是否可以发货审核
		orderAggRoot.checkCanSubmitShipmentAudit();
		// 3. 构建审核表单
		OrderAuditFormDto auditFormDto = buildOrderAuditFormDto(orderAggRoot);
		B2bOrderShipmentAuditForm shipmentAuditForm = b2bOrderAuditFormConvert.toB2bOrderShipmentAuditForm(auditFormDto);
		// 4. 创建BPM审批流程
		String processInstanceId = bmpProcessClient.startProcessInstance(shipmentAuditForm, orderId.toString());
		log.info("成功创建订单发货审核BPM审批流程，订单ID: {}, 流程实例ID: {}", orderId, processInstanceId);
		// 5. 更新订单发货审核状态为发货审核中
		b2bOrderService.updateShipmentReviewStatus(orderId, B2bShipmentReviewStatusEnum.REVIEWING);
		log.info("成功更新订单发货审核状态修改为发货审核中，订单ID: {}, 订单号: {}", orderId, orderAggRoot.getOrderNo());
	}

	/**
	 * 构建审核参数
	 * @param order 订单聚合根
	 * @return 审核字段信息
	 */
	private OrderAuditFormDto buildOrderAuditFormDto(B2bOrderAggRoot order) {
		OrderAuditFormDto auditForm = new OrderAuditFormDto();
		// 基本订单信息
		auditForm.setOrderNo(order.getOrderNo());
		auditForm.setCountryManagerUserId(List.of(new KeyAndValueDto(order.getCountryManagerUserId().toString(), order.getCountryManagerUserId().toString())));
		auditForm.setSalesAssistantUserId(List.of(new KeyAndValueDto(order.getSalesAssistantUserId().toString(), order.getSalesAssistantUserId().toString())));
		auditForm.setCreateTime(order.getCreateTime());
		// 客户信息
		B2bOrderCustomer customer = order.getCustomer();
		auditForm.setCustomerCompanyName(List.of(new KeyAndValueDto(customer.getCustomerCompanyName(), customer.getCustomerCompanyName())));
		auditForm.setIncotermsCode(List.of(new KeyAndValueDto(customer.getIncotermsCode(), customer.getIncotermsCode())));
		auditForm.setPaymentTermsCode(List.of(new KeyAndValueDto(customer.getPaymentTermsCode(), customer.getPaymentTermsCode())));
		// 查询保险信息
		CustomerInsuranceCreditInfoPO insuranceInfo = null;
		BigDecimal usedCreditAmount = BigDecimal.ZERO;
		BigDecimal remainingAmount = BigDecimal.ZERO;
		if (Objects.nonNull(customer.getCustomerId())) {
			try {
				Long customerId = order.getCustomer().getCustomerId();
				insuranceInfo = customerInsuranceCreditInfoService.findByCustomerId(customerId);
				if (insuranceInfo != null) {
					// 计算已用额度和剩余额度
					usedCreditAmount = calculateUsedCreditAmount(customerId, insuranceInfo.getInsuredCurrency());
					if (insuranceInfo.getInsuredAmount() == null) {
						throw new BusinessException("INSURED_AMOUNT_EMPTY");
					}
					remainingAmount = insuranceInfo.getInsuredAmount().subtract(usedCreditAmount);
				}
			}
			catch (NumberFormatException e) {
				log.warn("customerId不是有效的Id: {}", customer.getCustomerId());
			}
		}
		// 金额信息
		B2bOrderAmount amount = order.getAmount();
		auditForm.setTotalAmount(amount.getTotalAmount());
		auditForm.setPaymentAmount(amount.getReceiptAmount());
		if (insuranceInfo != null) {
			auditForm.setInsuranceCompany(insuranceInfo.getInsuranceCompany());
			auditForm.setInsuredAmount(insuranceInfo.getInsuredAmount());
		}
		auditForm.setUsedCreditAmount(usedCreditAmount);
		auditForm.setRemainingAmount(remainingAmount);
		return auditForm;
	}

	/**
	 * 处理订单发货审核回调结果
	 *
	 * @param shipmentProcessResultDto 订单发货审核结果DTO
	 */
	@Lock4j(name = Constants.B2B_ORDER_OPERATE, keys = "#shipmentProcessResultDto.bizId", acquireTimeout = 0L)
	public void processOrderShipmentAuditResult(ProcessResultDto shipmentProcessResultDto) {
		log.info("开始处理订单发货审核回调结果: {}", shipmentProcessResultDto);
		// 1. 取ProcessResultDto中的bizId,string类型，转成Long，查询B2bOrderPO,不存在直接报错结束
		B2bOrderPO order = getAndValidateOrder(shipmentProcessResultDto);
		Long orderId = order.getId();
		// 2. 判断当前订单发货审核状态是否为发货审核中，不是的话直接结束
		if (!B2bShipmentReviewStatusEnum.REVIEWING.getValue().equals(order.getShipmentReviewStatus())) {
			log.warn("订单发货审核状态不是发货审核中，无需处理审批结果。订单ID: {}, 当前状态: {}", orderId, order.getShipmentReviewStatus());
			return;
		}

		// 3. 取ProcessResultDto中的Integer result字段， 1 为已通过 ，2为已拒绝 4为已撤回
		Integer result = shipmentProcessResultDto.getResult();

		B2bShipmentReviewStatusEnum newStatus;
		String resultDesc = switch (result) {
			case 1 -> {
				// 已通过
				newStatus = B2bShipmentReviewStatusEnum.APPROVED;
				yield "审批通过";
			}
			case 2 -> {
				// 已拒绝
				newStatus = B2bShipmentReviewStatusEnum.REJECTED;
				yield "审批拒绝";
			}
			case 4 -> {
				// 已撤回
				newStatus = B2bShipmentReviewStatusEnum.REJECTED;
				yield "审批撤回";
			}
			default -> {
				log.error("未知的发货审批结果类型: {}, 订单ID: {}", result, orderId);
				throw new BusinessException("BMP_PROCESS_RESULT_UNKNOWN");
			}
		};

		// 4. 更新发货审核状态
		order.setShipmentReviewStatus(newStatus.getValue());
		order.setRefuseReason(shipmentProcessResultDto.getRefusedDesc());
		boolean updateCount = b2bOrderService.updateById(order);

		if (updateCount) {
			log.info("B2B订单发货审批结果处理成功。订单ID: {}, 订单号: {}, 审批结果: {}, 新状态: {}", orderId, order.getOrderNo(), resultDesc, newStatus.getDesc());
		}
		else {
			log.error("B2B订单发货审核状态更新失败。订单ID: {}", orderId);
			throw new BusinessException("B2B_ORDER_STATUS_UPDATE_FAILED");
		}
	}

	/**
	 * 获取并校验订单
	 * @param processResultDto 审批结果DTO
	 * @return B2bOrderPO
	 */
	private B2bOrderPO getAndValidateOrder(ProcessResultDto processResultDto) {
		String bizId = processResultDto.getBizId();
		if (bizId == null || bizId.trim().isEmpty()) {
			log.error("审批回调结果中bizId为空");
			throw new BusinessException("BMP_PROCESS_BIZ_ID_EMPTY");
		}

		Long orderId;
		try {
			orderId = Long.parseLong(bizId);
		}
		catch (NumberFormatException e) {
			log.error("审批回调结果中bizId格式错误: {}", bizId);
			throw new BusinessException("BMP_PROCESS_BIZ_ID_FORMAT_ERROR");
		}

		B2bOrderPO order = b2bOrderService.getById(orderId);
		if (order == null) {
			log.error("根据bizId未找到对应的B2B订单: {}", orderId);
			throw new BusinessException("B2B_ORDER_NOT_FOUND");
		}
		Integer result = processResultDto.getResult();
		if (result == null) {
			log.error("审批回调结果中result字段为空，订单ID: {}", orderId);
			throw new BusinessException("BMP_PROCESS_RESULT_EMPTY");
		}
		return order;
	}
}
