package com.renpho.erp.oms.adapter.web.controller.walmart;

import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtOrderLineService;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/walmart/**")
@RequestMapping("/walmart/line")
@AllArgsConstructor
public class WmtOrderLineWebController {
	private final WmtOrderLineService wmtOrderLineService;

	/**
	 * 沃尔玛订单商品列表
	 *
	 * @param orderId
	 * @return R<List < WmOrderLineVO>>
	 */
	@GetMapping("/list")
	public R<List<WmtOrderLineVO>> listByOrderId(@RequestParam Long orderId) {
		return wmtOrderLineService.listByOrderId(orderId);
	}

}
