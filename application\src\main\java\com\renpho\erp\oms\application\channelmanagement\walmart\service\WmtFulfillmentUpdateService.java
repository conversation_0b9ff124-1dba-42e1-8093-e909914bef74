package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.apiproxy.walmart.model.ShopAccount;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.oms.application.channelmanagement.walmart.task.WmtPullFulfillmentTask;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillment;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtFulfillmentRepository;
import com.renpho.erp.oms.infrastructure.config.WalmartConfig;
import com.renpho.erp.oms.infrastructure.feign.proxy.WalmartClient;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Slf4j
public class WmtFulfillmentUpdateService {

    private final WalmartConfig walmartConfig;
    private final WmtFulfillmentRepository wmtFulfillmentRepository;
    private final WalmartClient walmartClient;
    private final WmtFulfullmentService wmtFulfullmentService;

    public void updateFulfillment(WmtPullFulfillmentTask.PullFulfillmentParam pullFulfillmentParam) {
        if (Objects.nonNull(pullFulfillmentParam)) {
            List<WmtFulfillment> wmtFulfillmentList = wmtFulfillmentRepository.listByMulCondition(pullFulfillmentParam.getStartDateUTC(), pullFulfillmentParam.getEndDateUTC(), pullFulfillmentParam.getStoreId(), pullFulfillmentParam.getOrderNums());
            wmtFulfillmentList.forEach(this::doUpdate);
        } else {
            long startMillis = System.currentTimeMillis();
            int size = 0;
            int pageIndex = 1;
            do {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime startDate = now.minusDays(walmartConfig.getDay());
                List<WmtFulfillment> wmtFulfillmentList = wmtFulfillmentRepository.pageByOrderDate(startDate, now, pageIndex, walmartConfig.getPageSize());
                size += wmtFulfillmentList.size();
                wmtFulfillmentList.forEach(this::doUpdate);
                if (wmtFulfillmentList.size() < walmartConfig.getPageSize()) {
                    log.warn("扫描更新沃尔玛履约信息已完成，中断本次处理. 已处理数量: {}, 总耗时: {} ms.", size, System.currentTimeMillis() - startMillis);
                    break;
                }
                // 超时中断逻辑
                if (System.currentTimeMillis() - startMillis > walmartConfig.getTimeoutSeconds()) {
                    log.warn("扫描更新沃尔玛履约信息超时，中断本次处理. 已处理数量: {}, 总耗时: {} ms.", size, System.currentTimeMillis() - startMillis);
                    break;
                }
                // 翻页
                pageIndex++;
            } while (true);
        }
    }

    private void doUpdate(WmtFulfillment wmtFulfillment) {
        try {
            // 构建查询参数
            ShopAccount shopAccount = new ShopAccount();
            shopAccount.setShopCode(wmtFulfillment.getShopId());
            shopAccount.setSiteCode(wmtFulfillment.getSiteCode());

            GetFulfillmentOrdersStatusRequest getFulfillmentOrdersStatusRequest = new GetFulfillmentOrdersStatusRequest();
            getFulfillmentOrdersStatusRequest.setOrderNumber(wmtFulfillment.getSellerOrderId());

            R<GetFulfillmentOrdersStatusResponse> response = walmartClient.getFulfillmentOrdersStatus(shopAccount, getFulfillmentOrdersStatusRequest);

            log.info("查询沃尔玛履约单，返回信息为{}", JsonUtils.toJson(response));

            // 判断响应是否成功
            if (!response.isSuccess()) {
                log.error("查询沃尔玛履约单失败，失败原因：{}", response.getMessage());
                return;
            }

            // 如果返回数据不为空且有效
            if (response.getData() != null && CollectionUtil.isNotEmpty(response.getData().getPayload())) {
                // 批量插入或更新操作
                wmtFulfullmentService.batchInsertOrUpdate(response.getData().getPayload(),
                        wmtFulfillment.getShopId(),
                        wmtFulfillment.getSiteCode(),
                        wmtFulfillment.getStoreId());
                log.info("成功更新沃尔玛履约单，SellerOrderId: {}", wmtFulfillment.getSellerOrderId());
            } else {
                log.warn("没有获取到有效的履约单数据，SellerOrderId: {}", wmtFulfillment.getSellerOrderId());
            }

        } catch (Exception e) {
            // 详细的异常日志
            log.error("查询更新沃尔玛履约单失败, SellerOrderId: {}, ShopId: {}, SiteCode: {}",
                    wmtFulfillment.getSellerOrderId(), wmtFulfillment.getShopId(), wmtFulfillment.getSiteCode(), e);
        }
    }
}
