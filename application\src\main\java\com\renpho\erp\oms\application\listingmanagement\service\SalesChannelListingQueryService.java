package com.renpho.erp.oms.application.listingmanagement.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.oms.infrastructure.persistence.listing.mapper.SalesChannelListingMapper;
import com.renpho.erp.oms.infrastructure.persistence.listing.po.SalesChannelListingPO;

import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class SalesChannelListingQueryService {

	private final SalesChannelListingMapper salesChannelListingMapper;

	/**
	 * 查询Listing
	 * @param storeId 店铺id
	 * @param skuId skuId
	 */
	public List<SalesChannelListingPO> findByStoreIdAndSkuId(@NotBlank String storeId, @NotBlank String skuId) {
		return salesChannelListingMapper
			.selectList(new LambdaQueryWrapper<SalesChannelListingPO>().eq(SalesChannelListingPO::getStoreId, storeId)
				.eq(SalesChannelListingPO::getSkuId, skuId)
				.eq(SalesChannelListingPO::getIsQueryShow, 1));
	}
}
