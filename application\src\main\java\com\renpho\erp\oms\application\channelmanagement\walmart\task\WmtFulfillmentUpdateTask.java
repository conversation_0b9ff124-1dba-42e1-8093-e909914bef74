package com.renpho.erp.oms.application.channelmanagement.walmart.task;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtFulfillmentUpdateService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 沃尔玛更新履约信息任务
 */
@Component
@AllArgsConstructor
public class WmtFulfillmentUpdateTask {
    private final WmtFulfillmentUpdateService wmtFulfillmentUpdateService;

    @XxlJob("WmtFulfillmentUpdateTask")
    public void wmtFulfillmentUpdateTask() throws Exception {
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isNotEmpty(param)) {
            XxlJobHelper.log("沃尔玛主动更新履约单参数: {}", param);
            // 构建参数
            WmtPullFulfillmentTask.PullFulfillmentParam pullFulfillmentParam = JsonUtils.jsonToObject(param, WmtPullFulfillmentTask.PullFulfillmentParam.class);
            if (Objects.isNull(pullFulfillmentParam.getStoreId())) {
                XxlJobHelper.log("手动更新输入店铺参数为空");
                return;
            }
            boolean timeRange = StringUtils.isNotEmpty(pullFulfillmentParam.getCreateTimeAfter())
                    && StringUtils.isNotEmpty(pullFulfillmentParam.getCreateTimeBefore());
            if (!timeRange && CollectionUtil.isEmpty(pullFulfillmentParam.getOrderNums())) {
                XxlJobHelper.log("手动更新输入时间范围或订单号为空");
                return;
            }

            wmtFulfillmentUpdateService.updateFulfillment(pullFulfillmentParam);

        } else {
            wmtFulfillmentUpdateService.updateFulfillment(null);
        }

    }
}
