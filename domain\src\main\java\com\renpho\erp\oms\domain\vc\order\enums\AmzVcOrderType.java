package com.renpho.erp.oms.domain.vc.order.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 亚马逊VC订单类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AmzVcOrderType {

	/**
	 * 常规订单
	 */
	REGULAR("REGULAR", "常规订单"),

	/**
	 * 紧急订单
	 */
	RUSH("RUSH", "紧急订单"),

	/**
	 * 新品订单
	 */
	NEW_PRODUCT("NEW_PRODUCT", "新品订单"),

	/**
	 * 补货订单
	 */
	REPLENISHMENT("REPLENISHMENT", "补货订单"),

	/**
	 * 促销订单
	 */
	PROMOTIONAL("PROMOTIONAL", "促销订单"),

	/**
	 * 季节性订单
	 */
	SEASONAL("SEASONAL", "季节性订单");

	private final String value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return AmzVcOrderType
	 */
	public static AmzVcOrderType getByValue(String value) {
		return Arrays.stream(values())
			.filter(e -> e.getValue().equals(value))
			.findFirst()
			.orElse(null);
	}

}
