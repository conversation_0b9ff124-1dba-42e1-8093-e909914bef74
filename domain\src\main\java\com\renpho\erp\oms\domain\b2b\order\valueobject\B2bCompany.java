package com.renpho.erp.oms.domain.b2b.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class B2bCompany {

	/**
	 * 销售公司id
	 */
	private Integer salesCompanyId;

	/**
	 * 销售公司名称中文
	 */
	private String salesCompanyNameCn;

	/**
	 * 销售公司名称英文
	 */
	private String salesCompanyNameEn;
}
