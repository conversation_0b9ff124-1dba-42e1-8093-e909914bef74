package com.renpho.erp.oms.application.channelmanagement.amazon.task;


import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderTransService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 亚马逊订单转换推送任务
 */
@Component
@AllArgsConstructor
public class AmzOrdeTransTask {
	private final AmzOrderTransService amzOrderTransService;

	@XxlJob("AmzOrdeTransTask")
	public void amzOrdeTransTask() throws Exception {
		XxlJobHelper.log("亚马逊订单推送开始");
		amzOrderTransService.trans();
	}
}
