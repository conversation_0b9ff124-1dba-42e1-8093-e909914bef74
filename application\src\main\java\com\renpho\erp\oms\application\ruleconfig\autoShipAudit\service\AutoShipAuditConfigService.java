package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.erp.oms.infrastructure.feign.mdm.ChannelClient;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Sets;
import com.renpho.erp.ims.client.feign.inventory.common.ServiceProviderEnum;
import com.renpho.erp.ims.client.feign.inventory.common.WarehouseTypeEnum;
import com.renpho.erp.ims.client.feign.warehouse.query.WarehouseQuery;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.command.SaveAutoShipAuditConfigCmd;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.convertor.AutoShipAuditConfigConvertor;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigExportExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigImportBaseDataDTO;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigImportExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigTemplateExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.WarehouseTemplateExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.vo.AutoShipAuditWarehouseVO;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.PurchaseSkuQueryService;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.PurchaseSkuVO;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.query.AutoShipAuditConfigPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.vo.AutoShipAuditConfigPageVO;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.excel.SimpleExcelHeadCellStyleWriterHandler;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.oms.infrastructure.persistence.autoShipAudit.po.AutoShipAuditConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.autoShipAudit.repository.AutoShipAuditConfigRepositoryImpl;
import com.renpho.erp.oms.infrastructure.persistence.autoShipAudit.service.AutoShipAuditConfigPoService;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16 12:18
 */
@Service
@Slf4j
@AllArgsConstructor
public class AutoShipAuditConfigService {

    private final WarehouseClient warehouseClient;

    private final AutoShipAuditConfigConvertor autoShipAuditConfigConvertor;

    private final StoreClient storeClient;

    private final ChannelClient channelClient;

    private final AutoShipAuditConfigPoService autoShipAuditConfigPoService;

    private final AutoShipAuditConfigRepositoryImpl autoShipAuditConfigRepository;

    private final ProductClient productClient;

    private PurchaseSkuQueryService purchaseSkuQueryService;

    private final I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;

    private final StoreQueryService storeQueryService;

    /**
     * 是否str
     */
    private static List<String> yesOrNoList = Arrays.asList("是", "否", "Y", "N", "Yes", "No", "1", "0", "", null);

    /**
     * 是str
     */
    private static List<String> yesList = Arrays.asList("是", "Y", "Yes", "1");

    /**
     * 开启关闭str
     */
    private static List<String> onOrOffList = Arrays.asList("开启", "关闭", "On", "Off", "1", "0", "", null);

    /**
     * 开启或者关闭
     */
    private static List<String> onList = Arrays.asList("开启", "On", "1");

    private final FileClient fileClient;

    /**
     * 获取自动发货审核下列的仓库信息 仓库类型 == "自建仓" 的所有仓库 和 仓库类型 == "三方仓" 且 服务商 IN（京东、极智佳、Kingspark）的所有仓库
     *
     * @return
     */
    public List<AutoShipAuditWarehouseVO> getAutoShipAuditWarehouseInfo() {
        // 查找自建仓(type:3),三方仓的所有仓库(type:2)
        List<Integer> typeList = Arrays.asList(WarehouseTypeEnum.THIRD_PARTY_WAREHOUSE.getCode(),
                WarehouseTypeEnum.SELF_BUILT_WAREHOUSE.getCode());
        List<WarehouseVo> warehouseVoList = warehouseClient.getByTypeList(typeList);
        if (CollUtil.isEmpty(warehouseVoList)) {
            return new ArrayList<>();
        }
        return warehouseVoList.stream()
                // 过滤仓库
                .filter(this::isMatchAutoShipAuditWarehouse)
                .map(x -> autoShipAuditConfigConvertor.toAutoShipAuditWarehouseVO(x))
                .collect(Collectors.toList());
    }

    /**
     * 是否符合是发货审核配置的仓库,仓库类型 == "自建仓" 的所有仓库 和 仓库类型 == "三方仓" 且 服务商 IN（京东、极智佳、Kingspark）的所有仓库
     *
     * @param warehouseVo 仓库类型
     * @return
     */
    public boolean isMatchAutoShipAuditWarehouse(WarehouseVo warehouseVo) {
        // 京东服务商(0)、极智佳服务商(4)、Kingspark服务商(1)
        List<Integer> serviceProviderList = Arrays.asList(ServiceProviderEnum.JD.getValue(), ServiceProviderEnum.JIZHIJIA.getValue(),
                ServiceProviderEnum.KINGSPARK.getValue());
        return (Objects.equals(warehouseVo.getType(), WarehouseTypeEnum.SELF_BUILT_WAREHOUSE.getCode()))
                || (Objects.equals(warehouseVo.getType(), WarehouseTypeEnum.THIRD_PARTY_WAREHOUSE.getCode())
                && serviceProviderList.contains(warehouseVo.getServiceProvider()));
    }

    /**
     * 新增或者修改发货审核配置
     *
     * @param autoShipAuditConfigCmd
     */
    public void saveUpdateShipAuditConfig(SaveAutoShipAuditConfigCmd autoShipAuditConfigCmd) {
        // 校验仓库
        validateStore(autoShipAuditConfigCmd);
        // 校验仓库选择顺序
        validateWarehouseSelectorPriority(autoShipAuditConfigCmd);
        // 转成要保存的实体
        List<AutoShipAuditConfigPO> autoShipAuditConfigPOList = parseSavePoList(autoShipAuditConfigCmd);
        // 新增或者修改
        autoShipAuditConfigPoService.saveOrUpdateList(autoShipAuditConfigPOList);
    }

    /**
     * 校验仓库选择顺序不能重复
     *
     * @param autoShipAuditConfigCmd
     */
    private void validateWarehouseSelectorPriority(SaveAutoShipAuditConfigCmd autoShipAuditConfigCmd) {
        List<SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj> warehouseSelectorPriorityList = autoShipAuditConfigCmd.getWarehouseSelectorPriorityList();
        //去重后的id
        Set<Integer> warehouseIds = warehouseSelectorPriorityList.stream().map(SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj::getWarehouseId).collect(Collectors.toSet());
        if (warehouseSelectorPriorityList.size() != warehouseIds.size()) {
            throw new BusinessException(I18nMessageKit.getMessage("WAREHOUSE_REPEAT"));
        }
    }

    /**
     * 转成要保存的实体
     *
     * @param autoShipAuditConfigCmd
     * @return
     */
    private List<AutoShipAuditConfigPO> parseSavePoList(SaveAutoShipAuditConfigCmd autoShipAuditConfigCmd) {
        List<String> pskuList = autoShipAuditConfigCmd.getPskuList();
        List<AutoShipAuditConfigPO> autoShipAuditConfigPOList = Lists.newArrayList();
        AutoShipAuditConfigPO autoShipAuditConfigPO = autoShipAuditConfigConvertor.cmdToAutoShipAuditConfigPO(autoShipAuditConfigCmd);
        autoShipAuditConfigPO.setUpdateTime(LocalDateTime.now());
        autoShipAuditConfigPO.setCreateTime(LocalDateTime.now());
        autoShipAuditConfigPO.setCreateBy(SecurityUtils.getUserId());
        autoShipAuditConfigPO.setUpdateBy(SecurityUtils.getUserId());
        for (String psku : pskuList) {
            autoShipAuditConfigPO.setPsku(psku);
            AutoShipAuditConfigPO tempAutoShipAuditConfigPO = new AutoShipAuditConfigPO();
            BeanUtil.copyProperties(autoShipAuditConfigPO, tempAutoShipAuditConfigPO);
            autoShipAuditConfigPOList.add(tempAutoShipAuditConfigPO);
        }
        return autoShipAuditConfigPOList;
    }

    /**
     * 校验仓库
     *
     * @param autoShipAuditConfigCmd
     * @return
     */
    private StoreVo validateStore(SaveAutoShipAuditConfigCmd autoShipAuditConfigCmd) {
        List<StoreVo> storeVOS = storeClient.getByStoreIds(Collections.singletonList(autoShipAuditConfigCmd.getStoreId()));
        if (CollectionUtil.isEmpty(storeVOS)) {
            throw new BusinessException(I18nMessageKit.getMessage("STORE_NO_EXITS"));
        }
        StoreVo storeVo = storeVOS.get(0);
        if (!storeVo.getSalesChannelId().equals(autoShipAuditConfigCmd.getChannelId())) {
            throw new BusinessException(I18nMessageKit.getMessage("CHANNEL_NO_EXITS"));
        }
        return storeVo;
    }

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    public Paging<AutoShipAuditConfigPageVO> page(AutoShipAuditConfigPageQuery query) {
        IPage<AutoShipAuditConfigPageVO> page = autoShipAuditConfigRepository.page(query);
        Paging<AutoShipAuditConfigPageVO> paging = Paging.of(page.getRecords(), ((int) page.getTotal()), ((int) page.getPages()),
                ((int) page.getCurrent()));
        if (CollUtil.isEmpty(page.getRecords())) {
            return paging;
        }
        List<AutoShipAuditConfigPageVO> records = paging.getRecords();
        // 查询店铺信息
        List<Integer> storeIds = records.stream().map(AutoShipAuditConfigPageVO::getStoreId).distinct().collect(Collectors.toList());
        Map<Integer, StoreVo> storeVOMap = storeClient.getByStoreIds(storeIds)
                .stream()
                .collect(Collectors.toMap(StoreVo::getId, Function.identity()));
        // 查询仓库信息
        Map<Integer, AutoShipAuditWarehouseVO> warehouseVoMap = getWarehouseVoMap(records);
        for (AutoShipAuditConfigPageVO record : records) {
            // 店铺名称，渠道名称转换
            StoreVo storeVo = storeVOMap.getOrDefault(record.getStoreId(), new StoreVo());
            record.setStoreName(storeVo.getStoreName());
            record.setChannelName(storeVo.getSalesChannel());
            // 仓库选择顺序转换
            for (AutoShipAuditConfigPageVO.WarehouseSelectorPriorityObj warehouseSelectorPriorityObj : record
                    .getWarehouseSelectorPriorityObjList()) {
                if (!warehouseVoMap.containsKey(warehouseSelectorPriorityObj.getWarehouseId())) {
                    continue;
                }
                AutoShipAuditWarehouseVO autoShipAuditWarehouseVO = warehouseVoMap.get(warehouseSelectorPriorityObj.getWarehouseId());
                warehouseSelectorPriorityObj.setWarehouseCode(autoShipAuditWarehouseVO.getWarehouseCode());
                warehouseSelectorPriorityObj.setWarehouseName(autoShipAuditWarehouseVO.getWarehouseName());
                warehouseSelectorPriorityObj.setThirdPartWarehouseCode(autoShipAuditWarehouseVO.getThirdPartWarehouseCode());
            }
            // sku组合类型
            List<String> skuCombinationList = Lists.newArrayList();
            if (BooleanUtil.isTrue(record.getOneSkuOnePc())) {
                skuCombinationList.add(I18nMessageKit.getMessage("ONE_SKU_ONE_PC"));
            }
            if (BooleanUtil.isTrue(record.getOneSkuMultiPcs())) {
                skuCombinationList.add(I18nMessageKit.getMessage("ONE_SKU_MULTI_PCS"));
            }
            if (BooleanUtil.isTrue(record.getMultiSkus())) {
                skuCombinationList.add(I18nMessageKit.getMessage("MULTI_SKUS"));
            }
            record.setSkuCombinationList(skuCombinationList);
            // 审核开关
            if (BooleanUtil.isTrue(record.getAuditSwitch())) {
                record.setAuditSwitchName(I18nMessageKit.getMessage("SWITCH_ON"));
            } else {
                record.setAuditSwitchName(I18nMessageKit.getMessage("SWITCH_OFF"));
            }

        }
        return paging;
    }

    /**
     * 获取仓库map
     *
     * @param records
     * @return
     */
    private Map<Integer, AutoShipAuditWarehouseVO> getWarehouseVoMap(List<AutoShipAuditConfigPageVO> records) {
        List<Integer> warehouseIds = Lists.newArrayList();
        records.stream().forEach(x -> {
            warehouseIds.addAll(x.getWarehouseSelectorPriorityObjList()
                    .stream()
                    .map(AutoShipAuditConfigPageVO.WarehouseSelectorPriorityObj::getWarehouseId)
                    .collect(Collectors.toList()));
        });
        WarehouseQuery warehouseQuery = new WarehouseQuery();
        warehouseQuery.setIdList(warehouseIds.stream().distinct().collect(Collectors.toList()));
        Map<Integer, AutoShipAuditWarehouseVO> warehouseVoMap = warehouseClient.list(warehouseQuery)
                .stream()
                .map(x -> autoShipAuditConfigConvertor.toAutoShipAuditWarehouseVO(x))
                .collect(Collectors.toMap(AutoShipAuditWarehouseVO::getId, Function.identity()));
        return warehouseVoMap;
    }

    /**
     * 导入excel
     *
     * @param importExcelList
     * @return
     */
    public R importExcel(List<AutoShipAuditConfigImportExcel> importExcelList) {
        if (CollectionUtil.isEmpty(importExcelList)) {
            throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
        }
        // 1.数据准备,查询需要校验的基础数据
        AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO = queryImportExcelBaseDataDto(importExcelList);
        // 2.校验导入是否有异常
        List<AutoShipAuditConfigImportExcel> errorExcelList = validateImport(importBaseDateDTO, importExcelList);
        // 3.有异常输出错误文件
        if (CollUtil.isNotEmpty(errorExcelList)) {
            // 有异常
            return fileClient.getErrorExcelUrl(importExcelList, AutoShipAuditConfigImportExcel.class);
        }
        // 4. 无异常新增或者保存对应数据
        List<AutoShipAuditConfigPO> saveOrUpdatePoList = importExcelToPo(importBaseDateDTO, importExcelList);
        autoShipAuditConfigPoService.saveOrUpdateList(saveOrUpdatePoList);
        return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
    }

    /**
     * excel转成数据库实体
     *
     * @param importBaseDateDTO
     * @param importExcelList
     * @return
     */
    private List<AutoShipAuditConfigPO> importExcelToPo(AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO,
                                                        List<AutoShipAuditConfigImportExcel> importExcelList) {
        List<AutoShipAuditConfigPO> autoShipAuditConfigPOList = Lists.newArrayList();
        Map<String, StoreVo> storeNameMap = importBaseDateDTO.getStoreNameMap();
        Map<String, WarehouseVo> warehouseCodeMap = importBaseDateDTO.getWarehouseCodeMap();
        for (AutoShipAuditConfigImportExcel importExcel : importExcelList) {
            AutoShipAuditConfigPO autoShipAuditConfigPO = new AutoShipAuditConfigPO();
            StoreVo storeVo = storeNameMap.get(importExcel.getStore());
            autoShipAuditConfigPO.setChannelId(storeVo.getSalesChannelId());
            autoShipAuditConfigPO.setChannelCode(storeVo.getSalesChannelCode());
            autoShipAuditConfigPO.setStoreId(storeVo.getId());
            autoShipAuditConfigPO.setPsku(importExcel.getPsku());
            autoShipAuditConfigPO.setOneSkuOnePc(containIgnoreCase(yesList, importExcel.getOneSkuOnePC()));
            autoShipAuditConfigPO.setOneSkuMultiPcs(containIgnoreCase(yesList, importExcel.getOneSkuMultiPcs()));
            autoShipAuditConfigPO.setMultiSkus(containIgnoreCase(yesList, importExcel.getMultiSkus()));
            autoShipAuditConfigPO.setAuditSwitch(containIgnoreCase(onList, importExcel.getAutoSwitch()));
            autoShipAuditConfigPO.setCreateBy(SecurityUtils.getUserId());
            autoShipAuditConfigPO.setUpdateBy(SecurityUtils.getUserId());
            autoShipAuditConfigPO.setCreateTime(LocalDateTime.now());
            autoShipAuditConfigPO.setUpdateTime(LocalDateTime.now());
            List<SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj> warehouseSelectorPriorityList = Lists.newArrayList();
            String[] warehouseCodes = importExcel.getWarehouseCode().split(StrUtil.COMMA);
            for (int i = 0; i < warehouseCodes.length; i++) {
                Integer warehouseCodeId = warehouseCodeMap.get(warehouseCodes[i]).getId();
                SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj warehouseSelectorPriorityObj = new SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj();
                warehouseSelectorPriorityObj.setWarehouseId(warehouseCodeId);
                warehouseSelectorPriorityObj.setPriority((i + 1));
                warehouseSelectorPriorityObj.setWarehouseCode(warehouseCodes[i]);
                warehouseSelectorPriorityList.add(warehouseSelectorPriorityObj);
            }
            autoShipAuditConfigPO.setWarehouseSelectorPriority(JSONUtil.toJsonStr(warehouseSelectorPriorityList));
            autoShipAuditConfigPOList.add(autoShipAuditConfigPO);
        }
        return autoShipAuditConfigPOList;
    }

    /**
     * 校验是否通过校验
     *
     * @param importBaseDateDTO
     * @param importExcelList
     * @return
     */
    private List<AutoShipAuditConfigImportExcel> validateImport(AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO,
                                                                List<AutoShipAuditConfigImportExcel> importExcelList) {
        // 错误的行
        List<AutoShipAuditConfigImportExcel> errorList = Lists.newArrayList();
        // 唯一行,渠道+店铺+psKu
        Set<String> uniqueLineSet = new HashSet<>();
        for (AutoShipAuditConfigImportExcel importExcel : importExcelList) {
            // 每行的错误信息
            List<String> lineErrorMsg = Lists.newArrayList();
            // 渠道校验
            validateImportChannel(importExcel, importBaseDateDTO, lineErrorMsg);
            // 店铺校验
            validateImportStore(importExcel, importBaseDateDTO, lineErrorMsg);
            // psku校验
            validateImportPsku(importExcel, importBaseDateDTO, lineErrorMsg);
            // 仓库编码校验
            validateImportWarehouseCode(importExcel, importBaseDateDTO, lineErrorMsg);
            // sku组合类型校验
            validateImportSkuCombinationType(importExcel, lineErrorMsg);
            // 审核开关校验
            validateImportAutoSwitch(importExcel, lineErrorMsg);
            // 唯一行校验
            validateImportUniqueLine(importExcel, uniqueLineSet, lineErrorMsg);
            if (CollUtil.isNotEmpty(lineErrorMsg)) {
                // 校验不通过
                importExcel.setErrMessage(CollUtil.join(lineErrorMsg, ";\n"));
                errorList.add(importExcel);
            }
        }
        return errorList;
    }

    /**
     * 唯一行校验
     *
     * @param importExcel
     * @param uniqueLineSet
     * @param lineErrorMsg
     */
    private void validateImportUniqueLine(AutoShipAuditConfigImportExcel importExcel, Set<String> uniqueLineSet,
                                          List<String> lineErrorMsg) {
        String uniqueLine = StrUtil.concat(false, importExcel.getChannel(), importExcel.getStore(), importExcel.getPsku());
        if (uniqueLineSet.contains(uniqueLine)) {
            lineErrorMsg.add(I18nMessageKit.getMessage("CHANNEL_SHOP_PSKU_EXISTS"));
        }
        uniqueLineSet.add(uniqueLine);
    }

    /**
     * list 忽略大小写是否保存字符,支持空字符比较
     *
     * @param list
     * @param str
     * @return
     */
    public static boolean containIgnoreCase(Collection<String> list, String str) {
        for (String s : list) {
            if (StrUtil.equalsIgnoreCase(str, s)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 校验审核开关
     *
     * @param importExcel
     * @param lineErrorMsg
     */
    private void validateImportAutoSwitch(AutoShipAuditConfigImportExcel importExcel, List<String> lineErrorMsg) {
        if (!containIgnoreCase(onOrOffList, importExcel.getAutoSwitch())) {
            // 审核开关只能为开启/关闭,空
            lineErrorMsg.add(I18nMessageKit.getMessage("AUDIT_SWITCH_ON_OF"));
        }
    }

    /**
     * 校验组合类型
     *
     * @param importExcel
     * @param lineErrorMsg
     */
    private void validateImportSkuCombinationType(AutoShipAuditConfigImportExcel importExcel, List<String> lineErrorMsg) {
        if (StrUtil.isEmpty(importExcel.getOneSkuOnePC()) && StrUtil.isEmpty(importExcel.getOneSkuMultiPcs())
                && StrUtil.isEmpty(importExcel.getMultiSkus())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("SKU_COMBINATION_TYPE_EMPTY"));
            return;
        }
        if (!containIgnoreCase(yesOrNoList, importExcel.getOneSkuOnePC())
                || !containIgnoreCase(yesOrNoList, importExcel.getOneSkuMultiPcs())
                || !containIgnoreCase(yesOrNoList, importExcel.getMultiSkus())) {
            // sku组合类型只能为是、否、空
            lineErrorMsg.add(I18nMessageKit.getMessage("SKU_COMBINATION_TYPE_YES_NOT"));
            return;
        }
        if (!containIgnoreCase(yesList, importExcel.getOneSkuOnePC()) && !containIgnoreCase(yesList, importExcel.getOneSkuMultiPcs())
                && !containIgnoreCase(yesList, importExcel.getMultiSkus())) {
            // sku组合类型全为否
            lineErrorMsg.add(I18nMessageKit.getMessage("SKU_COMBINATION_TYPE_ALL_FALSE"));
            return;
        }

    }

    /**
     * @param importExcel
     * @param importBaseDateDTO
     * @param lineErrorMsg
     */
    private void validateImportWarehouseCode(AutoShipAuditConfigImportExcel importExcel,
                                             AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO, List<String> lineErrorMsg) {
        Map<String, WarehouseVo> warehouseCodeMap = importBaseDateDTO.getWarehouseCodeMap();
        if (StrUtil.isEmpty(importExcel.getWarehouseCode())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("WAREHOUSE_CODE_EMPTY"));
        } else {
            // 仓库code不存在set
            Set<String> noExistWarehouseCode = Sets.newHashSet();
            // 不符合条件set
            Set<String> invalidateWarehouseCode = Sets.newHashSet();
            // 重复的仓库编码
            boolean isRepeatWarehouseCode = false;
            Set<String> warehouseCodeSet = Sets.newHashSet();
            for (String s : importExcel.getWarehouseCode().split(StrUtil.COMMA)) {
                if (!warehouseCodeMap.containsKey(s)) {
                    noExistWarehouseCode.add(s);
                } else if (!isMatchAutoShipAuditWarehouse(warehouseCodeMap.get(s))) {
                    // 仓库存在，但不符合自建仓或者三方仓的要求
                    invalidateWarehouseCode.add(s);
                }
                if (warehouseCodeSet.contains(s)) {
                    isRepeatWarehouseCode = true;
                }
                warehouseCodeSet.add(s);
            }
            if (CollUtil.isNotEmpty(noExistWarehouseCode)) {
                String tempErrorMsg = String.format("%s:%s", I18nMessageKit.getMessage("WAREHOUSE_CODE_LIST_EXIST"),
                        CollUtil.join(noExistWarehouseCode, StrUtil.COMMA));
                lineErrorMsg.add(tempErrorMsg);
            }
            if (CollUtil.isNotEmpty(invalidateWarehouseCode)) {
                String tempErrorMsg = String.format("%s:%s", I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WAREHOUSE_CODE_INVALIDATE"),
                        CollUtil.join(invalidateWarehouseCode, StrUtil.COMMA));
                lineErrorMsg.add(tempErrorMsg);
            }
            if (isRepeatWarehouseCode) {
                //仓库编码错误
                lineErrorMsg.add(I18nMessageKit.getMessage("WAREHOUSE_REPEAT"));
            }

        }
    }

    /**
     * 校验导入的psku
     *
     * @param importExcel
     * @param importBaseDateDTO
     * @param lineErrorMsg
     */
    private void validateImportPsku(AutoShipAuditConfigImportExcel importExcel, AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO,
                                    List<String> lineErrorMsg) {
        List<String> dbPskuList = importBaseDateDTO.getDbPskuList();
        List<String> hadPermissionPskuList = importBaseDateDTO.getHadPermissionPskuList();
        if (StrUtil.isEmpty(importExcel.getPsku())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("PSKU_EMPTY"));
        } else {
            if (!dbPskuList.contains(importExcel.getPsku())) {
                // psku不存在
                lineErrorMsg.add(I18nMessageKit.getMessage("PSKU_NO_EXITS"));
            } else {
                // 存在,校验psku是否有该数据权限
                if (!hadPermissionPskuList.contains(importExcel.getPsku())) {
                    lineErrorMsg.add(I18nMessageKit.getMessage("PSKU_NO_DATA_PERMISSION"));
                }
            }
        }

    }

    /**
     * 校验店铺
     *
     * @param importExcel
     * @param importBaseDateDTO
     * @param lineErrorMsg
     */
    private void validateImportStore(AutoShipAuditConfigImportExcel importExcel, AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO,
                                     List<String> lineErrorMsg) {
        Map<String, StoreVo> storeNameMap = importBaseDateDTO.getStoreNameMap();
        Set<Integer> hadPermissionStoreIdSet = importBaseDateDTO.getHadPermissionStoreIdSet();
        if (StrUtil.isEmpty(importExcel.getStore())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("STORE_EMPTY"));
        } else if (!storeNameMap.containsKey(importExcel.getStore())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("STORE_NO_EXITS"));
        } else {
            StoreVo storeVo = storeNameMap.get(importExcel.getStore());
            // 存在，校验店铺是否有数据权限
            if (!hadPermissionStoreIdSet.contains(storeVo.getId())) {
                lineErrorMsg.add(I18nMessageKit.getMessage("STORE_NO_DATA_PERMISSION"));
            }
            // 渠道不为空，且渠道正确(存在),校验是否匹配店铺
            if (StrUtil.isNotEmpty(importExcel.getChannel()) && importExcel.isChannelExist()
                    && !StrUtil.equals(storeVo.getSalesChannel(), importExcel.getChannel())) {
                lineErrorMsg.add(I18nMessageKit.getMessage("STORE_SALE_CHANNEL_NOT_MATCH"));
            }
        }
    }

    /***
     * 校验渠道
     * @param importExcel
     * @param lineErrorMsg
     */
    private void validateImportChannel(AutoShipAuditConfigImportExcel importExcel, AutoShipAuditConfigImportBaseDataDTO importBaseDateDTO, List<String> lineErrorMsg) {
        if (StrUtil.isEmpty(importExcel.getChannel())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("CHANNLE_EMPTY"));
            importExcel.setChannelExist(false);
            return;
        }
        if (!importBaseDateDTO.getDbSalesChannelNameSet().contains(importExcel.getChannel())) {
            lineErrorMsg.add(I18nMessageKit.getMessage("CHANNEL_NO_EXITS"));
            importExcel.setChannelExist(false);
            return;
        }
        importExcel.setChannelExist(true);
    }

    /**
     * 查询导入的基础数据
     *
     * @param importExcelList
     * @return
     */
    private AutoShipAuditConfigImportBaseDataDTO queryImportExcelBaseDataDto(List<AutoShipAuditConfigImportExcel> importExcelList) {
        // 查询店铺
        List<String> storeList = importExcelList.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getStore()))
                .map(AutoShipAuditConfigImportExcel::getStore)
                .distinct()
                .collect(Collectors.toList());
        Map<String, StoreVo> storeVoMap = new HashMap<>();
        if (CollUtil.isNotEmpty(storeList)) {
            storeVoMap = storeClient.getByStoreNames(storeList)
                    .stream()
                    .collect(Collectors.toMap(StoreVo::getStoreName, Function.identity(), (v1, v2) -> v1));
        }
        // 查询psku
        List<String> pskuList = importExcelList.stream()
                .filter(x -> StrUtil.isNotEmpty(x.getPsku()))
                .map(AutoShipAuditConfigImportExcel::getPsku)
                .distinct()
                .collect(Collectors.toList());
        List<String> dbPskuList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(pskuList)) {
            dbPskuList = productClient.getPurchaseSku(pskuList)
                    .stream()
                    .map(PdsProductManagerBasicViewVo::getPurchaseSku)
                    .collect(Collectors.toList());
        }
        // 查询仓库编码
        List<String> warehouseCodeList = new ArrayList<>();
        List<String> finalWarehouseCodeList = warehouseCodeList;
        importExcelList.stream().filter(x -> StrUtil.isNotEmpty(x.getWarehouseCode())).forEach(x -> {
            for (String warehouseCode : x.getWarehouseCode().split(StrUtil.COMMA)) {
                finalWarehouseCodeList.add(warehouseCode);
            }
        });
        warehouseCodeList = warehouseCodeList.stream().distinct().collect(Collectors.toList());
        Map<String, WarehouseVo> warehouseCodeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(warehouseCodeList)) {
            WarehouseQuery warehouseQuery = new WarehouseQuery();
            warehouseQuery.setCodeList(warehouseCodeList);
            warehouseCodeMap = warehouseClient.list(warehouseQuery)
                    .stream()
                    .collect(Collectors.toMap(WarehouseVo::getCode, Function.identity(), (v1, v2) -> v1));
        }
        // 有权限的psku
        List<String> hadPermissionPskuList = purchaseSkuQueryService.listPermissionPurchaseSku()
                .stream()
                .map(PurchaseSkuVO::getPurchaseSku)
                .collect(Collectors.toList());
        // 有权限的店铺id
        Set<Integer> storeIdPermission = storeQueryService.listPermissionStoreIds();
        //销售渠道名称
        Set<String> channelNameSet = channelClient.findAll().stream().map(SalesChannelVo::getChannelName).collect(Collectors.toSet());
        return AutoShipAuditConfigImportBaseDataDTO.builder()
                .storeList(storeList)
                .pskuList(pskuList)
                .dbPskuList(dbPskuList)
                .warehouseCodeList(warehouseCodeList)
                .warehouseCodeMap(warehouseCodeMap)
                .storeNameMap(storeVoMap)
                .hadPermissionPskuList(hadPermissionPskuList)
                .hadPermissionStoreIdSet(storeIdPermission)
                .dbSalesChannelNameSet(channelNameSet)
                .build();
    }

    /**
     * 导出excel
     *
     * @param query
     * @param response
     */
    public void exportExcel(AutoShipAuditConfigPageQuery query, HttpServletResponse response) {
        String fileName = I18nMessageKit.getMessage("AUTO_SHIP_AUDIT_CONFIG_EXPORT_NAME") + ".xlsx";
        HttpUtil.setExportResponseHeader(response,fileName);

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(outputStream, AutoShipAuditConfigExportExcel.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(i18nHeaderCellWriteHandler)
                    .build();
            WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
            query.setPageSize(1000);
            int pageIndex = 1;
            while (true) {
                query.setPageIndex(pageIndex);
                List<AutoShipAuditConfigPageVO> records = this.page(query).getRecords();
                if (CollUtil.isEmpty(records)) {
                    break;
                }
                List<AutoShipAuditConfigExportExcel> excelData = records.stream()
                        .map(autoShipAuditConfigConvertor::voTOAutoShipAuditConfigExportExcel)
                        .collect(Collectors.toList());
                excelWriter.write(excelData, writeSheet);
                pageIndex++;
            }
            excelWriter.finish();
        } catch (IOException e) {
            log.error("导出Excel失败", e);
            throw new BusinessException(I18nMessageKit.getMessage("FILE_EXPORT_EXCEPTION"));
        }
    }

    /**
     * 下载导入的模板
     *
     * @param response
     */
    public void downloadImportTemplate(HttpServletResponse response) {
        String fileName = I18nMessageKit.getMessage("AUTO_SHIP_AUDIT_CONFIG_TEMPLATE_NAME") + ".xlsx";
        HttpUtil.setExportResponseHeader(response, fileName);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .registerWriteHandler(i18nHeaderCellWriteHandler)
                    .registerWriteHandler(new SimpleExcelHeadCellStyleWriterHandler())
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(18))
                    .build();
            WriteSheet writeSheet1 = EasyExcel.writerSheet(0, "sheet1").head(AutoShipAuditConfigTemplateExcel.class).build();
            WriteSheet writeSheet2 = EasyExcel.writerSheet(1, "warehouse code").head(WarehouseTemplateExcel.class).build();
            List<WarehouseTemplateExcel> warehouseTemplateExcelList = getAutoShipAuditWarehouseInfo().stream()
                    .map(autoShipAuditConfigConvertor::voToWarehouseTemplateExcel)
                    .collect(Collectors.toList());
            excelWriter.write(new ArrayList<>(), writeSheet1);
            excelWriter.write(warehouseTemplateExcelList, writeSheet2);
            excelWriter.finish();
        } catch (IOException e) {
            log.error("导出Excel失败", e);
            throw new BusinessException(I18nMessageKit.getMessage("FILE_EXPORT_EXCEPTION"));
        }
    }

}
