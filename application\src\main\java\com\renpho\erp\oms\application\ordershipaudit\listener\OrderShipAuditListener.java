package com.renpho.erp.oms.application.ordershipaudit.listener;

import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditService;
import com.renpho.erp.oms.domain.ordershipaudit.event.OrderShipAuditEvent;

import lombok.RequiredArgsConstructor;

/**
 * 订单发货审核-事件监听
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderShipAuditListener {

	private final OrderShipAuditService orderShipAuditService;

	@Async("eventMdcThreadPoolTaskExecutor")
	@EventListener
	public void listen(OrderShipAuditEvent event) {
		// 执行发货审核流程
		orderShipAuditService.shipAudit(event.auditNo(), true);
	}

}
