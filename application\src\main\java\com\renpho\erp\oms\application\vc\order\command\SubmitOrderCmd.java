package com.renpho.erp.oms.application.vc.order.command;

import com.renpho.erp.oms.domain.vc.order.enums.RejectReason;
import com.renpho.erp.oms.domain.vc.order.valueobject.SubmitAcknowledgmentItem;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

import java.util.List;
import java.util.Optional;

@Data
public class SubmitOrderCmd {
    /**
     * 订单id
     */
    @NotNull(message = "订单Id不能为空")
    private Long orderId;

    /**
     * 订单商品信息
     */
    @Valid
    @NotEmpty(message = "订单商品信息必填")
    private List<OrderItem> orderItemList;

    public List<SubmitAcknowledgmentItem> getSubmitAcknowledgmentItem() {
        return orderItemList.stream()
                .map(OrderItem::toDomain)
                .toList();
    }

    @Data
    public static class OrderItem {
        /**
         * 订单商品行id
         */
        @NotNull(message = "订单商品id不能为空")
        private Long id;
        /**
         * 接单数
         */
        @NotNull(message = "接单数必填")
        @PositiveOrZero(message = "接单数必须大于等于0")
        private Integer acceptedQuantity;
        /**
         * 拒单数
         */
        @NotNull(message = "拒单数必填")
        @PositiveOrZero(message = "拒单数必须大于等于0")
        private Integer rejectedQuantity = 0;
        /**
         * 原因
         */
        private Integer rejectReason;

        public SubmitAcknowledgmentItem toDomain() {
            return SubmitAcknowledgmentItem.builder()
                    .id(this.id)
                    .acceptedQuantity(this.acceptedQuantity)
                    .rejectedQuantity(this.rejectedQuantity)
                    .rejectSon(Optional.ofNullable(rejectReason).map(RejectReason::enumOf).orElse(null))
                    .build();
        }
    }
}
