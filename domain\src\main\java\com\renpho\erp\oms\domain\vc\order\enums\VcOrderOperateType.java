package com.renpho.erp.oms.domain.vc.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @desc: VC订单操作类型
 * @time: 2025-07-08 17:29:46
 * @author: <PERSON><PERSON>
 */
@Getter
@AllArgsConstructor
public enum VcOrderOperateType {
    RECEIVE_ORDER(1, "接单"),
    UPLOAD_LABEL(2, "上传箱唛"),
    SUBMIT_SUPPLIER_CHAIN(3, "提交供应链");

    private final Integer code;
    private final String desc;

    public static VcOrderOperateType enumOf(Integer value) {
        for (VcOrderOperateType vcOrderOperateType : values()) {
            if (vcOrderOperateType.getCode().equals(value)) {
                return vcOrderOperateType;
            }
        }
        return null;
    }

}