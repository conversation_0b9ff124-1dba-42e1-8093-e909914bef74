package com.renpho.erp.oms.application.listingmanagement.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 商家列表所有数据 dto
 *
 * <AUTHOR>
 * @Date 2024/12/25 12:18
 **/
@Data
public class AmzReportParseMerchantListingAllDataDto implements Serializable {

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 商品名称
	 */
	private String itemName;

	/**
	 * 商品描述
	 */
	private String itemDescription;

	/**
	 * 刊登ID
	 */
	private String listingId;

	/**
	 * 销售SKU
	 */
	private String sellerSku;

	/**
	 * 价格
	 */
	private String price;

	private String quantity;

	/**
	 * 刊登时间
	 */
	private String openDate;

	/**
	 * 图片地址
	 */
	private String imageUrl;

	private String itemIsMarketplace;

	private String productIdType;

	private String zShopShippingFee;

	private String itemNote;

	private String itemCondition;

	private String zShopCategory1;

	private String zShopBrowsePath;

	private String zShopStoreFrontFeature;

	/**
	 * ASIN码
	 */
	private String asin1;

	private String asin2;

	private String asin3;

	private String willShipInternationally;

	private String expeditedShipping;

	private String zShopBoldface;

	/**
	 * ASIN码
	 */
	private String productId;

	private String bidForFeaturedPlacement;

	private String addDelete;

	private String pendingQuantity;

	private String fulfillmentChannel;

	private String merchantShippingGroup;
	/**
	 * 状态
	 */
	private String status;
}
