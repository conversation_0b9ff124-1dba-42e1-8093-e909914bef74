package com.renpho.erp.oms.domain.vc.order.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * VC订单备注命令
 *
 * <AUTHOR>
 * @since 2025/07/08
 */
@Data
public class VcOrderRemarkCmd {
    /**
     * 订单id
     */
    @NotNull(message = "订单id不能为空")
    private Long orderId;
    /**
     * 备注
     */
    @NotBlank(message = "备注不能为空")
    private String remark;
}