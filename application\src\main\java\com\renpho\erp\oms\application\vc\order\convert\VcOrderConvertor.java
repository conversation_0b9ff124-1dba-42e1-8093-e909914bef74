package com.renpho.erp.oms.application.vc.order.convert;

import cn.hutool.core.collection.CollUtil;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.oms.application.vc.order.command.VcOrderLabelUploadCmd;
import com.renpho.erp.oms.application.vc.order.vo.VcOrderDetailVO;
import com.renpho.erp.oms.application.vc.order.vo.VcOrderOperatorDetailVo;
import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.entity.VcOrderDi;
import com.renpho.erp.oms.domain.vc.order.entity.VcOrderItemLabel;
import com.renpho.erp.oms.domain.vc.order.entity.VcOrderRemark;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderDiPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderRemarkPO;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import org.apache.commons.compress.utils.Lists;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


/**
 * VC订单转换器
 *
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface VcOrderConvertor {


    /**
     * 将订单ID和命令对象转换为 VcOrderItemLabel 对象
     *
     * @param item    命令对象
     * @return VcOrderItemLabel 对象
     */
    VcOrderItemLabel toVcOrderItemLabel( VcOrderLabelUploadCmd.VcOrderItemLabelCmd item);

    default VcOrderOperatorDetailVo toVcOrderOperatorDetailVo(VcOrderAggRoot orderAggRoot, Map<String, FileDetailResponse> fileMap) {
        VcOrderOperatorDetailVo detailVo = new VcOrderOperatorDetailVo();
        detailVo.setOrderId(orderAggRoot.getId());
        detailVo.setOrderNo(orderAggRoot.getOrderNo());
        detailVo.setStoreId(orderAggRoot.getStore().getStoreId());
        detailVo.setStoreName(orderAggRoot.getStore().getStoreName());
        detailVo.setVcPurchaseNo(orderAggRoot.getVcPurchaseNo());
        if (Optional.ofNullable(orderAggRoot.getShippingWindow()).isPresent()) {
            detailVo.setShippingWindowStart(orderAggRoot.getShippingWindow().getStartTime());
            detailVo.setShippingWindowEnd(orderAggRoot.getShippingWindow().getEndTime());
        }

        List<VcOrderOperatorDetailVo.VcOperatorDetailItemVo> itemVos = orderAggRoot.getItems().stream().map(item -> {
            VcOrderOperatorDetailVo.VcOperatorDetailItemVo itemVo = new VcOrderOperatorDetailVo.VcOperatorDetailItemVo();

            Optional.ofNullable(item.getProduct())
                    .map(product -> product.getImageId())
                    .map(fileMap::get)
                    .map(FileDetailResponse::getUrl)
                    .ifPresent(itemVo::setImageUrl);
            itemVo.setId(item.getId());
            itemVo.setAsin(item.getAsin());
            itemVo.setOrderedQuantity(item.getOrderedQuantity());
            itemVo.setAcceptedQuantity(item.getAcceptedQuantity());
            itemVo.setRejectedQuantity(item.getRejectedQuantity());
            // 设置商品信息
            if (Optional.ofNullable(item.getProduct()).isPresent()) {
                itemVo.setPsku(item.getProduct().getPsku());
                itemVo.setBarcode(item.getProduct().getBarcode());
                // 计算箱数
                Integer unitsPerBox = item.getProduct().getNumberOfUnitsPerBox();
                if (unitsPerBox != null && unitsPerBox > 0) {
                    if (item.getOrderedQuantity() != null) {
                        itemVo.setOrderedBoxQuantity(item.getOrderedQuantity() / unitsPerBox);
                    }
                    if (item.getAcceptedQuantity() != null) {
                        itemVo.setAcceptedBoxQuantity(item.getAcceptedQuantity() / unitsPerBox);
                    }
                    itemVo.setNumberOfUnitsPerBox(unitsPerBox);
                }
            }
            // 设置价格信息
            if (Optional.ofNullable(item.getAmount()).isPresent()) {
                itemVo.setUnitPrice(item.getAmount().getUnitPrice());
                itemVo.setCurrency(item.getAmount().getCurrency());
            }
            return itemVo;
        }).collect(Collectors.toList());
        detailVo.setItems(itemVos);

        return detailVo;
    }


    default VcOrderDetailVO toVcOrderDetailVO(VcOrderPO vcOrderPO, List<VcOrderItemPO> vcOrderItemPOList, VcOrderDiPO vcOrderDiPO, List<VcOrderRemarkPO> vcOrderRemarkPOList, Map<String, FileDetailResponse> fileMap, Map<Integer, OumUserInfoRes> userInfoMap) {
        String shipWindowStr = "";
        if (vcOrderPO.getShippingWindowStart() != null && vcOrderPO.getShippingWindowEnd() != null) {
            shipWindowStr = DateUtil.convertToStr(vcOrderPO.getShippingWindowStart(), "yyyy-MM-dd") + "--" + DateUtil.convertToStr(vcOrderPO.getShippingWindowEnd(), "yyyy-MM-dd");
        }
        //订单信息
        VcOrderDetailVO.OrderBaseInfo orderBaseInfo = VcOrderDetailVO.OrderBaseInfo.builder()
                .id(vcOrderPO.getId())
                .orderNo(vcOrderPO.getOrderNo())
                .orderStatus(vcOrderPO.getOrderStatus())
                .acceptedStatus(vcOrderPO.getAcceptedStatus())
                .vcPurchaseNo(vcOrderPO.getVcPurchaseNo())
                .businessType(vcOrderPO.getBusinessType())
                .createTime(vcOrderPO.getCreateTime())
                .updateTime(vcOrderPO.getUpdateTime())
                .orderedTime(vcOrderPO.getOrderedTime())
                .acceptedTime(vcOrderPO.getAcceptedTime())
                .shippedTime(vcOrderPO.getShippedTime())
                .invoicedTime(vcOrderPO.getInvoicedTime())
                .cancelledTime(vcOrderPO.getCancelledTime())
                .orderType(vcOrderPO.getOrderType())
                .invoiceType(vcOrderPO.getInvoiceType())
                .shipFrom(vcOrderPO.getShipFrom())
                .shipTo(vcOrderPO.getShipTo())
                .shipWindowStr(shipWindowStr)
                .build();

        //DI信息
        VcOrderDetailVO.DIInfo diInfo = null;
        if (vcOrderDiPO != null) {
            diInfo = VcOrderDetailVO.DIInfo.builder()
                    .paymentMethod(vcOrderDiPO.getPaymentMethod())
                    .incoterms(vcOrderDiPO.getIncoterms())
                    .containerType(vcOrderDiPO.getContainerType())
                    .shippingInstructions(vcOrderDiPO.getShippingInstructions())
                    .build();
        }

        //金额信息
        VcOrderDetailVO.AmountInfo amountInfo = VcOrderDetailVO.AmountInfo.builder()
                .productPrice(vcOrderPO.getProductPrice())
                .currency(vcOrderPO.getCurrency())
                .tax(vcOrderPO.getTax())
                .taxRate(vcOrderPO.getTaxRate() != null ? vcOrderPO.getTaxRate().multiply(new BigDecimal(100)) : null)
                .build();

        //remark 信息
        List<VcOrderDetailVO.RemarkInfo> remarkInfoList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(vcOrderRemarkPOList)) {
            remarkInfoList = vcOrderRemarkPOList.stream().map(remark -> {
                return VcOrderDetailVO.RemarkInfo.builder()
                        .remark(remark.getRemark())
                        .createTime(remark.getCreateTime())
                        .creator(Objects.equals(0, remark.getCreateBy()) ? "System" : userInfoMap.getOrDefault(remark.getCreateBy(), new OumUserInfoRes()).getName())
                        .creatorNo(Objects.equals(0, remark.getCreateBy()) ? "System" : userInfoMap.getOrDefault(remark.getCreateBy(), new OumUserInfoRes()).getCode())
                        .build();
            }).collect(Collectors.toList());
        }
        // 商品信息
        List<VcOrderDetailVO.ProductInfo> productInfoList = vcOrderItemPOList.stream().map(item -> {
            return VcOrderDetailVO.ProductInfo.builder()
                    .psku(item.getPsku())
                    .asin(item.getAsin())
                    .barcode(item.getBarcode())
                    .imageUrl(fileMap.get(item.getImageId()) != null ? fileMap.get(item.getImageId()).getUrl() : "")
                    .imageId(item.getImageId())
                    .orderedQuantity(item.getOrderedQuantity())
                    .acceptedQuantity(item.getAcceptedQuantity())
                    .shippedQuantity(item.getShippedQuantity())
                    .receivedQuantity(item.getReceivedQuantity())
                    .currency(item.getCurrency())
                    .unitPrice(item.getUnitPrice())
                    .tax(item.getTax())
                    .subTotal(item.getSubTotal())
                    .build();
        }).collect(Collectors.toList());

        VcOrderDetailVO detailVO = VcOrderDetailVO.builder()
                .orderId(vcOrderPO.getId())
                .orderBaseInfo(orderBaseInfo)
                .diInfo(diInfo)
                .amountInfo(amountInfo)
                .remarkList(remarkInfoList)
                .productList(productInfoList)
                .build();
        return detailVO;
    }
}