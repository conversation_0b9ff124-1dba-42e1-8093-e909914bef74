package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingExportExcel;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMapping;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingPageVO;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SkuMappingExcelConvertor {

	SkuMapping toDomain(SkuMappingImportExcel param);

	@Mapping(target = "updater", expression = "java(param.getUpdater()  +  '('  + param.getUpdaterNo() + ')'  )")
	@Mapping(target = "version", expression = "java(param.getVersion())")
	@Mapping(target = "fulfillmentType",
			expression = "java(com.renpho.erp.oms.domain.salemanagement.FulfillmentType.enumOf(param.getFulfillmentType()).getName())")
	@Mapping(target = "fbaInventoryCounted",
			expression = "java(param.getFbaInventoryCounted() == null ? null : (param.getFbaInventoryCounted() == 1 ? \"是\" : \"否\"))")
	@Mapping(target = "status", expression = "java(param.getStatus() == null ? null : (param.getStatus() == 1 ? \"启用\" : \"禁用\"))")
	SkuMappingExportExcel toExcel(SkuMappingPageVO param);
}
