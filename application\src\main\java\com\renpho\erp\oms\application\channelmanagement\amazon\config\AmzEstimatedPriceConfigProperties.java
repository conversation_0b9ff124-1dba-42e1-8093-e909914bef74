package com.renpho.erp.oms.application.channelmanagement.amazon.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/1/17
 */
@Data
@Component
@ConfigurationProperties(prefix = "renpho.amz.estimated-price")
public class AmzEstimatedPriceConfigProperties {
    private boolean enabled = true;
    private int day = 30;
    private int orderNum = 5;
    /**
     * listing缓存时间,默认6小时
     */
    private long listingCacheMinutes = TimeUnit.HOURS.toMinutes(6);
    private List<String> orderStatus = List.of("Shipped", "InvoiceUnconfirmed");
}
