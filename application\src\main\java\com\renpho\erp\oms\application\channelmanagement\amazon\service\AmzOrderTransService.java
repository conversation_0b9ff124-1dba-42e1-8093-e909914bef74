package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderItemPushDTO;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderPushDTO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItemsSourceJson;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderSourceJson;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderItemsSourceRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSourceJsonRepository;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class AmzOrderTransService {
    // 最大查询数
    private final Integer LIMIT = 100;


    private final AmzOrderSourceJsonRepository amzOrderSourceJsonRepository;
    private final AmzOrderItemsSourceRepository amzOrderItemsSourceRepository;
    private final AmzOrderService amzOrderService;

    public void trans() {
        // 一次查询100个
        List<AmzOrderSourceJson> amzOrderSourceJsonList = amzOrderSourceJsonRepository
                .listBySyncOmsStatus(Boolean.FALSE, LIMIT);
        if (CollectionUtil.isNotEmpty(amzOrderSourceJsonList)) {
            // 查询商品信息
            Map<String, AmzOrderItemsSourceJson> amzOrderItemsSourceJsonMap = amzOrderItemsSourceRepository
                    .mapByOrderIds(amzOrderSourceJsonList.stream()
                            .map(AmzOrderSourceJson::getPlatOrderId)
                            .collect(Collectors.toList()));
            // 组装
            amzOrderSourceJsonList.forEach(amzOrderSourceJson -> {
                try {
                    if (amzOrderItemsSourceJsonMap.containsKey(amzOrderSourceJson.getPlatOrderId())) {
                        AmzOrderPushDTO amazonOrderPushDTO = JSONKit.parseObject(amzOrderSourceJson.getOrderJson(),
                                AmzOrderPushDTO.class);
                        AmzOrderItemsSourceJson amzOrderItemsSourceJson = amzOrderItemsSourceJsonMap
                                .get(amzOrderSourceJson.getPlatOrderId());
                        List<AmzOrderItemPushDTO> amzOrderItemFilterPIIList = JSONKit
                                .parseObject(amzOrderItemsSourceJson.getOrderItemsJson(), List.class);
                        amazonOrderPushDTO.setSellerId(amzOrderSourceJson.getSellerId());
                        amazonOrderPushDTO.setStoreId(amzOrderSourceJson.getStoreId());
                        amazonOrderPushDTO.setAmzOrderItemFilterPIIList(amzOrderItemFilterPIIList);
                        SpringUtil.getBean(getClass()).doTrans(amazonOrderPushDTO);

                    }
                }
                catch (Exception e) {
                    log.error("处理Amazon订单同步异常", e);
                    throw e; // 抛出异常以触发事务回滚
                }
            });

        }
    }

    @Transactional
    public void doTrans(AmzOrderPushDTO amzOrderPushDTO) {
        amzOrderService.createOrUpdate(amzOrderPushDTO);
        amzOrderSourceJsonRepository.toSyncOmsStatus(amzOrderPushDTO.getAmazonOrderId());
    }
}
