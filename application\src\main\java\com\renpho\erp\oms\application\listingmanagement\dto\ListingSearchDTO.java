package com.renpho.erp.oms.application.listingmanagement.dto;

import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Listing分批拉取结果
 * <AUTHOR>
 */
@Getter
@Setter
public class ListingSearchDTO {

	/**
	 * 上一页返回的标记，用于检索下一页
	 */
	private String searchAfter;

	/**
	 * 时间范围检索listing的时候会用到,平台的接口支持时间范围大小有限制,过了这段时间范围,需要重置时间范围跟分页页码,例如ebay
	 */
	private LocalDateTime startTime;
	private LocalDateTime endTime;
	private Integer pageNo;

	/**
	 * 原始Listing数据列表
	 */
	private List<SalesChannelListingSource> listingSourceList;
}
