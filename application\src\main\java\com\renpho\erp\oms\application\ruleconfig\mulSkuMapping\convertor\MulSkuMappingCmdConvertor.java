package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor;

import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingUpdateCmd;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.model.MulSkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MulSkuMappingCmdConvertor {
	MulSkuMappingAggRoot toDomain(MulSkuMappingAddCmd param);

	@Mapping(target = "mulChannelType", expression = "java(com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType.enumOf(param.getMulChannelType()))")
	MulSkuMappingAggRoot.MulSkuMappingLine toDomain(MulSkuMappingAddCmd.MulSkuMappingLineAddCmd param);

	MulSkuMappingAggRoot toDomain(MulSkuMappingUpdateCmd param);
}
