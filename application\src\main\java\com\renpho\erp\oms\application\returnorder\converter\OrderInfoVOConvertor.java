package com.renpho.erp.oms.application.returnorder.converter;

import com.renpho.erp.oms.application.returnorder.vo.OrderInfoVO;
import com.renpho.erp.oms.application.returnorder.vo.OrderItemInfoVO;
import com.renpho.erp.oms.domain.returnorder.model.OrderItemInfo;
import com.renpho.erp.oms.domain.salemanagement.SaleOrder;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.util.List;
import java.util.Optional;

/**
 * @desc:
 * @time: 2025-03-20 14:18:22
 * @author: Alina
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface OrderInfoVOConvertor {

	@Mapping(target = "items", source = "orderItemInfoVOList")
	@Mapping(target = "warehouseId", expression = "java(mapFulfillmentPO(saleOrderFulfillmentPo))")
	OrderInfoVO toOrderInfoVO(SaleOrder saleOrder, List<OrderItemInfoVO> orderItemInfoVOList,
			SaleOrderFulfillmentPO saleOrderFulfillmentPo);

	default Integer mapFulfillmentPO(SaleOrderFulfillmentPO saleOrderFulfillmentPO) {
		return Optional.ofNullable(saleOrderFulfillmentPO).map(SaleOrderFulfillmentPO::getWarehouseId).orElse(null);
	}

	OrderItemInfoVO toOrderItemInfoVO(OrderItemInfo orderItemInfo);
}
