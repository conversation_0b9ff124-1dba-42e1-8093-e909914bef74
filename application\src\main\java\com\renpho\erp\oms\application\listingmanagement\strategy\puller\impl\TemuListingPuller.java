package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.apiproxy.temu.model.ShopAccount;
import com.renpho.erp.apiproxy.temu.model.TemuResponse;
import com.renpho.erp.apiproxy.temu.model.goods.GetGoodsListData;
import com.renpho.erp.apiproxy.temu.model.goods.GetGoodsPriceListData;
import com.renpho.erp.apiproxy.temu.model.goods.GetGoodsPriceListRequest;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.dto.temu.TemuListingPullDto;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.TemuClient;
import com.renpho.karma.json.JSONKit;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TemuListingPuller extends AbstractListingPullerTemplate {
    @Autowired
    private TemuClient temuClient;

    @Override
    protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
        log.info("temu站点拉取listing数据,storeId是{}, 店铺信息是{},分页参数是{}", storeAuthorization.getStoreId(), JSONKit.toJSONString(storeAuthorization), JSONKit.toJSONString(listingSearchQuery));
        String temuShopId = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
                .map(StoreAuthorizationVo::getAuthorization)
                .map(StoreAuthorizationVo.Authorization::getTemuShopId)
                .filter(StrUtil::isNotBlank)
                .orElseThrow(() -> new BusinessException("temu站点id为空,storeId是%s".formatted(storeAuthorization.getStoreId())));
        String siteCode = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
                .map(StoreAuthorizationVo::getSiteCode)
                .filter(StrUtil::isNotBlank)
                .orElseThrow(() -> new BusinessException("temu站点code为空,storeId是%s".formatted(storeAuthorization.getStoreId())));
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setShopId(temuShopId);
        shopAccount.setSiteCode(siteCode);
        // 请求获取listing数据
        TemuResponse<GetGoodsListData> temuResponse = temuClient.getGoodsList(shopAccount, listingSearchQuery.getPageNo(), listingSearchQuery.getPageSize());
        // 请求获取listing价格数据
        GetGoodsListData getGoodsListData = temuResponse.getResult();
        Map<Long, List<GetGoodsPriceListData.SkuSupplierPrice>> goodPriceMap = getGoodPriceMap(getGoodsListData, shopAccount);


        // 组装数据
        return buildListingSearchDTO(storeAuthorization, getGoodsListData, goodPriceMap);
    }

    private Map<Long, List<GetGoodsPriceListData.SkuSupplierPrice>> getGoodPriceMap(GetGoodsListData getGoodsListData, ShopAccount shopAccount) {
        List<Long> productSkuIdList = getGoodsListData.getData()
                .stream()
                .map(GetGoodsListData.Goods::getProductSkuSummaries)
                .flatMap(List::stream)
                .map(GetGoodsListData.ProductSkuSummary::getProductSkuId)
                .collect(Collectors.toList());
        Map<Long, List<GetGoodsPriceListData.SkuSupplierPrice>> result = new HashMap<>();
        // temu限制每次最大只能查50条
        for (List<Long> tmpProductSkuIdList : ListUtil.split(productSkuIdList, 50)) {
            GetGoodsPriceListRequest getGoodsPriceListRequest = new GetGoodsPriceListRequest();
            getGoodsPriceListRequest.setProductSkuIds(tmpProductSkuIdList);
            TemuResponse<GetGoodsPriceListData> goodsPriceList = temuClient.getGoodsPriceList(shopAccount, getGoodsPriceListRequest);
            Optional.ofNullable(goodsPriceList.getResult())
                    .map(GetGoodsPriceListData::getProductSkuSupplierPriceList)
                    .orElse(Collections.emptyList())
                    .forEach(skuSupplierPrice -> result.computeIfAbsent(skuSupplierPrice.getProductId(), k -> new ArrayList<>()).add(skuSupplierPrice));
        }

        return result;
    }

    private ListingSearchDTO buildListingSearchDTO(StoreAuthorizationDTO storeAuthorization, GetGoodsListData getGoodsListData, Map<Long, List<GetGoodsPriceListData.SkuSupplierPrice>> goodPriceMap) {
        List<GetGoodsListData.Goods> goodsList = getGoodsListData.getData();
        List<SalesChannelListingSource> salesChannelListingSourceList = new ArrayList<>(goodsList.size());
        for (GetGoodsListData.Goods goods : goodsList) {
            SalesChannelListingSource salesChannelListingSource = super.buildListingSource(storeAuthorization);
            salesChannelListingSource.setListingId(String.valueOf(goods.getProductId()));

            salesChannelListingSource.setContent(JSONKit.toJSONString(new TemuListingPullDto(goods, goodPriceMap.get(goods.getProductId()))));

            salesChannelListingSourceList.add(salesChannelListingSource);
        }

        ListingSearchDTO listingSearchDTO = new ListingSearchDTO();
        listingSearchDTO.setListingSourceList(salesChannelListingSourceList);
        return listingSearchDTO;
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.TEM_CHANNEL_CODE;
    }


    @NotNull
    @Override
    protected ListingSearchQuery buildSearchAfterQuery() {
        ListingSearchQuery listingSearchQuery = new ListingSearchQuery();
        // temu限制分页最大100
        listingSearchQuery.setPageNo(1);
        listingSearchQuery.setPageSize(100);
        return listingSearchQuery;
    }

    @Override
    protected void fillNextSearchQuery(ListingSearchQuery listingSearchQuery, ListingSearchDTO listingSearchDTO) {
        // 分页页码+1,分页下一页
        listingSearchQuery.setPageNo(listingSearchQuery.getPageNo() + 1);
    }
}
