package com.renpho.erp.oms.infrastructure.persistence.customer.service;


import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.oms.infrastructure.expression.b2b.RoleLabelExpression;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.CustomerInfoPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 客户数据权限服务
 * 专门处理带有数据权限控制的查询操作
 */
@Service
@RequiredArgsConstructor
public class CustomerDataPermissionService {
    private final CustomerInfoService customerInfoService;

    /**
     * 带数据权限的单个客户查询
     * 根据用户角色标签（CM-海外区域经理、B2BSA-销售助理）过滤数据
     *
     * @param id 客户id
     * @return CustomerInfoPO
     */
    @DataPermission(condition = RoleLabelExpression.class)
    public CustomerInfoPO selectOneWithPermission(Long id) {
        return customerInfoService.getById(id);
    }
}
