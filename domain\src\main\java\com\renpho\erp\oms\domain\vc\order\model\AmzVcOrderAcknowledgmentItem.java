package com.renpho.erp.oms.domain.vc.order.model;

import java.math.BigDecimal;
import java.time.LocalDate;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单确认商品值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderAcknowledgmentItem {

	/**
	 * 商品行号
	 */
	private String itemSequenceNumber;

	/**
	 * 亚马逊商品标识符
	 */
	private String amazonProductIdentifier;

	/**
	 * 供应商商品标识符
	 */
	private String vendorProductIdentifier;

	/**
	 * 确认数量
	 */
	private Integer acknowledgedQuantity;

	/**
	 * 计量单位
	 */
	private String unitOfMeasure;

	/**
	 * 单位尺寸
	 */
	private Integer unitSize;

	/**
	 * 单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 币种
	 */
	private String currencyCode;

	/**
	 * 确认状态
	 */
	private String acknowledgmentStatus;

	/**
	 * 拒绝原因
	 */
	private String rejectionReason;

	/**
	 * 预计发货日期
	 */
	private LocalDate estimatedShipDate;

	/**
	 * 预计交货日期
	 */
	private LocalDate estimatedDeliveryDate;

	/**
	 * 商品备注
	 */
	private String itemNotes;

	/**
	 * 检查是否被接受
	 * @return boolean
	 */
	public boolean isAccepted() {
		return "ACCEPTED".equals(acknowledgmentStatus);
	}

	/**
	 * 检查是否被拒绝
	 * @return boolean
	 */
	public boolean isRejected() {
		return "REJECTED".equals(acknowledgmentStatus);
	}

	/**
	 * 检查是否部分接受
	 * @return boolean
	 */
	public boolean isPartiallyAccepted() {
		return "PARTIALLY_ACCEPTED".equals(acknowledgmentStatus);
	}

}
