package com.renpho.erp.oms.application.ordershipaudit.service;

import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * @desc: 上传pdf文件服务
 * @time: 2025-04-09 11:37:04
 * @author: <PERSON><PERSON>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UploadPdfService {

	private final FileClient fileClient;

	/**
	 * 从URL下载PDF并上传
	 * @param bytes 文件
	 * @return 包含文件URL的响应结果
	 */
	public String uploadPdfFromBytes(Byte[] bytes, String url) {
		// 获取文件名
		String fileName = extractFileNameFromUrl(url);
		// 上传文件
		return fileClient.uploadPdf(bytes, fileName);
	}

	/**
	 * 从url中抽取文件名称
	 * @param url 文件地址
	 * @return 文件名称
	 */
	private String extractFileNameFromUrl(String url) {
		try {
			String path = new URL(url).getPath();
			String fileName = path.substring(path.lastIndexOf('/') + 1);
			// 如果文件名为空，生成带有时间戳的默认文件名
			if (fileName.isEmpty()) {
				return "document_" + System.currentTimeMillis() + ".pdf";
			}
			// 如果文件名没有 .pdf 后缀，强制修改为 .pdf
			if (!fileName.endsWith(".pdf")) {
				fileName = fileName.substring(0, fileName.lastIndexOf('.') + 1) + "pdf";
			}
			return fileName;
		}
		catch (MalformedURLException e) {
			return "document_" + System.currentTimeMillis() + ".pdf";
		}
	}
}
