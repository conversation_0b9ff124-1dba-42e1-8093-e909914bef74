package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderLineItemLog;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderLineItemVO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderLineItem;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface SpfOrderLineItemLogConvertor {
	SpfOrderLineItemLog toLog(SpfOrderLineItem param);

	SpfOrderLineItemVO toVO(SpfOrderLineItem param);
}
