package com.renpho.erp.oms.application.platform.convert;

import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;
import com.renpho.erp.oms.infrastructure.persistence.amazon.po.AmzOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.ebay.po.EbyOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.mercado.po.MclOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.shopify.po.SpfOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.temu.po.TemOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.tiktok.po.TtOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.walmart.po.WmtOrderPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description 监控平台订单的convertor
 * @date 2025/5/8 13:50
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MonitorPlatformOrderConvertor {

    @Mapping(source = "amazonOrderId", target = "orderId")
    MonitorPlatformOrderDto amazonOrderToDto(AmzOrderPO amzOrderPO);

    @Mapping(source = "purchaseOrderId", target = "orderId")
    MonitorPlatformOrderDto walmartOrderToDto(WmtOrderPO wmtOrderPO);

    @Mapping(source = "tiktokOrderId", target = "orderId")
    MonitorPlatformOrderDto titTokOrderToDto(TtOrderPO ttOrderPO);

    @Mapping(source = "mercadoOrderId", target = "orderId")
    MonitorPlatformOrderDto mercadoOrderToDto(MclOrderPO mclOrderPO);

    @Mapping(source = "shopifyOrderId", target = "orderId")
    MonitorPlatformOrderDto shopifyOrderToDto(SpfOrderPO spfOrderPO);

    @Mapping(source = "parentOrderSn", target = "orderId")
    MonitorPlatformOrderDto temuOrderToDto(TemOrderPO temOrderPO);

    @Mapping(source = "ebyOrderId", target = "orderId")
    MonitorPlatformOrderDto ebayOrderToDto(EbyOrderPO ebyOrderPO);
}
