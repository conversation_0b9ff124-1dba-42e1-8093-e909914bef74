<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderMapper">

    <select id="selectPermissionPage" resultType="com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderPO">
        SELECT
        o.id,
        o.order_no,
        o.vc_purchase_no,
        o.store_id,
        o.store_name,
        o.order_status,
        o.accepted_status,
        o.business_type,
        o.ship_from,
        o.ship_to,
        o.shipping_window_start,
        o.shipping_window_end,
        o.ordered_time,
        o.accepted_time,
        o.currency,
        o.product_price,
        o.exception_reason,
        o.remark,
        o.create_time,
        o.update_time
        FROM
        oms_vc_order o
        WHERE
        o.is_deleted = 0
        <if test="query.orderNos != null and query.orderNos.size() > 0">
            AND o.order_no IN
            <foreach collection="query.orderNos" item="orderNo" open="(" separator="," close=")">
                #{orderNo}
            </foreach>
        </if>
        <if test="query.vcPurchaseNos != null and query.vcPurchaseNos.size() > 0">
            AND o.vc_purchase_no IN
            <foreach collection="query.vcPurchaseNos" item="vcPurchaseNo" open="(" separator="," close=")">
                #{vcPurchaseNo}
            </foreach>
        </if>
        <if test="query.storeId != null">
            AND o.store_id = #{query.storeId}
        </if>
        <if test="query.orderStatus != null">
            AND o.order_status = #{query.orderStatus}
        </if>
        <if test="query.businessType != null">
            AND o.business_type = #{query.businessType}
        </if>
        <if test="query.siteCode != null and query.siteCode != ''">
            AND o.site_code = #{siteCode}
        </if>
        <if test="query.acceptedTimeStart != null and query.acceptedTimeEnd != null">
            AND o.accepted_time BETWEEN #{query.acceptedTimeStart} AND #{query.acceptedTimeEnd}
        </if>
        <if test="query.createTimeStart != null and query.createTimeEnd != null">
            AND o.create_time BETWEEN #{query.createTimeStart} AND #{query.createTimeEnd}
        </if>
        <if test="query.psku != null and query.psku != ''">
            AND EXISTS (
            SELECT 1 FROM oms_vc_order_item oi
            WHERE oi.order_id = o.id
            AND oi.is_deleted = 0
            AND oi.psku = #{query.psku}
            )
        </if>
        <if test="query.asin != null and query.asin != ''">
            AND EXISTS (
            SELECT 1 FROM oms_vc_order_item oi
            WHERE oi.order_id = o.id
            AND oi.is_deleted = 0
            AND oi.asin = #{query.asin}
            )
        </if>
        <if test="query.barcode != null and query.barcode != ''">
            AND EXISTS (
            SELECT 1 FROM oms_vc_order_item oi
            WHERE oi.order_id = o.id
            AND oi.is_deleted = 0
            AND oi.barcode = #{query.barcode}
            )
        </if>
        ORDER BY o.update_time DESC
    </select>

    <select id="selectDistinctSiteCodes" resultType="java.lang.String">
        SELECT DISTINCT o.site_code
        FROM oms_vc_order o
        WHERE o.is_deleted = 0
        AND o.site_code IS NOT NULL
        AND o.site_code != ''
        ORDER BY o.site_code
    </select>

</mapper>
