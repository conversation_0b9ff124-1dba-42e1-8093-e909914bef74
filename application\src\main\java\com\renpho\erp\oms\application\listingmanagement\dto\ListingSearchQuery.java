package com.renpho.erp.oms.application.listingmanagement.dto;

import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * Listing分批拉取查询参数
 * <AUTHOR>
 */
@Getter
@Setter
public class ListingSearchQuery {

	/**
	 * 上一页返回的标记（如id，游标等），用于检索下一页
	 */
	private String searchAfter;

	/**
	 * 根据时间范围查询
	 */
	private LocalDateTime startTime;
	private LocalDateTime endTime;

	/**
	 * 根据页码查询
	 */
	private Integer pageNo;
	private Integer pageSize;
}
