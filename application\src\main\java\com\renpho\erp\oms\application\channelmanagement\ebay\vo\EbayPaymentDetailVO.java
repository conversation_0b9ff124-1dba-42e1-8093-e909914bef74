package com.renpho.erp.oms.application.channelmanagement.ebay.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 付款明细出参
 * @time: 2025-04-14 14:38:25
 * @author: <PERSON><PERSON>
 */
@Data
public class EbayPaymentDetailVO {
	/**
	 * 时间：付款为 paymentDate，退款为 refundDate
	 */
	private LocalDateTime time;

	/**
	 * 类型：Payment 或 Refund
	 */
	private String type;

	/**
	 * 付款方式：付款时有值，退款时为空
	 */
	private String paymentMethod;

	/**
	 * 状态：付款为 paymentStatus，退款为 refundStatus
	 */
	private String status;

	/**
	 * 金额：付款和退款共用 amount
	 */
	private String amount;

    /**
     * 金额币种
     */
    private String currency;

	/**
	 * 关联 ID
	 */
	private String referId;
}
