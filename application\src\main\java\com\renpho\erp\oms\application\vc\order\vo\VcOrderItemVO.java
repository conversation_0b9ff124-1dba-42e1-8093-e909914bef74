package com.renpho.erp.oms.application.vc.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * VC订单商品信息VO
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单商品信息")
public class VcOrderItemVO {

    @Schema(description = "图片ID")
    private String imageId;

    @Schema(description = "图片URL")
    private String imageUrl;

    @Schema(description = "采购SKU")
    private String psku;

    @Schema(description = "ASIN")
    private String asin;

    @Schema(description = "发货数量")
    private Integer shippedQuantity;

    @Schema(description = "接收数量")
    private Integer receivedQuantity;

    @Schema(description = "下单数量")
    private Integer orderedQuantity;

    @Schema(description = "商品数量（总数）")
    private Integer itemCount;
}
