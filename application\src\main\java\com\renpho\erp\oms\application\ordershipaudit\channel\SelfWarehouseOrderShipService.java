package com.renpho.erp.oms.application.ordershipaudit.channel;

import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.converter.LogisticsShipmentConvertor;
import com.renpho.erp.oms.application.ordershipaudit.strategy.logistics.LogisticsFactory;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.repository.LogisticsShipmentRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 自建仓-发货 应用服务
 * <AUTHOR>
 */
@Slf4j
@Component
public class SelfWarehouseOrderShipService extends AbstractOrderShipService {

	private final SaleOrderRepository saleOrderRepository;
	private final LogisticsShipmentRepository logisticsShipmentRepository;
	private final LogisticsShipmentConvertor logisticsShipmentConvertor;

	public SelfWarehouseOrderShipService(SaleOrderQueryService saleOrderQueryService, SaleOrderRepository saleOrderRepository,
			LogisticsShipmentRepository logisticsShipmentRepository, LogisticsShipmentConvertor logisticsShipmentConvertor) {
		super(saleOrderQueryService);
		this.saleOrderRepository = saleOrderRepository;
		this.logisticsShipmentRepository = logisticsShipmentRepository;
		this.logisticsShipmentConvertor = logisticsShipmentConvertor;
	}

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.TEMU_ONLINE_LABEL, FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE,
				FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE);
	}

	@Override
	protected void createFulfillmentOrder(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress) {
		// 构建物流下单信息
		LogisticsShipmentAggRoot logisticsShipmentAggRoot = logisticsShipmentConvertor.createLogisticsShipment(shipAuditAggRoot,
				saleOrderAggRoot);
		// 物流下单
		LogisticsFactory.get(shipAuditAggRoot.getFulfillmentServiceType())
			.createLogisticsShipment(shipAuditAggRoot, saleOrderAggRoot, receivingAddress, logisticsShipmentAggRoot);
		// 更新履约物流单号
		saleOrderRepository.updateFulfillmentLogistics(saleOrderAggRoot);
		// 保存物流下单信息
		logisticsShipmentRepository.save(logisticsShipmentAggRoot);
		// 如果物流下单成功，后续交由定时任务获取面单及创建wms出库单
	}

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot) {

	}
}
