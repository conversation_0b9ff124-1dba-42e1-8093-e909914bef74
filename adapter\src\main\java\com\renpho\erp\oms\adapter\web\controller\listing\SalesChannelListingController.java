package com.renpho.erp.oms.adapter.web.controller.listing;

import com.renpho.erp.oms.application.listingmanagement.service.SalesChannelListingService;
import com.renpho.erp.oms.application.listingmanagement.vo.SalesChannelListingVO;
import com.renpho.erp.oms.domain.listingmanagement.query.SkuListingQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 刊登列表
 * <AUTHOR>
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/skuListing/**")
@RequestMapping("/skuListing")
@AllArgsConstructor
public class SalesChannelListingController {

	private final SalesChannelListingService salesChannelListingService;

	/**
	 * 分页查询
	 *
	 * @param query 查询参数
	 * @return Paging<SalesChannelListing>
	 */
	@PostMapping("/page")
	@PreAuthorize("hasPermission('oms:itemsListing:page')")
	public R<Paging<SalesChannelListingVO>> page(@RequestBody SkuListingQuery query) {
		return R.success(salesChannelListingService.page(query));
	}

	/**
	 * 导出
	 *
	 * @param query 查询参数
	 * @param response ServletResponse
	 */
	@PostMapping(value = "/export")
	@PreAuthorize("hasPermission('oms:itemsListing:export')")
	public void export(@RequestBody SkuListingQuery query, HttpServletResponse response) {
		salesChannelListingService.exportExcel(query, response);
	}
}
