package com.renpho.erp.oms.application.vc.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * VC订单开票信息VO
 * <p>
 * 用于显示开票提交界面所需的数据。
 * </p>
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单开票信息VO")
public class VcOrderInvoiceInfoVO {

    /**
     * 订单ID
     */
    @Schema(description = "订单ID")
    private Long orderId;

    /**
     * 门店ID
     */
    @Schema(description = "门店ID")
    private Integer storeId;

    /**
     * 门店名称
     */
    @Schema(description = "门店名称")
    private String storeName;

    /**
     * 发货窗口开始时间
     */
    @Schema(description = "发货窗口开始时间")
    private LocalDateTime shippingWindowStart;

    /**
     * 发货窗口结束时间
     */
    @Schema(description = "发货窗口结束时间")
    private LocalDateTime shippingWindowEnd;
}