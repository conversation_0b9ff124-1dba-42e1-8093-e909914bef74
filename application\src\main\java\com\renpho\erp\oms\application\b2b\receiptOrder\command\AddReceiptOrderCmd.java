package com.renpho.erp.oms.application.b2b.receiptOrder.command;

import com.renpho.erp.oms.infrastructure.common.validation.DecimalScale;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 添加收款单
 * @date 2025/6/20 15:12
 */
@Data
public class AddReceiptOrderCmd {

    /**
     * B2B订单号
     */
    @NotNull(message = "ORDER_NO_EMPTY")
    private String orderNo;


    /**
     * 银行参考号
     */
    private String bankReferNo;

    /**
     * 客户付款金额
     */
    @NotNull(message = "CUSTOMER_PAID_AMOUNT_NO_EMPTY")
    @DecimalMin(value = "0.00", inclusive = false, message = "CUSTOMER_PAID_AMOUNT_GT_ZERO_TWO_SCALE")
    @DecimalScale(scale = 2, message = "CUSTOMER_PAID_AMOUNT_GT_ZERO_TWO_SCALE")
    private BigDecimal customerPaidAmount;


    /**
     * 客户付款时间
     */
    @NotNull(message = "CUSTOMER_PAID_TIME_NO_EMPTY")
    private LocalDateTime customerPaidTime;

    /**
     * 客戶付款信息文件url,调/erp-ftm/file/upload接口，path传customerPaidInfoFile返回的url
     */
    private String customerPaidInfoFileUrl;

    /**
     * 客戶付款信息文件名.调/erp-ftm/file/upload接口返回的originalFilename
     */
    private String customerPaidInfoFileName;
}
