package com.renpho.erp.oms.application.channelmanagement.walmart.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WmtOrderLineStatusVO {
	/**
	 * 商品行号
	 */
	private String lineNumber;
	/**
	 * 卖方SKU
	 */
	private String itemSku;

	/**
	 * 状态 Created Acknowledged Shipped Delivered Cancelled Refund
	 */
	private String status;

	/**
	 * 数量
	 */
	private String statusQuantityAmount;

	/**
	 * 发货时间
	 */
	private LocalDateTime trackingInfoShipDateTime;

	/**
	 * 发货人
	 */
	private String trackingInfoCarrier;

	/**
	 * 追踪号码
	 */
	private String trackingInfoTrackingNumber;

	/**
	 * 取消原因
	 */
	private String cancellationReason;
}
