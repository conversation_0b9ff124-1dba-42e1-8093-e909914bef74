package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 客户信息分页vo
 * @time: 2025-05-06 09:58:00
 * @author: <PERSON><PERSON>
 */
@Data
public class CustomerPageVO {

	/**
	 * 客户id
	 */
	private Long id;

	/**
	 * 客户公司名称
	 */
	private String customerCompanyName;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 联系人 主联系人的资料
	 */
	private ContactInfo primaryContactInfo;

	/**
	 * 海外区域经理名称
	 */
	private String countryManagerName;

	/**
	 * 海外区域经理工号
	 */
	private String countryManagerCode;

	/**
	 * 销售助理
	 */
	private String salesAssistantName;

	/**
	 * 销售助理工号
	 */
	private String salesAssistantCode;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 状态 0 禁用 1启用
	 */
	private Integer status;

	@Data
	public static class ContactInfo {
		/**
		 * 联系人姓名
		 */
		private String contactName;
		/**
		 * 联系人电话
		 */
		private String contactPhone;
		/**
		 * 联系人邮箱
		 */
		private String contactEmail;
	}

}
