package com.renpho.erp.oms.application.vc.order.listener;

import com.renpho.karma.json.JSONKit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import com.renpho.erp.oms.application.vc.order.service.VcOrderConvertService;
import com.renpho.erp.oms.domain.vc.order.event.AmzVcOrderConvertEvent;

import lombok.RequiredArgsConstructor;

/**
 * 亚马逊VC订单转换-监听器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AmzVcOrderConvertListener {

	private final VcOrderConvertService vcOrderConvertService;


	/**
	 * 亚马逊VC订单转换监听（事务提交后处理） 如果没有事务，不处理该事件（交由定时任务处理）
	 * @param event 事件
	 */
	@Async("eventMdcThreadPoolTaskExecutor")
	@TransactionalEventListener
	public void listen(AmzVcOrderConvertEvent event) {
		log.info("VC订单转换ids:{}", JSONKit.toJSONString(event.ids()));
		vcOrderConvertService.convert(event.ids(), null);
	}
}
