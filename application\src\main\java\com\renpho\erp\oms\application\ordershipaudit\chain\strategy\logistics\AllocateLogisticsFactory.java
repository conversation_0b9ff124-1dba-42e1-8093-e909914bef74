package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

/**
 * 分物流-工厂
 * <AUTHOR>
 */
public class AllocateLogisticsFactory {

	private AllocateLogisticsFactory() {
	}

	private static final Map<Integer, AllocateLogistics> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 获取
	 * @param fulfillmentServiceType 履约服务类型
	 * @return ChannelConvertSaleOrderStrategy
	 */
	public static AllocateLogistics get(FulfillmentServiceType fulfillmentServiceType) {
		Assert.notNull(fulfillmentServiceType, "获取分物流策略，履约服务类型不能为空");
		if (STRATEGY_MAP.containsKey(fulfillmentServiceType.getValue())) {
			return STRATEGY_MAP.get(fulfillmentServiceType.getValue());
		}
		throw new IllegalArgumentException(MessageFormat.format("获取分物流策略，找不到 {0} ", fulfillmentServiceType.getName()));
	}

	/**
	 * 注册.
	 * @param fulfillmentServiceType 履约服务类型
	 */
	public static void register(FulfillmentServiceType fulfillmentServiceType, AllocateLogistics allocateLogistics) {
		Assert.notNull(fulfillmentServiceType, "履约服务类型不能为空");
		STRATEGY_MAP.put(fulfillmentServiceType.getValue(), allocateLogistics);
	}
}
