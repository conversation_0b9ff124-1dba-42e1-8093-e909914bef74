<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.mapper.MulSkuMappingMapper">

    <select id="page" resultType="com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.vo.MulSkuMappingPageVO">
        SELECT
        osm.id,
        osm.order_store_id,
        osm.status,
        osm.order_psku,
        osm.update_by,
        osm.update_time,
        osml.mul_channel_type AS mulChannelType,
        osml.mul_channel_store_id AS mulChannelStoreId,
        osml.mul_channel_msku AS mulChannelMsku,
        osml.priority
        FROM
        oms_multi_channel_sku_mapping_line osml
        LEFT JOIN
        oms_multi_channel_sku_mapping osm
        ON
        osm.id = osml.mul_channel_sku_mapping_id AND osml.is_deleted = 0
        <where>
            osm.is_deleted = 0

            <!-- 店铺ID -->
            <if test="mulSkuMappingPageQuery.orderStoreIds != null and mulSkuMappingPageQuery.orderStoreIds.size() > 0">
                AND osm.order_store_id IN
                <foreach item="storeId" collection="mulSkuMappingPageQuery.orderStoreIds" open="(" separator=","
                         close=")">
                    #{storeId}
                </foreach>
            </if>

            <!-- 渠道ID -->
            <if test="mulSkuMappingPageQuery.orderChannelId != null">
                AND osm.order_channel_id = #{mulSkuMappingPageQuery.orderChannelId}
            </if>

            <!-- 多渠道类型 -->
            <if test="mulSkuMappingPageQuery.mulChannelType != null">
                AND osml.mul_channel_type = #{mulSkuMappingPageQuery.mulChannelType}
            </if>

            <!-- 多渠道店铺 -->
            <if test="mulSkuMappingPageQuery.mulChannelStoreId != null">
                AND osml.mul_channel_store_id = #{mulSkuMappingPageQuery.mulChannelStoreId}
            </if>

            <!-- 多渠道msku -->
            <if test="mulSkuMappingPageQuery.mulChannelMsku != null and mulSkuMappingPageQuery.mulChannelMsku.trim().length() > 0">
                AND osml.mul_channel_msku LIKE CONCAT('%', #{mulSkuMappingPageQuery.mulChannelMsku}, '%')
            </if>

            <!-- 订单psku -->
            <if test="mulSkuMappingPageQuery.orderPsku != null and mulSkuMappingPageQuery.orderPsku.trim().length() > 0">
                AND osm.order_psku LIKE CONCAT('%', #{mulSkuMappingPageQuery.orderPsku}, '%')
            </if>

        </where>
        ORDER BY osm.update_time DESC
    </select>
    <select id="queryByOrderPskuList"
            resultType="com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.po.MulSkuMappingPO">
        select * from oms_multi_channel_sku_mapping
        <where>
            <if test="orderPskus != null and orderPskus.size() != 0">
                order_psku in
                <foreach item="orderPsku" collection="orderPskus" open="(" separator="," close=")">
                    #{orderPsku}
                </foreach>
            </if>
            <if test="orderStoreId != null">
                and order_store_id = #{orderStoreId}
            </if>
            and is_deleted = 0
        </where>
    </select>

</mapper>
