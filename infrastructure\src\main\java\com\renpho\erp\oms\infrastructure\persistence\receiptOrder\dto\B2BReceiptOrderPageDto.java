package com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class B2BReceiptOrderPageDto {
    private Long id;
    /**
     * 银行参考号
     */
    private String bankReferNo;

    /**
     * 自定义收款单号
     */
    private String receiptOrderNo;


    /**
     * 收款所属的订单号
     */
    private String b2bOrderNo;

    private Long b2bOrderId;

    /**
     * 客户id(oms_customer_info表的id)
     */
    private Long customerId;
    /**
     * 公司名称   aaaaaaaaaaaaaaa
     */
    private String customerCompanyName;

    /**
     * 客户国家  aaaaaaaaaaaaa
     */
    private String customerCountry;

    /**
     * 贸易条款编码 aaaaaaaaaaaa
     */
    private String incotermsCode;

    /**
     * 付款条款编码 aaaaaaaaaaaaaaaa
     */
    private String paymentTermsCode;

    /**
     * 币种 aaaaaaa
     */
    private String currency;

    /**
     * 客户付款金额
     */
    private BigDecimal customerPaidAmount;

    /**
     * 收款金额
     */
    private BigDecimal receiptAmount;



    /**
     * 订单总金额 aaaaaaaaaaaa
     */
    private BigDecimal orderTotalAmount;

    /**
     * 订单已收款的金额 aaaaaaa
     */
    private BigDecimal orderReceiptAmount;
    /**
     * 状态 1-待确认,2-已确认,3-已取消
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 客户付款时间
     */
    private LocalDateTime customerPaidTime;
    /**
     * 收款时间
     */
    private LocalDateTime receiptTime;

    private String fmsFundAccountCurrencyCnname;

    private String fmsFundAccountCurrencyEnname;

    /**
     * 订单已收款的占比，0.2代表20%  aaaaaaaaaaa
     */
    private BigDecimal orderReceiptRate;

}
