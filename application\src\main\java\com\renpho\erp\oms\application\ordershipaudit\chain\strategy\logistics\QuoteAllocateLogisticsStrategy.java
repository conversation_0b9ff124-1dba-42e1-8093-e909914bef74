package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics;

import java.util.*;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice.ShippingServiceFactory;
import com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice.ShippingServiceStrategy;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.domain.ordershipaudit.model.Carrier;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.Warehouse;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;

import lombok.extern.slf4j.Slf4j;

/**
 * 报价-分物流策略
 * <AUTHOR>
 */
@Slf4j
@Component
public class QuoteAllocateLogisticsStrategy extends AbstractAllocateLogisticsTemplate {

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.YPL_WAREHOUSE, FulfillmentServiceType.KING_SPARK_WAREHOUSE,
				FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE, FulfillmentServiceType.TEMU_ONLINE_LABEL,
				FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE);
	}

	@Override
	public String allocateLogistics(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 履约服务类型
		FulfillmentServiceType fulfillmentServiceType = orderAutoShipAudit.getFulfillmentServiceType();
		// 报价策略
		ShippingServiceStrategy strategy = ShippingServiceFactory.get(fulfillmentServiceType);
		// 构建报价查询入参
		ShippingServicesQuery query = this.buildShippingServicesQuery(orderAutoShipAudit, fulfillmentServiceType);
		// 查询报价
		List<ShippingServicesVO> shippingServiceList;
		String failMsg = String.format("发货服务类型[%s]查询报价失败", fulfillmentServiceType.getName());
		try {
			shippingServiceList = strategy.getShippingServices(query);
		}
		catch (Exception e) {
			log.error(failMsg, e);
			return failMsg;
		}
		// 是否存在金额为空的报价
		if (shippingServiceList.stream().map(ShippingServicesVO::getAmount).anyMatch(Objects::isNull)) {
			return failMsg;
		}
		// 金额最小的报价
		Optional<ShippingServicesVO> optionalMin = shippingServiceList.stream().min(Comparator.comparing(ShippingServicesVO::getAmount));
		if (optionalMin.isEmpty()) {
			return failMsg;
		}
		// 分物流成功
		ShippingServicesVO shippingService = optionalMin.get();
		// 承运商
		Carrier carrier = this.buildCarrier(shippingService);
		orderAutoShipAudit.setCarrier(carrier);
		XxlJobHelper.log("订单号：{}，分仓成功，承运商：{}", orderAutoShipAudit.getOrderNo(), JSONKit.toJSONString(carrier));
		return null;
	}

	/**
	 * 构建承运商
	 * @param shippingService 报价服务
	 * @return Carrier
	 */
	private Carrier buildCarrier(ShippingServicesVO shippingService) {
		return Carrier.builder()
			.carrierCode(shippingService.getCarrierCode())
			.carrierServiceCode(shippingService.getCarrierServiceCode())
			.packageTypeCode(shippingService.getPackageTypeCode())
			.shipChannelId(shippingService.getShipChannelId())
			.shipCompanyId(shippingService.getShipCompanyId())
			.ebayRateId(shippingService.getEbayRateId())
			.shippingQuoteId(shippingService.getShippingQuoteId())
			.build();
	}

	/**
	 * 报价查询入参
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @param fulfillmentServiceType 履约服务类型
	 * @return ShippingServicesQuery
	 */
	private @NotNull ShippingServicesQuery buildShippingServicesQuery(OrderAutoShipAuditDTO orderAutoShipAudit,
			FulfillmentServiceType fulfillmentServiceType) {
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// 仓库
		Warehouse warehouse = orderAutoShipAudit.getWarehouse();
		// 报价查询入参
		ShippingServicesQuery query = new ShippingServicesQuery();
		// 订单id
		query.setOrderId(orderAggRoot.getOrderId());
		// 店铺id
		query.setStoreId(orderAggRoot.getStoreId());
		// 履约服务类型
		query.setFulfillmentServiceType(fulfillmentServiceType.getValue());
		// 仓库id
		query.setWarehouseId(warehouse.getWarehouseId());
		// 订单的预计包裹尺寸，单位cm3
		String size = orderAggRoot.getSize();
		// 包裹长度cm
		query.setLength(SizeUtil.parseSize(size, 0));
		// 包裹宽度cm
		query.setWidth(SizeUtil.parseSize(size, 1));
		// 包裹高度cm
		query.setHeight(SizeUtil.parseSize(size, 2));
		// 包裹重量kg
		query.setWeight(orderAggRoot.getWeight());
		// 渠道订单行单号列表
		query.setChannelOrderLineIdList(orderAggRoot.getChannelOrderLineIdList());
		return query;
	}
}
