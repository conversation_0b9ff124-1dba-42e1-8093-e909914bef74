package com.renpho.erp.oms.application.channelmanagement.shopify.service;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderShippingLineLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderShippingLineVO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderShippingLine;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderShippingLineRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class SpfOrderShippingLineService {
	private final SpfOrderShippingLineRepository spfOrderShippingLineRepository;
	private final SpfOrderShippingLineLogConvertor spfOrderShippingLineLogConvertor;
	private final ObjectMapper objectMapper;

	public R<List<SpfOrderShippingLineVO>> listByOrderId(Long orderId) {
		List<SpfOrderShippingLine> spfOrderShippingLineList = spfOrderShippingLineRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(spfOrderShippingLineList)) {
			return R.success(spfOrderShippingLineList.stream().map(spfOrderShippingLine -> {
				SpfOrderShippingLineVO vo = spfOrderShippingLineLogConvertor.toVO(spfOrderShippingLine);
				try {
					// 提取json字段
					// 币种、单价
					if (StringUtils.isNotEmpty(spfOrderShippingLine.getPriceSet())) {
						SpfOrderDTO.MoneySet moneySet = JsonUtils.jsonToObject(spfOrderShippingLine.getPriceSet(),
								SpfOrderDTO.MoneySet.class);
						Optional.ofNullable(moneySet.getPresentment_money()).ifPresent(money -> vo.setCurrency(money.getCurrency_code()));
						Optional.ofNullable(moneySet.getPresentment_money())
							.ifPresent(money -> vo
								.setPrice(StringUtils.isNotEmpty(money.getAmount()) ? new BigDecimal(money.getAmount()) : BigDecimal.ZERO));
					}

					// 折扣
					if (StringUtils.isNotEmpty(spfOrderShippingLine.getDiscountedPriceSet())) {
						SpfOrderDTO.MoneySet moneySet = JsonUtils.jsonToObject(spfOrderShippingLine.getDiscountedPriceSet(),
								SpfOrderDTO.MoneySet.class);
						Optional.ofNullable(moneySet.getPresentment_money())
							.ifPresent(money -> vo.setDiscount(
									StringUtils.isNotEmpty(money.getAmount()) ? new BigDecimal(money.getAmount()) : BigDecimal.ZERO));
					}

					// 税
					// 解析 taxLines JSON 字符串
					List<SpfOrderDTO.Tax> taxList = objectMapper.readValue(spfOrderShippingLine.getTaxLines(), new TypeReference<>() {
					});

					// 计算总税金额
					BigDecimal totalTax = taxList.stream().map(tax -> {
						if (tax.getPrice_set() != null && tax.getPrice_set().getPresentment_money() != null) {
							String amount = tax.getPrice_set().getShop_money().getAmount();
							return StringUtils.isNotEmpty(amount) ? new BigDecimal(amount) : BigDecimal.ZERO;
						}
						return BigDecimal.ZERO;
					}).reduce(BigDecimal.ZERO, BigDecimal::add);

					// 设置到 VO 对象
					vo.setTax(totalTax);
				}
				catch (JsonProcessingException e) {
					log.error("Shopify发货行json提取转换失败：原因为{}", e);
				}

				return vo;
			}).collect(Collectors.toList()));
		}
		return R.success();
	}
}
