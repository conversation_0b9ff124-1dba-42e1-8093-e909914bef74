package com.renpho.erp.oms.domain.vc.order.entity;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单DI实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderDi {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 付款条款
	 */
	private String paymentMethod;

	/**
	 * 贸易条款
	 */
	private String incoterms;

	/**
	 * 集装箱规格
	 */
	private String containerType;

	/**
	 * 发货描述
	 */
	private String shippingInstructions;

	/**
	 * 创建人ID，0 System/系统
	 */
	private Integer createBy;

	/**
	 * 生成id
	 * @param orderId 订单id
	 */
	public void generateId(Long orderId) {
		this.id = IdWorker.getId();
		this.orderId = orderId;
	}

}
