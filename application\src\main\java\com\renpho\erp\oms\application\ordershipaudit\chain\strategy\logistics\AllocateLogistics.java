package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics;

import java.util.Set;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

/**
 * 分物流
 * <AUTHOR>
 */
public interface AllocateLogistics {

	/**
	 * 获取履约服务类型集
	 * @return Set
	 */
	Set<FulfillmentServiceType> getFulfillmentServiceTypes();

	/**
	 * 分物流
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return String 分物流失败原因
	 */
	String allocateLogistics(OrderAutoShipAuditDTO orderAutoShipAudit);
}
