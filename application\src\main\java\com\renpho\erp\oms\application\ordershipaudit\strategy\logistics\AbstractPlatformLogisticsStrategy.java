package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import java.util.Set;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditInventoryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.repository.LogisticsShipmentRepository;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceWarehouseService;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.service.SaleOrderService;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: 平台物流下单抽象类.
 * @time: 2025-04-03 14:18:43
 * @author: Alina
 */
@Slf4j
public abstract class AbstractPlatformLogisticsStrategy extends AbstractLogisticsStrategy implements InitializingBean {

    @Autowired
    protected FulfillmentServiceWarehouseService fulfillmentWarehouseService;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private OrderShipAuditInventoryService inventoryService;
    @Autowired
    private SaleOrderService saleOrderService;
    @Autowired
    protected OrderShipAuditRepository orderShipAuditRepository;
    @Autowired
    private LogisticsShipmentRepository logisticsShipmentRepository;

    @Override
    public void afterPropertiesSet() {
        // 使用代理对象，不用this，避免事务失效等问题
        Set<FulfillmentServiceType> serviceTypes = this.getFulfillmentServiceType();
        serviceTypes.forEach(serviceType -> LogisticsFactory.register(serviceType, SpringUtil.getBean(this.getClass())));
    }

    /**
     * 物流下单
     *
     * @param shipAuditAggRoot         发货审核领域驱动对象
     * @param saleOrderAggRoot         订单聚合根
     * @param receivingAddress         订单收货地址
     * @param logisticsShipmentAggRoot 物流下单聚合根
     */
    @Override
    public void createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
                                        SaleOrderAddressVO receivingAddress, LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
        try {
            // 获取店铺授权信息
            StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(saleOrderAggRoot.getStore().getStoreId());
            // 物流下单
            this.createLogisticsShipment(shipAuditAggRoot, saleOrderAggRoot, storeAuthorizationVo, logisticsShipmentAggRoot);
		} catch (Exception e) {
			log.error("创建物流下单异常：", e);
			// 物流下单标记失败
			logisticsShipmentAggRoot.markAsFailed("物流下单异常:" + e.getMessage());
			shipAuditAggRoot.createFail("物流下单异常:" + e.getMessage());
		}
    }

    /**
     * 物流下单
     *
     * @param shipAuditAggRoot     发货审核聚合根
     * @param saleOrderAggRoot     销售订单聚合根
     * @param storeAuthorizationVo 店铺授权信息
     */
    protected abstract void createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
                                                    StoreAuthorizationVo storeAuthorizationVo, LogisticsShipmentAggRoot logisticsShipmentAggRoot);

    /**
     * 获取并处理物流下单结果
     *
     * @param logisticsShipmentAggRoot 物流下单聚合根
     */
    @Override
    public void doProcessLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
        // 获取店铺授权信息
        StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(logisticsShipmentAggRoot.getStoreId());
        this.processLogisticsResult(logisticsShipmentAggRoot, storeAuthorizationVo);
    }

    /**
     * 获取并处理物流下单结果
     *
     * @param logisticsShipmentAggRoot 物流下单聚合根
     * @param storeAuthorizationVo     店铺授权信息
     */
    protected abstract void processLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot,
                                                   StoreAuthorizationVo storeAuthorizationVo);
}
