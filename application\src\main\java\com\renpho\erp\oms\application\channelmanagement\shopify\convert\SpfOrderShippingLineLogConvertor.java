package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderShippingLineLog;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderShippingLineVO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderShippingLine;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface SpfOrderShippingLineLogConvertor {
	SpfOrderShippingLineLog toLog(SpfOrderShippingLine param);

	SpfOrderShippingLineVO toVO(SpfOrderShippingLine param);
}