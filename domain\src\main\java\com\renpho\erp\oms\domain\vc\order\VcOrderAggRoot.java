package com.renpho.erp.oms.domain.vc.order;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.jmolecules.ddd.annotation.AggregateRoot;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.IdGeneratorCode;
import com.renpho.erp.oms.domain.IdGeneratorKit;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderInvoiceCmd;
import com.renpho.erp.oms.domain.vc.order.entity.*;
import com.renpho.erp.oms.domain.vc.order.enums.*;
import com.renpho.erp.oms.domain.vc.order.service.SubmitSupplierChainService;
import com.renpho.erp.oms.domain.vc.order.valueobject.*;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单聚合根
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@Builder
@AggregateRoot
public class VcOrderAggRoot {

	@Identity
	private Long id;

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * vc采购单号
	 */
	private String vcPurchaseNo;

	/**
	 * 店铺信息
	 */
	private VcStore store;

	/**
	 * 订单状态
	 */
	private VcOrderStatus orderStatus;

	/**
	 * 接单子状态
	 */
	private VcOrderAcceptedStatus acceptedStatus;

	/**
	 * 接单方式，是否ERP接单
	 */
	private VcOrderAcceptedMethod acceptedMethod;

	/**
	 * 解析SKU状态
	 */
	private VcOrderParseStatus parseStatus;

	/**
	 * 业务类型
	 */
	private VcOrderBusinessType businessType;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 发票类型
	 */
	private String invoiceType;

	/**
	 * 交货窗口
	 */
	private VcShippingWindow shippingWindow;

	/**
	 * 下单时间
	 */
	private LocalDateTime orderedTime;

	/**
	 * 接单时间
	 */
	private LocalDateTime acceptedTime;

	/**
	 * 发货时间
	 */
	private LocalDateTime shippedTime;

	/**
	 * 开票时间
	 */
	private LocalDateTime invoicedTime;

	/**
	 * 取消时间
	 */
	@Getter
	private LocalDateTime cancelledTime;

	/**
	 * 订单金额
	 */
	private VcOrderAmount amount;

	/**
	 * 订单地址
	 */
	private VcOrderAddress address;

	/**
	 * 异常原因
	 */
	private String exceptionReason;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 订单商品列表
	 */
	private List<VcOrderItem> items;

	@Builder.Default
	private List<VcOrderItem> saveOrUpdateItems = new ArrayList<>();

	/**
	 * 订单DI信息
	 */
	private VcOrderDi di;

	/**
	 * 订单发票列表
	 */
	private List<VcOrderInvoice> invoices;

	/**
	 * 新增或者保存的发票
	 */
	private VcOrderInvoice saveOrUpdateInvoice;

	/**
	 * 订单备注列表
	 */
	private List<VcOrderRemark> remarks;

	/**
	 * 订单商品箱唛列表
	 */
	private List<VcOrderItemLabel> itemLabels;

	/**
	 * 生成id和订单号
	 */
	public void generateIdAndOrderNo() {
		// 生成ID
		this.generateId();
		// 生成子实体ID
		this.generateSubEntityIds();
		// 生成订单号
		this.generateOrderNo();
	}

	/**
	 * 生成订单号
	 */
	public void generateOrderNo() {
		this.orderNo = IdGeneratorKit.get(IdGeneratorCode.VC_ORDER_CODE).generateAsString();
	}

	/**
	 * 生成ID
	 */
	public void generateId() {
		this.id = IdWorker.getId();
	}

	/**
	 * 生成子实体ID
	 */
	public void generateSubEntityIds() {
		// 生成商品ID
		if (this.items != null) {
			this.items.forEach(item -> item.generateId(this.id));
		}

		// 生成DI信息ID
		if (this.di != null) {
			this.di.generateId(this.id);
		}

		// 生成发票ID
		if (this.invoices != null) {
			this.invoices.forEach(invoice -> invoice.generateId(this.id));
		}

		// 生成备注ID
		if (this.remarks != null) {
			this.remarks.forEach(r -> r.generateId(this.id));
		}

		// 生成箱唛ID
		if (this.itemLabels != null) {
			this.itemLabels.forEach(label -> label.generateId(this.id));
		}
	}

	// 重写List类型的getter方法，返回不可修改的集合

	/**
	 * 获取订单商品列表（不可修改）
	 *
	 * @return List<VcOrderItem>
	 */
	public List<VcOrderItem> getItems() {
		return items == null ? Collections.emptyList() : Collections.unmodifiableList(items);
	}

	/**
	 * 获取订单发票列表（不可修改）
	 *
	 * @return List<VcOrderInvoice>
	 */
	public List<VcOrderInvoice> getInvoices() {
		return invoices == null ? Collections.emptyList() : Collections.unmodifiableList(invoices);
	}

	/**
	 * 获取订单备注列表（不可修改）
	 *
	 * @return List<VcOrderRemark>
	 */
	public List<VcOrderRemark> getRemarks() {
		return remarks == null ? Collections.emptyList() : Collections.unmodifiableList(remarks);
	}

	/**
	 * 获取订单商品箱唛列表（不可修改）
	 *
	 * @return List<VcOrderItemLabel>
	 */
	public List<VcOrderItemLabel> getItemLabels() {
		return itemLabels == null ? Collections.emptyList() : Collections.unmodifiableList(itemLabels);
	}

	/**
	 * 校验订单是否可以上传箱唛
	 */
	public void validateCanUploadLabel() {
		// 校验业务类型为DO
		if (!VcOrderBusinessType.DO.equals(this.businessType)) {
			throw DomainException.ofKey("VC_ORDER_BUSINESS_TYPE_NOT_DO");
		}
		// 校验解析状态为已解析
		if (!VcOrderParseStatus.SUCCESS.equals(this.parseStatus)) {
			throw DomainException.ofKey("VC_ORDER_PARSE_STATUS_NOT_SUCCESS");
		}
		// 校验接单状态为已接单或接单异常
		if (!(VcOrderStatus.PENDING_UPLOAD_LABEL.equals(this.orderStatus) || VcOrderStatus.ACCEPT_EXCEPTION.equals(this.orderStatus))) {
			throw DomainException.ofKey("VC_ORDER_ACCEPTED_STATUS_INVALID");
		}
		boolean allItemsZeroAccept = this.items.stream().allMatch(item -> item.getAcceptedQuantity() == 0);
		if (allItemsZeroAccept) {
			// 如果所有订单商品行的接单数量都为0，则提示不能上传箱唛
			throw DomainException.ofKey("VC_ORDER_ALL_ITEMS_ZERO");
		}
	}

	/**
	 * 上传箱唛
	 *
	 * @param labelList 箱唛列表
	 */
	public void uploadLabels(List<VcOrderItemLabel> labelList) {
		// 1. 校验订单是否可以上传箱唛
		validateCanUploadLabel();
		// 2. 判空处理
		if (CollectionUtils.isEmpty(labelList)) {
			throw DomainException.ofKey("VC_ORDER_ITEM_EMPTY");
		}
		// 3. 校验每个箱唛的orderItemId是否存在于订单商品中
		Set<Long> itemIds = this.items.stream().map(VcOrderItem::getId).collect(Collectors.toSet());
		for (VcOrderItemLabel label : labelList) {
			if (!itemIds.contains(label.getOrderItemId())) {
				throw DomainException.ofKey("VC_ORDER_ITEM_NOT_FOUND");
			}
		}
		// 4. 生成箱唛ID
		labelList.forEach(label -> {
			label.generateId(this.id);
		});
		// 5. 更新箱唛信息
		this.itemLabels = labelList;
		// 6. 更新订单状态为备货中
		this.orderStatus = VcOrderStatus.PREPARING;
	}

	public void submitAcknowledgementCheck(List<SubmitAcknowledgmentItem> submitAcknowledgmentItemList) {
		validateCanSubmitAcknowledgement();
		Map<Long, SubmitAcknowledgmentItem> submitAcknowledgmentItemMap = submitAcknowledgmentItemList.stream()
			.collect(Collectors.toMap(SubmitAcknowledgmentItem::getId, Function.identity()));
		for (VcOrderItem item : this.items) {
			SubmitAcknowledgmentItem submitAcknowledgmentItem = Optional.ofNullable(submitAcknowledgmentItemMap.get(item.getId()))
				.orElseThrow(() -> DomainException.ofKey("VC_ORDER_ITEM_NOT_FOUND"));
			item.submitAcknowledgmentCheck(submitAcknowledgmentItem);
		}
	}

	/**
	 * 校验是否可以提交接单
	 */
	public void validateCanSubmitAcknowledgement() {
		// 订单状态待接单 接单子状态待接单的允许接单
		boolean flag = VcOrderStatus.PENDING_ACCEPT.equals(orderStatus) && VcOrderAcceptedStatus.NOT_ACCEPTED.equals(acceptedStatus);
		if (!flag) {
			throw DomainException.ofKey("VC_ORDER_STATUS_ERROR");
		}
		flag = VcOrderParseStatus.SUCCESS.equals(parseStatus);
		// 解析状态成功的才能提交
		if (!flag) {
			throw DomainException.ofKey("VC_ORDER_PARSE_STATUS_NOT_SUCCESS");
		}
	}

	public void submitAcknowledgement(List<SubmitAcknowledgmentItem> submitAcknowledgmentItemList) {
		Map<Long, SubmitAcknowledgmentItem> submitAcknowledgmentItemMap = submitAcknowledgmentItemList.stream()
			.collect(Collectors.toMap(SubmitAcknowledgmentItem::getId, Function.identity()));
		for (VcOrderItem item : items) {
			item.submitAcknowledgment(submitAcknowledgmentItemMap.get(item.getId()));
			saveOrUpdateItems.add(item);
		}

		this.acceptedMethod = VcOrderAcceptedMethod.YES;
		this.acceptedTime = LocalDateTime.now();
		this.acceptedStatus = VcOrderAcceptedStatus.ACCEPTED;
	}

	/**
	 * 解析SKU 参照SaleOrderAggRoot的associateSku方法设计
	 *
	 * @param asinToProductMap ASIN到VcProduct的映射Map
	 */
	public void parseSku(Map<String, VcProduct> asinToProductMap) {
		log.info("开始解析VC订单SKU，订单号：{}", this.orderNo);

		// 前置校验：订单状态只有在待接单、接单异常、待上传箱唛可以重新解析
		if (!canReparse()) {
			log.warn("VC订单状态不允许重新解析，订单号：{}，当前状态：{}", this.orderNo, this.orderStatus);
			return;
		}

		// 标记是否解析成功
		boolean parseSuccess = true;
		String exceptionReason = null;

		// 遍历订单商品进行SKU解析
		if (CollectionUtils.isNotEmpty(this.items)) {
			for (VcOrderItem item : this.items) {
				// 根据ASIN查找对应的VcProduct
				VcProduct product = asinToProductMap.get(item.getAsin());
				if (product == null) {
					parseSuccess = false;
					exceptionReason = String.format("ASIN %s 未找到对应的SKU映射", item.getAsin());
					log.warn("VC订单商品SKU解析失败，订单号：{}，ASIN：{}，原因：未找到SKU映射", this.orderNo, item.getAsin());
					continue;
				}

				// 通过领域方法关联SKU信息
				item.associateProduct(product);

				log.info("VC订单商品SKU解析成功，订单号：{}，ASIN：{}，PSKU：{}", this.orderNo, item.getAsin(), product.getPsku());
			}
		}

		// 更新解析状态：全部商品行解析成功后订单的parse_status变成解析成功
		if (parseSuccess) {
			this.parseStatus = VcOrderParseStatus.SUCCESS;
			this.exceptionReason = null;
			log.info("VC订单SKU解析成功，订单号：{}，全部商品行解析完成", this.orderNo);
		}
		else {
			this.parseStatus = VcOrderParseStatus.SKU_MAPPING_EXCEPTION;
			this.exceptionReason = exceptionReason;
			log.warn("VC订单SKU解析失败，订单号：{}，原因：{}", this.orderNo, exceptionReason);
		}
	}

	/**
	 * 判断是否可以重新解析SKU 订单状态只有在待接单、接单异常、待上传箱唛可以重新解析
	 *
	 * @return boolean
	 */
	private boolean canReparse() {
		return this.orderStatus == VcOrderStatus.PENDING_ACCEPT || this.orderStatus == VcOrderStatus.ACCEPT_EXCEPTION
				|| this.orderStatus == VcOrderStatus.PENDING_UPLOAD_LABEL;
	}

	/**
	 * 准备提交供应商链
	 */
	public void prepareForSupplierChain() {
		// 校验订单是否可以提交供应商链
		validateCanSubmitSupplierChain();
		// 检查所有订单商品行的接单数量是否都为0
		boolean allItemsZeroAccept = this.items.stream().allMatch(item -> item.getAcceptedQuantity() == 0);
		if (allItemsZeroAccept) {
			// 如果所有订单商品行的接单数量都为0，则更新订单状态为取消
			this.orderStatus = VcOrderStatus.CANCELLED;
		}
		else {
			// 更新订单状态为备货中
			this.orderStatus = VcOrderStatus.PREPARING;
		}
	}

	/**
	 * 校验是否可以提交供应商链
	 */
	public void validateCanSubmitSupplierChain() {
		// 1. 校验SKU解析状态是否为解析成功
		if (!VcOrderParseStatus.SUCCESS.equals(this.parseStatus)) {
			throw DomainException.ofKey("VC_ORDER_PARSE_STATUS_NOT_SUCCESS");
		}

		// 2. 校验订单状态是否为接单异常
		if (!VcOrderStatus.ACCEPT_EXCEPTION.equals(this.orderStatus)) {
			throw DomainException.ofKey("VC_ORDER_STATUS_ACCEPT_EXCEPTION");
		}

		// 3. 对于DO订单，检查是否已上传箱唛
		if (VcOrderBusinessType.DO.equals(this.businessType)) {
			if (CollectionUtils.isEmpty(this.itemLabels)) {
				throw DomainException.ofKey("VC_ORDER_LABEL_NOT_UPLOADED");
			}
		}
	}

	/**
	 * 开发票校验
	 */
	public void invoiceCheck() {
		// 上传供应链后状态变成备货中
		boolean flag = VcOrderStatus.PREPARING.equals(orderStatus);
		if (!flag) {
			throw DomainException.ofKey("VC_ORDER_STATUS_ERROR");
		}

		// todo 后续对接供应链后,这里需要校验是否备货
	}

	/**
	 * 开发票
	 */
	public void invoice(VcOrderInvoiceCmd cmd) {
		VcOrderInvoice vcOrderInvoice = VcOrderInvoice.builder()
			.invoiceNo(cmd.getInvoiceNo())
			.invoiceDate(cmd.getInvoiceDate())
			.taxRate(cmd.getTaxRate())
			.tax(cmd.getInvoiceAmount())
			.build();
		vcOrderInvoice.generateId(id);
		invoices.add(vcOrderInvoice);
		saveOrUpdateInvoice = vcOrderInvoice;

		// 摊分税
		vcOrderInvoice.taxCalculate(items);

		// 开票时间
		this.invoicedTime = LocalDateTime.now();

	}

	/**
	 * 计算金额
	 */
	public void calculateAmount() {
		// 计算订单商品行金额
		this.items.forEach(i -> i.calculateAmount(this.isNotAccepted()));
		if (Objects.isNull(amount)) {
			// 币种任取其中一个订单商品行的
			String currency = this.items.stream()
				.map(VcOrderItem::getCurrency)
				.filter(Objects::nonNull)
				.findFirst()
				.orElseThrow(() -> DomainException.of("currency is null"));
			this.amount = VcOrderAmount.builder().currency(currency).build();
		}
		// 计算订单金额
		this.amount.calculateAmount(this.items);
	}

	/**
	 * 是否未接单
	 *
	 * @return boolean
	 */
	public boolean isNotAccepted() {
		// 未接单
		return VcOrderStatus.PENDING_ACCEPT.equals(this.orderStatus) && VcOrderAcceptedStatus.NOT_ACCEPTED.equals(this.acceptedStatus);
	}

	/**
	 * 是否无需修改
	 *
	 * @return boolean
	 */
	public boolean isNoNeedToModify() {
		// 无需修改的状态（已开票/已完成/已取消）
		Set<VcOrderStatus> noNeedToModifyStatus = Set.of(VcOrderStatus.PENDING_INVOICE, VcOrderStatus.COMPLETED, VcOrderStatus.CANCELLED);
		return noNeedToModifyStatus.contains(this.orderStatus);
	}

	/**
	 * 计算订单状态（创建时）
	 *
	 * @param amzOrderState 亚马逊订单状态
	 */
	public void calculateStatusOfCreate(AmzVcPurchaseOrderState amzOrderState) {
		if (Objects.isNull(amzOrderState)) {
			throw DomainException.of("Amz purchaseOrderState is null");
		}
		switch (amzOrderState) {
			// New -> 订单状态为待接单，接单子状态为未接单
			case NEW -> {
				this.orderStatus = VcOrderStatus.PENDING_ACCEPT;
				this.acceptedStatus = VcOrderAcceptedStatus.NOT_ACCEPTED;
			}
			// Acknowledged/Closed -> 标记接单异常（订单状态、接单子状态）
			case ACKNOWLEDGED, CLOSED -> this.markAcceptException("已在店铺后台接单");
			default -> throw DomainException.of("Amz purchaseOrderState not found");
		}
	}

	/**
	 * 计算订单状态（修改时）
	 *
	 * @param amzOrderState 亚马逊-渠道单状态
	 * @param acceptedQuantityMap 转换订单商品行-接受数量Map（key为orderItemId,value为acceptedQuantity）
	 */
	public void calculateStatusOfModify(AmzVcPurchaseOrderState amzOrderState, Map<Long, Integer> acceptedQuantityMap) {
		// 根据订单状态路由
		switch (this.orderStatus) {
			// 接单异常/待上传箱唛/备货中
			case ACCEPT_EXCEPTION, PENDING_UPLOAD_LABEL, PREPARING -> {
				// 正常更新（无需修改状态）
			}
			// 待接单
			case PENDING_ACCEPT -> this.handlePendingAccept(amzOrderState, acceptedQuantityMap);
			// 非以上状态-出现异常
			default -> throw DomainException.of("orderStatus error");
		}
		// 更新-接受数量
		this.items.forEach(i -> i.modifyAcceptedQuantity(acceptedQuantityMap));
	}

	/**
	 * 处理订单状态-待接单
	 *
	 * @param amzOrderState 亚马逊-渠道单状态
	 * @param acceptedQuantityMap 转换订单商品行-接受数量Map（key为orderItemId,value为acceptedQuantity）
	 */
	private void handlePendingAccept(AmzVcPurchaseOrderState amzOrderState, Map<Long, Integer> acceptedQuantityMap) {
		// 根据接单子状态路由
		switch (this.acceptedStatus) {
			// 未接单
			case NOT_ACCEPTED -> this.handleNotAccepted(amzOrderState);
			// 接单确认中
			case ACCEPTING -> this.handleAccepting(amzOrderState, acceptedQuantityMap);
			default -> throw DomainException.of("acceptedStatus error");
		}
	}

	/**
	 * 处理接单子状态-未接单
	 *
	 * @param amzOrderState 亚马逊-渠道单状态
	 */
	private void handleNotAccepted(AmzVcPurchaseOrderState amzOrderState) {
		// 根据渠道单状态路由
		switch (amzOrderState) {
			// New
			case NEW -> {
				// 正常更新，无需修改状态
			}
			// Acknowledged/Closed -> 标记接单异常（订单状态、接单子状态）
			case ACKNOWLEDGED, CLOSED -> this.markAcceptException("已在店铺后台接单");
			default -> throw DomainException.of("amzOrderState error");
		}
	}

	/**
	 * 标记接单异常（订单状态、接单子状态）
	 */
	private void markAcceptException(String exceptionReason) {
		this.orderStatus = VcOrderStatus.ACCEPT_EXCEPTION;
		this.acceptedStatus = VcOrderAcceptedStatus.ACCEPT_EXCEPTION;
		this.exceptionReason = exceptionReason;
	}

	/**
	 * 处理接单子状态-接单确认中
	 *
	 * @param amzOrderState 亚马逊-渠道单状态
	 * @param acceptedQuantityMap 转换订单商品行-接受数量Map（key为orderItemId,value为acceptedQuantity）
	 */
	private void handleAccepting(AmzVcPurchaseOrderState amzOrderState, Map<Long, Integer> acceptedQuantityMap) {
		// 根据渠道单状态路由
		switch (amzOrderState) {
			// New
			case NEW -> this.handleChannelOrderNewState();
			// Acknowledged/Closed
			case ACKNOWLEDGED, CLOSED -> this.handleChannelOrderAckState(acceptedQuantityMap);
			default -> throw DomainException.of("amzOrderState error");
		}
	}

	/**
	 * 处理渠道单New状态
	 */
	private void handleChannelOrderNewState() {
		// 超时判断
		// 未超时：当前时间 VS 接单时间，未超过40分钟（暂定）
		if (ChronoUnit.MINUTES.between(this.acceptedTime, LocalDateTime.now()) <= 40) {
			// 正常更新（接单状态还没更新到位）
			return;
		}
		// 超时
		// 标记接单异常（订单状态、接单子状态）
		this.markAcceptException("等待接单响应超时");
	}

	/**
	 * 处理渠道单Acknowledged/Closed状态
	 * @param acceptedQuantityMap 转换订单商品行-接受数量Map（key为orderItemId,value为acceptedQuantity）
	 */
	private void handleChannelOrderAckState(Map<Long, Integer> acceptedQuantityMap) {
		// 根据接单数量判断是否一致
		boolean equal = this.items.stream().allMatch(i -> Objects.equals(i.getAcceptedQuantity(), acceptedQuantityMap.get(i.getId())));
		// 不一致（可能是亚马逊VC后台接的单，或者改了）
		if (!equal) {
			// 标记接单异常（订单状态、接单子状态）
			this.markAcceptException("后台接单数量与ERP不一致");
			return;
		}
		// 一致
		// 接单子状态改为【已接单】
		this.acceptedStatus = VcOrderAcceptedStatus.ACCEPTED;
		// 判断接单数是否都等于0
		boolean allAcceptedQuantityIsZero = acceptedQuantityMap.values().stream().allMatch(q -> Integer.valueOf(0).equals(q));
		// 接单数都为0
		if (allAcceptedQuantityIsZero) {
			// 订单状态变为【已取消】
			this.orderStatus = VcOrderStatus.CANCELLED;
			// 取消时间，取接单时间
			this.cancelledTime = this.acceptedTime;
			return;
		}
		// 接单数不都为0
		// 判断业务类型
		// DI订单
		if (VcOrderBusinessType.DI.equals(this.businessType)) {
			// 状态暂不变，待后续流程-自动提交供应链
			return;
		}
		// DO订单
		// 状态变为【待上传箱唛】
		this.orderStatus = VcOrderStatus.PENDING_UPLOAD_LABEL;
	}

	/**
	 * 是否自动提交供应链（前置校验）
	 * @return boolean
	 */
	public boolean isAutoSubmitSupplyChain() {
		// 以下条件都成立
		// 1.订单状态为待接单
		boolean one = VcOrderStatus.PENDING_ACCEPT.equals(this.orderStatus);
		// 2.接单子状态为已接单
		boolean two = VcOrderAcceptedStatus.ACCEPTED.equals(this.acceptedStatus);
		// 3.存在订单商品行的接单数量大于0
		boolean three = this.items.stream().anyMatch(i -> Objects.nonNull(i.getAcceptedQuantity()) && i.getAcceptedQuantity() > 0);
		// 4.业务类型为DI
		boolean four = VcOrderBusinessType.DI.equals(this.businessType);
		return one && two && three && four;
	}

	/**
	 * 提交供应链
	 * @param sscService 提交供应链服务
	 */
	public void submitSupplyChain(SubmitSupplierChainService sscService) {
		// 提交供应链
		SubmitSupplierChainService.SuccessR r = sscService.submit(this);
		// 是否成功
		if (r.success()) {
			// 状态变为【备货中】
			this.orderStatus = VcOrderStatus.PREPARING;
		}
		else {
			// 状态变为【接单异常】
			this.orderStatus = VcOrderStatus.ACCEPT_EXCEPTION;
			this.exceptionReason = "提交供应链失败：" + r.failMsg();
		}
	}
}
