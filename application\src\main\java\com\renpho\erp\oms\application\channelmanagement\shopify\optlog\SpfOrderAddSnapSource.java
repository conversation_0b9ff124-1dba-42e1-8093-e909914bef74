package com.renpho.erp.oms.application.channelmanagement.shopify.optlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderLog;
import com.renpho.erp.oms.application.channelmanagement.shopify.service.SpfOrderService;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import org.springframework.stereotype.Component;

@Component
public class SpfOrderAddSnapSource implements SnapshotDatatSource {
	private final SpfOrderService spfOrderService;

	public JSONObject getOldData(Object[] args) {
		return new JSONObject();
	}

	public JSONObject getNewData(Object[] args, JSONObject result) {
		SpfOrderLog spfOrderLog = this.spfOrderService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(spfOrderLog));
	}

	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

	public SpfOrderAddSnapSource(final SpfOrderService spfOrderService) {
		this.spfOrderService = spfOrderService;
	}
}
