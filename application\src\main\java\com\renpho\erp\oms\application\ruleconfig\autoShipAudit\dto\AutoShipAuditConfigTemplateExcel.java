package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 模板
 * @date 2025/5/20 11:43
 */
@Data
public class AutoShipAuditConfigTemplateExcel {

    /**
     * 渠道
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.CHANNEL",order = 0)
    private String channel;

    /**
     * 店铺
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.STORE",order = 1)
    private String store;

    /**
     * psku
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.PSKU",order = 2)
    private String psku;

    /**
     * 仓库编码列表
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.WAREHOUSECODE",order = 3)
    private String warehouseCode;

    /**
     * 单品单件
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.ONESKUONEPC",order = 4)
    private String oneSkuOnePC;

    /**
     * 单品多件
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.ONESKUMULTIPCS",order = 5)
    private String oneSkuMultiPcs;

    /**
     * 多品多件
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_EXCEL.MULTISKUS",order = 6)
    private String multiSkus;

    /**
     * 自动发货审核开关
     */
    @ExcelProperty(value = "AUTO_SHIP_AUDIT_CONFIG_IMPORT_EXCEL.AUTOSWITCH",order = 7)
    private String autoSwitch;
}
