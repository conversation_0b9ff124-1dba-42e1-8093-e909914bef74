package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.convertor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.FieldComparator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseLanguageVo;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.command.SaveAutoShipAuditConfigCmd;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigExportExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.WarehouseTemplateExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.vo.AutoShipAuditWarehouseVO;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.vo.AutoShipAuditConfigPageVO;
import com.renpho.erp.oms.infrastructure.persistence.autoShipAudit.po.AutoShipAuditConfigPO;
import com.renpho.karma.json.JSONKit;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/16 14:46
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface AutoShipAuditConfigConvertor {

	@Mapping(target = "warehouseCode", source = "code")
	@Mapping(target = "thirdPartWarehouseCode", source = "thirdWarehouseCode")
	@Mapping(target = "warehouseName", expression = "java(getLocalizedWarehouseName(warehouseVo))")
	AutoShipAuditWarehouseVO toAutoShipAuditWarehouseVO(WarehouseVo warehouseVo);

	@Mapping(target = "warehouseSelectorPriority", expression = "java(sortWarehouseSelectorPriority(cmd))")
	@Mapping(target = "auditSwitch", expression = "java(cmd.getAuditSwitch() == null ? false : cmd.getAuditSwitch())")
	@Mapping(target = "oneSkuOnePc", expression = "java(cmd.getOneSkuOnePc() == null ? false : cmd.getOneSkuOnePc())")
	@Mapping(target = "oneSkuMultiPcs", expression = "java(cmd.getOneSkuMultiPcs() == null ? false : cmd.getOneSkuMultiPcs())")
	@Mapping(target = "multiSkus", expression = "java(cmd.getMultiSkus() == null ? false : cmd.getMultiSkus())")
	AutoShipAuditConfigPO cmdToAutoShipAuditConfigPO(SaveAutoShipAuditConfigCmd cmd);

	@Mapping(target = "channel", source = "channelName")
	@Mapping(target = "store", source = "storeName")
	@Mapping(target = "autoSwitch", source = "auditSwitchName")
	@Mapping(target = "warehouseSelectorPriority", expression = "java(exportWarehouseSelectorPriority(autoShipAuditConfigPageVO))")
	@Mapping(target = "skuCombinationType", expression = "java(exportSkuCombinationType(autoShipAuditConfigPageVO))")
	AutoShipAuditConfigExportExcel voTOAutoShipAuditConfigExportExcel(AutoShipAuditConfigPageVO autoShipAuditConfigPageVO);


	WarehouseTemplateExcel voToWarehouseTemplateExcel(AutoShipAuditWarehouseVO autoShipAuditWarehouseVO);

	/**
	 * 导出的sku组合类型
	 * @param autoShipAuditConfigPageVO
	 * @return
	 */
	default String exportSkuCombinationType(AutoShipAuditConfigPageVO autoShipAuditConfigPageVO){
		return autoShipAuditConfigPageVO.getSkuCombinationList().stream().collect(Collectors.joining(StrUtil.COMMA));
	}

	/**
	 * 导出的仓库选择类型
	 * @param autoShipAuditConfigPageVO
	 * @return
	 */
	default String exportWarehouseSelectorPriority(AutoShipAuditConfigPageVO autoShipAuditConfigPageVO) {
		return autoShipAuditConfigPageVO.getWarehouseSelectorPriorityObjList()
			.stream()
			.map(x -> StrUtil.concat(false, String.valueOf(x.getPriority()), StrUtil.DOT, x.getWarehouseName()))
			.collect(Collectors.joining(StrUtil.LF));
	}

	/**
	 * 按优先级排序
	 * @param cmd
	 * @return
	 */
	default String sortWarehouseSelectorPriority(SaveAutoShipAuditConfigCmd cmd) {
		List<SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj> warehouseSelectorPriorityList = cmd
			.getWarehouseSelectorPriorityList();
		warehouseSelectorPriorityList = CollUtil.sort(warehouseSelectorPriorityList, Comparator.comparing(SaveAutoShipAuditConfigCmd.WarehouseSelectorPriorityObj::getPriority));
		return JSONKit.toJSONString(warehouseSelectorPriorityList);
	}

	/**
	 * 获取仓库名称
	 * @param warehouseVo
	 * @return
	 */
	default String getLocalizedWarehouseName(WarehouseVo warehouseVo) {
		String language = Optional.of(LocaleContextHolder.getLocale()).map(java.util.Locale::toLanguageTag).orElse("zh-CN");
		return ObjectUtil.defaultIfNull(warehouseVo.getLanguages(), Collections.<WarehouseLanguageVo> emptyList())
			.stream()
			.filter(warehouseLanguageVo -> StrUtil.equalsIgnoreCase(language, warehouseLanguageVo.getLanguage()))
			.findFirst()
			.map(WarehouseLanguageVo::getName)
			.orElse("");
	}
}
