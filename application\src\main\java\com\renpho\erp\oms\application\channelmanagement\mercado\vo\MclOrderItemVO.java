package com.renpho.erp.oms.application.channelmanagement.mercado.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class MclOrderItemVO {

	/**
	 * 美客多商品id
	 */
	private String itemId;

	/**
	 * seller SKU
	 */
	private String sellerSku;

	/**
	 * 变化id
	 */
	private Long variationId;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 销售金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal saleFee;

	/**
	 * 单价
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal unitPrice;

}
