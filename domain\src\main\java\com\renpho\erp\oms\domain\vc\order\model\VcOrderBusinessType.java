package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VC订单业务类型枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VcOrderBusinessType {

	/**
	 * DI（直接进口）
	 */
	DI(1, "DI（直接进口）"),

	/**
	 * DO（海外仓备货模式）
	 */
	DO(2, "DO（海外仓备货模式）");

	private final Integer value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return VcOrderBusinessType
	 */
	public static VcOrderBusinessType getByValue(Integer value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
