package com.renpho.erp.oms.application.returnorder.service;

import java.io.OutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.util.ArrayUtil;
import org.apache.commons.lang.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.returnorder.vo.AmzReturnOrderPageVo;
import com.renpho.erp.oms.domain.returnorder.query.AmzFbaReturnPageQuery;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.mapper.AmzReportFbaCustomReturnMapper;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.po.AmzReportFbaCustomReturnPO;
import com.renpho.karma.dto.Paging;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class AmzReturnOrderQueryService {
	@Autowired
	private AmzReportFbaCustomReturnMapper amzReportFbaCustomRefundMapper;
	@Autowired
	private FileClient fileClient;
	@Autowired
	private StoreClient storeClient;

	public Paging<AmzReturnOrderPageVo> pageQuery(AmzFbaReturnPageQuery query) {
		Wrapper<AmzReportFbaCustomReturnPO> wrapper = buildWrapper(query);
		Page<AmzReportFbaCustomReturnPO> pageParam = new Page<>(query.getPageIndex(), query.getPageSize());
		// 不让mp去分页
		if (BooleanUtil.isTrue(query.getExportFlag())) {
			pageParam.setMaxLimit(null);
			pageParam.setSearchCount(false);
			pageParam.setSize(-1);
		}
		Page<AmzReportFbaCustomReturnPO> amzReportFbaCustomRefundPOPage = amzReportFbaCustomRefundMapper.selectPermissionPage(pageParam,
				wrapper);
		List<AmzReportFbaCustomReturnPO> records = amzReportFbaCustomRefundPOPage.getRecords();
		if (CollUtil.isEmpty(records)) {
			return Paging.of(Collections.emptyList(), 0, query.getPageSize(), query.getPageIndex());
		}

		// 图片信息
		Set<String> imageIdSet = records.stream()
			.map(AmzReportFbaCustomReturnPO::getImageId)
			.filter(StrUtil::isNotBlank)
			.collect(Collectors.toSet());
		Map<String, FileDetailResponse> imageMap = fileClient.getFileMap(imageIdSet);
		// 门店信息
		Set<Integer> storeIdSet = records.stream().map(AmzReportFbaCustomReturnPO::getStoreId).collect(Collectors.toSet());
		Map<Integer, StoreVo> storeVoMap = storeClient.getByStoreIds(storeIdSet)
			.stream()
			.filter(Objects::nonNull)
			.collect(Collectors.toMap(StoreVo::getId, Function.identity()));

		IPage<AmzReturnOrderPageVo> page = amzReportFbaCustomRefundPOPage.convert(amzReportFbaCustomRefund -> {
			String storeName = Optional.ofNullable(storeVoMap.get(amzReportFbaCustomRefund.getStoreId()))
				.map(StoreVo::getStoreName)
				.orElse("");
			String imageUrl = Optional.ofNullable(imageMap.get(amzReportFbaCustomRefund.getImageId()))
				.map(FileDetailResponse::getUrl)
				.orElse("");

			return AmzReturnOrderPageVo.toAmzRefundOrderPageVo(amzReportFbaCustomRefund, storeName, imageUrl);
		});
		return Paging.of(page.getRecords(), ((int) page.getTotal()), ((int) page.getPages()), ((int) page.getCurrent()));

	}

	public void export(AmzFbaReturnPageQuery query, OutputStream outputStream) {
		query.setExportFlag(true);
		Paging<AmzReturnOrderPageVo> amzRefundOrderPageVoPaging = this.pageQuery(query);
		EasyExcel.write()
			.file(outputStream)
			.head(AmzReturnOrderPageVo.class)
			.registerWriteHandler(new LanguageExcelHeaderWriterHandler())
			.sheet(0)
			.doWrite(amzRefundOrderPageVoPaging.getRecords());
	}

	private Wrapper<AmzReportFbaCustomReturnPO> buildWrapper(AmzFbaReturnPageQuery query) {
        String[] orderIdArr = ArrayUtils.EMPTY_STRING_ARRAY;
		if (StrUtil.isNotBlank(query.getOrderId())) {
			orderIdArr = query.getOrderId().split("\\s+");
		}
		return Wrappers.<AmzReportFbaCustomReturnPO> lambdaQuery()
			.in(CollUtil.isNotEmpty(query.getStoreIds()), AmzReportFbaCustomReturnPO::getStoreId, query.getStoreIds())
			.in(ArrayUtil.isNotEmpty(orderIdArr), AmzReportFbaCustomReturnPO::getOrderId, orderIdArr)

			.eq(StrUtil.isNotBlank(query.getAsin()), AmzReportFbaCustomReturnPO::getAsin, query.getAsin())
			.eq(StrUtil.isNotBlank(query.getMsku()), AmzReportFbaCustomReturnPO::getMsku, query.getMsku())
			.eq(StrUtil.isNotBlank(query.getPsku()), AmzReportFbaCustomReturnPO::getPsku, query.getPsku())
			.eq(StrUtil.isNotBlank(query.getFnsku()), AmzReportFbaCustomReturnPO::getFnSku, query.getFnsku())

			.ge(query.getCreateTimeStart() != null, AmzReportFbaCustomReturnPO::getCreateTime, query.getCreateTimeStart())
			.le(query.getCreateTimeEnd() != null, AmzReportFbaCustomReturnPO::getCreateTime, query.getCreateTimeEnd())

			.ge(query.getReturnOrderTimeStart() != null, AmzReportFbaCustomReturnPO::getReturnDate, query.getReturnOrderTimeStart())
			.le(query.getReturnOrderTimeEnd() != null, AmzReportFbaCustomReturnPO::getReturnDate, query.getReturnOrderTimeEnd())

			.orderByDesc(List.of(AmzReportFbaCustomReturnPO::getUpdateTime, AmzReportFbaCustomReturnPO::getId));
	}

}
