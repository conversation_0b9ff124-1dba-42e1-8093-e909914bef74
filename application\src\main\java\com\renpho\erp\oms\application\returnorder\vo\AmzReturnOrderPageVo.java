package com.renpho.erp.oms.application.returnorder.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import com.renpho.erp.oms.infrastructure.common.excel.TimeZoneLocalDateTimeStringConverter;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.po.AmzReportFbaCustomReturnPO;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AmzReturnOrderPageVo {
    /**
     * 主键
     */
    @ExcelIgnore
    private Long id;

    /**
     * 门店id
     */
    @ExcelIgnore
    private Integer storeId;

    /**
     * 亚马逊渠道订单id
     */
    @ExcelHeadName(zhCnName = "店铺单号", enName = "Shop Order #")
    private String orderId;

    /**
     * 门店名
     */
    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String storeName;

    /**
     * 亚马逊市场id
     */
    @ExcelIgnore
    private String marketplaceId;

    /**
     * 亚马逊销售sku
     */
    @ExcelHeadName(zhCnName = "msku", enName = "msku")
    private String msku;

    /**
     * 亚马逊asin
     */
    @ExcelHeadName(zhCnName = "asin", enName = "asin")
    private String asin;

    /**
     * 亚马逊fnsku
     */
    @ExcelHeadName(zhCnName = "fnsku", enName = "fnsku")
    private String fnSku;

    /**
     * 采购SKU,从sku映射关系注入
     */
    @ExcelHeadName(zhCnName = "psku", enName = "psku")
    private String psku;

    /**
     * 退货数量
     */
    @ExcelHeadName(zhCnName = "数量", enName = "Quantity")
    private Integer quantity;

    /**
     * 处置信息
     */
    @ExcelHeadName(zhCnName = "处理信息", enName = "detailed-disposition")
    private String detailedDisposition;

    /**
     * 状态
     */
    @ExcelHeadName(zhCnName = "状态", enName = "Status")
    private String status;

    /**
     * 退货原因
     */
    @ExcelHeadName(zhCnName = "原因", enName = "Reason")
    private String reason;
    /**
     * 退货备注
     */
    @ExcelHeadName(zhCnName = "备注", enName = "customer-comments")
    private String customerComments;

    /**
     * 退货时间
     */
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    @ExcelHeadName(zhCnName = "退货时间", enName = "Received")
    private LocalDateTime refundDate;

    /**
     * 创建时间
     */
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    @ExcelHeadName(zhCnName = "创建时间", enName = "Created")
    private LocalDateTime createTime;

    /**
     * 创建人ID
     */
    @ExcelIgnore
    private Integer createBy;


    /**
     * 图片ID，来自PDS，用于换取临时访问地址
     */
    @ExcelIgnore
    private String imageId;

    /**
     * 图片链接
     */
    @ExcelIgnore
    private String imageUrl;

    /**
     * 解析sku映射状态 1成功 0失败
     */
    @ExcelIgnore
    private Integer parseFlag;

    /**
     * 亚马逊商品名称
     */
    @ExcelIgnore
    private String productName;



    /**
     * 亚马逊平台仓退货仓库编码
     */
    @ExcelIgnore
    private String fulfillmentCenterId;







    /**
     * 退货唯一id,亚马逊提供
     */
    @ExcelIgnore
    private String licensePlateNumber;







    /**
     * 更新人ID
     */
    @ExcelIgnore
    private Integer updateBy;

    /**
     * 更新时间
     */
    @ExcelIgnore
    private LocalDateTime updateTime;

    public static AmzReturnOrderPageVo toAmzRefundOrderPageVo(AmzReportFbaCustomReturnPO amzReportFbaCustomRefund, String storeName, String imageUrl) {
        AmzReturnOrderPageVo vo = new AmzReturnOrderPageVo();
        vo.setId(amzReportFbaCustomRefund.getId());
        vo.setStoreId(amzReportFbaCustomRefund.getStoreId());
        vo.setStoreName(storeName);
        vo.setMarketplaceId(amzReportFbaCustomRefund.getMarketplaceId());
        vo.setRefundDate(amzReportFbaCustomRefund.getReturnDate());
        vo.setOrderId(amzReportFbaCustomRefund.getOrderId());
        vo.setMsku(amzReportFbaCustomRefund.getMsku());
        vo.setAsin(amzReportFbaCustomRefund.getAsin());
        vo.setFnSku(amzReportFbaCustomRefund.getFnSku());
        vo.setPsku(amzReportFbaCustomRefund.getPsku());
        vo.setImageId(amzReportFbaCustomRefund.getImageId());
        vo.setImageUrl(imageUrl);
        vo.setParseFlag(amzReportFbaCustomRefund.getParseFlag());
        vo.setProductName(amzReportFbaCustomRefund.getProductName());
        vo.setQuantity(amzReportFbaCustomRefund.getQuantity());
        vo.setFulfillmentCenterId(amzReportFbaCustomRefund.getFulfillmentCenterId());
        vo.setDetailedDisposition(amzReportFbaCustomRefund.getDetailedDisposition());
        vo.setReason(amzReportFbaCustomRefund.getReason());
        vo.setStatus(amzReportFbaCustomRefund.getStatus());
        vo.setLicensePlateNumber(amzReportFbaCustomRefund.getLicensePlateNumber());
        vo.setCustomerComments(amzReportFbaCustomRefund.getCustomerComments());
        vo.setCreateBy(amzReportFbaCustomRefund.getCreateBy());
        vo.setCreateTime(amzReportFbaCustomRefund.getCreateTime());
        vo.setUpdateBy(amzReportFbaCustomRefund.getUpdateBy());
        vo.setUpdateTime(amzReportFbaCustomRefund.getUpdateTime());
        return vo;
    }
}
