package com.renpho.erp.oms.adapter.web.controller.walmart;

import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtOrderLineStatusService;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineStatusVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/walmart/**")
@RequestMapping("/walmart/linestatus")
@AllArgsConstructor
public class WmtOrderLineStatusWebController {
	private final WmtOrderLineStatusService wmtOrderLineStatusService;

	/**
	 * 沃尔玛订单商品状态行列表
	 *
	 * @param orderId
	 * @return R<List < WmOrderLineStatusVO>>
	 */
	@GetMapping("/list")
	public R<List<WmtOrderLineStatusVO>> listByOrderId(@RequestParam Long orderId) {
		return wmtOrderLineStatusService.listByOrderId(orderId);
	}

}
