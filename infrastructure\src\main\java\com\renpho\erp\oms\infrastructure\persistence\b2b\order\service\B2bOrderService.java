package com.renpho.erp.oms.infrastructure.persistence.b2b.order.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderStatusEnum;
import com.renpho.erp.oms.domain.b2b.order.B2bShipmentReviewStatusEnum;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderPO;
import org.springframework.stereotype.Service;

/**
 * @desc: B2B订单服务
 * @time: 2025-06-27 16:56:40
 * @author: Alina
 */
@Service
public class B2bOrderService extends ServiceImpl<B2bOrderMapper, B2bOrderPO> {
    /**
     * 更新订单发货审核状态
     *
     * @param orderId 订单ID
     * @param b2bShipmentReviewStatusEnum 发货审核状态枚举
     */
    public void updateShipmentReviewStatus(Long orderId, B2bShipmentReviewStatusEnum b2bShipmentReviewStatusEnum) {
        LambdaUpdateWrapper<B2bOrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(B2bOrderPO::getId, orderId)
                .set(B2bOrderPO::getShipmentReviewStatus, b2bShipmentReviewStatusEnum.getValue())
                .set(B2bOrderPO::getRefuseReason, null);
        this.update(updateWrapper);
    }

    /**
     * 更新订单状态
     *
     * @param orderId            订单ID
     * @param b2bOrderStatusEnum 订单状态枚举
     */
    public void updateOrderStatus(Long orderId, B2bOrderStatusEnum b2bOrderStatusEnum) {
        LambdaUpdateWrapper<B2bOrderPO> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(B2bOrderPO::getId, orderId)
                .set(B2bOrderPO::getOrderStatus, b2bOrderStatusEnum.getValue())
                .set(B2bOrderPO::getRefuseReason, null);
        this.update(updateWrapper);
    }

}