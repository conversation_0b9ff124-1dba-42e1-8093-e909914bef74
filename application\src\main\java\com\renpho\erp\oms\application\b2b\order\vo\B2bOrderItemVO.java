package com.renpho.erp.oms.application.b2b.order.vo;

import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * B2B订单商品信息VO
 *
 */
@Data
public class B2bOrderItemVO implements VO {

    /**
     * 商品ID
     */
    private Long id;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 采购SKU
     */
    private String psku;

    /**
     * 图片ID
     */
    private String imageId;

    /**
     * 图片URL
     */
    private String imageUrl;

    /**
     * 条码
     */
    private String barcode;

    /**
     * 下单数量
     */
    private Integer orderedQuantity;

    /**
     * 发货数量
     */
    private Integer shippedQuantity;

    /**
     * 商品数量（该订单包含的商品种类数）
     */
    private Integer itemCount;

    /**
     * PSKU中文名称
     */
    private String pskuNameZh;

    /**
     * PSKU英文名称
     */
    private String pskuNameEn;

    /**
     * 装箱数量（每箱的psku数量）
     */
    private Integer numberOfUnitsPerBox;
}
