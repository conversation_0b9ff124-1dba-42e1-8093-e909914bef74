package com.renpho.erp.oms.application.b2b.receiptOrder.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.query.B2bReceiptOrderPageQuery;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.vo.B2bReceiptOrderPageVo;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderAmountMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderCustomerMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderAmountPO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderCustomerPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.mapper.PaymentTermsConfigMapper;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.PaymentTermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageQueryDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.mapper.B2bReceiptOrderMapper;
import com.renpho.karma.dto.Paging;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class B2bReceiptOrderQueryService {
    @Autowired
    private B2bReceiptOrderMapper b2bReceiptOrderMapper;
    @Autowired
    private B2bOrderCustomerMapper b2bOrderCustomerMapper;
    @Autowired
    private B2bOrderAmountMapper b2bOrderAmountMapper;
    @Autowired
    private PaymentTermsConfigMapper paymentTermsConfigMapper;

    /**
     * 查询分页
     */
    @TransMethodResult
    public Paging<B2bReceiptOrderPageVo> page(B2bReceiptOrderPageQuery query) {
        Page<B2BReceiptOrderPageDto> page = new Page<>(query.getPageIndex(), query.getPageSize(), !query.isExportFlag());
        page = b2bReceiptOrderMapper.page(BeanUtil.copyProperties(query, B2BReceiptOrderPageQueryDto.class), page);
        List<B2BReceiptOrderPageDto> b2BReceiptOrderPageDtoList = page.getRecords();
        if (b2BReceiptOrderPageDtoList.isEmpty()) {
            return Paging.of(Collections.emptyList(), 0, query.getPageSize(), query.getPageIndex());
        }
        Set<Long> orderIdSet = b2BReceiptOrderPageDtoList.stream()
                .map(B2BReceiptOrderPageDto::getB2bOrderId)
                .collect(Collectors.toSet());
        Set<String> paymentTermCodeSet = new HashSet<>();
        Map<Long, B2bOrderCustomerPO> customerMap = b2bOrderCustomerMapper.selectList(Wrappers.<B2bOrderCustomerPO>lambdaQuery().in(B2bOrderCustomerPO::getOrderId, orderIdSet))
                .stream()
                .peek(orderCustomer -> paymentTermCodeSet.add(orderCustomer.getPaymentTermsCode()))
                .collect(Collectors.toMap(B2bOrderCustomerPO::getOrderId, Function.identity()));
        Map<Long, B2bOrderAmountPO> amountMap = b2bOrderAmountMapper.selectList(Wrappers.<B2bOrderAmountPO>lambdaQuery().in(B2bOrderAmountPO::getOrderId, orderIdSet))
                .stream()
                .collect(Collectors.toMap(B2bOrderAmountPO::getOrderId, Function.identity()));
        Map<String, String> paymentTermsNameMap = paymentTermsConfigMapper.selectList(Wrappers.<PaymentTermsConfigPO>lambdaQuery().in(PaymentTermsConfigPO::getCode, paymentTermCodeSet))
                .stream()
                .collect(Collectors.toMap(PaymentTermsConfigPO::getCode, PaymentTermsConfigPO::getName, (existing, replacement) -> existing));


        IPage<B2bReceiptOrderPageVo> b2BReceiptOrderPageVoPage =
                page.convert(b2BReceiptOrderPageDto ->
                        B2bReceiptOrderPageVo.form(b2BReceiptOrderPageDto, amountMap.get(b2BReceiptOrderPageDto.getB2bOrderId()), customerMap.get(b2BReceiptOrderPageDto.getB2bOrderId()), paymentTermsNameMap));
        Paging<B2bReceiptOrderPageVo> pageVoPaging = new Paging<>();
        pageVoPaging.setRecords(b2BReceiptOrderPageVoPage.getRecords());
        pageVoPaging.setPageIndex(query.getPageIndex());
        pageVoPaging.setPageSize(query.getPageSize());
        pageVoPaging.setTotalCount(((int) b2BReceiptOrderPageVoPage.getTotal()));
        pageVoPaging.setRecords(b2BReceiptOrderPageVoPage.getRecords());
        return pageVoPaging;
    }

    @TransMethodResult
    public void export(B2bReceiptOrderPageQuery query, HttpServletResponse response) throws IOException {
        HttpUtil.setExportResponseHeader(response, "b2bReceiptOrder.xlsx");
        @Cleanup
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                .autoCloseStream(true)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0)
                .head(B2bReceiptOrderPageVo.class)
                .build();

        query.setPageSize(1000);
        query.setPageIndex(1);
        B2bReceiptOrderQueryService bean = SpringUtil.getBean(this.getClass());
        do {
            List<B2bReceiptOrderPageVo> records = bean.page(query).getRecords();
            excelWriter.write(records, writeSheet);
            if (!Objects.equals(records.size(), query.getPageSize())) {
                break;
            }
            query.setPageIndex(query.getPageIndex() + 1);
        } while (true);
    }
}
