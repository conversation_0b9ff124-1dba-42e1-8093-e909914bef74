package com.renpho.erp.oms.domain.vc.order;

import java.util.List;
import java.util.Optional;

import com.renpho.erp.oms.domain.vc.order.command.VcOrderRemarkCmd;
import com.renpho.erp.oms.domain.vc.order.model.VcOrderAggRoot;

/**
 * VC订单仓储接口
 * <AUTHOR>
 */
public interface VcOrderRepository {

	/**
	 * 保存订单
	 * @param order 订单聚合根
	 */
	void save(VcOrderAggRoot order);

	/**
	 * 更新订单
	 * @param order 订单聚合根
	 */
	void update(VcOrderAggRoot order);

	/**
	 * 根据ID查询订单
	 * @param id 订单ID
	 * @return VcOrderAggRoot
	 */
	VcOrderAggRoot findById(Long id);

	/**
	 * 根据订单号查询订单
	 * @param orderNo 订单号
	 * @return Optional<VcOrderAggRoot>
	 */
	Optional<VcOrderAggRoot> findByOrderNo(String orderNo);

	/**
	 * 根据店铺ID查询订单列表
	 * @param storeId 店铺ID
	 * @return List<VcOrderAggRoot>
	 */
	List<VcOrderAggRoot> findByStoreId(Integer storeId);

	/**
	 * 删除订单
	 * @param id 订单ID
	 */
	void deleteById(Long id);

	/**
	 * 保存订单商品箱唛
	 * @param order 订单聚合根
	 */
	void saveOrderItemLabels(VcOrderAggRoot order);

	/**
	 * 根据VC采购单号和店铺ID查询订单ID
	 * @param vcPurchaseNo VC采购单号
	 * @param storeId 店铺ID
	 * @return Long 订单ID，不存在时返回null
	 */
	Long findIdByVcPurchaseNoAndStoreId(String vcPurchaseNo, Integer storeId);

	/**
	 * 接单
	 */
	void submitAcknowledgement(VcOrderAggRoot vcOrderAggRoot);

	/**
	 * 保存SKU解析结果 只更新解析相关的字段：parse_status、exception_reason和商品的product信息
	 * @param order 订单聚合根
	 */
	void saveSkuParseResult(VcOrderAggRoot order);

	void updateOrderStatus(VcOrderAggRoot order);

	/**
	 * 订单备注
	 * @param cmd 备注命令
	 */
	void remark(VcOrderRemarkCmd cmd);

	/**
	 * 开发票
	 * @param vcOrderAggRoot 订单聚合根
	 */
	void invoice(VcOrderAggRoot vcOrderAggRoot);
}
