package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLineStatus;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.List;
import java.util.Objects;

public class WmtOrderLineStatusAppConvertor {

	public static List<WmtOrderLineStatus> toDomainList(List<WmtOrderDTO.OrderLineStatus> orderLineStatusList, String lineNumber,
			String itemSku) {
		List<WmtOrderLineStatus> wmtOrderLineStatusList = Lists.newArrayList();
		orderLineStatusList.forEach(orderLineStatus -> {
			wmtOrderLineStatusList.add(toDomain(orderLineStatus, lineNumber, itemSku));
		});
		return wmtOrderLineStatusList;
	}

	public static WmtOrderLineStatus toDomain(WmtOrderDTO.OrderLineStatus orderLineStatus, String lineNumber, String itemSku) {
		if (Objects.nonNull(orderLineStatus)) {
			WmtOrderLineStatus wmtOrderLineStatus = new WmtOrderLineStatus();
			wmtOrderLineStatus.setLineNumber(lineNumber);
			wmtOrderLineStatus.setItemSku(itemSku);
			wmtOrderLineStatus.setStatus(orderLineStatus.getStatus());
			if (Objects.nonNull(orderLineStatus.getStatusQuantity())) {
				wmtOrderLineStatus.setStatusQuantityUnit(orderLineStatus.getStatusQuantity().getUnitOfMeasurement());
				wmtOrderLineStatus.setStatusQuantityAmount(orderLineStatus.getStatusQuantity().getAmount());
			}
			wmtOrderLineStatus.setCancellationReason(orderLineStatus.getCancellationReason());
			if (Objects.nonNull(orderLineStatus.getTrackingInfo())) {
				wmtOrderLineStatus
					.setTrackingInfoShipDateTime(DateUtil.convertToDateTime(orderLineStatus.getTrackingInfo().getShipDateTime()));
				if (Objects.nonNull(orderLineStatus.getTrackingInfo().getCarrierName())) {
					wmtOrderLineStatus.setTrackingInfoOtherCarrier(orderLineStatus.getTrackingInfo().getCarrierName().getOtherCarrier());
					wmtOrderLineStatus.setTrackingInfoCarrier(orderLineStatus.getTrackingInfo().getCarrierName().getCarrier());
				}
				wmtOrderLineStatus.setTrackingInfoMethodCode(orderLineStatus.getTrackingInfo().getMethodCode());
				wmtOrderLineStatus.setTrackingInfoTrackingNumber(orderLineStatus.getTrackingInfo().getTrackingNumber());
				if (Objects.nonNull(orderLineStatus.getReturnCenterAddress())) {
					wmtOrderLineStatus.setReturnCenterAddress(JsonUtils.toJson(orderLineStatus.getReturnCenterAddress()));
				}
			}
			return wmtOrderLineStatus;
		}
		return null;

	}
}
