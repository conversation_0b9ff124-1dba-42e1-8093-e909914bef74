package com.renpho.erp.oms.application.ordershipaudit.channel.offline;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.renpho.erp.ims.client.feign.warehouse.query.WarehouseQuery;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseLanguageVo;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipExcelService;
import com.renpho.erp.oms.application.ordershipaudit.channel.ThirdPartOrderShipExcelDto;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.OrderStatus;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelListener;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderFulfillmentMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderPO;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Cleanup;
import org.springframework.transaction.support.TransactionTemplate;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Component
public class OfflineChannelOrderShipExcelService implements ChannelOrderShipExcelService {
	@Autowired
	private WarehouseClient warehouseClient;
	@Autowired
	private StoreClient storeClient;
	@Autowired
	private Validator validator;
	@Autowired
	private SaleOrderMapper saleOrderMapper;
	@Autowired
	private SaleOrderFulfillmentMapper saleOrderFulfillmentMapper;
	@Autowired
	private SaleOrderItemMapper saleOrderItemMapper;
	@Autowired
	private FileClient fileClient;
	@Autowired
	private OrderShipAuditCreateService orderShipAuditCreateService;

	@Override
	public FulfillmentServiceType getFulfillmentServiceType() {
		return FulfillmentServiceType.OFFLINE;
	}

	@Override
	public R<String> readExcel(InputStream inputStream) {
		List<OfflineExcelDto> offlineExcelDtoList = EasyExcel.read(inputStream)
			.head(OfflineExcelDto.class)
			.registerReadListener(new LanguageExcelListener<>(List.of("errorInfo")))
			.sheet(0)
			.doReadSync();

		if (CollUtil.isEmpty(offlineExcelDtoList)) {
			throw new BusinessException(I18nMessageKit.getMessage("FILE_CANNOT_BE_EMPTY"));
		}

		Map<String, StoreVo> storeMap = new HashMap<>();
		Map<String, SaleOrderPO> saleOrderPOMap = new HashMap<>();
		Map<String, WarehouseVo> warehouseMap = new HashMap<>();
		Set<Long> orderIdSet = new HashSet<>();
		boolean failFlag = checkData(offlineExcelDtoList, storeMap, saleOrderPOMap, orderIdSet, warehouseMap);
		if (failFlag) {
			offlineExcelDtoList.forEach(OfflineExcelDto::buildErrorInfo);
			ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
			EasyExcelFactory.write(byteArrayOutputStream)
				.head(OfflineExcelDto.class)
				.registerWriteHandler(new LanguageExcelHeaderWriterHandler())
				.sheet()
				.doWrite(offlineExcelDtoList);
			return fileClient.uploadExcel(byteArrayOutputStream);
		}
		Multimap<Long, Long> itemMultimap = HashMultimap.create();
		for (List<Long> orderIds : CollUtil.split(orderIdSet, 100)) {
			saleOrderItemMapper
				.selectList(Wrappers.<SaleOrderItemPO> lambdaQuery()
					.select(SaleOrderItemPO::getOrderId, SaleOrderItemPO::getId)
					.in(SaleOrderItemPO::getOrderId, orderIds)
					.eq(SaleOrderItemPO::getDeleted, false))
				.forEach(item -> itemMultimap.put(item.getOrderId(), item.getId()));
		}

		for (OfflineExcelDto offlineExcelDto : offlineExcelDtoList) {
			SaleOrderPO saleOrder = saleOrderPOMap.get(offlineExcelDto.getOrderNo());
			WarehouseVo warehouseVo = warehouseMap.get(offlineExcelDto.getImsWarehouseCode());

			OrderShipAuditDTO orderShipAuditDTO = new OrderShipAuditDTO();
			orderShipAuditDTO.setOrderId(saleOrder.getId());
			orderShipAuditDTO.setFulfillmentServiceType(getFulfillmentServiceType().getValue());
			orderShipAuditDTO.setWarehouseId(warehouseVo.getId());
			orderShipAuditDTO.setWarehouseCode(warehouseVo.getCode());

			List<OrderShipAuditDTO.Item> items = new ArrayList<>();
			for (Long itemId : itemMultimap.get(saleOrder.getId())) {
				items.add(OrderShipAuditDTO.Item.builder().itemId(itemId).build());
			}

			orderShipAuditDTO.setItems(items);
			orderShipAuditCreateService.create(orderShipAuditDTO, true);
		}

		return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
	}

	private boolean checkData(List<OfflineExcelDto> offlineExcelDtoList, Map<String, StoreVo> storeMap,
			Map<String, SaleOrderPO> saleOrderPOMap, Set<Long> orderIdSet, Map<String, WarehouseVo> warehouseMap) {
		boolean failFlag = false;
		Map<String, List<OfflineExcelDto>> orderNoExcelDtoMap = offlineExcelDtoList.stream()
			.collect(Collectors.groupingBy(OfflineExcelDto::getOrderNo));

		// 销售订单号不能填写重复
		for (Map.Entry<String, List<OfflineExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
			List<OfflineExcelDto> oneOrderNoExcelDtoList = entry.getValue();
			if (oneOrderNoExcelDtoList.size() > 1) {
				failFlag = true;
				oneOrderNoExcelDtoList.forEach(thirdPartOrderShipExcelDto -> thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NO_REPEAT")));
			}
		}

		// 获取仓库信息
		Set<String> warehouseCodeSet = offlineExcelDtoList.stream().map(OfflineExcelDto::getImsWarehouseCode).collect(Collectors.toSet());
		warehouseMap.putAll(warehouseClient.getByCode(warehouseCodeSet));
		// 获取店铺信息
		Set<String> storeNameSet = offlineExcelDtoList.stream().map(OfflineExcelDto::getShop).collect(Collectors.toSet());
		storeClient.getByStoreNames(new ArrayList<>(storeNameSet)).forEach(storeVo -> storeMap.put(storeVo.getStoreName(), storeVo));


		for (OfflineExcelDto offlineExcelDto : offlineExcelDtoList) {
			// jsr303校验
			Set<ConstraintViolation<OfflineExcelDto>> errorInfos = validator.validate(offlineExcelDto);
			if (!errorInfos.isEmpty()) {
				failFlag = true;
			}
			errorInfos.forEach(constraintViolation -> offlineExcelDto.addErrorInfo(constraintViolation.getMessage()));

			// 店铺名是否填写错误
			StoreVo storeVo = storeMap.get(offlineExcelDto.getShop());
			if (storeVo == null) {
				failFlag = true;
				offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SHOP_NOT_EXISTS"));
			}
			// 仓库是否填写错误
			WarehouseVo warehouseVo = warehouseMap.get(offlineExcelDto.getImsWarehouseCode());
			if (warehouseVo == null) {
				failFlag = true;
				offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WAREHOUSE_CODE_INVALIDATE"));
			}

			SaleOrderPO saleOrder = saleOrderMapper.selectOne(Wrappers.<SaleOrderPO> lambdaQuery()
				.eq(SaleOrderPO::getOrderNo, offlineExcelDto.getOrderNo())
				.eq(SaleOrderPO::getDeleted, false)
				.last("limit 1"));
			// 订单是否存在
			if (saleOrder == null) {
				failFlag = true;
				offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_EXISTS"));
			}
			if (saleOrder != null) {
				saleOrderPOMap.put(offlineExcelDto.getOrderNo(), saleOrder);
				orderIdSet.add(saleOrder.getId());
				// 订单是否属于这个店铺
				if (storeVo != null && !Objects.equals(saleOrder.getStoreId(), storeVo.getId())) {
					failFlag = true;
					offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_BELONG_SHOP"));
				}
				SaleOrderFulfillmentPO saleOrderFulfillment = saleOrderFulfillmentMapper
					.selectOne(Wrappers.<SaleOrderFulfillmentPO> lambdaQuery()
						.eq(SaleOrderFulfillmentPO::getOrderId, saleOrder.getId())
						.last("limit 1"));
				// 订单是否自发货订单
				if (saleOrderFulfillment != null
						&& !Objects.equals(saleOrderFulfillment.getFulfillmentType(), FulfillmentType.MERCHANT.getValue())) {
					failFlag = true;
					offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_FULFILLMENT_BY_MERCHANT"));
				}
				// 订单状态是否可以创建发货审核
				boolean flag = OrderStatus.canCreateOrderShipAudit()
					.stream()
					.anyMatch(orderStatus -> orderStatus.getValue().equals(saleOrder.getOrderStatus()));
				if (!flag) {
					failFlag = true;
					offlineExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_STATUS_NOT_CANT_AUDIT"));
				}
			}
		}

		return failFlag;
	}

	@Override
	public void writeExcelTemplate(OutputStream outputStream) {
		@Cleanup
		ExcelWriter excelWriter = EasyExcelFactory.write()
			.file(outputStream)
			.registerWriteHandler(new LanguageExcelHeaderWriterHandler())
			.build();

		excelWriter.write(Collections.emptyList(),
				EasyExcelFactory.writerSheet(0).head(OfflineExcelDto.class).excludeColumnFieldNames(List.of("errorInfo")).build());

		WarehouseQuery warehouseQuery = new WarehouseQuery();
		warehouseQuery.setStatus(1);
		warehouseQuery.setPageSize(10000);
		List<WarehouseVo> warehouseVoList = warehouseClient.list(warehouseQuery);
		List<ThirdPartOrderShipExcelDto.PlatformWarehouse> platformWarehouseList = new ArrayList<>();
		String language = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");
		for (WarehouseVo warehouseVo : warehouseVoList) {
			ThirdPartOrderShipExcelDto.PlatformWarehouse platformWarehouse = new ThirdPartOrderShipExcelDto.PlatformWarehouse();
			platformWarehouse.setPlatformWarehouseCode(warehouseVo.getCode());

			String warehouseName = ObjectUtil.defaultIfNull(warehouseVo.getLanguages(), Collections.<WarehouseLanguageVo> emptyList())
				.stream()
				.filter(warehouseLanguageVo -> StrUtil.equalsIgnoreCase(language, warehouseLanguageVo.getLanguage()))
				.findFirst()
				.map(WarehouseLanguageVo::getName)
				.orElse("");
			platformWarehouse.setPlatformWarehouseName(warehouseName);
			platformWarehouseList.add(platformWarehouse);
		}
		excelWriter.write(platformWarehouseList,
				EasyExcelFactory.writerSheet(1).head(ThirdPartOrderShipExcelDto.PlatformWarehouse.class).build());
	}
}
