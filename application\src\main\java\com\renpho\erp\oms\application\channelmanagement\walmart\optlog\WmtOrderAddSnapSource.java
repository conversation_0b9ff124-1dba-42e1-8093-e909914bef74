package com.renpho.erp.oms.application.channelmanagement.walmart.optlog;

import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtOrderService;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLog;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oplog.log.SnapshotDatatSource;

import lombok.AllArgsConstructor;

/**
 *
 * @since 2024/9/24
 */
@Component
@AllArgsConstructor
public class WmtOrderAddSnapSource implements SnapshotDatatSource {

	private final WmtOrderService wmtOrderService;

	@Override
	public JSONObject getOldData(Object[] args) {
		return new JSONObject();
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		WmtOrderLog amazonOrderLog = wmtOrderService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(amazonOrderLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
