package com.renpho.erp.oms.application.b2b.order.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.anno.TransMethodResult;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.fhs.trans.service.impl.TransService;
import com.fhs.trans.utils.TransUtil;
import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.mdm.client.rate.vo.LatestFluctuateExchangeRateResponse;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderPageQuery;
import com.renpho.erp.oms.application.b2b.order.dto.B2bOrderExportExcel;
import com.renpho.erp.oms.application.b2b.order.vo.*;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderFeeTypeEnum;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bOrderFreeDetail;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.erp.oms.infrastructure.common.util.JsonUtil;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.RateClient;
import com.renpho.erp.oms.infrastructure.feign.pds.CountryClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.*;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.*;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.service.B2bOrderDataPermissionService;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.service.B2bOrderRemarkPOService;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.CustomerInsuranceCreditInfoPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.IncotermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.PaymentTermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.CustomerInsuranceCreditInfoService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.IncotermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.PaymentTermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.mapper.B2bReceiptOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po.B2bReceiptOrderPO;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.json.JSONKit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.Cleanup;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * B2B订单查询服务
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class B2bOrderQueryService {

    private final B2bOrderMapper b2bOrderMapper;
    private final B2bOrderCustomerMapper b2bOrderCustomerMapper;
    private final B2bOrderAmountMapper b2bOrderAmountMapper;
    private final B2bOrderItemMapper b2bOrderItemMapper;
    private final B2bOrderAddressMapper b2bOrderAddressMapper;
    private final B2bOrderShipmentRequirementMapper b2bOrderShipmentRequirementMapper;
    private final B2bOrderCustomerContactMapper b2bOrderCustomerContactMapper;
    private final B2bReceiptOrderMapper b2bReceiptOrderMapper;
    private final RateClient rateClient;
    private final B2bOrderRemarkPOService b2bOrderRemarkPOService;
    private final FileClient fileClient;
    private final IncotermsConfigService incotermsConfigService;
    private final PaymentTermsConfigService paymentTermsConfigService;
    private final CustomerInsuranceCreditInfoService customerInsuranceCreditInfoService;
    private final UserClient userClient;
    private final CountryClient countryClient;
    private final B2bOrderDataPermissionService b2bOrderDataPermissionService;
    private final I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;
    private final B2bOrderCustomerInvoiceMapper b2bOrderCustomerInvoiceMapper;
    private final TransService transService;

    /**
     * B2B订单分页查询
     *
     * @param query 查询参数
     * @return 分页结果
     */
    @TransMethodResult
    public Paging<B2bOrderPageVO> page(B2bOrderPageQuery query) {
        // 1. 构建查询条件并分页查询主表（带数据权限控制）
        Page<B2bOrderPO> pageParam = new Page<>(query.getPageIndex(), query.getPageSize());
        LambdaQueryWrapper<B2bOrderPO> wrapper = buildQueryWrapper(query);
        IPage<B2bOrderPO> orderPage = b2bOrderDataPermissionService.selectPageWithPermission(pageParam, wrapper);

        if (CollUtil.isEmpty(orderPage.getRecords())) {
            return Paging.of(Collections.emptyList(), 0, query.getPageSize(), query.getPageIndex());
        }

        // 2. 收集订单ID
        List<Long> orderIds = orderPage.getRecords().stream().map(B2bOrderPO::getId).collect(Collectors.toList());

        // 3. 批量查询关联数据
        Map<Long, B2bOrderCustomerPO> customerMap = b2bOrderCustomerMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.toMap(B2bOrderCustomerPO::getOrderId, Function.identity()));

        Map<Long, B2bOrderAmountPO> amountMap = b2bOrderAmountMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.toMap(B2bOrderAmountPO::getOrderId, Function.identity()));

        // 查询所有商品信息用于统计数量
        Map<Long, List<B2bOrderItemPO>> orderItemInfoMap = b2bOrderItemMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.groupingBy(B2bOrderItemPO::getOrderId));

        // 4. 批量查询贸易条款和付款条款配置
        Set<String> incotermsCodeSet = customerMap.values()
                .stream()
                .map(B2bOrderCustomerPO::getIncotermsCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> paymentTermsCodeSet = customerMap.values()
                .stream()
                .map(B2bOrderCustomerPO::getPaymentTermsCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        final Map<String, IncotermsConfigPO> incotermsConfigMap = CollUtil.isNotEmpty(incotermsCodeSet) ? incotermsConfigService.list()
                .stream()
                .filter(config -> incotermsCodeSet.contains(config.getCode()))
                .collect(Collectors.toMap(IncotermsConfigPO::getCode, Function.identity())) : new HashMap<>();

        final Map<String, PaymentTermsConfigPO> paymentTermsConfigMap = CollUtil.isNotEmpty(paymentTermsCodeSet)
                ? paymentTermsConfigService.list()
                .stream()
                .filter(config -> paymentTermsCodeSet.contains(config.getCode()))
                .collect(Collectors.toMap(PaymentTermsConfigPO::getCode, Function.identity()))
                : new HashMap<>();

        // 5. 收集图片ID并获取图片信息
        Set<String> imageIdSet = orderItemInfoMap.values()
                .stream()
                .flatMap(List::stream)
                .map(B2bOrderItemPO::getImageId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);

        // 6. 组装返回结果
        List<B2bOrderPageVO> records = orderPage.getRecords()
                .stream()
                .map(order -> convertToPageVO(order, customerMap.get(order.getId()), amountMap.get(order.getId()),
                        orderItemInfoMap.get(order.getId()), incotermsConfigMap, paymentTermsConfigMap, fileMap))
                .collect(Collectors.toList());

        return Paging.of(records, (int) orderPage.getTotal(), (int) orderPage.getPages(), (int) orderPage.getCurrent());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<B2bOrderPO> buildQueryWrapper(B2bOrderPageQuery query) {
        LambdaQueryWrapper<B2bOrderPO> wrapper = new LambdaQueryWrapper<>();

        // 订单号集合精确搜索
        if (CollUtil.isNotEmpty(query.getOrderNos())) {
            wrapper.in(B2bOrderPO::getOrderNo, query.getOrderNos());
        }

        // 客户参考号集合精确搜索
        if (CollUtil.isNotEmpty(query.getReferNos())) {
            wrapper.in(B2bOrderPO::getReferNo, query.getReferNos());
        }

        // 发票号集合精确搜索
        if (CollUtil.isNotEmpty(query.getInvoiceNos())) {
            wrapper.in(B2bOrderPO::getInvoiceNo, query.getInvoiceNos());
        }

        // 客户ID
        if (query.getCustomerId() != null) {
            wrapper.eq(B2bOrderPO::getCustomerId, query.getCustomerId());
        }

        // 订单状态
        if (query.getOrderStatus() != null) {
            wrapper.eq(B2bOrderPO::getOrderStatus, query.getOrderStatus());
        }

        // 创建时间范围
        if (query.getCreateTimeStart() != null) {
            wrapper.ge(B2bOrderPO::getCreateTime, query.getCreateTimeStart());
        }
        if (query.getCreateTimeEnd() != null) {
            wrapper.le(B2bOrderPO::getCreateTime, query.getCreateTimeEnd());
        }

        // 发票时间范围
        if (query.getInvoiceTimeStart() != null) {
            wrapper.ge(B2bOrderPO::getInvoiceTime, query.getInvoiceTimeStart());
        }
        if (query.getInvoiceTimeEnd() != null) {
            wrapper.le(B2bOrderPO::getInvoiceTime, query.getInvoiceTimeEnd());
        }

        // 发货时间范围
        if (query.getShippedTimeStart() != null) {
            wrapper.ge(B2bOrderPO::getShippedTime, query.getShippedTimeStart());
        }
        if (query.getShippedTimeEnd() != null) {
            wrapper.le(B2bOrderPO::getShippedTime, query.getShippedTimeEnd());
        }

        // 按创建时间倒序
        wrapper.orderByDesc(B2bOrderPO::getUpdateTime);

        return wrapper;
    }

    /**
     * 转换为分页VO
     */
    private B2bOrderPageVO convertToPageVO(B2bOrderPO order, B2bOrderCustomerPO customer, B2bOrderAmountPO amount,
                                           List<B2bOrderItemPO> orderItems, Map<String, IncotermsConfigPO> incotermsConfigMap,
                                           Map<String, PaymentTermsConfigPO> paymentTermsConfigMap, Map<String, FileDetailResponse> fileMap) {
        B2bOrderPageVO vo = new B2bOrderPageVO();

        // 订单基本信息
        vo.setId(order.getId());
        vo.setOrderNo(order.getOrderNo());
        vo.setReferNo(order.getReferNo());
        vo.setCreateTime(order.getCreateTime());
        vo.setInvoiceTime(order.getInvoiceTime());
        vo.setShippedTime(order.getShippedTime());
        vo.setOrderStatus(order.getOrderStatus());
        vo.setRemark(order.getRemark());
        vo.setRefuseReason(order.getRefuseReason());

        // 客户信息
        if (customer != null) {
            vo.setCountry(customer.getCountry());
            vo.setCustomerCompanyName(customer.getCustomerCompanyName());
            vo.setCountryManagerName(customer.getCountryManagerName());
            vo.setSalesAssistantName(customer.getSalesAssistantName());
            vo.setIncotermsCode(customer.getIncotermsCode());
            vo.setPaymentTermsCode(customer.getPaymentTermsCode());

            // 设置贸易条款名称
            if (customer.getIncotermsCode() != null && incotermsConfigMap.containsKey(customer.getIncotermsCode())) {
                IncotermsConfigPO incotermsConfig = incotermsConfigMap.get(customer.getIncotermsCode());
                if (incotermsConfig != null) {
                    vo.setIncotermsName(incotermsConfig.getName());
                }
            }

            // 设置付款条款名称
            if (customer.getPaymentTermsCode() != null && paymentTermsConfigMap.containsKey(customer.getPaymentTermsCode())) {
                PaymentTermsConfigPO paymentTermsConfig = paymentTermsConfigMap.get(customer.getPaymentTermsCode());
                if (paymentTermsConfig != null) {
                    vo.setPaymentTermsName(paymentTermsConfig.getName());
                }
            }
        }

        // 金额信息
        if (amount != null) {
            vo.setTotalAmount(amount.getTotalAmount());
            vo.setReceiptAmount(amount.getReceiptAmount());
            vo.setReceiptRate(amount.getReceiptRate());
            vo.setCurrency(amount.getCurrency());
        }

        // 商品信息，参考SaleOrderQueryService的实现
        if (CollUtil.isNotEmpty(orderItems)) {
            B2bOrderItemPO firstItem = orderItems.get(0);
            B2bOrderItemVO itemVO = new B2bOrderItemVO();
            itemVO.setPsku(firstItem.getPsku());
            itemVO.setImageId(firstItem.getImageId());
            itemVO.setBarcode(firstItem.getBarcode());
            itemVO.setOrderedQuantity(firstItem.getOrderedQuantity());
            itemVO.setShippedQuantity(firstItem.getShippedQuantity());
            itemVO.setItemCount(orderItems.size());

            // 设置图片URL
            if (firstItem.getImageId() != null && fileMap.containsKey(firstItem.getImageId())) {
                FileDetailResponse fileDetail = fileMap.get(firstItem.getImageId());
                if (fileDetail != null) {
                    itemVO.setImageUrl(fileDetail.getUrl());
                }
            }

            vo.setItem(itemVO);
        }

        return vo;
    }

    /**
     * 获取B2B订单详情
     */
    @TransMethodResult
    public B2bOrderDetailVO getB2bOrderDetail(Long orderId) {
        // 查询订单基本信息
        B2bOrderPO order = b2bOrderMapper.selectById(orderId);
        if (order == null) {
            return null;
        }

        // 查询客户信息
        B2bOrderCustomerPO customer = b2bOrderCustomerMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderCustomerPO>().eq(B2bOrderCustomerPO::getOrderId, orderId));

        // 查询金额信息
        B2bOrderAmountPO amount = b2bOrderAmountMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderAmountPO>().eq(B2bOrderAmountPO::getOrderId, orderId));

        // 查询商品信息
        List<B2bOrderItemPO> items = b2bOrderItemMapper
                .selectList(new LambdaQueryWrapper<B2bOrderItemPO>().eq(B2bOrderItemPO::getOrderId, orderId));

        // 查询备注信息
        List<B2bOrderRemarkPO> remarks = b2bOrderRemarkPOService.findByOrderId(orderId);

        // 获取贸易条款和付款条款配置
        Map<String, IncotermsConfigPO> incotermsConfigMap = new HashMap<>();
        Map<String, PaymentTermsConfigPO> paymentTermsConfigMap = new HashMap<>();

        if (customer != null) {
            // 获取贸易条款配置
            if (customer.getIncotermsCode() != null) {
                try {
                    IncotermsConfigPO incotermsConfig = incotermsConfigService.getByCode(customer.getIncotermsCode());
                    if (incotermsConfig != null) {
                        incotermsConfigMap.put(customer.getIncotermsCode(), incotermsConfig);
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
            }

            // 获取付款条款配置
            if (customer.getPaymentTermsCode() != null) {
                try {
                    PaymentTermsConfigPO paymentTermsConfig = paymentTermsConfigService.getByCode(customer.getPaymentTermsCode());
                    if (paymentTermsConfig != null) {
                        paymentTermsConfigMap.put(customer.getPaymentTermsCode(), paymentTermsConfig);
                    }
                } catch (Exception e) {
                    // 忽略异常
                }
            }
        }

        // 组装详情VO
        B2bOrderDetailVO detailVO = new B2bOrderDetailVO();

        // 设置订单基本信息
        B2bOrderDetailVO.OrderInfo orderInfo = new B2bOrderDetailVO.OrderInfo();
        orderInfo.setId(order.getId());
        orderInfo.setCustomerId(order.getCustomerId());
        orderInfo.setOrderNo(order.getOrderNo());
        orderInfo.setReferNo(order.getReferNo());
        orderInfo.setInvoiceNo(order.getInvoiceNo());
        orderInfo.setOrderStatus(order.getOrderStatus());
        orderInfo.setCreateTime(order.getCreateTime());
        orderInfo.setUpdateTime(order.getUpdateTime());
        orderInfo.setInvoiceTime(order.getInvoiceTime());
        orderInfo.setShippedTime(order.getShippedTime());
        detailVO.setOrderInfo(orderInfo);

        // 设置客户信息
        if (customer != null) {
            B2bOrderDetailVO.CustomerInfo customerInfo = new B2bOrderDetailVO.CustomerInfo();
            customerInfo.setCompanyName(customer.getCustomerCompanyName());
            customerInfo.setReferNo(order.getReferNo()); // 从订单获取参考号
            customerInfo.setCountry(customer.getCountry());
            // 获取国家名称，参照分页查询的方法
            customerInfo.setCountryName(getCountryName(customer.getCountry()));

            // 根据当前语言环境设置账号名称
            String languageTag = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");

            if ("zh-CN".equals(languageTag)) {
                customerInfo.setSalesCompanyName(customer.getSalesCompanyNameCn());
            } else {
                customerInfo.setSalesCompanyName(customer.getSalesCompanyNameEn());
            }
            customerInfo.setIncotermsCode(customer.getIncotermsCode());
            customerInfo.setPaymentTermsCode(customer.getPaymentTermsCode());

            // 设置贸易条款名称，参照分页查询的方法
            if (customer.getIncotermsCode() != null && incotermsConfigMap.containsKey(customer.getIncotermsCode())) {
                IncotermsConfigPO incotermsConfig = incotermsConfigMap.get(customer.getIncotermsCode());
                if (incotermsConfig != null) {
                    customerInfo.setIncotermsName(incotermsConfig.getName());
                }
            }

            // 设置付款条款名称，参照分页查询的方法
            if (customer.getPaymentTermsCode() != null && paymentTermsConfigMap.containsKey(customer.getPaymentTermsCode())) {
                PaymentTermsConfigPO paymentTermsConfig = paymentTermsConfigMap.get(customer.getPaymentTermsCode());
                if (paymentTermsConfig != null) {
                    customerInfo.setPaymentTermsName(paymentTermsConfig.getName());
                }
            }

            // 添加负责人信息
            customerInfo.setCountryManagerName(customer.getCountryManagerName());
            customerInfo.setSalesAssistantName(customer.getSalesAssistantName());

            // 查询保险信息
            if (order.getCustomerId() != null) {
                try {
                    Long customerId = order.getCustomerId();
                    CustomerInsuranceCreditInfoPO insuranceInfo = customerInsuranceCreditInfoService.findByCustomerId(customerId);
                    if (insuranceInfo != null) {
                        customerInfo.setInsuranceCompany(insuranceInfo.getInsuranceCompany());
                        customerInfo.setInsuredCurrency(insuranceInfo.getInsuredCurrency());
                        customerInfo.setInsuredAmount(insuranceInfo.getInsuredAmount());

                        // 查询客户订单欠款列表
                        try {
                            List<B2bCustomerOrderDebtVO> outstandingOrders = getCustomerOrderDebts(customerId,
                                    insuranceInfo.getInsuredCurrency());
                            customerInfo.setOutstandingOrders(outstandingOrders);

                            // 计算已用额度相关字段
                            calculateUsedCreditInfo(customerInfo, outstandingOrders, insuranceInfo);
                        } catch (Exception e) {
                            log.error("查询客户订单欠款失败，客户ID: {}", customerId, e);
                            customerInfo.setOutstandingOrders(Collections.emptyList());
                        }
                    }
                } catch (Exception e) {
                    // 保险信息查询失败不影响主流程
                    log.error("查询保险信息失败", e);
                }
            }

            detailVO.setCustomerInfo(customerInfo);
        }

        // 查询地址信息
        B2bOrderAddressPO address = b2bOrderAddressMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderAddressPO>().eq(B2bOrderAddressPO::getOrderId, orderId));

        if (address != null) {
            B2bOrderDetailVO.AddressInfo addressInfo = new B2bOrderDetailVO.AddressInfo();
            addressInfo.setCity(address.getCity());
            addressInfo.setCountry(address.getCountry());
            // 获取国家名称，参照分页查询的方法
            addressInfo.setCountryName(getCountryName(address.getCountry()));
            addressInfo.setPostalCode(address.getZipCode());
            addressInfo.setAddress(address.getAddress());
            addressInfo.setAttnTo(address.getAttnTo());
            addressInfo.setContactNumber(address.getContactNumber());
            addressInfo.setShippingCompany(address.getShippingCompany());
            addressInfo.setComment(address.getComment());
            detailVO.setAddressInfo(addressInfo);
        }

        // 设置金额信息
        if (amount != null) {
            B2bOrderDetailVO.AmountInfo amountInfo = new B2bOrderDetailVO.AmountInfo();
            amountInfo.setCurrency(amount.getCurrency());
            amountInfo.setProductPrice(amount.getProductPrice());
            amountInfo.setTax(amount.getTax());
            amountInfo.setShippingFee(amount.getShippingFee());
            amountInfo.setTotalAmount(amount.getTotalAmount());
            amountInfo.setReceiptAmount(amount.getReceiptAmount());
            amountInfo.setReceiptRate(amount.getReceiptRate());
            amountInfo.setOtherFees(amount.getOtherFees());

            // 解析费用明细
            List<B2bOrderDetailVO.FeeDetailInfo> feeDetails = parseFeesDetail(amount.getFeesDetail());
            amountInfo.setFeesDetail(feeDetails);

            detailVO.setAmountInfo(amountInfo);
        }

        // 查询发货要求
        B2bOrderShipmentRequirementPO shipmentRequirement = b2bOrderShipmentRequirementMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderShipmentRequirementPO>().eq(B2bOrderShipmentRequirementPO::getOrderId, orderId));

        if (shipmentRequirement != null) {
            B2bOrderDetailVO.ShipmentRequirements shipmentRequirements = new B2bOrderDetailVO.ShipmentRequirements();
            shipmentRequirements.setRequestDeliveryDate(shipmentRequirement.getRequestDeliveryDate());
            shipmentRequirements.setBlType(shipmentRequirement.getBlType());
            shipmentRequirements.setPalletizationRequired(shipmentRequirement.getPalletizationRequired());
            shipmentRequirements.setOtherRequirements(shipmentRequirement.getOtherRequirements());
            shipmentRequirements.setMasterCartonLabelName(shipmentRequirement.getMasterCartonLabelName());
            detailVO.setShipmentRequirements(shipmentRequirements);
        }

        // 设置商品信息
        if (CollUtil.isNotEmpty(items)) {
            List<B2bOrderDetailVO.ProductInfo> products = items.stream().map(item -> {
                B2bOrderDetailVO.ProductInfo product = new B2bOrderDetailVO.ProductInfo();
                product.setPsku(item.getPsku());
                product.setBarcode(item.getBarcode());
                product.setImageId(item.getImageId());
                product.setOrderedQuantity(item.getOrderedQuantity());
                product.setShippedQuantity(item.getShippedQuantity());
                // 计算订单数量：原数量除以每箱单位数量，四舍五入保留两位小数
                if (item.getOrderedQuantity() != null && item.getNumberOfUnitsPerBox() != null && item.getNumberOfUnitsPerBox() > 0) {
                    BigDecimal orderedQty = new BigDecimal(item.getOrderedQuantity()).divide(new BigDecimal(item.getNumberOfUnitsPerBox()),
                            2, RoundingMode.HALF_UP);
                    product.setOrderedBoxNumber(orderedQty);
                }

                // 计算发货数量：原数量除以每箱单位数量，四舍五入保留两位小数
                if (item.getShippedQuantity() != null && item.getNumberOfUnitsPerBox() != null && item.getNumberOfUnitsPerBox() > 0) {
                    BigDecimal shippedQty = new BigDecimal(item.getShippedQuantity()).divide(new BigDecimal(item.getNumberOfUnitsPerBox()),
                            2, RoundingMode.HALF_UP);
                    product.setShippedBoxNumber(shippedQty);
                }

                product.setUnitPrice(item.getUnitPrice());
                product.setTax(item.getTax());
                product.setSubTotal(item.getSubTotal());
                product.setNumberOfUnitsPerBox(item.getNumberOfUnitsPerBox());
                product.setTaxRate(item.getTaxRate());
                return product;
            }).collect(Collectors.toList());

            // 设置商品图片
            setProductImagesForDetail(products);
            detailVO.setProducts(products);
        }

        // 设置备注信息，参考SaleOrderQueryService的fillCreator方法
        if (CollUtil.isNotEmpty(remarks)) {
            List<B2bOrderDetailVO.NoteInfo> noteInfos = remarks.stream().map(remark -> {
                B2bOrderDetailVO.NoteInfo noteInfo = new B2bOrderDetailVO.NoteInfo();
                noteInfo.setCreateTime(remark.getCreateTime());
                noteInfo.setCreateBy(remark.getCreateBy());
                noteInfo.setContent(remark.getRemark());
                return noteInfo;
            }).collect(Collectors.toList());

            // 填充创建人信息
            fillCreatorInfo(noteInfos);
            detailVO.setNoteInfo(noteInfos);
        }

        return detailVO;
    }

    /**
     * 更新时获取b2b订单
     */
    @TransMethodResult
    public B2bOrderDetailForUpdateVo getB2bOrderDetailForUpdate(Long orderId) {
        // 查询订单基本信息
        B2bOrderPO order = b2bOrderMapper.selectById(orderId);
        if (order == null) {
            return null;
        }

        B2bOrderDetailForUpdateVo b2bOrderDetailForUpdateVo = new B2bOrderDetailForUpdateVo();
        // 订单id
        b2bOrderDetailForUpdateVo.setOrderId(order.getId());
        // 参考单号
        b2bOrderDetailForUpdateVo.setReferNo(order.getReferNo());

        // 查询客户信息
        B2bOrderCustomerPO customer = b2bOrderCustomerMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderCustomerPO>().eq(B2bOrderCustomerPO::getOrderId, orderId));
        // 海外经理
        b2bOrderDetailForUpdateVo.setCountryManagerName(customer.getCountryManagerName());
        // 销售助理
        b2bOrderDetailForUpdateVo.setSalesAssistantName(customer.getSalesAssistantName());

        // 客户联系方式
        B2bOrderDetailForUpdateVo.Customer customerInfo = new B2bOrderDetailForUpdateVo.Customer();
        customerInfo.setCustomerId(order.getCustomerId());
        // 贸易条款
        customerInfo.setIncotermsCode(customer.getIncotermsCode());
        // 支付条款
        customerInfo.setPaymentTermsCode(customer.getPaymentTermsCode());
        // 销售公司
        customerInfo.setSalesCompanyId(customer.getSalesCompanyId());
        b2bOrderDetailForUpdateVo.setCustomer(customerInfo);

        // 联系人
        List<B2bOrderCustomerContactPO> contacts = b2bOrderCustomerContactMapper
                .selectList(new LambdaQueryWrapper<B2bOrderCustomerContactPO>().eq(B2bOrderCustomerContactPO::getOrderId, orderId));
        List<B2bOrderDetailForUpdateVo.CustomerContact> contactList = contacts.stream()
                .map(contactPo -> B2bOrderDetailForUpdateVo.CustomerContact.builder()
                        // 联系人类型，1-主联系人 2-采购 3-物流 4-财务
                        .contactType(contactPo.getContactType())
                        // 客户联系信息id
                        .customerContactId(contactPo.getCustomerContactId())
                        // 岗位
                        .contactPosition(contactPo.getContactPosition())
                        // 名字
                        .contactName(contactPo.getContactName())
                        // 备注
                        .remark(contactPo.getRemark())
                        .build())
                .toList();
        b2bOrderDetailForUpdateVo.setCustomerContacts(contactList);

        // 发票信息
        B2bOrderCustomerInvoicePO customerInvoice = b2bOrderCustomerInvoiceMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderCustomerInvoicePO>().eq(B2bOrderCustomerInvoicePO::getOrderId, orderId));
        if (customerInvoice != null) {
            B2bOrderDetailForUpdateVo.CustomerInvoice customerInvoiceInfo = new B2bOrderDetailForUpdateVo.CustomerInvoice();
            // 收件人
            customerInvoiceInfo.setAttnTo(customerInvoice.getAttnTo());
            // 发票公司
            customerInvoiceInfo.setInvoiceCompany(customerInvoice.getInvoiceCompany());
            // 地址
            customerInvoiceInfo.setAddress(customerInvoice.getAddress());
            // 邮编
            customerInvoiceInfo.setZipCode(customerInvoice.getZipCode());
            // 城市
            customerInvoiceInfo.setCity(customerInvoice.getCity());
            // 国家
            customerInvoiceInfo.setCountry(customerInvoice.getCountry());
            // 联系号码
            customerInvoiceInfo.setContactNumber(customerInvoice.getContactNumber());
            // 增值税号
            customerInvoiceInfo.setVatNumber(customerInvoice.getVatNumber());
            b2bOrderDetailForUpdateVo.setCustomerInvoice(customerInvoiceInfo);
        }

        // 查询收货地址信息
        B2bOrderAddressPO address = b2bOrderAddressMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderAddressPO>().eq(B2bOrderAddressPO::getOrderId, orderId));
        if (address != null) {
            B2bOrderDetailForUpdateVo.Address addressInfo = new B2bOrderDetailForUpdateVo.Address();
            // 收件人
            addressInfo.setAttnTo(address.getAttnTo());
            // 收货方公司
            addressInfo.setShippingCompany(address.getShippingCompany());
            // 收货地址
            addressInfo.setAddress(address.getAddress());
            // 邮编
            addressInfo.setZipCode(address.getZipCode());
            // 城市
            addressInfo.setCity(address.getCity());
            // 国家
            addressInfo.setCountry(address.getCountry());
            // 联系号码
            addressInfo.setContactNumber(address.getContactNumber());
            // 备注
            addressInfo.setComment(address.getComment());
            b2bOrderDetailForUpdateVo.setAddress(addressInfo);
        }

        // 查询商品信息
        List<B2bOrderItemPO> items = b2bOrderItemMapper
                .selectList(new LambdaQueryWrapper<B2bOrderItemPO>().eq(B2bOrderItemPO::getOrderId, orderId));
        List<B2bOrderDetailForUpdateVo.Item> itemList = items.stream().map(itemPo -> {
            B2bOrderDetailForUpdateVo.Item item = new B2bOrderDetailForUpdateVo.Item();
            // 订单商品主键id
            item.setOrderItemId(itemPo.getId());
            // psku
            item.setPsku(itemPo.getPsku());
            // 下单数量
            item.setOrderedQuantity(itemPo.getOrderedQuantity());
            // 单价
            item.setUnitPrice(itemPo.getUnitPrice());
            // 税率
            item.setTaxRate(itemPo.getTaxRate());
            // 税金
            item.setTax(itemPo.getTax());
            // 条码
            item.setBarcode(itemPo.getBarcode());
            // 装箱数量（每箱的psku数量）
            item.setNumberOfUnitsPerBox(itemPo.getNumberOfUnitsPerBox());
            // 下单的箱数:下单数量除以装箱数量，四舍五入保留两位小数
            item.setOrderedBoxNumber(new BigDecimal(itemPo.getOrderedQuantity()).divide(new BigDecimal(itemPo.getNumberOfUnitsPerBox()), 2,
                    RoundingMode.HALF_UP));
            // 小计
            item.setSubTotal(itemPo.getSubTotal());
            // psku中文or英文名称，根据语言环境返回
            if ("zh-CN".equals(LocaleContextHolder.getLocale().toLanguageTag())) {
                item.setPskuName(itemPo.getPskuNameZh());
            } else {
                item.setPskuName(itemPo.getPskuNameEn());
            }
            return item;
        }).toList();
        b2bOrderDetailForUpdateVo.setItems(itemList);

        // 查询金额信息
        B2bOrderAmountPO amount = b2bOrderAmountMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderAmountPO>().eq(B2bOrderAmountPO::getOrderId, orderId));
        B2bOrderDetailForUpdateVo.Amount amountInfo = new B2bOrderDetailForUpdateVo.Amount();
        // 币种
        amountInfo.setCurrency(amount.getCurrency());
        // 运费收入
        amountInfo.setShippingFee(amount.getShippingFee());
        // 其他收入
        amountInfo.setOtherFees(amount.getOtherFees());
        // 金额合计
        amountInfo.setTotalAmount(amount.getTotalAmount());
        // 费用明细
        List<B2bOrderFreeDetail> b2bOrderFreeDetailList = JsonUtil.toList(StrUtil.blankToDefault(amount.getFeesDetail(), "[]"),
                B2bOrderFreeDetail.class);
        List<B2bOrderDetailForUpdateVo.FreeDetail> freeDetailList = b2bOrderFreeDetailList.stream().map(feeDetail -> {
            B2bOrderDetailForUpdateVo.FreeDetail feeDetailInfo = new B2bOrderDetailForUpdateVo.FreeDetail();
            feeDetailInfo.setFeeType(feeDetail.getFeeType());
            feeDetailInfo.setFeeAmount(feeDetail.getFeeAmount());
            return feeDetailInfo;
        }).toList();
        amountInfo.setFeesDetails(freeDetailList);
        b2bOrderDetailForUpdateVo.setAmount(amountInfo);

        // 查询发货要求
        B2bOrderShipmentRequirementPO shipmentRequirement = b2bOrderShipmentRequirementMapper
                .selectOne(new LambdaQueryWrapper<B2bOrderShipmentRequirementPO>().eq(B2bOrderShipmentRequirementPO::getOrderId, orderId));
        B2bOrderDetailForUpdateVo.ShipmentRequirement shipmentRequirementInfo = new B2bOrderDetailForUpdateVo.ShipmentRequirement();
        // 货物最晚交期
        shipmentRequirementInfo.setRequestDeliveryDate(shipmentRequirement.getRequestDeliveryDate());
        // 是否打托
        shipmentRequirementInfo.setPalletizationRequired(shipmentRequirement.getPalletizationRequired());
        // 提单类型
        shipmentRequirementInfo.setBlType(shipmentRequirement.getBlType());
        // 外箱标签名称
        shipmentRequirementInfo.setMasterCartonLabelName(shipmentRequirement.getMasterCartonLabelName());
        // 外箱标签链接
        shipmentRequirementInfo.setMasterCartonLabelUrl(shipmentRequirement.getMasterCartonLabelUrl());
        // 其他要求
        shipmentRequirementInfo.setOtherRequirements(shipmentRequirement.getOtherRequirements());
        b2bOrderDetailForUpdateVo.setShipmentRequirement(shipmentRequirementInfo);

        return b2bOrderDetailForUpdateVo;
    }

    /**
     * 获取B2B订单客户联系人列表
     */
    @TransMethodResult
    public List<B2bOrderCustomerContactVO> getB2bOrderCustomerContacts(Long orderId) {
        List<B2bOrderCustomerContactPO> contacts = b2bOrderCustomerContactMapper
                .selectList(new LambdaQueryWrapper<B2bOrderCustomerContactPO>().eq(B2bOrderCustomerContactPO::getOrderId, orderId));

        // 转换PO为VO
        return contacts.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 将B2bOrderCustomerContactPO转换为B2bOrderCustomerContactVO
     */
    private B2bOrderCustomerContactVO convertToVO(B2bOrderCustomerContactPO po) {
        B2bOrderCustomerContactVO vo = new B2bOrderCustomerContactVO();
        vo.setId(po.getId());
        vo.setOrderId(po.getOrderId());
        vo.setContactType(po.getContactType());
        vo.setContactName(po.getContactName());
        vo.setContactEmail(po.getContactEmail());
        vo.setContactPhone(po.getContactPhone());
        vo.setRemark(po.getRemark());
        return vo;
    }

    /**
     * 获取B2B订单收款单列表
     */
    public List<B2bReceiptOrderVO> getB2bReceiptOrders(Long orderId) {
        List<B2bReceiptOrderPO> receiptOrders = b2bReceiptOrderMapper
                .selectList(new LambdaQueryWrapper<B2bReceiptOrderPO>().eq(B2bReceiptOrderPO::getB2bOrderId, orderId));

        // 转换PO为VO
        return receiptOrders.stream().map(this::convertReceiptOrderToVO).collect(Collectors.toList());
    }

    /**
     * 将B2BReceiptOrderPO转换为B2bReceiptOrderVO
     */
    private B2bReceiptOrderVO convertReceiptOrderToVO(B2bReceiptOrderPO po) {
        B2bReceiptOrderVO vo = new B2bReceiptOrderVO();
        vo.setBankReferNo(po.getBankReferNo());
        vo.setCustomerPaidTime(po.getCustomerPaidTime());
        vo.setReceiptTime(po.getReceiptTime());
        vo.setCustomerPaidAmount(po.getCustomerPaidAmount());
        vo.setReceiptAmount(po.getReceiptAmount());
        vo.setFeesAmount(po.getFeesAmount());
        vo.setStatus(po.getStatus());

        // 根据当前语言环境设置账号名称
        String languageTag = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");

        if ("zh-CN".equals(languageTag)) {
            vo.setAccountName(po.getFmsFundAccountCurrencyCnname());
        } else {
            vo.setAccountName(po.getFmsFundAccountCurrencyEnname());
        }

        return vo;
    }

    /**
     * 获取B2B客户订单欠款列表
     *
     * @param customerId      客户ID
     * @param insuredCurrency 投保币种
     * @return 客户订单欠款列表
     */
    public List<B2bCustomerOrderDebtVO> getCustomerOrderDebts(Long customerId, String insuredCurrency) {
        // 1. 查询客户的未完结订单（状态不是6-已完结 7-已取消）
        List<B2bOrderPO> orders = b2bOrderMapper.selectList(new LambdaQueryWrapper<B2bOrderPO>().eq(B2bOrderPO::getCustomerId, customerId)
                .notIn(B2bOrderPO::getOrderStatus, Arrays.asList(6, 7)));

        if (orders.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取订单ID列表
        List<Long> orderIds = orders.stream().map(B2bOrderPO::getId).collect(Collectors.toList());

        // 查询订单金额信息
        List<B2bOrderAmountPO> amounts = b2bOrderAmountMapper
                .selectList(new LambdaQueryWrapper<B2bOrderAmountPO>().in(B2bOrderAmountPO::getOrderId, orderIds));

        // 转换为Map便于查找
        Map<Long, B2bOrderAmountPO> amountMap = amounts.stream()
                .collect(Collectors.toMap(B2bOrderAmountPO::getOrderId, Function.identity()));

        // 2. 过滤掉 total_amount <= receipt_amount 的数据
        List<OrderDebtInfo> debtInfos = orders.stream().map(order -> {
            B2bOrderAmountPO amount = amountMap.get(order.getId());
            if (amount == null) {
                return null;
            }

            BigDecimal totalAmount = amount.getTotalAmount();
            BigDecimal receiptAmount = amount.getReceiptAmount();

            // 过滤掉已收款完成的订单
            if (totalAmount == null || receiptAmount == null || totalAmount.compareTo(receiptAmount) <= 0) {
                return null;
            }

            return new OrderDebtInfo(order.getOrderNo(), amount.getCurrency(), totalAmount, receiptAmount);
        }).filter(Objects::nonNull).collect(Collectors.toList());

        if (debtInfos.isEmpty()) {
            return Collections.emptyList();
        }

        // 3. 查询汇率信息
        Map<String, LatestFluctuateExchangeRateResponse> exchangeRateMap = getExchangeRateMap(insuredCurrency);

        // 4. 转换金额并计算欠款
        return debtInfos.stream()
                .map(debtInfo -> convertToDebtVO(debtInfo, exchangeRateMap, insuredCurrency))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 获取汇率Map（临时实现，等依赖导入后修正）
     */
    private Map<String, LatestFluctuateExchangeRateResponse> getExchangeRateMap(String targetCurrency) {
        try {
            return rateClient.getLatestExchangeRateMap(targetCurrency);

        } catch (Exception e) {
            log.error("查询汇率失败", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 转换为欠款VO
     */
    private B2bCustomerOrderDebtVO convertToDebtVO(OrderDebtInfo debtInfo, Map<String, LatestFluctuateExchangeRateResponse> exchangeRateMap,
                                                   String insuredCurrency) {
        BigDecimal convertedTotalAmount;
        BigDecimal convertedReceiptAmount;

        // 优化：如果订单币种与投保币种一致，无需转换
        if (Objects.equals(debtInfo.getCurrency(), insuredCurrency)) {
            convertedTotalAmount = debtInfo.getTotalAmount();
            convertedReceiptAmount = debtInfo.getReceiptAmount();
        } else {
            // 需要汇率转换
            LatestFluctuateExchangeRateResponse latestFluctuateExchangeRateResponse = exchangeRateMap.get(debtInfo.getCurrency());
            if (latestFluctuateExchangeRateResponse == null || latestFluctuateExchangeRateResponse.getExchangeRate() == null) {
                throw new RuntimeException(
                        String.format("找不到从 %s 到 %s 的汇率", debtInfo.getCurrency(), insuredCurrency));
            }
            BigDecimal exchangeRate = latestFluctuateExchangeRateResponse.getExchangeRate();

            convertedTotalAmount = debtInfo.getTotalAmount().multiply(exchangeRate);
            convertedReceiptAmount = debtInfo.getReceiptAmount().multiply(exchangeRate);
        }

        BigDecimal debtAmount = convertedTotalAmount.subtract(convertedReceiptAmount);

        B2bCustomerOrderDebtVO vo = new B2bCustomerOrderDebtVO();
        vo.setOrderNo(debtInfo.getOrderNo());
        vo.setConvertedTotalAmount(convertedTotalAmount);
        vo.setConvertedReceiptAmount(convertedReceiptAmount);
        vo.setDebtAmount(debtAmount);

        return vo;
    }

    /**
     * 订单欠款信息内部类
     */
    private static class OrderDebtInfo {
        private final String orderNo;
        private final String currency;
        private final BigDecimal totalAmount;
        private final BigDecimal receiptAmount;

        public OrderDebtInfo(String orderNo, String currency, BigDecimal totalAmount, BigDecimal receiptAmount) {
            this.orderNo = orderNo;
            this.currency = currency;
            this.totalAmount = totalAmount;
            this.receiptAmount = receiptAmount;
        }

        public String getOrderNo() {
            return orderNo;
        }

        public String getCurrency() {
            return currency;
        }

        public BigDecimal getTotalAmount() {
            return totalAmount;
        }

        public BigDecimal getReceiptAmount() {
            return receiptAmount;
        }
    }

    /**
     * 计算已用额度相关信息
     */
    private void calculateUsedCreditInfo(B2bOrderDetailVO.CustomerInfo customerInfo, List<B2bCustomerOrderDebtVO> outstandingOrders,
                                         CustomerInsuranceCreditInfoPO insuranceInfo) {
        // 1. 已用额度币种，等于insuredCurrency
        customerInfo.setUsedCreditCurrency(insuranceInfo.getInsuredCurrency());

        // 2. 已用额度金额：B2bCustomerOrderDebtVO List debtAmount字段累计
        BigDecimal totalDebtAmount = outstandingOrders.stream()
                .map(B2bCustomerOrderDebtVO::getDebtAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        customerInfo.setUsedCreditAmount(totalDebtAmount);

        // 3. 已用额度比例：已用额度金额/insuredAmount 保留两位小数
        BigDecimal insuredAmount = insuranceInfo.getInsuredAmount();
        if (insuredAmount != null && insuredAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal ratio = totalDebtAmount.divide(insuredAmount, 4, RoundingMode.HALF_UP).setScale(4, RoundingMode.HALF_UP);
            customerInfo.setUsedCreditRatio(ratio);
        } else {
            customerInfo.setUsedCreditRatio(BigDecimal.ZERO);
        }
    }


    /**
     * 为详情页设置商品图片URL
     */
    private void setProductImagesForDetail(List<B2bOrderDetailVO.ProductInfo> products) {
        if (CollUtil.isEmpty(products)) {
            return;
        }

        List<String> imageIds = products.stream()
                .map(B2bOrderDetailVO.ProductInfo::getImageId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(imageIds)) {
            return;
        }

        try {
            Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(new HashSet<>(imageIds));
            products.forEach(product -> {
                if (product.getImageId() != null && fileMap.containsKey(product.getImageId())) {
                    FileDetailResponse fileDetail = fileMap.get(product.getImageId());
                    if (fileDetail != null) {
                        product.setImageUrl(fileDetail.getUrl());
                    }
                }
            });
        } catch (Exception e) {
            // 图片获取失败不影响主流程
            // log.warn("获取商品图片失败", e);
        }
    }

    /**
     * 填充创建人信息，参考SaleOrderQueryService的fillCreator方法
     */
    private void fillCreatorInfo(List<B2bOrderDetailVO.NoteInfo> noteInfos) {
        if (CollUtil.isEmpty(noteInfos)) {
            return;
        }

        // 获取所有创建者的用户ID列表
        List<Integer> userIdList = noteInfos.stream()
                .map(B2bOrderDetailVO.NoteInfo::getCreateBy)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(userIdList)) {
            return;
        }

        try {
            // 根据用户ID列表获取用户信息并存储到Map中
            Map<Integer, OumUserInfoRes> userInfoMap = userClient.getUserByIds(userIdList)
                    .stream()
                    .collect(Collectors.toMap(OumUserInfoRes::getId, Function.identity()));

            // 设置用户信息
            noteInfos.forEach(noteInfo -> {
                Integer createBy = noteInfo.getCreateBy();
                if (createBy != null) {
                    if (Integer.valueOf(0).equals(createBy)) {
                        // 创建人 0 System/系统
                        noteInfo.setCreator("System");
                        noteInfo.setCreatorNo("System");
                    } else {
                        OumUserInfoRes userInfo = userInfoMap.get(createBy);
                        if (userInfo != null) {
                            noteInfo.setCreator(userInfo.getName());
                            noteInfo.setCreatorNo(userInfo.getCode());
                        }
                    }
                }
            });
        } catch (Exception e) {
            // 用户信息获取失败不影响主流程
            // log.warn("获取用户信息失败", e);
        }
    }

    /**
     * 获取国家名称，参照分页查询的方法
     */
    private String getCountryName(String countryCode) {
        if (countryCode == null) {
            return null;
        }
        try {
            return countryClient.getLanguageCountryName(countryCode);
        } catch (Exception e) {
            // 获取国家名称失败，返回原始代码
            return countryCode;
        }
    }

    /**
     * 解析费用明细JSON字符串为费用明细列表
     */
    private List<B2bOrderDetailVO.FeeDetailInfo> parseFeesDetail(String feesDetailJson) {
        if (feesDetailJson == null || feesDetailJson.trim().isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 使用JSONKit解析JSON为B2bOrderFreeDetail列表
            List<B2bOrderFreeDetail> freeDetails = JSONKit.parseObject(feesDetailJson, new TypeReference<List<B2bOrderFreeDetail>>() {
            });

            // 转换为FeeDetailInfo列表
            return freeDetails.stream().map(freeDetail -> {
                B2bOrderDetailVO.FeeDetailInfo feeDetail = new B2bOrderDetailVO.FeeDetailInfo();
                feeDetail.setFeeType(freeDetail.getFeeType());
                feeDetail.setFeeAmount(freeDetail.getFeeAmount());
                return feeDetail;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            // JSON解析失败，返回空列表
            // log.warn("解析费用明细失败: {}", feesDetailJson, e);
            return Collections.emptyList();
        }
    }

    /**
     * B2B订单导出
     *
     * @param query    查询参数
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @TransMethodResult
    public void export(B2bOrderPageQuery query, HttpServletResponse response) throws IOException {
        HttpUtil.setExportResponseHeader(response, "b2bOrder.xlsx");
        @Cleanup
        ExcelWriter excelWriter = EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(i18nHeaderCellWriteHandler)
                .autoCloseStream(true)
                .build();
        WriteSheet writeSheet = EasyExcelFactory.writerSheet(0).head(B2bOrderExportExcel.class).build();

        query.setPageSize(1000);
        query.setPageIndex(1);
        B2bOrderQueryService bean = SpringUtil.getBean(this.getClass());
        do {
            List<B2bOrderExportExcel> records = bean.getB2bOrderExportData(query);
            excelWriter.write(records, writeSheet);
            if (!Objects.equals(records.size(), query.getPageSize())) {
                break;
            }
            query.setPageIndex(query.getPageIndex() + 1);
        }
        while (true);
    }

    /**
     * 获取B2B订单导出数据（按商品维度）
     *
     * @param query 查询参数
     * @return 导出数据列表
     */
    @TransMethodResult
    public List<B2bOrderExportExcel> getB2bOrderExportData(B2bOrderPageQuery query) {
        // 1. 构建查询条件并分页查询主表（带数据权限控制）
        Page<B2bOrderPO> pageParam = new Page<>(query.getPageIndex(), query.getPageSize(), false);
        LambdaQueryWrapper<B2bOrderPO> wrapper = buildQueryWrapper(query);
        IPage<B2bOrderPO> orderPage = b2bOrderDataPermissionService.selectPageWithPermission(pageParam, wrapper);

        if (CollUtil.isEmpty(orderPage.getRecords())) {
            return Collections.emptyList();
        }

        // 2. 收集订单ID
        List<Long> orderIds = orderPage.getRecords().stream().map(B2bOrderPO::getId).collect(Collectors.toList());

        // 3. 批量查询关联数据
        Map<Long, B2bOrderPO> orderMap = orderPage.getRecords().stream().collect(Collectors.toMap(B2bOrderPO::getId, Function.identity()));

        Map<Long, B2bOrderCustomerPO> customerMap = b2bOrderCustomerMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.toMap(B2bOrderCustomerPO::getOrderId, Function.identity()));

        Map<Long, B2bOrderAmountPO> amountMap = b2bOrderAmountMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.toMap(B2bOrderAmountPO::getOrderId, Function.identity()));

        // 查询发货要求信息
        Map<Long, B2bOrderShipmentRequirementPO> shipmentMap = b2bOrderShipmentRequirementMapper.getByOrderIds(orderIds)
                .stream()
                .collect(Collectors.toMap(B2bOrderShipmentRequirementPO::getOrderId, Function.identity()));

        // 查询所有商品信息
        List<B2bOrderItemPO> allItems = b2bOrderItemMapper.getByOrderIds(orderIds);

        // 4. 批量查询贸易条款和付款条款配置
        Set<String> incotermsCodeSet = customerMap.values()
                .stream()
                .map(B2bOrderCustomerPO::getIncotermsCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        Set<String> paymentTermsCodeSet = customerMap.values()
                .stream()
                .map(B2bOrderCustomerPO::getPaymentTermsCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        final Map<String, IncotermsConfigPO> incotermsConfigMap = CollUtil.isNotEmpty(incotermsCodeSet) ? incotermsConfigService.list()
                .stream()
                .filter(config -> incotermsCodeSet.contains(config.getCode()))
                .collect(Collectors.toMap(IncotermsConfigPO::getCode, Function.identity())) : new HashMap<>();

        final Map<String, PaymentTermsConfigPO> paymentTermsConfigMap = CollUtil.isNotEmpty(paymentTermsCodeSet)
                ? paymentTermsConfigService.list()
                .stream()
                .filter(config -> paymentTermsCodeSet.contains(config.getCode()))
                .collect(Collectors.toMap(PaymentTermsConfigPO::getCode, Function.identity()))
                : new HashMap<>();

        // 5. 按商品维度组装导出数据
        List<B2bOrderExportExcel> exportList = new ArrayList<>();
        for (B2bOrderItemPO item : allItems) {
            B2bOrderPO order = orderMap.get(item.getOrderId());
            B2bOrderCustomerPO customer = customerMap.get(item.getOrderId());
            B2bOrderAmountPO amount = amountMap.get(item.getOrderId());
            B2bOrderShipmentRequirementPO shipment = shipmentMap.get(item.getOrderId());

            B2bOrderExportExcel exportExcel = convertToExportExcel(order, customer, amount, shipment, item, incotermsConfigMap,
                    paymentTermsConfigMap);
            exportList.add(exportExcel);
        }

        return exportList;
    }

    /**
     * 转换为导出Excel对象
     */
    private B2bOrderExportExcel convertToExportExcel(B2bOrderPO order, B2bOrderCustomerPO customer, B2bOrderAmountPO amount,
                                                     B2bOrderShipmentRequirementPO shipment, B2bOrderItemPO item, Map<String, IncotermsConfigPO> incotermsConfigMap,
                                                     Map<String, PaymentTermsConfigPO> paymentTermsConfigMap) {
        B2bOrderExportExcel excel = new B2bOrderExportExcel();

        // 订单基本信息
        excel.setOrderNo(order.getOrderNo());
        excel.setReferNo(order.getReferNo());
        Optional.ofNullable(order.getCreateTime())
                .map(LocalDateTimeTransKits::tansByUserZoneId)
                .ifPresent(time -> excel.setCreateTime(time));
        Optional.ofNullable(order.getInvoiceTime())
                .map(LocalDateTimeTransKits::tansByUserZoneId)
                .ifPresent(time -> excel.setInvoiceTime(time));
        Optional.ofNullable(order.getShippedTime())
                .map(LocalDateTimeTransKits::tansByUserZoneId)
                .ifPresent(time -> excel.setShippedTime(time));
        excel.setRemark(order.getRemark());

        String languageTag = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");

        // 客户信息
        if (customer != null) {
            excel.setCustomerCompanyName(customer.getCustomerCompanyName());

            if ("zh-CN".equals(languageTag)) {
                excel.setSalesCompanyName(customer.getSalesCompanyNameCn());
            } else {
                excel.setSalesCompanyName(customer.getSalesCompanyNameEn());
            }

            excel.setCountryManagerName(customer.getCountryManagerName());
            excel.setSalesAssistantName(customer.getSalesAssistantName());
            excel.setIncotermsCode(customer.getIncotermsCode());
            excel.setPaymentTermsCode(customer.getPaymentTermsCode());

            // 设置贸易条款名称
            if (customer.getIncotermsCode() != null && incotermsConfigMap.containsKey(customer.getIncotermsCode())) {
                IncotermsConfigPO incotermsConfig = incotermsConfigMap.get(customer.getIncotermsCode());
                if (incotermsConfig != null) {
                    excel.setIncotermsName(incotermsConfig.getName());
                }
            }

            // 设置付款条款名称
            if (customer.getPaymentTermsCode() != null && paymentTermsConfigMap.containsKey(customer.getPaymentTermsCode())) {
                PaymentTermsConfigPO paymentTermsConfig = paymentTermsConfigMap.get(customer.getPaymentTermsCode());
                if (paymentTermsConfig != null) {
                    excel.setPaymentTermsName(paymentTermsConfig.getName());
                }
            }
        }

        // 金额信息
        if (amount != null) {
            excel.setCurrency(amount.getCurrency());
            excel.setShippingFee(amount.getShippingFee());
            excel.setTotalAmount(amount.getTotalAmount());
            excel.setReceiptAmount(amount.getReceiptAmount());

            // 解析其他费用
            try {
                List<B2bOrderFreeDetail> feeDetails = parseFeesDetailToFreeDetail(amount.getFeesDetail());
                if (CollUtil.isNotEmpty(feeDetails)) {
                    BigDecimal otherFeeTotal = feeDetails.stream()
                            .map(B2bOrderFreeDetail::getFeeAmount)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    excel.setOtherFee(otherFeeTotal);

                    // 费用明细描述 - 使用多语言翻译
                    String feeDetailStr = buildFeeDetailString(feeDetails);
                    excel.setOtherFeeDetail(feeDetailStr);
                }
            } catch (Exception e) {
                log.error("B2B订单导出解析其他费用失败", e);
                excel.setOtherFeeDetail("");
            }
        }

        // 商品信息
        if (item != null) {
            excel.setId(item.getId());
            excel.setPsku(item.getPsku());
            excel.setBarcode(item.getBarcode());
            excel.setOrderedQuantity(item.getOrderedQuantity());
            excel.setUnitPrice(item.getUnitPrice());
            excel.setTaxRate(item.getTaxRate());
            excel.setTax(item.getTax());

            // 根据当前语言环境设置PSKU名称

            if ("zh-CN".equals(languageTag)) {
                excel.setPskuName(item.getPskuNameZh());
            } else {
                excel.setPskuName(item.getPskuNameEn());
            }
        }

        // 发货要求信息
        if (shipment != null) {
            excel.setRequestDeliveryDate(shipment.getRequestDeliveryDate());
            excel.setPalletizationRequired(shipment.getPalletizationRequired());
            excel.setBlType(shipment.getBlType());
            excel.setOtherRequirements(shipment.getOtherRequirements());
        }

        // 设置内部字段
        excel.setOrderId(order.getId());

        return excel;
    }

    /**
     * 解析费用明细为B2bOrderFreeDetail对象列表
     */
    private List<B2bOrderFreeDetail> parseFeesDetailToFreeDetail(String feesDetailJson) {
        if (feesDetailJson == null || feesDetailJson.trim().isEmpty()) {
            return Collections.emptyList();
        }

        try {
            return JSONKit.parseObject(feesDetailJson, new TypeReference<List<B2bOrderFreeDetail>>() {
            });
        } catch (Exception e) {
            return Collections.emptyList();
        }
    }

    /**
     * 获取B2B订单商品列表
     *
     * @param orderId 订单ID
     * @return 商品列表
     */
    @TransMethodResult
    public List<B2bOrderItemVO> getB2bOrderItemList(Long orderId) {
        // 查询订单商品信息
        List<B2bOrderItemPO> items = b2bOrderItemMapper
                .selectList(new LambdaQueryWrapper<B2bOrderItemPO>().eq(B2bOrderItemPO::getOrderId, orderId).orderByAsc(B2bOrderItemPO::getId));

        if (CollUtil.isEmpty(items)) {
            return Collections.emptyList();
        }

        // 收集图片ID并获取图片信息
        Set<String> imageIdSet = items.stream().map(B2bOrderItemPO::getImageId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);

        // 转换为VO对象
        return items.stream().map(item -> convertToItemVO(item, fileMap)).collect(Collectors.toList());
    }

    /**
     * 转换为商品VO对象
     */
    private B2bOrderItemVO convertToItemVO(B2bOrderItemPO item, Map<String, FileDetailResponse> fileMap) {
        B2bOrderItemVO vo = new B2bOrderItemVO();

        vo.setId(item.getId());
        vo.setOrderId(item.getOrderId());
        vo.setPsku(item.getPsku());
        vo.setImageId(item.getImageId());
        vo.setBarcode(item.getBarcode());
        vo.setOrderedQuantity(item.getOrderedQuantity());
        vo.setShippedQuantity(item.getShippedQuantity());
        vo.setPskuNameZh(item.getPskuNameZh());
        vo.setPskuNameEn(item.getPskuNameEn());
        vo.setNumberOfUnitsPerBox(item.getNumberOfUnitsPerBox());

        // 设置图片URL
        if (item.getImageId() != null && fileMap.containsKey(item.getImageId())) {
            FileDetailResponse fileDetail = fileMap.get(item.getImageId());
            if (fileDetail != null) {
                vo.setImageUrl(fileDetail.getUrl());
            }
        }

        return vo;
    }

    /**
     * 构建费用明细字符串（支持多语言翻译）
     *
     * @param feeDetails 费用明细列表
     * @return 费用明细字符串
     */
    private String buildFeeDetailString(List<B2bOrderFreeDetail> feeDetails) {
        if (CollUtil.isEmpty(feeDetails)) {
            return "";
        }

        // 创建临时VO列表用于翻译
        List<FeeDetailTransVO> transVOList = feeDetails.stream().map(fee -> {
            FeeDetailTransVO vo = new FeeDetailTransVO();
            vo.setFeeType(fee.getFeeType());
            vo.setFeeAmount(fee.getFeeAmount());
            return vo;
        }).collect(Collectors.toList());

        // 尝试使用TransUtil进行翻译
        try {
            if (transService != null) {
                TransUtil.transOne(transVOList, transService, true, new ArrayList<>(), null, null);
                log.debug("Successfully applied Trans translation to fee details");

                // 使用翻译后的名称构建字符串
                return transVOList.stream()
                        .map(vo -> (vo.getFeeTypeName() != null ? vo.getFeeTypeName() : vo.getFeeType().toString()) + ":" + vo.getFeeAmount())
                        .collect(Collectors.joining(";"));
            } else {
                log.debug("TransService is null, fallback to enum mapping");
            }
        } catch (Exception e) {
            log.warn("Failed to use TransUtil for translation, fallback to enum mapping: {}", e.getMessage());
        }

        // 回退到枚举映射（保持原有逻辑作为备选方案）
        return feeDetails.stream().map(fee -> {
            String feeTypeName = fee.getFeeType().toString();
            B2bOrderFeeTypeEnum feeTypeEnum = B2bOrderFeeTypeEnum.getByValue(fee.getFeeType());
            if (feeTypeEnum != null) {
                feeTypeName = feeTypeEnum.getDesc();
            }
            return feeTypeName + ":" + fee.getFeeAmount();
        }).collect(Collectors.joining(";"));
    }

    /**
     * 费用明细翻译VO
     */
    @Data
    public static class FeeDetailTransVO implements VO {
        /**
         * 费用类型：1-推广费 2-广告费
         */
        @Trans(type = TransType.DICTIONARY, key = "ToB_fee_type", ref = "feeTypeName")
        private Integer feeType;

        /**
         * 费用类型名称
         */
        private String feeTypeName;

        /**
         * 费用金额
         */
        private BigDecimal feeAmount;
    }
}
