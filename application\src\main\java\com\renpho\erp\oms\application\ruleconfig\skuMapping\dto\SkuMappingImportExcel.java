package com.renpho.erp.oms.application.ruleconfig.skuMapping.dto;

import java.math.BigDecimal;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SkuMappingImportExcel {
	/**
	 * 店铺
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.STORE")
	private String store;

	/**
	 * 销售SKU
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.SELLERSKU")
	private String msku;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL.FNSKU")
	private String fnSku;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL.ASIN")
	private String asin;

	/**
	 * 采购SKU
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.PURCHASESKU")
	private String psku;

	/**
	 * 运营工号
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.OPERATORNO")
	private String operatorNo;

	/**
	 * 发货方式
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.FULFILLMENTTYPE")
	private Integer fulfillmentType;

	/**
	 * 条形码商品数量
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.FNSKUQUANTITY")
	private Integer fnskuQuantity;

	/**
	 * 零售单价
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.RETAILPRICE")
	private BigDecimal retailPrice;

	/**
	 * 是否统计FBA库存
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.COUNTFBAINVENTORY")
	private Integer fbaInventoryCounted;

	@ExcelProperty("SKU_MAPPING_EXCEL.ERRORMESSAGE")
	private String errMessage;

}
