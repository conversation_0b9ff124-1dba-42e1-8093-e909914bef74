package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 客户附件上传返回视图对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerAttachmentUploadVO {
    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件原始名称
     */
    private String originalFileName;

    /**
     * 文件格式
     */
    private String format;
}
