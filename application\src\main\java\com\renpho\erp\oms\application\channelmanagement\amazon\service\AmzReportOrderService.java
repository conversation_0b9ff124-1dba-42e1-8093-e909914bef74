package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItem;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzReportOrder;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderItemRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderRepository;
import com.renpho.erp.oms.infrastructure.common.enums.AmzOrderItemEstimatedPriceStatus;
import com.renpho.erp.oms.infrastructure.convert.amazon.AmzReportOrderConvert;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 亚马逊订单报表服务类
 *
 * <AUTHOR>
 * @since 2025/1/7
 */
@Slf4j
@Service
@AllArgsConstructor
public class AmzReportOrderService {
    private final AmzOrderRepository amzOrderRepository;
    private final AmzOrderItemRepository amzOrderItemRepository;

    private final AmzReportOrderConvert amzReportOrderConvert;


    @Lock4j(name = "oms:amazon:sync", keys = {"#amzReportOrder.amazonOrderId", "#amzReportOrder.sellerId", "#amzReportOrder.marketplaceId"})
    public void syncToAmzOrderItem(AmzReportOrder amzReportOrder) {
        AmzOrder amzOrderFromDb = amzOrderRepository.getByUniqueIndex(amzReportOrder.getAmazonOrderId(), amzReportOrder.getSellerId(), amzReportOrder.getMarketplaceId());
        if (amzOrderFromDb == null) {
            log.info("亚马逊渠道订单不存在, amazonOrderId={}", amzReportOrder.getAmazonOrderId());
            throw new RuntimeException("亚马逊渠道订单不存在");
        }

        Long omsAmzOrderId = amzOrderFromDb.getId();
        AmzOrderItem amzOrderItemFromDb = amzOrderItemRepository.findByOmsAmzOrderIdAndSku(omsAmzOrderId, amzReportOrder.getSku());
        if (amzOrderItemFromDb == null) {
            log.info("亚马逊渠道订单商品不存在, amazonOrderId是{}, sku是{}", amzReportOrder.getAmazonOrderId(), amzReportOrder.getSku());
            throw new RuntimeException("亚马逊渠道订单商品不存在");
        }

        AmzOrderItem amzOrderItem = amzReportOrderConvert.toAmzOrderItem(amzReportOrder);
        amzOrderItem.setId(amzOrderItemFromDb.getId());
        amzOrderItem.setOmsAmzOrderId(omsAmzOrderId);
        // 没有价格需要预估
        if (amzOrderFromDb.needEstimatedPrice() && amzOrderItem.getItemPriceAmount() == null) {
            amzOrderItem.setEstimatedPriceStatus(AmzOrderItemEstimatedPriceStatus.UN_ESTIMATE.getStatus());
        }
        this.amzOrderItemRepository.updateById(amzOrderItem);
    }
}
