package com.renpho.erp.oms.application.platform.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 监控平台的查询参数
 * @date 2025/5/8 14:13
 */
@Data
public class MonitorPlatformOrderQuery {

    /**
     * 开始mpds同步订单的时间
     */
    private LocalDateTime startLastMpdsSyncOrderTime;

    /**
     * 结束mpds同步订单的时间
     */
    private LocalDateTime endLastMpdsSyncOrderTime;
}
