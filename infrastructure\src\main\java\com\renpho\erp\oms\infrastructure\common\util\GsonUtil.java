package com.renpho.erp.oms.infrastructure.common.util;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import io.swagger.client.model.vendoRetailProcurement.orders.OrderDetails;
import org.threeten.bp.OffsetDateTime;
/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 10:54
 */
public class GsonUtil {

    private static Gson gson = new GsonBuilder()
            .registerTypeAdapter(OffsetDateTime.class, new OffsetDateTimeAdapter())
            .create();

    public static String toJson(Object obj) {
        return gson.toJson(obj);
    }

    public static void main(String[] args) {
        OrderDetails orderDetails = new OrderDetails();
        orderDetails.setPurchaseOrderDate(OffsetDateTime.parse("2025-05-05T01:48:38Z"));

        String json = gson.toJson(orderDetails);
        System.out.println(json);
    }
}
