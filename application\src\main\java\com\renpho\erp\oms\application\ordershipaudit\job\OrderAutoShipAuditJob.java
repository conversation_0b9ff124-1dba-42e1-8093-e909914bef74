package com.renpho.erp.oms.application.ordershipaudit.job;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.service.OrderAutoShipAuditService;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.RequiredArgsConstructor;

/**
 * 订单自动发货审核（分仓分物流）-定时任务
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderAutoShipAuditJob {

	private final OrderAutoShipAuditService orderAutoShipAuditService;

	/**
	 * 订单自动发货审核（分仓分物流）
	 */
	@XxlJob("orderAutoShipAudit")
	public void orderAutoShipAudit() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		OrderAutoShipAuditService.AutoShipAuditCmd cmd = JSONKit.parseObject(jobParam, OrderAutoShipAuditService.AutoShipAuditCmd.class);
		// 订单-自动发货审核（分仓分物流）
		orderAutoShipAuditService.autoShipAudit(cmd);
	}
}
