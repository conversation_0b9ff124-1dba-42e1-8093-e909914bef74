package com.renpho.erp.oms.application.channelmanagement.mercado.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclOrderPaymentLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderPaymentVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPayment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderPaymentRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MclOrderPaymentService {
	private final MclOrderPaymentRepository mclOrderPaymentRepository;
	private final MclOrderPaymentLogConvertor mclOrderPaymentLogConvertor;

	public R<List<MclOrderPaymentVO>> listByOrderId(Long orderId) {
		List<MclOrderPayment> mclOrderPaymentList = mclOrderPaymentRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(mclOrderPaymentList)) {
			return R.success(mclOrderPaymentList.stream()
				.map(mclOrderPayment -> mclOrderPaymentLogConvertor.toVO(mclOrderPayment))
				.collect(Collectors.toList()));
		}
		return R.success();
	}
}
