package com.renpho.erp.oms.application.ordershipaudit.job;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.service.WmsOutboundService;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.RequiredArgsConstructor;

/**
 * wms出库单创建-定时任务
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class WmsOutboundCreateJob {

	private final WmsOutboundService wmsOutboundCreateService;

	/**
	 * 创建wms出库单
	 */
	@XxlJob("createWmsOutbound")
	public void createWmsOutbound() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		WmsOutboundService.WmsOutboundCreateParam param = JSONKit.parseObject(jobParam, WmsOutboundService.WmsOutboundCreateParam.class);
		// 创建wms出库单
		wmsOutboundCreateService.batchCreateWmsOutbound(param);
	}

}
