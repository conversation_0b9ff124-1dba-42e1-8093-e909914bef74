package com.renpho.erp.oms.application.channelmanagement.amazon.dto;

import java.util.List;

import io.swagger.client.model.orders.*;
import lombok.Data;

/**
 * 亚马逊商品过滤PII信息
 */
@Data
public class AmzOrderItemPushDTO {

	private String ASIN;

	private String sellerSKU;

	private String orderItemId;

	/**
	 * 客户随产品购买的关联商品列表
	 */
	private List<AssociatedItem> associatedItems;

	private String title;

	/**
	 * 商品数量
	 */
	private Integer quantityOrdered;

	/**
	 * 发货数量
	 */
	private Integer quantityShipped;

	/**
	 * 商品的产品信息
	 */
	private ProductInfoDetail productInfo;

	/**
	 * 购买商品时授予的亚马逊积分的数量和价值
	 */
	private PointsGrantedDetail pointsGranted;

	/**
	 * 商品金额
	 */
	private Money itemPrice;

	/**
	 * 发货金额
	 */
	private Money shippingPrice;

	/**
	 * 商品税费
	 */
	private Money itemTax;

	/**
	 * 发货税费
	 */
	private Money shippingTax;

	/**
	 * 发货折扣金额
	 */
	private Money shippingDiscount;

	/**
	 * 发货折扣税费
	 */
	private Money shippingDiscountTax;

	/**
	 * 优惠中所有促销折扣的总额
	 */
	private Money promotionDiscount;

	/**
	 * 优惠中所有促销折扣的总额税费
	 */
	private Money promotionDiscountTax;

	/**
	 * 促销id列表
	 */
	private PromotionIdList promotionIds;

	/**
	 * cod费用
	 */
	private Money coDFee;

	/**
	 * cod折扣
	 */
	private Money coDFeeDiscount;

	/**
	 * 是否礼品
	 */
	private String isGift;

	/**
	 * 商品描述
	 */
	private String conditionNote;

	/**
	 * 商品描述id
	 */
	private String conditionId;

	/**
	 * 商品子描述id
	 */
	private String conditionSubtypeId;

	/**
	 * 预计交货开始日期
	 */
	private String scheduledDeliveryStartDate;

	/**
	 * 预计交货结束日期
	 */
	private String scheduledDeliveryEndDate;

	/**
	 * 价格标识
	 */
	private String priceDesignation;

	/**
	 * 预扣税信息
	 */
	private TaxCollection taxCollection;

	/**
	 * 是否需要序列号
	 */
	private Boolean serialNumberRequired;

	private Boolean isTransparency;

	private String iossNumber;

	private String storeChainStoreId;

	/**
	 * 经销商类别
	 */
	private OrderItem.DeemedResellerCategoryEnum deemedResellerCategory;

	/**
	 * 取消信息
	 */
	private BuyerRequestedCancel buyerRequestedCancel;

	private List<String> serialNumbers;

	/**
	 * 订单商品的替代偏好
	 */
	private SubstitutionPreferences substitutionPreferences;

	private Measurement measurement;

	/**
	 * 适用于此订单的运输限制
	 */
	private ShippingConstraints shippingConstraints;

	private AmazonPrograms amazonPrograms;
}
