package com.renpho.erp.oms.domain.vc.order.model;

import java.math.BigDecimal;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单商品行金额值对象 包含货币代码和可选的按重量定价商品的计量单位
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderItemMoney {

	/**
	 * 货币代码 ISO 4217格式的三位数货币代码
	 */
	private String currencyCode;

	/**
	 * 金额 无精度损失的十进制数，遵循RFC7159数字表示法
	 */
	private BigDecimal amount;

	/**
	 * 计量单位 按重量销售商品的价格计量单位，如果此字段不存在，则商品按个销售 POUNDS: 按磅定价 OUNCES: 按盎司定价 GRAMS: 按克定价
	 * KILOGRAMS: 按千克定价
	 */
	private String unitOfMeasure;

	/**
	 * 检查金额是否有效
	 * @return boolean
	 */
	public boolean isValid() {
		return currencyCode != null && !currencyCode.trim().isEmpty() && amount != null && amount.compareTo(BigDecimal.ZERO) >= 0;
	}

	/**
	 * 检查是否按重量定价
	 * @return boolean
	 */
	public boolean isPricedByWeight() {
		return unitOfMeasure != null && !unitOfMeasure.trim().isEmpty();
	}

	/**
	 * 检查是否按个定价
	 * @return boolean
	 */
	public boolean isPricedByEach() {
		return !isPricedByWeight();
	}

	/**
	 * 检查是否为零金额
	 * @return boolean
	 */
	public boolean isZero() {
		return amount != null && amount.compareTo(BigDecimal.ZERO) == 0;
	}

	/**
	 * 检查是否为正金额
	 * @return boolean
	 */
	public boolean isPositive() {
		return amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
	}

	/**
	 * 获取格式化的金额字符串
	 * @return String
	 */
	public String getFormattedAmount() {
		if (!isValid()) {
			return "0.00";
		}

		StringBuilder sb = new StringBuilder();
		sb.append(currencyCode).append(" ").append(amount);

		if (isPricedByWeight()) {
			sb.append(" per ").append(unitOfMeasure);
		}

		return sb.toString();
	}

}
