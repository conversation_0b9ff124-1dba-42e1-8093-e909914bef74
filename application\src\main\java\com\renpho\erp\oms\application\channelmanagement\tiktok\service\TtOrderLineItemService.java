package com.renpho.erp.oms.application.channelmanagement.tiktok.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderLineItemLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.tiktok.vo.TtOrderLineItemVO;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderLineItem;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.repository.TtOrderLineItemRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class TtOrderLineItemService {

	private final TtOrderLineItemRepository ttOrderLineItemRepository;
	private final TtOrderLineItemLogConvertor ttOrderLineItemLogConvertor;

	public R<List<TtOrderLineItemVO>> listByOrderId(Long orderId) {
		List<TtOrderLineItem> ttOrderLineItemList = ttOrderLineItemRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(ttOrderLineItemList)) {
			return R.success(ttOrderLineItemList.stream()
				.map(ttOrderLineItem -> ttOrderLineItemLogConvertor.toVO(ttOrderLineItem))
				.collect(Collectors.toList()));
		}
		return R.success();
	}
}
