package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.service;

import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingUpdateCmd;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.query.MulSkuMappingQuery;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor.MulSkuMappingAppConvertor;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor.MulSkuMappingCmdConvertor;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor.MulSkuMappingExcelConvertor;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor.MulSkuMappingImportExcelConvertor;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingExportExcel;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingImportExcel;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.vo.MulSkuMappingPriorityVO;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.vo.MulSkuMappingVO;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.model.MulSkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.query.MulSkuMappingPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.repository.MulSkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.vo.MulSkuMappingPageVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.enums.StatusEnum;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.mapper.MulSkuMappingLineMapper;
import com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.mapper.MulSkuMappingMapper;
import com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.po.MulSkuMappingLinePO;
import com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.po.MulSkuMappingPO;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.core.collection.CollectionUtil;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@AllArgsConstructor
public class MulSkuMappingService {

	private final MulSkuMappingRepository mulSkuMappingRepository;
	private final SkuMappingRepository skuMappingRepository;
	private final MulSkuMappingAppConvertor mulSkuMappingAppConvertor;
	private final MulSkuMappingCmdConvertor mulSkuMappingCmdConvertor;
	private final MulSkuMappingExcelConvertor mulSkuMappingExcelConvertor;
	private final StoreClient storeClient;
	private final FileClient fileClient;
	private final ProductClient productClient;
	private final I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;
	private final MulSkuMappingMapper mulSkuMappingMapper;
	private final MulSkuMappingLineMapper mulSkuMappingLineMapper;

	@Lock4j(name = "oms:addMulSkuMapping",
			keys = { "#mulSkuMappingAddCmd.orderStoreId", "#mulSkuMappingAddCmd.orderChannelId", "#mulSkuMappingAddCmd.orderPsku" })
	public R<Long> addMulSkuMapping(MulSkuMappingAddCmd mulSkuMappingAddCmd) {
		// 校验平台 + 店铺 + PSKU 的唯一性
		validateUniqueMulSkuMapping(mulSkuMappingAddCmd);
		// 校验映射行
		validateMulSkuMappingLines(mulSkuMappingAddCmd.getMulSkuMappingLineAddCmdList());
		// 保存映射并返回结果
		MulSkuMappingAggRoot mulSkuMappingAggRoot = mulSkuMappingCmdConvertor.toDomain(mulSkuMappingAddCmd);
		mulSkuMappingAggRoot
			.setMulSkuMappingLineList(mulSkuMappingAddCmd.getMulSkuMappingLineAddCmdList().stream().map(mulSkuMappingLineAddCmd -> {
				return mulSkuMappingCmdConvertor.toDomain(mulSkuMappingLineAddCmd);
			}).collect(Collectors.toList()));
		mulSkuMappingRepository.addMulSkuMapping(mulSkuMappingAggRoot);
		return R.success();
	}

	private void validateUniqueMulSkuMapping(MulSkuMappingAddCmd mulSkuMappingAddCmd) {
		// 查询psku是否存在
		List<PdsProductManagerBasicViewVo> pdsProductManagerBasicViewVoList = productClient
			.getPurchaseSku(Arrays.asList(mulSkuMappingAddCmd.getOrderPsku()));
		if (CollectionUtil.isEmpty(pdsProductManagerBasicViewVoList)) {
			throw new BusinessException(I18nMessageKit.getMessage("PURCHASE_SKU_NO_EXITS"));
		}
		Boolean exist = mulSkuMappingRepository.uniqueIndexExist(mulSkuMappingAddCmd.getOrderStoreId(),
				mulSkuMappingAddCmd.getOrderChannelId(), mulSkuMappingAddCmd.getOrderPsku());
		if (exist) {
			throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_EXITS"));
		}
	}

	private void validateMulSkuMappingLines(List<MulSkuMappingAddCmd.MulSkuMappingLineAddCmd> mulSkuMappingLineAddCmdList) {
		if (CollectionUtil.isEmpty(mulSkuMappingLineAddCmdList)) {
			throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_LINE_EMPTY"));
		}
		// 查询行内msku和storeId映射关系
		SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
		skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
		skuMappingQuery.setSellerSkuList(mulSkuMappingLineAddCmdList.stream()
			.map(MulSkuMappingAddCmd.MulSkuMappingLineAddCmd::getMulChannelMsku)
			.distinct()
			.collect(Collectors.toList()));
		skuMappingQuery.setStoreIdList(mulSkuMappingLineAddCmdList.stream()
			.map(MulSkuMappingAddCmd.MulSkuMappingLineAddCmd::getMulChannelStoreId)
			.distinct()
			.collect(Collectors.toList()));
		Map<String, Set<Integer>> msku2Store = skuMappingRepository.list(skuMappingQuery)
			.stream()
			.collect(Collectors.groupingBy(SkuMappingFulfillmentVO::getSellerSku,
					Collectors.mapping(SkuMappingFulfillmentVO::getStoreId, Collectors.toSet())));

		Set<String> lineUniqueness = new HashSet<>();
		int priority = 1;
		for (MulSkuMappingAddCmd.MulSkuMappingLineAddCmd line : mulSkuMappingLineAddCmdList) {
			validateMulLineUniqueness(line, lineUniqueness);
			validateLineFields(line, msku2Store);
			line.setPriority(priority++);
		}
	}

	private void validateMulLineUniqueness(MulSkuMappingAddCmd.MulSkuMappingLineAddCmd line, Set<String> lineUniqueness) {
		String key = line.getMulChannelStoreId() + "_" + line.getMulChannelMsku();
		if (!lineUniqueness.add(key)) {
			throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_LINE_REPEAT"));
		}
	}

	private void validateLineFields(MulSkuMappingAddCmd.MulSkuMappingLineAddCmd line, Map<String, Set<Integer>> msku2Store) {
		// 多渠道类型校验
		if (Objects.isNull(line.getMulChannelType())) {
			throw new BusinessException(I18nMessageKit.getMessage("MULTI_CHANNEL_TYPE_EMPTY"));
		}

		if (!FulfillmentServiceType.isMulChannel(line.getMulChannelType())) {
			throw new BusinessException(I18nMessageKit.getMessage("MULTI_CHANNEL_TYPE_NO_EXITS"));
		}

		// 多渠道MSKU校验
		if (StringUtils.isEmpty(line.getMulChannelMsku())) {
			throw new BusinessException(I18nMessageKit.getMessage("MULTI_CHANNEL_MSKU_EMPTY"));
		}
		if (!msku2Store.containsKey(line.getMulChannelMsku())) {
			throw new BusinessException(I18nMessageKit.getMessage("MULTI_CHANNEL_MSKU_NO_EXITS"));
		}

		if (!msku2Store.get(line.getMulChannelMsku()).contains(line.getMulChannelStoreId())) {
			throw new BusinessException(I18nMessageKit.getMessage("STORE_MSKU_NOT_MATCH"));
		}

	}

	public R<MulSkuMappingVO> getById(Long id) {
		MulSkuMappingAggRoot mulSkuMappingAggRoot = mulSkuMappingRepository.getById(id);
		if (Objects.nonNull(mulSkuMappingAggRoot)) {
			MulSkuMappingVO mulSkuMappingVO = mulSkuMappingAppConvertor.toVO(mulSkuMappingAggRoot);
			// 使用 Set 来避免重复，并将两个 ID 列表合并
			Set<Integer> uniqueStoreIds = mulSkuMappingAggRoot.getMulSkuMappingLineList()
				.stream()
				.map(MulSkuMappingAggRoot.MulSkuMappingLine::getMulChannelStoreId)
				.distinct() // 进一步去重
				.collect(Collectors.toSet());
			uniqueStoreIds.add(mulSkuMappingVO.getOrderStoreId());
			// 查询店铺信息
			Map<Integer, StoreVo> storeVOMap = storeClient.getByStoreIds(new ArrayList<>(uniqueStoreIds))
				.stream()
				.collect(Collectors.toMap(StoreVo::getId, Function.identity()));
			mulSkuMappingVO.setMulSkuMappingLineVOList(mulSkuMappingAggRoot.getMulSkuMappingLineList().stream().map(mulSkuMappingLine -> {
				MulSkuMappingVO.MulSkuMappingLineVO mulSkuMappingLineVO = mulSkuMappingAppConvertor.toLineVO(mulSkuMappingLine);
				if (storeVOMap.containsKey(mulSkuMappingLineVO.getMulChannelStoreId())) {
					mulSkuMappingLineVO.setMulChannelStore(storeVOMap.get(mulSkuMappingLineVO.getMulChannelStoreId()).getStoreName());
				}
				return mulSkuMappingLineVO;
			}).collect(Collectors.toList()));
			if (storeVOMap.containsKey(mulSkuMappingVO.getOrderStoreId())) {
				mulSkuMappingVO.setOrderStore(storeVOMap.get(mulSkuMappingVO.getOrderStoreId()).getStoreName());
				mulSkuMappingVO.setOrderChannel(storeVOMap.get(mulSkuMappingVO.getOrderStoreId()).getSalesChannel());
			}
			return R.success(mulSkuMappingVO);
		}
		return R.fail(new BusinessException(I18nMessageKit.getMessage("DATA_NOT_EXITS")));
	}

	public R updateMulSkuMapping(MulSkuMappingUpdateCmd mulSkuMappingUpdateCmd) {
		MulSkuMappingAggRoot mulSkuMappingAggRoot = mulSkuMappingRepository.getById(mulSkuMappingUpdateCmd.getId());
		if (Objects.isNull(mulSkuMappingAggRoot)) {
			throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_NO_EXITS"));
		}
		// 校验映射行
		validateMulSkuMappingLines(mulSkuMappingUpdateCmd.getMulSkuMappingLineUpdateCmdList());
		// 转换赋值
		MulSkuMappingAggRoot updateRoot = mulSkuMappingCmdConvertor.toDomain(mulSkuMappingUpdateCmd);
		updateRoot.setOrderChannelId(mulSkuMappingAggRoot.getOrderChannelId());
		updateRoot.setOrderStoreId(mulSkuMappingAggRoot.getOrderStoreId());
		updateRoot.setOrderPsku(mulSkuMappingAggRoot.getOrderPsku());
		updateRoot
			.setMulSkuMappingLineList(mulSkuMappingUpdateCmd.getMulSkuMappingLineUpdateCmdList().stream().map(mulSkuMappingLineAddCmd -> {
				return mulSkuMappingCmdConvertor.toDomain(mulSkuMappingLineAddCmd);
			}).collect(Collectors.toList()));
		mulSkuMappingRepository.updateMulSkuMapping(mulSkuMappingAggRoot, updateRoot);
		return R.success();

	}

	public R<Paging<MulSkuMappingPageVO>> page(MulSkuMappingPageQuery mulSkuMappingPageQuery) {
		IPage<MulSkuMappingPageVO> page = mulSkuMappingRepository.page(mulSkuMappingPageQuery);
		if (CollectionUtil.isEmpty(page.getRecords())) {
			return R.success(Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));
		}

		return R.success(Paging.of(page.getRecords(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));

	}

	public void exportExcel(MulSkuMappingPageQuery mulSkuMappingPageQuery, HttpServletResponse response) {
		try {
			String fileName ="多渠道SKU映射.xlsx";
			HttpUtil.setExportResponseHeader(response,fileName);

			try (ServletOutputStream out = response.getOutputStream();
					ExcelWriter excelWriter = EasyExcel.write(out, MulSkuMappingExportExcel.class)
						.registerWriteHandler(i18nHeaderCellWriteHandler)
						.build()) {

				WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
				mulSkuMappingPageQuery.setPageSize(1000);
				int pageIndex = 1;
				long total = -1; // 记录第一页total

				while (true) {
					mulSkuMappingPageQuery.setPageIndex(pageIndex);
					// 仅第一页count，后续分页跳过count
					if (pageIndex == 1) {
						// 默认count
						mulSkuMappingPageQuery.setSkipCount(false);
					} else {
						mulSkuMappingPageQuery.setSkipCount(true);
						mulSkuMappingPageQuery.setTotal(total); // 复用第一页total
					}
					Paging<MulSkuMappingPageVO> page = this.page(mulSkuMappingPageQuery).getData();

					if (pageIndex == 1) {
						total = page.getTotalCount();
					}

					List<MulSkuMappingExportExcel> excelData = page.getRecords().stream().map(mulSkuMappingPageVO -> {
						MulSkuMappingExportExcel mulSkuMappingExportExcel = mulSkuMappingExcelConvertor.toExcel(mulSkuMappingPageVO);
						Optional.ofNullable(mulSkuMappingExportExcel.getUpdateTime())
							.map(LocalDateTimeTransKits::tansByUserZoneId)
							.ifPresent(mulSkuMappingExportExcel::setUpdateTime);
						return mulSkuMappingExportExcel;
					}).collect(Collectors.toList());

					if (excelData == null || excelData.isEmpty()) {
						break;
					}

					excelWriter.write(excelData, writeSheet);
					pageIndex++;
				}

				excelWriter.finish();
			}
		}
		catch (IOException e) {
			log.error("导出Excel失败", e);
			throw new BusinessException(I18nMessageKit.getMessage("FILE_EXPORT_EXCEPTION"));
		}
	}

	@Transactional
	public R importExcel(List<MulSkuMappingImportExcel> mulSkuMappingImportExcels) {
		// 使用 Set 来避免重复，并将两个 ID 列表合并
		Set<String> uniqueStore = mulSkuMappingImportExcels.stream()
			.flatMap(mulSkuMappingImportExcel -> Stream.of(mulSkuMappingImportExcel.getOrderStore(),
					mulSkuMappingImportExcel.getMulChannelStore()))
			.filter(Objects::nonNull) // 过滤掉 null 值
			.distinct() // 进一步去重
			.collect(Collectors.toSet());
		// 调用远程查询
		List<StoreVo> storeVOList = storeClient.getByStoreNames(new ArrayList<>(uniqueStore));
		Map<String, StoreVo> storeMap = storeVOList.stream().collect(Collectors.toMap(StoreVo::getStoreName, Function.identity()));
		Set<String> pskuSet = productClient
			.getPurchaseSku(mulSkuMappingImportExcels.stream().map(MulSkuMappingImportExcel::getOrderPsku).distinct().toList())
			.stream()
			.map(PdsProductManagerBasicViewVo::getPurchaseSku)
			.collect(Collectors.toSet());
		List<MulSkuMappingImportExcel> erroList = Lists.newArrayList();
		List<MulSkuMappingImportExcel> filterList = Lists.newArrayList();
		// 最外层校验
		mulSkuMappingImportExcels.stream().forEach(mulSkuMappingImportExcel -> {
			StringBuilder errorMsgBuilder = new StringBuilder();
			if (StringUtils.isEmpty(mulSkuMappingImportExcel.getOrderStore())) {
				errorMsgBuilder.append(I18nMessageKit.getMessage("STORE_EMPTY") + "\n");

			}
			if (!storeMap.containsKey(mulSkuMappingImportExcel.getOrderStore())) {
				// 店铺不存在
				errorMsgBuilder.append(I18nMessageKit.getMessage("STORE_NO_EXITS") + "\n");
			}

			if (StringUtils.isEmpty(mulSkuMappingImportExcel.getOrderChannel())) {
				// 店铺不存在
				errorMsgBuilder.append(I18nMessageKit.getMessage("CHANNLE_EMPTY") + "\n");
			}

			if (storeMap.containsKey(mulSkuMappingImportExcel.getOrderStore()) && !storeMap.get(mulSkuMappingImportExcel.getOrderStore())
				.getSalesChannel()
				.equals(mulSkuMappingImportExcel.getOrderChannel())) {
				// 店铺与渠道不匹配
				errorMsgBuilder.append(I18nMessageKit.getMessage("ORDER_STORE_CHANNEL_NOT_MATCH") + "\n");
			}

			if (StringUtils.isEmpty(mulSkuMappingImportExcel.getOrderPsku())) {
				errorMsgBuilder.append(I18nMessageKit.getMessage("MUL_SKU_ORDER_PSKU_EMPTY") + "\n");

			}

			if (StringUtils.isNotEmpty(errorMsgBuilder.toString())) {
				mulSkuMappingImportExcel.setErrMessage(errorMsgBuilder.toString());
				erroList.add(mulSkuMappingImportExcel);
			}
			else {
				filterList.add(mulSkuMappingImportExcel);
			}
		});

		// 先按照唯一值分组
		List<List<MulSkuMappingImportExcel>> excelGroup = filterList.stream()
			.collect(Collectors.groupingBy(mulSkuMappingImportExcel -> String.format("%s_%s_%s", mulSkuMappingImportExcel.getOrderStore(),
					mulSkuMappingImportExcel.getOrderChannel(), mulSkuMappingImportExcel.getOrderPsku())))
			.values()
			.stream()
			.toList();
		// 更新组
		List<List<MulSkuMappingImportExcel>> updateGroup = Lists.newArrayList();
		// 插入组
		List<List<MulSkuMappingImportExcel>> insertGroup = Lists.newArrayList();
		// 按组操作
		excelGroup.stream().forEach(excelList -> {
			StoreVo storeVo = storeMap.get(excelList.get(0).getOrderStore());
			// 主数据取第一个判断
			MulSkuMappingImportExcel mulSkuMappingImportExcel = excelList.get(0);
			Boolean exist = mulSkuMappingRepository.uniqueIndexExist(storeVo.getId(), storeVo.getSalesChannelId(),
					mulSkuMappingImportExcel.getOrderPsku());

			// 校验
			if (exist) {
				// 更新
				// 校验映射行
				validateSkuMappingLines(excelList, storeMap, pskuSet);
				updateGroup.add(excelList);
			}
			else {
				// 插入
				// 校验映射行
				validateSkuMappingLines(excelList, storeMap, pskuSet);
				insertGroup.add(excelList);
			}
		});

		// 存在错误返回错误信息
		if (CollectionUtil.isNotEmpty(erroList)
				|| insertGroup.stream().flatMap(Collection::stream).anyMatch(s -> StringUtils.isNotEmpty(s.getErrMessage()))
				|| updateGroup.stream().flatMap(Collection::stream).anyMatch(s -> StringUtils.isNotEmpty(s.getErrMessage()))) {
			erroList.addAll(insertGroup.stream().flatMap(Collection::stream).collect(Collectors.toList()));
			erroList.addAll(updateGroup.stream().flatMap(Collection::stream).collect(Collectors.toList()));
			return fileClient.getErrorExcelUrl(erroList, MulSkuMappingImportExcel.class);
		}
		// 新增
		insertGroup.forEach(skuMappingImportExcelList ->

		{
			MulSkuMappingAggRoot mulSkuMappingAggRoot = MulSkuMappingImportExcelConvertor.convertByImportExcleList(storeMap,
					skuMappingImportExcelList);
			mulSkuMappingRepository.addMulSkuMapping(mulSkuMappingAggRoot);
		});
		// 更新
		updateGroup.forEach(skuMappingImportExcelList ->

		{
			StoreVo storeVo = storeMap.get(skuMappingImportExcelList.get(0).getOrderStore());
			MulSkuMappingAggRoot oldSkuMapping = mulSkuMappingRepository.getByUniqueIndex(storeVo.getId(), storeVo.getSalesChannelId(),
					skuMappingImportExcelList.get(0).getOrderPsku());
			MulSkuMappingAggRoot mulSkuMappingAggRoot = MulSkuMappingImportExcelConvertor.convertByImportExcleList(storeMap,
					skuMappingImportExcelList);
			mulSkuMappingRepository.updateMulSkuMapping(oldSkuMapping, mulSkuMappingAggRoot);
		});

		return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
	}

	private void validateSkuMappingLines(List<MulSkuMappingImportExcel> mulSkuMappingImportExcelList, Map<String, StoreVo> storeMap,
			Set<String> pskuSet) {

		Set<String> lineUniqueness = new HashSet<>();

		for (MulSkuMappingImportExcel mulSkuMappingImportExcel : mulSkuMappingImportExcelList) {
			StringBuilder stringBuilder = new StringBuilder();
			// 行内唯一值校验
			validateLineUniqueness(stringBuilder, mulSkuMappingImportExcel, lineUniqueness);
			// PSKU校验
			addErrorAndReturnTrue(stringBuilder, I18nMessageKit.getMessage("PURCHASE_SKU_NO_EXITS"),
					!pskuSet.contains(mulSkuMappingImportExcel.getOrderPsku()));
			// 查询行内msku和storeId映射关系
			SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
			skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
			skuMappingQuery.setSellerSkuList(mulSkuMappingImportExcelList.stream()
				.map(MulSkuMappingImportExcel::getMulChannelMsku)
				.distinct()
				.collect(Collectors.toList()));
			skuMappingQuery.setStoreIdList(mulSkuMappingImportExcelList.stream()
				.filter(mulSkuMappingImportExcel1 -> StringUtils.isNotEmpty(mulSkuMappingImportExcel1.getMulChannelStore())
						&& storeMap.containsKey(mulSkuMappingImportExcel1.getMulChannelStore()))
				.map(mulSkuMappingImportExcel1 -> {
					return storeMap.get(mulSkuMappingImportExcel1.getMulChannelStore()).getId();
				})
				.distinct()
				.collect(Collectors.toList()));
			Map<String, Set<Integer>> msku2Store = skuMappingRepository.list(skuMappingQuery)
				.stream()
				.collect(Collectors.groupingBy(SkuMappingFulfillmentVO::getSellerSku,
						Collectors.mapping(SkuMappingFulfillmentVO::getStoreId, Collectors.toSet())));

			// 字段合法性校验
			validateLineFields(stringBuilder, mulSkuMappingImportExcel, storeMap, msku2Store);
			if (stringBuilder.length() > 0) {
				if (StringUtils.isNotEmpty(mulSkuMappingImportExcel.getErrMessage())) {
					mulSkuMappingImportExcel.setErrMessage(mulSkuMappingImportExcel.getErrMessage() + "\n" + stringBuilder);
				}
				else {
					mulSkuMappingImportExcel.setErrMessage(stringBuilder.toString());
				}
			}
		}
	}

	private void validateLineUniqueness(StringBuilder erroMsg, MulSkuMappingImportExcel mulSkuMappingImportExcel,
			Set<String> lineUniqueness) {
		String key = mulSkuMappingImportExcel.getOrderStore() + "_" + mulSkuMappingImportExcel.getMulChannelMsku();
		addErrorAndReturnTrue(erroMsg, I18nMessageKit.getMessage("SKUMAPPING_LINE_REPEAT"), !lineUniqueness.add(key));
	}

	private void validateLineFields(StringBuilder msgBulider, MulSkuMappingImportExcel mulSkuMappingImportExcel,
			Map<String, StoreVo> storeMap, Map<String, Set<Integer>> msku2Store) {
		// 多渠道类型校验
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_TYPE_EMPTY"),
				Objects.isNull(mulSkuMappingImportExcel.getMulChannelType()));
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_TYPE_NO_EXITS"),
				!FulfillmentServiceType.isMulChannel(mulSkuMappingImportExcel.getMulChannelType()));

		// 多渠道MSKU校验
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_MSKU_EMPTY"),
				StringUtils.isEmpty(mulSkuMappingImportExcel.getMulChannelMsku()));
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_MSKU_NO_EXITS"),
				!msku2Store.containsKey(mulSkuMappingImportExcel.getMulChannelMsku()));
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_STORE_EMPTY"),
				StringUtils.isEmpty(mulSkuMappingImportExcel.getMulChannelStore()));
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_STORE_NO_EXITS"),
				!storeMap.containsKey(mulSkuMappingImportExcel.getMulChannelStore()));
		if (FulfillmentServiceType.enumOf(mulSkuMappingImportExcel.getMulChannelType()) == FulfillmentServiceType.FBA_MULTI_CHANNEL) {
			// FBA只能是亚马逊店铺
			addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_STORE_NOT_MATCH"),
					!StringUtils.isEmpty(mulSkuMappingImportExcel.getMulChannelMsku())
							&& storeMap.containsKey(mulSkuMappingImportExcel.getMulChannelStore()) && !ChannelCode.AMZ_CHANNEL_CODE
								.equals(storeMap.get(mulSkuMappingImportExcel.getMulChannelStore()).getSalesChannelCode()));
		}
		else if (FulfillmentServiceType.enumOf(mulSkuMappingImportExcel.getMulChannelType()) == FulfillmentServiceType.WFS_MULTI_CHANNEL) {
			// WFS只能是沃尔玛店铺
			addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("MULTI_CHANNEL_STORE_NOT_MATCH"),
					!StringUtils.isEmpty(mulSkuMappingImportExcel.getMulChannelMsku())
							&& storeMap.containsKey(mulSkuMappingImportExcel.getMulChannelStore()) && !ChannelCode.WMT_CHANNEL_CODE
								.equals(storeMap.get(mulSkuMappingImportExcel.getMulChannelStore()).getSalesChannelCode()));
		}
		addErrorAndReturnTrue(msgBulider, I18nMessageKit.getMessage("STORE_MSKU_NOT_MATCH"),
				msku2Store.containsKey(mulSkuMappingImportExcel.getMulChannelMsku())
						&& storeMap.containsKey(mulSkuMappingImportExcel.getMulChannelStore())
						&& (!msku2Store.get(mulSkuMappingImportExcel.getMulChannelMsku())
							.contains(storeMap.get(mulSkuMappingImportExcel.getMulChannelStore()).getId())));
	}

	// 辅助方法：添加错误信息并返回true
	private boolean addErrorAndReturnTrue(StringBuilder errorsMsg, String message, boolean condition) {
		if (condition) {
			errorsMsg.append(message).append("\n");
		}
		return condition;
	}

	public R changeStatus(Long id, StatusEnum newStatus, StatusEnum oldStatus) {
		if (mulSkuMappingRepository.changeStatus(id, newStatus, oldStatus)) {
			return R.success();
		}
		else {
			return R.fail(I18nMessageKit.getMessage("UPDATE_FAIL"));
		}
	}

	public R<List<MulSkuMappingPriorityVO>> macthList(MulSkuMappingQuery mulSkuMappingQuery) {
		// 查询多渠道映射主表
		List<MulSkuMappingPO> mulSkuMappingPOList = mulSkuMappingMapper.queryByOrderPskuList(mulSkuMappingQuery.getOrderPsku());
		if (CollectionUtil.isEmpty(mulSkuMappingPOList)) {
			return R.success();
		}
		// 查询多渠道映射行
		List<MulSkuMappingLinePO> mulSkuMappingLinePOList = mulSkuMappingLineMapper.queryMaxPriority(
				mulSkuMappingPOList.stream().map(MulSkuMappingPO::getId).collect(Collectors.toList()),
				mulSkuMappingQuery.getMulChannelType(), mulSkuMappingQuery.getMulChannelStoreId());
		// 根据多渠道msku,店铺查询SKU映射
		SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
		skuMappingQuery
			.setSellerSkuList(mulSkuMappingLinePOList.stream().map(MulSkuMappingLinePO::getMulChannelMsku).collect(Collectors.toList()));
		skuMappingQuery
			.setStoreIdList(mulSkuMappingLinePOList.stream().map(MulSkuMappingLinePO::getMulChannelStoreId).collect(Collectors.toList()));
		List<SkuMappingFulfillmentVO> skuMappingFulfillmentVOList = skuMappingRepository.list(skuMappingQuery);
		// 组装数据
		Map<Long, String> mulskuId2orderPsku = mulSkuMappingPOList.stream()
			.collect(Collectors.toMap(MulSkuMappingPO::getId, MulSkuMappingPO::getOrderPsku));

		Map<String, SkuMappingFulfillmentVO> key2SkuMapping = skuMappingFulfillmentVOList.stream()
			.collect(Collectors.toMap(vo -> vo.getStoreId() + "_" + vo.getSellerSku(), Function.identity(),
					(existing, replacement) -> existing // 如果键冲突，保留现有值
			));
		return R.success(mulSkuMappingLinePOList.stream().map(mulSkuMappingLinePO -> {
			MulSkuMappingPriorityVO mulSkuMappingPriorityVO = new MulSkuMappingPriorityVO();
			mulSkuMappingPriorityVO.setMulChannelMsku(mulSkuMappingLinePO.getMulChannelMsku());
			mulSkuMappingPriorityVO.setOrderPsku(mulskuId2orderPsku.get(mulSkuMappingLinePO.getMulChannelSkuMappingId()));
			SkuMappingFulfillmentVO skuMappingFulfillmentVO = key2SkuMapping
				.get(mulSkuMappingLinePO.getMulChannelStoreId() + "_" + mulSkuMappingLinePO.getMulChannelMsku());
			if (Objects.nonNull(skuMappingFulfillmentVO)) {
				mulSkuMappingPriorityVO.setPsku(skuMappingFulfillmentVO.getPurchaseSku());
				mulSkuMappingPriorityVO.setFnsku(skuMappingFulfillmentVO.getFnSku());
			}
			return mulSkuMappingPriorityVO;
		}).collect(Collectors.toList()));
	}
}
