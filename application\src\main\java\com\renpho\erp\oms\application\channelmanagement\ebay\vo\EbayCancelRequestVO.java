package com.renpho.erp.oms.application.channelmanagement.ebay.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: Ebay 取消状态出参
 * @time: 2025-04-14 15:27:24
 * @author: <PERSON><PERSON>
 */
@Data
public class EbayCancelRequestVO {
	/**
	 * ID
	 */
	private String cancelRequestId;
	/**
	 * 发起时间
	 */
	private LocalDateTime cancelRequestedDate;
	/**
	 * 发起方
	 */
	private String cancelInitiator;
	/**
	 * 取消原因
	 */
	private String cancelReason;
	/**
	 * 状态
	 */
	private String cancelRequestState;
	/**
	 * 完成时间
	 */
	private LocalDateTime cancelCompletedDate;
}
