package com.renpho.erp.oms.domain.vc.order.entity;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单商品箱唛实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderItemLabel {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * vc订单行id
	 */
	private Long orderItemId;

	/**
	 * 物流标-文件url
	 */
	private String asinLabelUrl;

	/**
	 * 物流标-文件名
	 */
	private String asinLabelName;

	/**
	 * 外箱标-文件url
	 */
	private String cartonLabelUrl;

	/**
	 * 外箱标-文件名
	 */
	private String cartonLabelName;

	/**
	 * 生成id
	 * @param orderId 订单id
	 */
	public void generateId(Long orderId) {
		this.id = IdWorker.getId();
		this.orderId = orderId;
	}

	/**
	 * 设置订单商品ID
	 * @param orderItemId 订单商品ID
	 */
	public void setOrderItemId(Long orderItemId) {
		this.orderItemId = orderItemId;
	}

}
