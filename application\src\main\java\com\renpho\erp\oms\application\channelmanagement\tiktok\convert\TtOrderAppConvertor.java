package com.renpho.erp.oms.application.channelmanagement.tiktok.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrder;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.DateUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class TtOrderAppConvertor {
	public static TtOrder toDomain(TtOrderDTO ttOrderDTO) {
		if (Objects.nonNull(ttOrderDTO)) {
			TtOrder ttOrder = new TtOrder();
			ttOrder.setStoreId(ttOrderDTO.getStoreId());
			ttOrder.setShopId(ttOrderDTO.getShopId());
			ttOrder.setTiktokOrderId(ttOrderDTO.getId());
			ttOrder.setBuyerMessage(ttOrderDTO.getBuyer_message());
			ttOrder.setCancellationInitiator(ttOrderDTO.getCancellation_initiator());
			ttOrder.setShippingProviderId(ttOrderDTO.getShipping_provider_id());
			ttOrder.setTiktokOrderCreateTime(DateUtil.convertToDateTime(ttOrderDTO.getCreate_time()));
			ttOrder.setShippingProvider(ttOrderDTO.getShipping_provider());
			if (CollectionUtil.isNotEmpty(ttOrderDTO.getNext_page_token())) {
				ttOrder.setPackages(JsonUtils.toJson(ttOrderDTO.getNext_page_token()));
			}
			// 支付信息
			if (Objects.nonNull(ttOrderDTO.getPayment())) {
				TtOrderDTO.Payment payment = ttOrderDTO.getPayment();
				buildPaymentInfo(ttOrder, payment);
			}
			// 地址信息
			if (Objects.nonNull(ttOrderDTO.getRecipient_address())) {
				TtOrderDTO.Address recipientAddress = ttOrderDTO.getRecipient_address();
				buildAddressInfo(ttOrder, recipientAddress);
			}

			ttOrder.setFulfillmentType(ttOrderDTO.getFulfillment_type());
			ttOrder.setStatus(ttOrderDTO.getStatus());
			ttOrder.setDeliveryType(ttOrderDTO.getDelivery_type());
			ttOrder.setPaidTime(DateUtil.convertToDateTime(ttOrderDTO.getPaid_time()));
			ttOrder.setRtsSlaTime(DateUtil.convertToDateTime(ttOrderDTO.getRts_sla_time()));
			ttOrder.setTtsSlaTime(DateUtil.convertToDateTime(ttOrderDTO.getTts_sla_time()));
			ttOrder.setCancelReason(ttOrderDTO.getCancel_reason());
			ttOrder.setTiktokOrderUpdateTime(DateUtil.convertToDateTime(ttOrderDTO.getUpdate_time()));
			ttOrder.setPaymentMethodName(ttOrderDTO.getPayment_method_name());
			ttOrder.setRtsTime(DateUtil.convertToDateTime(ttOrderDTO.getRts_time()));
			ttOrder.setTrackingNumber(ttOrderDTO.getTracking_number());
			ttOrder.setSplitOrCombineTag(ttOrderDTO.getSplit_or_combine_tag());
			ttOrder.setHasUpdatedRecipientAddress(ttOrderDTO.getHas_updated_recipient_address());
			ttOrder.setWarehouseId(ttOrderDTO.getWarehouse_id());
			ttOrder.setRequestCancelTime(DateUtil.convertToDateTime(ttOrderDTO.getCancel_time()));
			ttOrder.setShippingType(ttOrderDTO.getShipping_type());
			ttOrder.setUserId(ttOrderDTO.getUser_id());
			ttOrder.setSellerNote(ttOrderDTO.getSeller_note());
			ttOrder.setDeliverySlaTime(DateUtil.convertToDateTime(ttOrderDTO.getDelivery_sla_time()));
			ttOrder.setIsCod(ttOrderDTO.getIs_cod());
			ttOrder.setDeliveryOptionId(ttOrderDTO.getDelivery_option_id());
			ttOrder.setCancelTime(DateUtil.convertToDateTime(ttOrderDTO.getCancel_time()));
			ttOrder.setNeedUploadInvoice(ttOrderDTO.getNeed_upload_invoice());
			ttOrder.setDeliveryOptionName(ttOrderDTO.getDelivery_option_name());
			ttOrder.setCpf(ttOrderDTO.getCpf());
			ttOrder.setBuyerEmail(ttOrderDTO.getBuyer_email());
			ttOrder.setDeliveryDueTime(DateUtil.convertToDateTime(ttOrderDTO.getDelivery_due_time()));
			ttOrder.setIsSampleOrder(ttOrderDTO.getIs_sample_order());
			ttOrder.setShippingDueTime(DateUtil.convertToDateTime(ttOrderDTO.getShipping_due_time()));
			ttOrder.setCollectionDueTime(DateUtil.convertToDateTime(ttOrderDTO.getCollection_due_time()));
			ttOrder.setIsOnHoldOrder(ttOrderDTO.getIs_on_hold_order());
			ttOrder.setDeliveryTime(DateUtil.convertToDateTime(ttOrderDTO.getDelivery_time()));
			ttOrder.setIsReplacementOrder(ttOrderDTO.getIs_replacement_order());
			ttOrder.setCollectionTime(DateUtil.convertToDateTime(ttOrderDTO.getCollection_time()));
			ttOrder.setReplacedOrderId(ttOrderDTO.getReplaced_order_id());
			ttOrder.setIsBuyerRequestCancel(ttOrderDTO.getIs_buyer_request_cancel());
			ttOrder.setPickUpCutOffTime(DateUtil.convertToDateTime(ttOrderDTO.getPick_up_cut_off_time()));
			ttOrder.setFastDispatchSlaTime(DateUtil.convertToDateTime(ttOrderDTO.getFast_dispatch_sla_time()));
			ttOrder.setCommercePlatform(ttOrderDTO.getCommerce_platform());
			ttOrder.setOrderType(ttOrderDTO.getOrder_type());
			ttOrder.setReleaseDate(DateUtil.convertToDateTime(ttOrderDTO.getRelease_date()));
			if (Objects.nonNull(ttOrderDTO.getHandling_duration())) {
				ttOrder.setHandlingDuration(JsonUtils.toJson(ttOrderDTO.getHandling_duration()));
			}
			ttOrder.setCancelOrderSlaTime(DateUtil.convertToDateTime(ttOrderDTO.getCancel_order_sla_time()));
			ttOrder.setLastMpdsSyncOrderTime(
					StrUtil.isNotEmpty(ttOrderDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(ttOrderDTO.getSyncTime()) : null);
			return ttOrder;
		}
		return null;
	}

	private static void buildAddressInfo(TtOrder ttOrder, TtOrderDTO.Address recipientAddress) {
		ttOrder.setPostalCode(recipientAddress.getPostal_code());
		ttOrder.setRegionCode(recipientAddress.getRegion_code());
		if (Objects.nonNull(recipientAddress.getDistrict_info())) {
			ttOrder.setDistrictInfo(JsonUtils.toJson(recipientAddress.getDistrict_info()));
		}
	}

	private static void buildPaymentInfo(TtOrder ttOrder, TtOrderDTO.Payment payment) {
		ttOrder.setPaymentCurrency(payment.getCurrency());
		if (StringUtils.isNotEmpty(payment.getSub_total())) {
			ttOrder.setPaymentSubTotal(new BigDecimal(payment.getSub_total()));
		}
		if (StringUtils.isNotEmpty(payment.getShipping_fee())) {
			ttOrder.setPaymentShippingFee(new BigDecimal(payment.getShipping_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getSeller_discount())) {
			ttOrder.setPaymentSellerDiscount(new BigDecimal(payment.getSeller_discount()));
		}

		if (StringUtils.isNotEmpty(payment.getPlatform_discount())) {
			ttOrder.setPaymentPlatformDiscount(new BigDecimal(payment.getPlatform_discount()));
		}

		if (StringUtils.isNotEmpty(payment.getOriginal_total_product_price())) {
			ttOrder.setPaymentOriginalTotalProductPrice(new BigDecimal(payment.getOriginal_total_product_price()));
		}

		if (StringUtils.isNotEmpty(payment.getOriginal_shipping_fee())) {
			ttOrder.setPaymentOriginalShippingFee(new BigDecimal(payment.getOriginal_shipping_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getShipping_fee_seller_discount())) {
			ttOrder.setPaymentShippingFeeSellerDiscount(new BigDecimal(payment.getShipping_fee_seller_discount()));
		}

		if (StringUtils.isNotEmpty(payment.getShipping_fee_platform_discount())) {
			ttOrder.setPaymentShippingFeePlatformDiscount(new BigDecimal(payment.getShipping_fee_platform_discount()));
		}

		if (StringUtils.isNotEmpty(payment.getShipping_fee_platform_discount())) {
			ttOrder.setPaymentShippingFeePlatformDiscount(new BigDecimal(payment.getShipping_fee_platform_discount()));
		}

		if (StringUtils.isNotEmpty(payment.getTax())) {
			ttOrder.setPaymentTax(new BigDecimal(payment.getTax()));
		}

		if (StringUtils.isNotEmpty(payment.getSmall_order_fee())) {
			ttOrder.setPaymentSmallOrderFee(new BigDecimal(payment.getSmall_order_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getShipping_fee_tax())) {
			ttOrder.setPaymentShippingFeeTax(new BigDecimal(payment.getShipping_fee_tax()));
		}

		if (StringUtils.isNotEmpty(payment.getProduct_tax())) {
			ttOrder.setPaymentProductTax(new BigDecimal(payment.getProduct_tax()));
		}

		if (StringUtils.isNotEmpty(payment.getRetail_delivery_fee())) {
			ttOrder.setPaymentRetailDeliveryFee(new BigDecimal(payment.getRetail_delivery_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getBuyer_service_fee())) {
			ttOrder.setPaymentBuyerServiceFee(new BigDecimal(payment.getBuyer_service_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getHandling_fee())) {
			ttOrder.setPaymentHandlingFee(new BigDecimal(payment.getHandling_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getShipping_insurance_fee())) {
			ttOrder.setPaymentShippingInsuranceFee(new BigDecimal(payment.getShipping_insurance_fee()));
		}

		if (StringUtils.isNotEmpty(payment.getItem_insurance_fee())) {
			ttOrder.setPaymentItemInsuranceFee(new BigDecimal(payment.getItem_insurance_fee()));
		}

	}
}
