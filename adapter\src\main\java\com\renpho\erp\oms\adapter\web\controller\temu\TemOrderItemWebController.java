package com.renpho.erp.oms.adapter.web.controller.temu;


import com.renpho.erp.oms.application.channelmanagement.temu.service.TemOrderPlatformService;
import com.renpho.erp.oms.application.channelmanagement.temu.vo.TemOrderItemVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/temu/**")
@RequestMapping("/temu/item")
@AllArgsConstructor
public class TemOrderItemWebController {

    private final TemOrderPlatformService temOrderPlatformService;

    /**
     * Temu订单商品列表
     * @param orderId
     * @return R<List<temOrderItemService>>
     */
    @GetMapping("/list")
    public R<List<TemOrderItemVO>> listByOrderId(@RequestParam Long orderId) {
        return temOrderPlatformService.listItemByOrderId(orderId);
    }
}
