package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import com.renpho.erp.apiproxy.temu.model.ShopAccount;
import com.renpho.erp.apiproxy.temu.model.TemuResponse;
import com.renpho.erp.apiproxy.temu.model.logistics.GetShippingServicesData;
import com.renpho.erp.apiproxy.temu.model.logistics.GetShippingServicesRequest;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import com.renpho.erp.oms.infrastructure.feign.proxy.TemuClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceWarehousePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceWarehouseService;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @desc: Temu 查询发货方式报价
 * @time: 2025-04-22 18:17:33
 * @author: Alina
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TemuShippingServiceStrategy extends AbstractShippingServiceStrategy {

	private final FulfillmentServiceWarehouseService fulfillmentWarehouseService;
	private final TemuClient temuClient;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.TEMU_ONLINE_LABEL);
	}

	@Override
	public List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query) {
		GetShippingServicesRequest request = buildGetShippingServicesRequest(query);
		ShopAccount shopAccount = getShopAccount(query.getStoreId());
		// 调用TEMU查询发货方式报价表API
		TemuResponse<GetShippingServicesData> response = temuClient.getShippingServices(shopAccount, request);
		List<GetShippingServicesData.OnlineChannel> onlineChannels = Optional.ofNullable(response)
			.map(TemuResponse::getResult)
			.map(GetShippingServicesData::getOnlineChannelDtoList)
			.orElse(Collections.emptyList())
			.stream()
			.filter(channel -> {
				String company = Optional.ofNullable(channel.getShippingCompanyName()).orElse("").toLowerCase();
				return "ups".equals(company) || "usps".equals(company);
			})
			.toList();

		return onlineChannels.stream().map(shipAuditConvertor::toShippingServicesVO).collect(Collectors.toList());
	}

	/**
	 * 构建TEMU查询发货方式报价表入参
	 * @param query 查询发货方式报价表入参
	 * @return GetShippingServicesRequest
	 */
	private GetShippingServicesRequest buildGetShippingServicesRequest(ShippingServicesQuery query) {
		GetShippingServicesRequest request = new GetShippingServicesRequest();
		// warehouseID 映射
		FulfillmentServiceWarehousePO fulfillmentWarehouse = fulfillmentWarehouseService.getByWarehouse(query.getWarehouseId(),
				FulfillmentServiceType.TEMU_ONLINE_LABEL.getValue());
		request.setLength(SizeUtil.formatToInUnit(query.getLength()));
		request.setWidth(SizeUtil.formatToInUnit(query.getWidth()));
		request.setHeight(SizeUtil.formatToInUnit(query.getHeight()));
		request.setWeight(SizeUtil.formatToLbUnit(query.getWeight()));
		request.setDimensionUnit(Constants.TEMU_DIMENSION_UNIT);
		request.setWeightUnit(Constants.TEMU_WEIGHT_UNIT);
		request.setWarehouseId(fulfillmentWarehouse.getPlatformWarehouseId());
		request.setOrderSnList(query.getChannelOrderLineIdList());
		return request;
	}

	/**
	 * 获取店铺授权信息
	 * @param storeId 店铺Id
	 * @return 店铺信息
	 */
	private ShopAccount getShopAccount(Integer storeId) {
		StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(storeId);
		ShopAccount shopAccount = new ShopAccount();
		if (StringUtils.isEmpty(storeAuthorizationVo.getAuthorization().getTemuShopId())) {
			throw new BusinessException(I18nMessageKit.getMessage("TEMU_STORE_ID_CHECK"));
		}
		shopAccount.setShopId(storeAuthorizationVo.getAuthorization().getTemuShopId());
		shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());
		return shopAccount;
	}
}
