package com.renpho.erp.oms.application.ordershipaudit.chain.handler;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

/**
 * 订单自动发货审核-前置校验-处理器 (处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建）
 * <AUTHOR>
 */
@Component
public class OrderPreCheckHandler implements OrderAutoShipAuditHandler {

	/**
	 * 处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建
	 * @return int
	 */
	@Override
	public int getOrder() {
		return 1;
	}

	/**
	 * 前置校验
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return String 校验失败原因
	 */
	@Override
	public String handle(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// Function列表
		List<Function<SaleOrderAggRoot, String>> functionList = new ArrayList<>();
		// 客服备注
		functionList.add(this.buildBuyerRemarkFunction(orderAggRoot));
		// 预售订单
		functionList.add(this.buildPreMarkFunction(orderAggRoot));
		// 捆绑sku
		functionList.add(this.buildBundleSkuFunction(orderAggRoot));
		// 执行校验
		for (Function<SaleOrderAggRoot, String> function : functionList) {
			String failMsg = function.apply(orderAggRoot);
			// 存在错误信息
			if (StringUtils.isNotBlank(failMsg)) {
				return failMsg;
			}
		}
		return null;
	}

	/**
	 * 构建客服备注Function
	 * @param orderAggRoot 订单聚合根
	 * @return Function<订单聚合根, 错误信息>
	 */
	private Function<SaleOrderAggRoot, String> buildBuyerRemarkFunction(SaleOrderAggRoot orderAggRoot) {
		return data -> {
			if (orderAggRoot.isBuyerRemark()) {
				return "带客户备注订单无法自动发货审核";
			}
			return null;
		};
	}

	/**
	 * 构建预售订单Function
	 * @param orderAggRoot 订单聚合根
	 * @return Function<订单聚合根, 错误信息>
	 */
	private Function<SaleOrderAggRoot, String> buildPreMarkFunction(SaleOrderAggRoot orderAggRoot) {
		return data -> {
			if (orderAggRoot.isPreMark()) {
				return "预售订单无法自动发货审核";
			}
			return null;
		};
	}

	private Function<SaleOrderAggRoot, String> buildBundleSkuFunction(SaleOrderAggRoot orderAggRoot) {
		return data -> {
			if (orderAggRoot.isContainBundleSku()) {
				return "捆绑sku订单无法自动发货审核";
			}
			return null;
		};
	}
}
