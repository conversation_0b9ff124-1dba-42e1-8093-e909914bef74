package com.renpho.erp.oms.application.salemanagement.converter;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.SaleOrderInfoConverterDto;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclLogisticMode;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclLogisticType;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderRepository;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclShipmentRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.Product;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.domain.salemanagement.convert.ChannelConvertServiceFactory;
import com.renpho.erp.oms.domain.salemanagement.convert.ChannelConvertServiceStrategy;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;

/**
 * 美客多渠道订单转销售订单策略实现类.
 * <AUTHOR>
 * @date 2025/1/5 16:42
 */
@Component
public class MclChannelConvertSaleOrderStrategy extends AbstractChannelConvertSaleOrderStrategy {

	private final MclOrderRepository mclOrderRepository;
	private final MclShipmentRepository mclShipmentRepository;
	private final LockTemplate lockTemplate;

	public MclChannelConvertSaleOrderStrategy(MclOrderRepository mclOrderRepository, MclShipmentRepository mclShipmentRepository,
			LockTemplate lockTemplate, SaleOrderRepository saleOrderRepository, StoreClient storeClient,
			SkuMappingRepository skuMappingRepository, ProductClient productClient) {
		super(saleOrderRepository, storeClient, skuMappingRepository, productClient);
		this.mclOrderRepository = mclOrderRepository;
		this.mclShipmentRepository = mclShipmentRepository;
		this.lockTemplate = lockTemplate;
	}

	@Override
	public SaleChannelType getChannelType() {
		return SaleChannelType.MERCADO;
	}

	@Override
	public FulfillmentType getFulfillmentType() {
		return FulfillmentType.PLATFORM;
	}

	@Override
	public List<SaleOrderInfoConverterDto> getChannelOrders(StoreDTO storeDTO, Set<String> channelOrderIds, Integer pageSize, Long lastId) {
		return mclOrderRepository.findUnCompletedOrders(storeDTO.getStoreId(), channelOrderIds, pageSize, lastId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void convert(SaleOrderInfoConverterDto saleOrderInfoConverterDto, StoreDTO storeDTO) {
		// 美客多运单
		MclShipment mclShipment = mclShipmentRepository.getByMclOrderId(saleOrderInfoConverterDto.getChannelOrderNo(), storeDTO.getStoreId());
		// 非平台履约，结束
		if (Objects.isNull(mclShipment) || !MclLogisticMode.ME2.getValue().equals(mclShipment.getLogisticMode())
				|| !MclLogisticType.FULFILLMENT.getValue().equals(mclShipment.getLogisticType())) {
			return;
		}
		ChannelConvertServiceStrategy converter = ChannelConvertServiceFactory.get(this.getChannelType(), this.getFulfillmentType());
		// 查询商品SKU映射列表
		List<Product> products = super.findProductsByChannelOrderIdAndStoreId(saleOrderInfoConverterDto.getChannelOrderNo(), storeDTO.getStoreId());
		// 构建转换DTO
		ChannelConvertServiceStrategy.ConverterDTO converterDTO = super.buildConverterDTO(storeDTO, products);
		// 美客多运单主键id
		Long shipmentId = mclShipment.getId();
		// 运费填充的美客多订单id，运费未填充到销售订单时，值为空
		Long costMercadoOrderId = mclShipment.getCostMercadoOrderId();
		// 是否需要加分布式锁
		if (Objects.isNull(shipmentId) || Objects.nonNull(costMercadoOrderId)) {
			// 无需加锁，转换销售订单
			converter.convert(saleOrderInfoConverterDto, converterDTO);
			return;
		}
		// 加分布式锁（以运单id为粒度加锁），转换销售订单，解决并发：存在合并订单【多个订单对应一个运单】，运费只放在其中一个订单上
		this.lockConvert(saleOrderInfoConverterDto, storeDTO.getStoreId(), shipmentId, converter, converterDTO);
	}

	@Override
	protected Set<String> findSellerSkusByChannelOrderId(String channelOrderNo, Integer storeId) {
		// 查询渠道订单的销售sku集
		return mclOrderRepository.findSellerSkusById(channelOrderNo, storeId);
	}

	/**
	 * 加分布式锁（以运单id为粒度加锁），转换销售订单
	 * @param channelOrder 渠道订单
	 * @param storeId 店铺id
	 * @param shipmentId 美客多运单主键id
	 * @param converter 转换器
	 * @param converterDTO 转换DTO
	 */
	private void lockConvert(SaleOrderInfoConverterDto channelOrder, Integer storeId, Long shipmentId, ChannelConvertServiceStrategy converter,
							 ChannelConvertServiceStrategy.ConverterDTO converterDTO) {
		LockInfo lockInfo = null;
		try {
			String key = String.format("oms:sale:order:fulfillment:convert:%s-%s", storeId, shipmentId);
			// 锁等待时间设置为0，无需等待，Redisson的leaseTime为-1时（默认30s），才会有看门狗续期任务
			lockInfo = lockTemplate.lock(key, -1L, 0L, RedissonLockExecutor.class);
			if (Objects.isNull(lockInfo)) {
				throw new RuntimeException("");
			}
			// 转换销售订单
			converter.convert(channelOrder, converterDTO);
		}
		finally {
			if (Objects.nonNull(lockInfo)) {
				lockTemplate.releaseLock(lockInfo);
			}
		}
	}

}
