package com.renpho.erp.oms.application.b2b.order.vo;


import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;


/**
 * B2B订单客户联系人VO
 */
@Data
public class B2bOrderCustomerContactVO implements VO {

    /**
     * 联系人记录ID
     */
    private Long id;

    /**
     * B2B订单ID
     */
    private Long orderId;

    @Trans(type = TransType.DICTIONARY, key = "Tob_contactType", ref = "contactPosition")
    private  Integer contactType;

    /**
     * 联系人岗位
     */
    private String contactPosition;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系人邮箱
     */
    private String contactEmail;

    /**
     * 联系人电话
     */
    private String contactPhone;

    /**
     * 备注信息
     */
    private String remark;

}
