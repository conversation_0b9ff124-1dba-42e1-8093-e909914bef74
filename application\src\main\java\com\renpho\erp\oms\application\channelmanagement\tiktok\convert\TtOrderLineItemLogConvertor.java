package com.renpho.erp.oms.application.channelmanagement.tiktok.convert;

import com.renpho.erp.oms.application.channelmanagement.tiktok.vo.TtOrderLineItemVO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderLineItemLog;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderLineItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface TtOrderLineItemLogConvertor {

	TtOrderLineItemLog toLog(TtOrderLineItem ttOrderLineItem);

	@Mapping(target = "tax", expression = "java(BaseConvertor.convertTax(ttOrderLineItem.getTaxInfo()))")
	TtOrderLineItemVO toVO(TtOrderLineItem ttOrderLineItem);
}
