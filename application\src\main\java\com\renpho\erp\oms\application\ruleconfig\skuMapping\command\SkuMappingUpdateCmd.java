package com.renpho.erp.oms.application.ruleconfig.skuMapping.command;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class SkuMappingUpdateCmd {

	@NotNull(message = "{ID_EMPTY}")
	private Long id;


	private String asin;

	/**
	 * 销售SKU
	 */
	private String msku;


	/**
	 * 运营人员ID
	 */
	private Integer operatorId;

	private Integer fbaInventoryCounted;

	/**
	 * 映射行
	 */
	private List<SkuMappingAddCmd.SkuMappingLineAddCmd> skuMappingLineUpdateCmdList;


}
