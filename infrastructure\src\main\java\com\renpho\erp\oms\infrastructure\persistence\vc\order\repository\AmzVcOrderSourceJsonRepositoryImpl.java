package com.renpho.erp.oms.infrastructure.persistence.vc.order.repository;

import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.enums.VcParseSourceOrderStatus;
import com.renpho.erp.oms.domain.vc.order.repository.AmzVcOrderSourceJsonRepository;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.builder.AmzVcOrderAggRootBuilder;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.AmzVcOrderSourceJsonMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.AmzVcOrderSourceJsonPO;

import lombok.RequiredArgsConstructor;

/**
 * 亚马逊VC源订单-仓储
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
public class AmzVcOrderSourceJsonRepositoryImpl implements AmzVcOrderSourceJsonRepository {

	private final AmzVcOrderSourceJsonMapper amzVcOrderSourceJsonMapper;
	private final AmzVcOrderAggRootBuilder amzVcOrderAggRootBuilder;

	@Override
	public List<Long> findNeedParseAmzVcOrderIds(Set<Integer> storeIds, Set<String> vcPurchaseNos, Integer size) {
		// 需要解析的状态
		Set<Integer> needParseStatus = Set.of(VcParseSourceOrderStatus.WAIT_PARSE.getValue(),
				VcParseSourceOrderStatus.FAILED_PARSE.getValue());
		LambdaQueryWrapper<AmzVcOrderSourceJsonPO> qw = new LambdaQueryWrapper<AmzVcOrderSourceJsonPO>()
			.eq(AmzVcOrderSourceJsonPO::getParseOrderStatus, needParseStatus)
			.in(!CollectionUtils.isEmpty(storeIds), AmzVcOrderSourceJsonPO::getStoreId, storeIds)
			.in(!CollectionUtils.isEmpty(vcPurchaseNos), AmzVcOrderSourceJsonPO::getPurchaseOrderNumber, vcPurchaseNos)
			.orderByAsc(AmzVcOrderSourceJsonPO::getId)
			.last("limit " + size);
		List<AmzVcOrderSourceJsonPO> sourceList = amzVcOrderSourceJsonMapper.selectList(qw);
		return sourceList.stream().map(AmzVcOrderSourceJsonPO::getId).collect(Collectors.toList());
	}

	@Override
	public AmzVcOrderAggRoot findById(Long id) {
		// 1. 根据ID查询PO对象
		AmzVcOrderSourceJsonPO po = amzVcOrderSourceJsonMapper.selectById(id);
		if (Objects.isNull(po)) {
			throw DomainException.of(String.format("amz_vc_order_source_json id[%s] not found", id));
		}
		// 2. 使用聚合根构建器解析orderJson和orderStatusJson，构建完整的聚合根
		return amzVcOrderAggRootBuilder.build(po);
	}

	@Override
	public void updateParseStatus(Long id, Integer parseOrderStatus, Integer failCount) {
		AmzVcOrderSourceJsonPO po = new AmzVcOrderSourceJsonPO();
		po.setId(id);
		// 解析状态
		po.setParseOrderStatus(parseOrderStatus);
		po.setFailCount(failCount);
		amzVcOrderSourceJsonMapper.updateById(po);
	}
}
