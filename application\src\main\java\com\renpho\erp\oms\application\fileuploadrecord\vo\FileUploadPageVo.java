package com.renpho.erp.oms.application.fileuploadrecord.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.persistence.upload.po.FileUploadRecordPO;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;

@Data
public class FileUploadPageVo implements VO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 模板语言
     */
    private String language;

    /**
     * 文件下载路径
     */
    private String fileUrl;

    /**
     * 错误文件下载路径
     */
    private String errorFileUrl;

    /**
     * 文件上传类型（0-销售订单 2-库存清单 3-物流单）
     */
    @Trans(type = TransType.DICTIONARY, key = "file_upload_type", ref = "fileUploadTypeName")
    private Integer fileUploadType;

    private String fileUploadTypeName;
    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 数据行数
     */
    private Integer totalRows;

    /**
     * 上传人ID
     */
    private Integer uploadBy;

    /**
     * 上传人工号
     */
    private String uploadByNo;

    /**
     * 上传人名称
     */
    private String uploadByName;

    /**
     * 上传时间
     */
    private LocalDateTime uploadTime;

    /**
     * 解析完成时间
     */
    private LocalDateTime parseFinishTime;

    /**
     * 上传状态（0-解析中 1-成功 2-失败）
     */
    @Trans(type = TransType.DICTIONARY, key = "file_upload_status", ref = "uploadStatusName")
    private Integer uploadStatus;

    private String uploadStatusName;

    public static FileUploadPageVo toFileUploadRecordPageVo(FileUploadRecordPO record, Map<Integer, OumUserInfoRes> userInfoMap) {
        FileUploadPageVo fileUploadPageVo = new FileUploadPageVo();
        fileUploadPageVo.setId(record.getId());
        fileUploadPageVo.setFileName(record.getFileName());
        fileUploadPageVo.setLanguage(record.getLanguage());
        fileUploadPageVo.setFileUrl(record.getFileUrl());
        fileUploadPageVo.setErrorFileUrl(record.getErrorFileUrl());
        fileUploadPageVo.setFileUploadType(record.getFileUploadType());
        fileUploadPageVo.setChannelCode(record.getChannelCode());
        fileUploadPageVo.setChannelName(record.getChannelName());
        fileUploadPageVo.setTotalRows(record.getTotalRows());
        fileUploadPageVo.setUploadBy(record.getUploadBy());
        fileUploadPageVo.setUploadTime(record.getUploadTime());
        fileUploadPageVo.setParseFinishTime(record.getParseFinishTime());
        fileUploadPageVo.setUploadStatus(record.getUploadStatus());

        Optional.ofNullable(userInfoMap.get(record.getUploadBy()))
                .ifPresent(userInfoRes -> {
                    fileUploadPageVo.setUploadByName(userInfoRes.getName());
                    fileUploadPageVo.setUploadByNo(userInfoRes.getCode());
                });

        return fileUploadPageVo;
    }
}
