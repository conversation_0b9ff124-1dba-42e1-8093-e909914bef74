package com.renpho.erp.oms.application.listingmanagement.strategy.puller;

import com.renpho.erp.mdm.client.saleschannel.vo.SalesChannelVo;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourcePushMqStatusEnum;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourceResolveStatusEnum;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.xxl.job.core.context.XxlJobHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.ApplicationEventPublisher;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 抽象Listing拉取器模板
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractListingPullerTemplate implements ListingPuller {

	@Resource
	private StoreClient storeClient;
	@Resource
	private SalesChannelListingSourceRepository salesChannelListingSourceRepository;
	@Resource
	private ApplicationEventPublisher eventPublisher;

	/**
	 * 拉取Listing模板方法
	 */
	@Override
	public final void pullListing() {
		// 1.从MDM获取渠道店铺授权信息，key为店铺id，value为店铺授权信息
		Map<Integer, StoreAuthorizationDTO> storeAuthorizationMap = storeClient.getStoreAuthorizationMap(this.getSalesChannelCode());
		// 2.构建分批拉取查询参数
		ListingSearchQuery listingSearchQuery = this.buildSearchAfterQuery();
		// 3.按店铺维度拉取
		storeAuthorizationMap.forEach((storeId, storeAuthorization) -> {
			// 4.单个店铺，循环拉取（直到无数据）
			try {
				while (true) {
					// 5.根据店铺授权信息，调用API代理服务，分批拉取Listing数据
					ListingSearchDTO listingSearchDTO = this.pullSourceListing(storeAuthorization, listingSearchQuery);
					// 5.1 当前店铺接口出现异常 or Listing数据列表不存在，结束循环
					if (Objects.isNull(listingSearchDTO) || CollectionUtils.isEmpty(listingSearchDTO.getListingSourceList())) {
						break;
					}
					// 5.2 原始Listing数据列表
					List<SalesChannelListingSource> listingSourceList = listingSearchDTO.getListingSourceList();
					// 6.按销售渠道+店铺+Listing维度，保存API Listing原始数据，标记原始Listing状态：“待解析”，“待推送”
					this.saveListingSource(listingSourceList, this.isNeedPushMq());
					// 7.发布进程内任务，异步解析原始Listing
					this.publishSourceListingParseTask(listingSourceList);
					// 8.推送MQ（不同平台渠道可自主选择是否推送），通知下游，标记原始Listing状态：“推送成功”，“推送时间”
					this.pushSourceListing(listingSourceList, this.isNeedPushMq());
					// 9.填充下一批拉取的查询参数
					this.fillNextSearchQuery(listingSearchQuery, listingSearchDTO);
				}
			} catch (Exception e) {
				XxlJobHelper.log("拉取Listing失败，storeId:{}_{}", this.getSalesChannelCode());
				XxlJobHelper.log(e);
				log.error("拉取Listing失败，storeId:{}_{}", this.getSalesChannelCode(), storeId, e);
			}

		});
	}

	/**
	 * 根据店铺授权信息，调用API代理服务，拉取Listing数据
	 * @param storeAuthorization 店铺授权信息
	 * @return 原始Listing数据列表
	 */
	protected abstract ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery);

	/**
	 * 构建分批拉取查询参数
	 * @return SearchAfterQuery
	 */
	protected @NotNull ListingSearchQuery buildSearchAfterQuery() {
		return new ListingSearchQuery();
	}

	/**
	 * 填充下一批拉取的查询参数
	 * @param listingSearchQuery 下一批拉取的查询参数
	 * @param listingSearchDTO 上一批查询结果
	 */
	protected void fillNextSearchQuery(ListingSearchQuery listingSearchQuery, ListingSearchDTO listingSearchDTO) {
		// 上一页返回的标记，用于检索下一页
		listingSearchQuery.setSearchAfter(listingSearchDTO.getSearchAfter());
	}

	/**
	 * 按销售渠道+店铺+Listing维度，保存API Listing原始数据，标记原始Listing状态：“待解析”，“待推送”
	 * @param listingSourceList Listing原始数据列表
	 * @param isNeedPushMq 是否需要推送MQ
	 */
	protected void saveListingSource(List<SalesChannelListingSource> listingSourceList, boolean isNeedPushMq) {
		listingSourceList.forEach(listingSource -> {
			// Listing 报文解析状态：待解析
			listingSource.setResolveStatus(ListingSourceResolveStatusEnum.WAIT_RESOLVE.getStatus());
			// 推送MQ状态
			Integer pushMqStatus = isNeedPushMq ? ListingSourcePushMqStatusEnum.WAIT_PUSH.getStatus()
					: ListingSourcePushMqStatusEnum.NO_NEED_PUSH.getStatus();
			listingSource.setPushImsStatus(pushMqStatus);
		});
		salesChannelListingSourceRepository.saveList(listingSourceList);
	}

	/**
	 * 推送进程内任务，异步解析原始Listing
	 * @param listingSourceList 原始Listing列表
	 */
	protected void publishSourceListingParseTask(List<SalesChannelListingSource> listingSourceList) {
		// 注册进程内任务（原始Listing解析）领域事件
		listingSourceList.forEach(listingSource -> listingSource.registerParseTaskEvent(eventPublisher));
	}

	/**
	 * 推送MQ，通知下游(例:IMS)，标记原始Listing状态：“推送成功”，“推送时间”
	 * @param listingSourceList 原始Listing列表
	 * @param isNeedPushMq 是否需要推送MQ
	 */
	protected void pushSourceListing(List<SalesChannelListingSource> listingSourceList, boolean isNeedPushMq) {
		if (!isNeedPushMq) {
			return;
		}
		// 注册推送（原始Listing）领域事件
		listingSourceList.forEach(listingSource -> listingSource.registerPushEvent(eventPublisher));
	}

	/**
	 * 构建销售渠道原始Listing
	 * @param storeAuthorization 店铺授权信息
	 * @return SalesChannelListingSource
	 */
	protected @NotNull SalesChannelListingSource buildListingSource(StoreAuthorizationDTO storeAuthorization) {
		SalesChannelListingSource listingSource = new SalesChannelListingSource();
		// 销售渠道信息
		Optional<SalesChannelVo> channelOptional = Optional.ofNullable(storeAuthorization.getSalesChannelVo());
		// 销售渠道ID
		listingSource.setSalesChannelId(channelOptional.map(SalesChannelVo::getId).orElse(null));
		// 销售渠道编码
		listingSource.setSalesChannelCode(this.getSalesChannelCode());
		// 销售渠道名称
		listingSource.setSalesChannelName(channelOptional.map(SalesChannelVo::getChannelName).orElse(null));
		// 店铺信息
		Optional<StoreAuthorizationVo> storeOptional = Optional.ofNullable(storeAuthorization.getStoreAuthorization());
		// 店铺ID
		listingSource.setStoreId(storeOptional.map(StoreAuthorizationVo::getId).orElse(null));
		// 店铺名称
		listingSource.setStoreName(storeOptional.map(StoreAuthorizationVo::getStoreName).orElse(null));
		// 站点编码
		listingSource.setSiteCode(storeOptional.map(StoreAuthorizationVo::getSiteCode).orElse(null));
		return listingSource;
	}

}
