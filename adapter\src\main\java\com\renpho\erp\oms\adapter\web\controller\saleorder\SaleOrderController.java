package com.renpho.erp.oms.adapter.web.controller.saleorder;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.renpho.erp.oms.adapter.web.controller.listener.OfflineFulfillmentExcelListener;
import com.renpho.erp.oms.adapter.web.controller.listener.SaleOrderExcelListener;
import com.renpho.erp.oms.application.common.remark.fetcher.SaleOrderRemarkFetcher;
import com.renpho.erp.oms.application.common.remark.service.RemarkQueryService;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.application.fileuploadrecord.vo.FileUploadPageVo;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipExcelService;
import com.renpho.erp.oms.application.ordershipaudit.cmd.OrderShipAuditCmd;
import com.renpho.erp.oms.application.ordershipaudit.service.OrderShipAuditOperateService;
import com.renpho.erp.oms.application.ordershipaudit.vo.ThirdpartWarehouseVo;
import com.renpho.erp.oms.application.salemanagement.cmd.CancelOrderCmd;
import com.renpho.erp.oms.application.salemanagement.cmd.MergeOrderCmd;
import com.renpho.erp.oms.application.salemanagement.cmd.OrderIdsCmd;
import com.renpho.erp.oms.application.salemanagement.cmd.SplitOrderCmd;
import com.renpho.erp.oms.application.salemanagement.dto.OfflineFulfillmentImportExcel;
import com.renpho.erp.oms.application.salemanagement.dto.SaleOrderImportExcel;
import com.renpho.erp.oms.application.salemanagement.service.*;
import com.renpho.erp.oms.application.salemanagement.vo.*;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.cmd.*;
import com.renpho.erp.oms.domain.salemanagement.query.SaleOrderIdQuery;
import com.renpho.erp.oms.domain.salemanagement.query.SaleOrderPageQuery;
import com.renpho.erp.oms.domain.upload.FileUploadType;
import com.renpho.erp.oms.domain.upload.query.FileUploadRecordPageQuery;
import com.renpho.erp.oms.infrastructure.common.component.DownLoadComponent;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.CurrencyClient;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Set;

import static com.google.common.io.ByteStreams.toByteArray;

/**
 * 销售订单.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/sale/order/**")
@RequestMapping("/sale/order")
@AllArgsConstructor
public class SaleOrderController {

    private final SaleOrderQueryService saleOrderQueryService;
    private final SaleOrderSplitService saleOrderSplitService;
    private final SaleOrderMergeService saleOrderMergeService;
    private final SaleOrderInterceptService saleOrderInterceptService;
    private final SaleOrderOperateService saleOrderOperateService;
    private final StoreClient storeClient;
    private final OrderShipAuditOperateService orderShipAuditOperateService;
    private final DownLoadComponent downLoadComponent;
    private final CurrencyClient currencyClient;
    private final RemarkQueryService remarkQueryService;
    private final SaleOrderRemarkFetcher saleOrderRemarkFetcher;

    /**
     * 分页查询.
     *
     * @param query 参数
     * @return R
     */
    @PostMapping("/page")
    @PreAuthorize(PermissionConstant.SaleOrder.PAGE)
    public R<Paging<SaleOrderPageVO>> page(@RequestBody @Valid SaleOrderPageQuery query) {
        return R.success(saleOrderQueryService.page(query));
    }

    /**
     * 商品行-列表查询.
     *
     * @param query 参数
     * @return R
     */
    @PostMapping("/item/list")
    @PreAuthorize(PermissionConstant.SaleOrder.SHOW_ITEM_LIST)
    public R<List<SaleOrderItemVO>> listItem(@RequestBody @Valid SaleOrderIdQuery query) {
        return R.success(saleOrderQueryService.listItem(query));
    }

    /**
     * 详情.
     *
     * @param query 参数
     * @return R
     */
    @PostMapping("/detail")
    @PreAuthorize(PermissionConstant.SaleOrder.SHOW_DETAIL)
    public R<SaleOrderDetailVO> detail(@RequestBody @Valid SaleOrderIdQuery query) {
        return R.success(saleOrderQueryService.detail(query));
    }

    /**
     * 获取收货地址信息.
     *
     * @param orderNo 订单编码
     * @return R<ChannelOrderAddressDto>
     */
    @GetMapping("/address")
    @PreAuthorize(PermissionConstant.SaleOrder.SHOW_ADDRESS_DETAIL)
    public R<SaleOrderAddressVO> getReceivingAddress(@RequestParam String orderNo) {
        return R.success(saleOrderQueryService.getReceivingAddress(orderNo, false, true));
    }

    /**
     * 获取拆单详情页面信息.
     *
     * @param orderNo 订单编码
     * @return R<ChannelOrderAddressDto>
     */
    @GetMapping("/splitOrderDetail")
    @PreAuthorize(PermissionConstant.SaleOrder.SPLIT_ORDER)
    public R<SplitOrderDetailVO> getSplitOrderDetail(@RequestParam String orderNo) {
        return R.success(saleOrderQueryService.getSplitOrderDetail(orderNo));
    }

    /**
     * 拆单
     *
     * @param cmd 指令
     * @return 拆分后的新订单号集合
     */
    @PostMapping("/splitOrder")
    @PreAuthorize(PermissionConstant.SaleOrder.SPLIT_ORDER)
    public R<Set<String>> splitOrder(@RequestBody @Valid SplitOrderCmd cmd) {
        return R.success(saleOrderSplitService.splitOrder(cmd));
    }

    /**
     * 合单
     *
     * @param cmd 指令
     * @return 合并后的新订单号
     */
    @PostMapping("/mergeOrder")
    @PreAuthorize(PermissionConstant.SaleOrder.MERGE_ORDER)
    public R<String> mergeOrder(@RequestBody @Valid MergeOrderCmd cmd) {
        return R.success(saleOrderMergeService.mergeOrder(cmd));
    }

    /**
     * 截单
     *
     * @param cmd 指令
     * @return R<Void>
     */
    @PostMapping("/interceptOrder")
    @PreAuthorize(PermissionConstant.SaleOrder.INTERCEPT_ORDER)
    public R<Void> interceptOrder(@RequestBody @Valid OrderIdsCmd cmd) {
        saleOrderInterceptService.interceptOrder(cmd.getOrderIds());
        return R.success();
    }

    /**
     * 恢复
     *
     * @param cmd 指令
     * @return R<Void>
     */
    @PostMapping("/release")
    @PreAuthorize(PermissionConstant.SaleOrder.RELEASE)
    public R<Void> release(@RequestBody @Valid OrderIdsCmd cmd) {
        saleOrderOperateService.release(cmd.getOrderIds());
        return R.success();
    }

    /**
     * 冻结
     *
     * @param cmd 指令
     * @return R<Void>
     */
    @PostMapping("/hold")
    @PreAuthorize(PermissionConstant.SaleOrder.HOLD)
    public R<Void> hold(@RequestBody @Valid OrderIdsCmd cmd) {
        saleOrderOperateService.hold(cmd.getOrderIds());
        return R.success();
    }

    /**
     * 取消订单
     *
     * @param cmd 指令
     * @return R<Void>
     */
    @PostMapping("/cancel")
    @PreAuthorize(PermissionConstant.SaleOrder.CANCEL)
    public R<Void> cancel(@RequestBody @Valid CancelOrderCmd cmd) {
        saleOrderOperateService.cancel(cmd);
        return R.success(null);
    }

    /**
     * 订单备注
     */
    @PostMapping("/remark")
    @PreAuthorize(PermissionConstant.SaleOrder.REMARK)
    public R<Void> remark(@RequestBody @Valid OrderRemarkCmd orderRemarkCmd) {
        saleOrderOperateService.remark(orderRemarkCmd);
        return R.success();
    }

    /**
     * 订单历史备注
     *
     * @param orderId 订单id
     */
    @GetMapping("/orderRemarkHistory/{orderId}")
    @PreAuthorize(PermissionConstant.SaleOrder.ORDER_REMARK_HISTORY)
    public R<List<OrderRemarkHistoryVO>> orderRemarkHistory(@PathVariable("orderId") Long orderId) {
        return R.success(remarkQueryService.getRemarkHistory(orderId,saleOrderRemarkFetcher));
    }

    @GetMapping("/getStoreByChannelCode/{channelCode}")
    public R<List<StoreDTO>> getStoreByChannelCode(@PathVariable(name = "channelCode") String channelCode) {
        return R.success(storeClient.getByChannelCode(channelCode, Set.of()));
    }

    /**
     * 更新地址
     */
    @PostMapping("/updateAddress")
    @PreAuthorize(PermissionConstant.SaleOrder.UPDATE_ADDRESS)
    public R<Void> updateAddress(@RequestBody @Valid UpdateOrderAddressCmd updateOrderAddressCmd) {
        saleOrderOperateService.updateAddress(updateOrderAddressCmd);
        return R.success();
    }

    /**
     * 订单重新解析
     */
    @PostMapping("/reparseOrder")
    @PreAuthorize(PermissionConstant.SaleOrder.REPARSE_ORDER)
    public R<Void> reparseOrder(@RequestBody SaleOrderPageQuery saleOrderPageQuery) {
        saleOrderOperateService.reparseOrder(saleOrderPageQuery);
        return R.success();
    }

    /**
     * 发货审核
     */
    @PostMapping("/orderShipAudit")
    @PreAuthorize(PermissionConstant.SaleOrder.CREATE_ORDER_SHIP_AUDIT)
    public R<Void> orderShipAudit(@RequestBody @Valid OrderShipAuditCmd cmd) {
        orderShipAuditOperateService.shipAudit(cmd);
        return R.success();
    }

    /**
     * 手动发货接口
     */
    @PostMapping("/offlineFulfillment")
    @PreAuthorize(PermissionConstant.SaleOrder.OFFLINE_FULFILLMENT)
    public R<Void> offlineFulfillment(@RequestBody @Valid OfflineFulfillmentCmd offlineFulfillmentCmd) {
        saleOrderOperateService.offlineFulfillment(offlineFulfillmentCmd, false);
        return R.success();
    }

    /**
     * 手动发货导入模板下载
     */
    @PostMapping("/offlineFulfillment/template/download")
    public void offlineFulfillment(HttpServletResponse response) {
        downLoadComponent.templateDownLoad("OFFLINE_FULFILLMENT_TEMPLATE_FILE_NAME", response);
    }

    /**
     * 手动发货导入
     */
    @PostMapping("/offlineFulfillment/import")
    @PreAuthorize(PermissionConstant.SaleOrder.OFFLINE_FULFILLMENT_IMPORT)
    public R offlineFulfillmentImport(@RequestParam("file") MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            OfflineFulfillmentExcelListener offlineFulfillmentExcelListener = new OfflineFulfillmentExcelListener();
            EasyExcel.read(inputStream, OfflineFulfillmentImportExcel.class, offlineFulfillmentExcelListener).sheet().doRead();
            List<OfflineFulfillmentImportExcel> offlineFulfillmentImportExcelList = offlineFulfillmentExcelListener
                    .getOfflineFulfillmentImportExcelList();
            if (CollectionUtil.isEmpty(offlineFulfillmentImportExcelList)) {
                throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
            }
            return saleOrderOperateService.offlineFulfillmentImport(offlineFulfillmentImportExcelList);
        } catch (Exception e) {
            log.error("导入失败", e);
            if(e instanceof BusinessException){
                return R.fail(((BusinessException) e).getI18nMessage());
            }
            return R.fail(e.getMessage());
        }
    }

    /**
     * 修改商品
     */
    @PostMapping("/modifyItem")
    @PreAuthorize(PermissionConstant.SaleOrder.MODIFY_ITEM)
    public R modifyItem(@RequestBody @Valid ModifyItemCmd modifyItemCmd) {
        return saleOrderOperateService.modifyItem(modifyItemCmd);
    }

    /**
     * 修改商品获取订单信息
     */
    @GetMapping("/modify/get")
    public R<SaleOrderBaseVO> getBase(@RequestParam Long id) {
        return saleOrderQueryService.getBase(id);
    }

    /**
     * 批量替换商品
     */
    @PostMapping("/batchReplaceItem")
    @PreAuthorize(PermissionConstant.SaleOrder.BATCH_REPLACE_ITEM)
    public R batchReplaceItem(@RequestBody @Valid BatchReplaceItemCmd batchReplaceItemCmd) {
        return saleOrderOperateService.batchReplaceItem(batchReplaceItemCmd);
    }

    /**
     * 订单发货审核excel模板
     *
     * @param fulfillmentServiceType 发货服务类型
     */
    @PostMapping("/orderShipAuditExcelTemplate/{fulfillmentServiceType}")
    public void orderShipAuditExcelTemplate(HttpServletResponse response, @PathVariable Integer fulfillmentServiceType) throws IOException {
        HttpUtil.setExportResponseHeader(response, "template.xlsx");
        @Cleanup
        ServletOutputStream outputStream = response.getOutputStream();
        ChannelOrderShipExcelService.Factory.getInstance(FulfillmentServiceType.enumOf(fulfillmentServiceType))
                .writeExcelTemplate(outputStream);
    }

    /**
     * 订单发货审核excel导入 类似sku映射导入,失败的时候会返回链接
     *
     * @param fulfillmentServiceType 发货服务类型
     */
    @PostMapping("/orderShipAuditExcelImport")
    @PreAuthorize(PermissionConstant.SaleOrder.ORDER_SHIP_AUDIT_EXCEL_IMPORT)
    public R<String> orderShipAuditExcelImport(@NotNull(message = "{GET_UPLOADED_FILE_FAILED}") MultipartFile file, @RequestParam Integer fulfillmentServiceType)
            throws IOException {
        @Cleanup
        InputStream inputStream = file.getInputStream();
        return ChannelOrderShipExcelService.Factory.getInstance(FulfillmentServiceType.enumOf(fulfillmentServiceType))
                .readExcel(inputStream);
    }

    /**
     * 获取三方仓信息
     *
     * @param fulfillmentServiceType 发货服务类型
     */
    @GetMapping("/thirdPartWarehouse")
    public R<List<ThirdpartWarehouseVo>> thirdPartWarehouse(@RequestParam Integer fulfillmentServiceType) {
        return R.success(saleOrderQueryService.getThirdPartWarehouseInfo(fulfillmentServiceType));
    }

    /**
     * 导出销售订单
     */
    @PostMapping("/export")
    @PreAuthorize(PermissionConstant.SaleOrder.EXPORT)
    public void export(@RequestBody @Valid SaleOrderPageQuery query, HttpServletResponse response) {
        saleOrderQueryService.export(query, response);
    }

    /**
     * 新增销售订单
     */
    @PostMapping("/create")
    @PreAuthorize(PermissionConstant.SaleOrder.CREATE)
    public R createOrder(@RequestBody @Valid SaleOrderCreateCmd saleOrderCreateCmd) {
        saleOrderCreateCmd.setSaleOrderFulfillmentCreateCmd(null);
        return saleOrderOperateService.createOrder(saleOrderCreateCmd);
    }

    /**
     * 删除订单
     * @param batchIdCmd 批量删除订单id
     * @return R
     */
    @PostMapping("/delete")
    @PreAuthorize(PermissionConstant.SaleOrder.DELETE)
    public R<Void> delete(@RequestBody BatchIdCmd batchIdCmd) {
        saleOrderOperateService.delete(batchIdCmd.getIds());
        return R.success();
    }

    /**
     * 提交订单
     * @param batchIdCmd 批量提交订单id
     */
    @PostMapping("/submit")
    @PreAuthorize(PermissionConstant.SaleOrder.SUBMIT)
    public R<Void> submit(@RequestBody BatchIdCmd batchIdCmd) {
        saleOrderOperateService.submit(batchIdCmd.getIds());
        return R.success();
    }

    /**
     * 导入销售订单模版下载
     */
    @PostMapping("/downLoadSaleOrderTemplate/importSalesOrderTemplate")
    public void importSalesOrderTemplate(HttpServletResponse response) {
        downLoadComponent.templateDownLoad("IMPORT_SALES_ORDER_TEMPLATE", response);
    }

    /**
     * 导入已发货销售订单模版
     */
    @PostMapping("/downLoadSaleOrderTemplate/importShippedSalesOrderTemplate")
    public void importShippedSalesOrderTemplate(HttpServletResponse response) throws IOException {
        HttpUtil.setExportResponseHeader(response, "template.xlsx");
        @Cleanup
        ServletOutputStream outputStream = response.getOutputStream();
        saleOrderQueryService.importShippedSalesOrderTemplate(outputStream);
    }

    /**
     * 已发货订单导入
     */
    @PostMapping("/shipped/import")
    @PreAuthorize(PermissionConstant.SaleOrder.IMPORT_TO_SHIPPED)
    public R shippedImport(@RequestParam("file") MultipartFile file,
                           @RequestParam String channelCode) {
        return handleOrderImport(file, channelCode, FileUploadType.SHIPPED_ORDER);
    }

    /**
     * 待发货订单导入
     */
    @PostMapping("/toBeShipped/import")
    @PreAuthorize(PermissionConstant.SaleOrder.IMPORT_TO_BE_SHIPPED)
    public R toBeShippedImport(@RequestParam("file") MultipartFile file,
                               @RequestParam String channelCode) {
        return handleOrderImport(file, channelCode, FileUploadType.TO_BE_SHIPPED_ORDER);
    }

    /**
     * 分页查询.
     *
     * @param query 参数
     * @return R
     */
    @PostMapping("/fileUploadPage")
    @PreAuthorize(PermissionConstant.SaleOrder.FILE_UPLOAD_RECORD_PAGE)
    public R<Paging<FileUploadPageVo>> page(@RequestBody @Valid FileUploadRecordPageQuery query) {
        return R.success(saleOrderQueryService.page(query));
    }

    /**
     * 获取货币列表
     * @param status 货币状态
     */
    @GetMapping("/currencyList")
    public R<List<String>> currencyList(@RequestParam @NotNull(message = "货币状态不能为空") Integer status) {
        return R.success(currencyClient.getCurrencyCodes(status));
    }


    private R handleOrderImport(MultipartFile file, String channelCode, FileUploadType importType) {
        try {
            byte[] fileBytes = toByteArray(file.getInputStream());
            // 初始化监听器并读取数据
            SaleOrderExcelListener listener = new SaleOrderExcelListener(importType);
            EasyExcel.read(new ByteArrayInputStream(fileBytes), SaleOrderImportExcel.class, listener)
                    .sheet().headRowNumber(2).doRead();

            // 数据有效性校验
            List<SaleOrderImportExcel> dataList = listener.getSaleOrderImportExcelList();
            if (CollectionUtil.isEmpty(dataList)) {
                throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
            }

            // 统一服务调用
            return saleOrderOperateService.importExcel(
                    file.getOriginalFilename(),
                    fileBytes,
                    dataList,
                    channelCode,
                    importType
            );
        } catch (Exception e) {
            log.error("导入失败[类型:{}]", importType, e);
            if(e instanceof BusinessException){
                return R.fail(((BusinessException) e).getI18nMessage());
            }
            return R.fail(e.getMessage());
        }
    }
}
