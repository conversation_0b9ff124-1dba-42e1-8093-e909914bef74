package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.channelmanagement.amazon.convert.AmzAppOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.channelmanagement.amazon.vo.AmzOrderVO;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.amazon.mapper.AmzOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.amazon.po.AmzOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service(ChannelCode.AMZ_CHANNEL_CODE)
@AllArgsConstructor
public class AmzOrderPlatformService extends AbstractDefaultPlatfomService {
	private final AmzOrderRepository amzOrderRepository;
	private final AmzAppOrderConvertor amzAppOrderConvertor;
	private final PlatformOrderConvertor platformOrderConvertor;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private final AmzOrderMapper amzOrderMapper;

	@Override
	public Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<AmzOrderPage> page = amzOrderRepository.page(platformPageQuery);
		List<AmzOrderPage> amzOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(amzOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}

		List<PlatformOrderVO> records = amzOrderPageList.stream().map(amazonOrderPage -> {
			return platformOrderConvertor.amazonOrderToVO(amazonOrderPage);
		}).collect(Collectors.toList());

		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		AmzOrder amzOrder = amzOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(amzOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		AmzOrderVO amzOrderVO = amzAppOrderConvertor.toVO(amzOrder);
		// 查询店铺
		amzOrderVO.setStoreName(getStoreName(amzOrder.getStoreId()));
		return R.success(amzOrderVO);
	}

	/**
	 * 查询监控的平台订单量
	 * @param monitorPlatformOrderQuery
	 * @return
	 */
	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> monitorPlatformOrderDtoList = amzOrderMapper.selectList(Wrappers.<AmzOrderPO>lambdaQuery()
						.select(AmzOrderPO::getAmazonOrderId, AmzOrderPO::getLastMpdsSyncOrderTime, AmzOrderPO::getStoreId)
						.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, AmzOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
						.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, AmzOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
				.stream()
				.map(x -> monitorPlatformOrderConvertor.amazonOrderToDto(x))
				.collect(Collectors.toList());
		return monitorPlatformOrderDtoList;

	}
}
