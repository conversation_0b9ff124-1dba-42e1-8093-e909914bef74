package com.renpho.erp.oms.application.channelmanagement.walmart.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class WmtOrderLineLog {

	/**
	 * 卖家的销售订单id
	 */
	private String sellerOrderId;

	/**
	 * 商品行号
	 */
	private String lineNumber;

	/**
	 * 卖方SKU
	 */
	private String itemSku;

	/**
	 * 商品名称
	 */
	private String itemProductName;

	/**
	 * 有关商品状况的信息
	 */
	private String itemCondition;

	/**
	 * 商品图片地址
	 */
	private String itemImageUrl;

	/**
	 * 商品重量
	 */
	private String itemWeightValue;

	/**
	 * 商品重量单位
	 */
	private String itemWeightUnit;

	/**
	 * 费用信息json
	 */
	private String charges;

	/**
	 * 商品数量单位 EACH EA
	 */
	private String orderLineQuantityUnit;

	/**
	 * 商品数量
	 */
	private String orderLineQuantityAmount;

	/**
	 * 最近订单状态显示的日期
	 */
	private LocalDateTime statusDate;

	/**
	 * 全额退款时创建的退货订单 ID
	 */
	private String returnOrderId;

	/**
	 * 退款id
	 */
	private String refundId;

	/**
	 * 退款备注
	 */
	private String refundComments;

	/**
	 * 退款明细json
	 */
	private String refundCharges;

	/**
	 * 原始运输方式
	 */
	private String originalCarrierMethod;

	/**
	 * 参考行 ID
	 */
	private String referenceLineId;

	/**
	 * 履约选项
	 */
	private String fulfillmentOption;

	/**
	 * 履约发货方式
	 */
	private String fulfillmentShipMethod;

	/**
	 * storeId
	 */
	private String fulfillmentStoreId;

	/**
	 * 收货时间
	 */
	private LocalDateTime fulfillmentPickUpdateTime;

	/**
	 * 收货人
	 */
	private String fulfillmentPickBy;

	/**
	 * 发货计划信息
	 */
	private String fulfillmentShippingProgramType;

	/**
	 * 商品序列号数组json
	 */
	private String serialNumbers;

	private String intentToCancel;

	/**
	 * 设置个性化订单的 ConfigID
	 */
	private String configId;

	private List<WmtOrderLineStatusLog> wmtOrderLineStatusLogList;
}
