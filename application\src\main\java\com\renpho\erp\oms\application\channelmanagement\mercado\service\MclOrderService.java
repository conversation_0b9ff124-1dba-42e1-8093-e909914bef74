package com.renpho.erp.oms.application.channelmanagement.mercado.service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.*;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclOrderAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclOrderBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclOrderUpdateSnapSource;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrder;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderItem;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPayment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderItemRepository;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderPaymentRepository;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderRepository;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@AllArgsConstructor
@Slf4j
public class MclOrderService {
	private final MclOrderRepository mclOrderRepository;
	private final MclOrderItemRepository mclOrderItemRepository;
	private final MclOrderLogConvertor mclOrderLogConvertor;
	private final MclOrderItemLogConvertor mclOrderItemLogConvertor;
	private final MclOrderPaymentLogConvertor mclOrderPaymentLogConvertor;
	private final MclOrderPaymentRepository mclOrderPaymentRepository;

	@Lock4j(name = "oms:mecardo:sync", keys = { "#mclOrderDTO.id", "#mclOrderDTO.mclUserId", "#mclOrderDTO.storeId" })
	public void createOrUpdate(MclOrderDTO mclOrderDTO) {
		// 解析成平台单
		MclOrder mclOrder = MclOrderAppConvertor.toDomain(mclOrderDTO);
		List<MclOrderItem> mclOrderItemList = MclOrderItemAppConvertor.toDomainList(mclOrderDTO.getOrder_items());
		List<MclOrderPayment> mclOrderPaymentList = MclOrderPaymentAppConvertor.toDomainList(mclOrderDTO.getPayments());
		// 查询有没有
		MclOrder exitsOrder = mclOrderRepository.getByUniqueIndex(mclOrderDTO.getId(), mclOrderDTO.getMclUserId());
		// 更新插入
		if (Objects.nonNull(exitsOrder)) {
			mclOrder.setId(exitsOrder.getId());
			SpringUtil.getBean(MclOrderService.class).doUpdate(mclOrder, mclOrderItemList, mclOrderPaymentList);
		}
		else {
			SpringUtil.getBean(MclOrderService.class).doCreate(mclOrder, mclOrderItemList, mclOrderPaymentList);
		}
	}

	@Transactional
	@OpLog(snaptSource = MclOrderAddSnapSource.class, title = "新增美客多订单", businessType = BusinessType.INSERT,
			businessModule = MclOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doCreate(MclOrder mclOrder, List<MclOrderItem> mclOrderItemList, List<MclOrderPayment> mclOrderPaymentList) {
		// 渠道单转销售单状态:未转换
		mclOrder.setTransOmsStatus(TransOmsStatus.NOT_TRANS.getValue());
		// 插入订单
		Long orderId = mclOrderRepository.insert(mclOrder);
		// 插入商品
		mclOrderItemRepository.batchInsert(mclOrderItemList, orderId);
		// 插入支付信息
		mclOrderPaymentRepository.batchInsert(mclOrderPaymentList, orderId);
		// 返回主键增加日志
		return R.success(orderId);
	}

	@Transactional
	@OpLog(snaptSource = MclOrderUpdateSnapSource.class, title = "更新美客多订单", businessType = BusinessType.UPDATE,
			businessModule = MclOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doUpdate(MclOrder mclOrder, List<MclOrderItem> mclOrderItemList, List<MclOrderPayment> mclOrderPaymentList) {
		// 先删除原有商品
		mclOrderItemRepository.deleteByOrderId(mclOrder.getId());
		// 插入商品
		mclOrderItemRepository.batchInsert(mclOrderItemList, mclOrder.getId());
		// 删除支付信息
		mclOrderPaymentRepository.deleteByOrderId(mclOrder.getId());
		// 插入支付信息
		mclOrderPaymentRepository.batchInsert(mclOrderPaymentList, mclOrder.getId());
		// 更新订单
		Long orderId = mclOrderRepository.update(mclOrder);
		// 返回主键增加日志
		return R.success(orderId);
	}

	public MclOrderLog getLogById(Long orderId) {
		// 查询订单
		MclOrder mclOrder = mclOrderRepository.getById(orderId);
		if (Objects.isNull(mclOrder)) {
			return null;
		}
		MclOrderLog mclOrderLog = mclOrderLogConvertor.toLog(mclOrder);
		// 查询商品
		List<MclOrderItem> mclOrderItemList = mclOrderItemRepository.listByOrderId(orderId);
		mclOrderLog.setMclOrderItemLogList(mclOrderItemList.stream().map(mclOrderItem -> {
			return mclOrderItemLogConvertor.toLog(mclOrderItem);
		}).collect(Collectors.toList()));

		// 查询支付信息
		List<MclOrderPayment> mclOrderPaymentList = mclOrderPaymentRepository.listByOrderId(orderId);
		mclOrderLog.setMclOrderPaymentLogList(mclOrderPaymentList.stream().map(mclOrderPayment -> {
			return mclOrderPaymentLogConvertor.toLog(mclOrderPayment);
		}).collect(Collectors.toList()));
		return mclOrderLog;
	}
}
