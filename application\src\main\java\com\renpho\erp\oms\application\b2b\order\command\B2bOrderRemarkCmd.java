package com.renpho.erp.oms.application.b2b.order.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @desc: B2B订单备注
 * @time: 2025-06-20 10:36:22
 * @author: <PERSON><PERSON>
 */
@Data
public class B2bOrderRemarkCmd {
    /**
     * b2b订单主键
     */
    @NotNull(message = "ORDER_ID_NOT_NULL")
    private Long b2bOrderId;
    /**
     * 备注
     */
    @NotBlank(message = "REMARK_NOT_NULL")
    private String remark;
}
