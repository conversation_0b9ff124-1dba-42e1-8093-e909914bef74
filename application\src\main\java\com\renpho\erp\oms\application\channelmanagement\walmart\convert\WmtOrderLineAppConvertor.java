package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineVO;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLine;
import com.renpho.erp.oms.infrastructure.common.enums.WmtChargeTypeEnum;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.List;
import java.util.Objects;

@Slf4j
public class WmtOrderLineAppConvertor {

	public static List<WmtOrderLine> toDomainList(WmtOrderDTO.OrderLines orderLines) {
		List<WmtOrderLine> wmtOrderLineList = Lists.newArrayList();
		if (Objects.nonNull(orderLines) && CollectionUtil.isNotEmpty(orderLines.getOrderLine())) {
			orderLines.getOrderLine().forEach(orderLine -> {
				wmtOrderLineList.add(toDomain(orderLine));
			});

		}
		return wmtOrderLineList;
	}

	public static WmtOrderLine toDomain(WmtOrderDTO.OrderLine orderLine) {
		if (Objects.nonNull(orderLine)) {
			WmtOrderLine wmtOrderLine = new WmtOrderLine();
			wmtOrderLine.setSellerOrderId(orderLine.getSellerOrderId());
			wmtOrderLine.setLineNumber(orderLine.getLineNumber());
			// 详细信息
			if (Objects.nonNull(orderLine.getItem())) {
				wmtOrderLine.setItemSku(orderLine.getItem().getSku());
				wmtOrderLine.setItemProductName(orderLine.getItem().getProductName());
				wmtOrderLine.setItemCondition(orderLine.getItem().getCondition());
				wmtOrderLine.setItemImageUrl(orderLine.getItem().getImageUrl());
				if (Objects.nonNull(orderLine.getItem().getWeight())) {
					wmtOrderLine.setItemWeightUnit(orderLine.getItem().getWeight().getUnit());
					wmtOrderLine.setItemWeightValue(orderLine.getItem().getWeight().getValue());
				}
			}
			// 费用信息
			if (Objects.nonNull(orderLine.getCharges())) {
				wmtOrderLine.setCharges(JsonUtils.toJson(orderLine.getCharges()));
			}

			if (Objects.nonNull(orderLine.getOrderLineQuantity())) {
				wmtOrderLine.setOrderLineQuantityUnit(orderLine.getOrderLineQuantity().getUnitOfMeasurement());
				wmtOrderLine.setOrderLineQuantityAmount(orderLine.getOrderLineQuantity().getAmount());
			}

			wmtOrderLine.setStatusDate(DateUtil.convertToDateTime(orderLine.getStatusDate()));
			wmtOrderLine.setReturnOrderId(orderLine.getReturnOrderId());

			// 退款信息
			if (Objects.nonNull(orderLine.getRefund())) {
				wmtOrderLine.setRefundComments(orderLine.getRefund().getRefundComments());
				wmtOrderLine.setRefundId(orderLine.getRefund().getRefundId());
				if (Objects.nonNull(orderLine.getRefund().getRefundCharges())
						&& CollectionUtil.isNotEmpty(orderLine.getRefund().getRefundCharges().getRefundCharge())) {
					wmtOrderLine.setRefundCharges(JsonUtils.toJson(orderLine.getRefund().getRefundCharges().getRefundCharge()));
				}
			}

			wmtOrderLine.setOriginalCarrierMethod(orderLine.getOriginalCarrierMethod());
			wmtOrderLine.setReferenceLineId(orderLine.getReferenceLineId());
			// 履约信息
			if (Objects.nonNull(orderLine.getFulfillment())) {
				wmtOrderLine.setFulfillmentOption(orderLine.getFulfillment().getFulfillmentOption());
				wmtOrderLine.setFulfillmentShipMethod(orderLine.getFulfillment().getShipMethod());
				wmtOrderLine.setFulfillmentStoreId(orderLine.getFulfillment().getStoreId());
				wmtOrderLine.setFulfillmentPickUpdateTime(DateUtil.convertToDateTime(orderLine.getFulfillment().getPickUpDateTime()));
				wmtOrderLine.setFulfillmentPickBy(orderLine.getFulfillment().getPickUpBy());
				wmtOrderLine.setFulfillmentShippingProgramType(orderLine.getFulfillment().getShippingProgramType());
			}
			if (CollectionUtil.isNotEmpty(orderLine.getSerialNumbers())) {
				wmtOrderLine.setSerialNumbers(JsonUtils.toJson(orderLine.getSerialNumbers()));
			}
			wmtOrderLine.setIntentToCancel(orderLine.getIntentToCancel());
			wmtOrderLine.setConfigId(orderLine.getConfigId());
			// 商品状态行
			if (Objects.nonNull(orderLine.getOrderLineStatuses())) {
				wmtOrderLine.setWmtOrderLineStatusList(WmtOrderLineStatusAppConvertor.toDomainList(
						orderLine.getOrderLineStatuses().getOrderLineStatus(), wmtOrderLine.getLineNumber(), wmtOrderLine.getItemSku()));
			}
			return wmtOrderLine;

		}
		return null;
	}

	public static List<WmtOrderLineVO> toVOList(List<WmtOrderLine> wmtOrderLineList) {
		if (CollectionUtil.isEmpty(wmtOrderLineList)) {
			return null;
		}
		List<WmtOrderLineVO> wmtOrderLineVOList = Lists.newArrayList();
		wmtOrderLineList.forEach(wmOrderLine -> {
			wmtOrderLineVOList.add(toVO(wmOrderLine));
		});
		return wmtOrderLineVOList;

	}

	public static WmtOrderLineVO toVO(WmtOrderLine wmtOrderLine) {
		if (Objects.isNull(wmtOrderLine)) {
			return null;
		}

		WmtOrderLineVO wmtOrderLineVO = new WmtOrderLineVO();
		wmtOrderLineVO.setLineNumber(wmtOrderLine.getLineNumber());
		wmtOrderLineVO.setItemSku(wmtOrderLine.getItemSku());
		wmtOrderLineVO.setOrderLineQuantityAmount(wmtOrderLine.getOrderLineQuantityAmount());
		// 解析费用信息
		if (StringUtils.isNotEmpty(wmtOrderLine.getCharges())) {
			try {
				WmtOrderDTO.Charges charges = JsonUtils.jsonToObject(wmtOrderLine.getCharges(), WmtOrderDTO.Charges.class);
				if (CollectionUtil.isNotEmpty(charges.getCharge())) {
					wmtOrderLineVO.setCurrency(charges.getCharge().get(0).getChargeAmount().getCurrency());
					// 设置商品费用、商品税费，运费，运费税费
					charges.getCharge().forEach(charge -> {
						// 商品费用
						if (charge.getChargeType().equals(WmtChargeTypeEnum.PRODUCT.getValue())) {
							wmtOrderLineVO.setPriceAmount(charge.getChargeAmount().getAmount());
							if (Objects.nonNull(charge.getTax())) {
								wmtOrderLineVO.setPriceTaxAmount(charge.getTax().getTaxAmount().getAmount());
							}
						}
						// 运费
						if (charge.getChargeType().equals(WmtChargeTypeEnum.SHIPPING.getValue())) {
							wmtOrderLineVO.setShippingAmount(charge.getChargeAmount().getAmount());
							if (Objects.nonNull(charge.getTax())) {
								wmtOrderLineVO.setShippingTaxAmount(charge.getTax().getTaxAmount().getAmount());
							}
						}
					});

				}

			}
			catch (Exception e) {
				log.error("解析商品费用信息出错，原因为{}", e);
			}
		}
		return wmtOrderLineVO;
	}
}
