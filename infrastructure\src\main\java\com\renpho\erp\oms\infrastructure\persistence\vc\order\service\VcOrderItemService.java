package com.renpho.erp.oms.infrastructure.persistence.vc.order.service;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.oms.domain.vc.order.entity.VcOrderItem;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderItemPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * VC订单商品服务
 * <AUTHOR>
 */
@Slf4j
@Service
public class VcOrderItemService extends ServiceImpl<VcOrderItemMapper, VcOrderItemPO> {


    
    /**
     * 真正的批量更新商品SKU解析结果
     * 使用MyBatis的foreach批量更新，性能最优
     * 
     * @param orderId 订单ID
     * @param items 商品列表
     * @return 更新成功的商品数量
     */
    @Transactional
    public int batchUpdateSkuParseResultWithMapper(Long orderId, List<VcOrderItem> items) {
        if (orderId == null || items == null || items.isEmpty()) {
            log.warn("批量更新VC订单商品SKU解析结果参数为空，订单ID：{}，商品数量：{}", orderId, items == null ? 0 : items.size());
            return 0;
        }
        
        // 筛选出有product信息的商品
        List<VcOrderItem> validItems = items.stream()
                .filter(item -> item.getProduct() != null && item.getAsin() != null)
                .collect(Collectors.toList());
                
        if (validItems.isEmpty()) {
            log.debug("没有需要更新的商品，订单ID：{}", orderId);
            return 0;
        }
        
        try {
            // 构建批量更新的PO对象列表
            LocalDateTime now = LocalDateTime.now();
            List<VcOrderItemPO> updatePoList = validItems.stream()
                    .map(item -> {
                        VcOrderItemPO po = new VcOrderItemPO();
                        po.setOrderId(orderId);
                        po.setAsin(item.getAsin());
                        po.setPsku(item.getProduct().getPsku());
                        po.setBarcode(item.getProduct().getBarcode());
                        po.setNumberOfUnitsPerBox(item.getProduct().getNumberOfUnitsPerBox());
                        po.setImageId(item.getProduct().getImageId());
                        po.setUpdateTime(now);
                        return po;
                    })
                    .collect(Collectors.toList());
            
            // 使用自定义Mapper方法执行真正的批量更新
            int updateCount = baseMapper.batchUpdateSkuParseResult(updatePoList);
            
            log.info("批量更新VC订单商品SKU解析结果完成，订单ID：{}，总商品数：{}，更新成功数：{}", 
                     orderId, validItems.size(), updateCount);
            return updateCount;
            
        } catch (Exception e) {
            log.error("批量更新VC订单商品SKU解析结果异常，订单ID：{}", orderId, e);
            throw e;
        }
    }

}
