package com.renpho.erp.oms.application.returnorder.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.google.common.collect.SetMultimap;
import com.renpho.erp.oms.domain.returnorder.model.fba.AmzReportFbaCustomReturn;
import com.renpho.erp.oms.domain.returnorder.repository.fba.AmzReportFbaCustomReturnRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDTO;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AmzReturnOrderOperateService {
    @Autowired
    private SkuMappingRepository skuMappingRepository;
    @Autowired
    private ProductClient productClient;
    @Autowired
    private AmzReportFbaCustomReturnRepository amzReportFbaCustomReturnRepository;

    @Lock4j(name = "amzReturnOrderDataReparse")
    public void reparseAll(Integer pageSize) {
        Long maxId = 0L;
        pageSize = ObjectUtil.defaultIfNull(pageSize, 1000);

        List<AmzReportFbaCustomReturn> unParseList;
        do {
            unParseList = amzReportFbaCustomReturnRepository.getUnParseList(maxId, pageSize);
            this.associateSku(unParseList);
            maxId = Optional.ofNullable(CollUtil.getLast(unParseList))
                        .map(AmzReportFbaCustomReturn::getId)
                        .orElse(null);
        } while (Objects.equals(CollUtil.size(unParseList), pageSize));

    }

    private void associateSku(List<AmzReportFbaCustomReturn> amzReportFbaCustomReturnList) {
        // 根据门店id归并数据
        Multimap<Integer, AmzReportFbaCustomReturn> storeIdFbaCustomReturnMap = HashMultimap.create();
        SetMultimap<Integer, String> storeIdMskuMap = HashMultimap.create();
        for (AmzReportFbaCustomReturn amzReportFbaCustomReturn : amzReportFbaCustomReturnList) {
            storeIdFbaCustomReturnMap.put(amzReportFbaCustomReturn.getStoreId(), amzReportFbaCustomReturn);

            storeIdMskuMap.put(amzReportFbaCustomReturn.getStoreId(), amzReportFbaCustomReturn.getMsku());
        }


        // 根据skumapping查询对应的psku做关联
        for (Integer storeId : storeIdFbaCustomReturnMap.keySet()) {
            SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
            skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
            skuMappingQuery.setSellerSkuList(new ArrayList<>(storeIdMskuMap.get(storeId)));
            skuMappingQuery.setStoreIdList(ListUtil.toList(storeId));
            skuMappingQuery.setStatus(1);

            Set<String> pskuSet = new HashSet<>();
            // 查询销售sku映射关系
            Map<String, SkuMappingFulfillmentVO> skuMappingMap = skuMappingRepository.list(skuMappingQuery)
                    .stream()
                    .peek(skuMapping -> pskuSet.add(skuMapping.getPurchaseSku()))
                    .collect(Collectors.toMap(SkuMappingFulfillmentVO::getSellerSku,
                            Function.identity(), (a, b) -> b));
            Map<String, PurchaseProductDTO> purchaseProductMap = productClient.getPurchaseProductList(pskuSet);

            for (AmzReportFbaCustomReturn amzReportFbaCustomRefund : storeIdFbaCustomReturnMap.get(storeId)) {
                SkuMappingFulfillmentVO skuMappingFulfillmentVO = skuMappingMap.get(amzReportFbaCustomRefund.getMsku());
                if (skuMappingFulfillmentVO == null) {
                    // 没有对应skumapping关系
                    continue;
                }
                // 关联数据
                amzReportFbaCustomRefund.setPsku(skuMappingFulfillmentVO.getPurchaseSku());
                String imageId = Optional.ofNullable(purchaseProductMap.get(skuMappingFulfillmentVO.getPurchaseSku()))
                        .map(PurchaseProductDTO::getImageId)
                        .orElse("");
                amzReportFbaCustomRefund.associateSku(skuMappingFulfillmentVO.getPurchaseSku(), imageId);
                amzReportFbaCustomReturnRepository.associateSku(amzReportFbaCustomRefund);
            }

        }


    }
}
