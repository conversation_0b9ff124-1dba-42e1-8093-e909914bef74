package com.renpho.erp.oms.application.channelmanagement.tiktok.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDateTime;

@Data
public class TtOrderVO {

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 店铺id
	 */
	private String shopId;

	/**
	 * Tiktok shop order id
	 */
	private String tiktokOrderId;

	/**
	 * 订单状态
	 */
	private String status;

	/**
	 * 是否已更新收件人地址 0:否 1:是
	 */
	private Boolean hasUpdatedRecipientAddress;

	/**
	 * 是否货到付款
	 */
	private Boolean isCod;

	/**
	 * 是否是替换订单 0:否 1:是
	 */
	private Boolean isReplacementOrder;

	/**
	 * 是否是样品订单 0:否 1:是
	 */
	private Boolean isSampleOrder;

	/**
	 * 订单是否经历或将会经历 ON_HOLD 状态 0:否 1:是
	 */
	private Boolean isOnHoldOrder;

	/**
	 * 发货类型
	 */
	private String deliveryType;

	/**
	 * 配送选项名称
	 */
	private String deliveryOptionName;

	/**
	 * 履约类型
	 */
	private String fulfillmentType;

	/**
	 * 发货方式
	 */
	private String shippingType;

	/**
	 * 发货商名字
	 */
	private String shippingProvider;

	/**
	 * 区域代码
	 */
	private String regionCode;

	/**
	 * 邮政编码
	 */
	private String postalCode;

	/**
	 * L1级别地址对应的
	 * <p>
	 * recipient_address.district_info.address_name
	 */
	private String state;

	/**
	 * L3级别地址对应的
	 * <p>
	 * recipient_address.district_info.address_name
	 */
	private String city;

	/**
	 * 支付货币
	 */
	private String paymentCurrency;

	/**
	 * 支付方式
	 */
	private String paymentMethodName;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 产品原价总额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentOriginalTotalProductPrice;

	/**
	 * 平台折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentPlatformDiscount;

	/**
	 * 卖家折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentSellerDiscount;

	/**
	 * 买家支付订单中所有 SKU 的总金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentSubTotal;

	/**
	 * 商品税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentProductTax;

	/**
	 * 原价运费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentOriginalShippingFee;

	/**
	 * 平台提供运费折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentShippingFeePlatformDiscount;

	/**
	 * 卖家提供运费折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentShippingFeeSellerDiscount;

	/**
	 * 运费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentShippingFee;

	/**
	 * 运费税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentShippingFeeTax;

	/**
	 * 税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal paymentTax;

	/**
	 * Tiktok订单创建时间
	 */
	private LocalDateTime tiktokOrderCreateTime;

	/**
	 * 支付时间
	 */
	private LocalDateTime paidTime;

	/**
	 * 平台指定的订单自动取消时间
	 */
	private LocalDateTime cancelOrderSlaTime;

	/**
	 * 平台规定的最晚发货时间
	 */
	private LocalDateTime rtsSlaTime;

	/**
	 * 卖家发货订单的时间
	 */
	private LocalDateTime rtsTime;

	/**
	 * 平台规定的最晚收款时间
	 */
	private LocalDateTime ttsSlaTime;

	/**
	 * 揽收时间
	 */
	private LocalDateTime collectionTime;

	/**
	 * 平台规定的最晚发货时间
	 */
	private LocalDateTime deliverySlaTime;

	/**
	 * 发货时间
	 */
	private LocalDateTime deliveryTime;

	/**
	 * Tiktok订单更新时间
	 */
	private LocalDateTime tiktokOrderUpdateTime;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	// 实付商品 + 实付运费 + 税合计
	public String getTotalAmount() {
		DecimalFormat decimalFormat = new DecimalFormat("0.00");
		return decimalFormat.format(this.getPaymentSubTotal().add(this.getPaymentShippingFee()).add(this.getPaymentTax()));
	}
}
