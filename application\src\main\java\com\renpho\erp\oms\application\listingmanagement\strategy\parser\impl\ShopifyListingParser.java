package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.renpho.erp.apiproxy.shopify.model.Nodes;
import com.renpho.erp.apiproxy.shopify.model.products.RetrieveProductListResponse;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * shopify Listing解析器
 * <AUTHOR> wei
 */
@Slf4j
@Component
@AllArgsConstructor
public class ShopifyListingParser extends AbstractListingParserTemplate<RetrieveProductListResponse.Product> {

	@Override
	public String getSalesChannelCode() {
		return ChannelCode.SPF_CHANNEL_CODE;
	}

	@Override
	protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, RetrieveProductListResponse.Product product) {
		log.info("解析shopify listing开始, 解析SalesChannelListingSource id 是{}, 平台内容是{}", source.getId(), source.getContent());
		List<RetrieveProductListResponse.Variant> variantList = Optional.ofNullable(product.getVariants())
			.map(Nodes::getNodes)
			.orElse(Collections.emptyList());
		if (CollUtil.isEmpty(variantList)) {
			log.error("解析shopify listing, product没有variant信息, storeId是{}, SalesChannelListingSource id 是{}", source.getStoreId(),
					source.getId());
			return Collections.emptyList();
		}

		Map<String, String> extField = JSONKit.parseObject(source.getExtField(), new TypeReference<>() {});
		String shopifyUrl = extField.get("shopifyUrl");

		Set<String> sellerSkuSet = variantList.stream().map(RetrieveProductListResponse.Variant::getSku).collect(Collectors.toSet());
		Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), sellerSkuSet);
		Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(
				skuMappingListMap.values().stream().flatMap(Collection::stream).toList());

		List<SalesChannelListing> salesChannelListingList = new ArrayList<>();
		for (RetrieveProductListResponse.Variant variant : variantList) {
			// 没有销售sku过滤
			if (StrUtil.isBlank(variant.getSku())) {
				continue;
			}

			SalesChannelListing salesChannelListing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap,
					variant.getSku(), source.getStoreId());
			// item编码
			salesChannelListing.setItem(StrUtil.subAfter(variant.getId(), "/", true));
			// 销售sku
			salesChannelListing.setMsku(variant.getSku());
			// 标题
			salesChannelListing.setTitle(product.getTitle());
			// 状态
			salesChannelListing.setStatus(product.getStatus());
			// 商品链接
			salesChannelListing.setLink(String.format("%s/products/%s", shopifyUrl, product.getHandle()));
			// 币种
			String currencyCode = Optional.ofNullable(variant.getContextualPricing())
				.map(RetrieveProductListResponse.ContextualPricing::getPrice)
				.map(RetrieveProductListResponse.Price::getCurrencyCode)
				.orElse(null);
			salesChannelListing.setCurrency(currencyCode);
			// 价格
			BigDecimal price = Optional.ofNullable(variant.getPrice()).map(BigDecimal::new).orElse(null);
			salesChannelListing.setPrice(price);

			// 上架时间
			if (StrUtil.isNotBlank(product.getPublishedAt())) {
				ZonedDateTime publishedZoneDateTime = ZonedDateTime.parse(product.getPublishedAt(),
						DateTimeFormatter.ofPattern(DatePattern.UTC_WITH_XXX_OFFSET_PATTERN));
				LocalDateTime publishProductTime = LocalDateTime.ofInstant(publishedZoneDateTime.toInstant(), ZoneId.systemDefault());
				salesChannelListing.setPublishedTime(publishProductTime);
			}
			// 更新时间
			ZonedDateTime updateItemZoneDateTime = ZonedDateTime.parse(variant.getUpdatedAt(),
					DateTimeFormatter.ofPattern(DatePattern.UTC_WITH_XXX_OFFSET_PATTERN));
			LocalDateTime itemUpdateTime = LocalDateTime.ofInstant(updateItemZoneDateTime.toInstant(), ZoneId.systemDefault());
			salesChannelListing.setItemUpdatedTime(itemUpdateTime);

			salesChannelListingList.add(salesChannelListing);

		}
		log.info("解析shopify listing成功, 解析SalesChannelListingSource id 是{}, 平台内容是{}", source.getId(), source.getContent());
		return salesChannelListingList;
	}
}
