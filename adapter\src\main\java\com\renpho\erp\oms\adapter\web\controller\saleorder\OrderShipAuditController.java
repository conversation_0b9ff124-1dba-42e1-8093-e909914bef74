package com.renpho.erp.oms.adapter.web.controller.saleorder;

import com.renpho.erp.oms.application.ordershipaudit.service.OrderShipAuditQueryService;
import com.renpho.erp.oms.application.ordershipaudit.vo.AllowModifyShipAuditVO;
import com.renpho.erp.oms.application.ordershipaudit.vo.SelfWarehouseVO;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.domain.ordershipaudit.query.AllowModifyShipAuditQuery;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.karma.dto.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 发货审核
 * @time: 2025-04-01 14:23:20
 * @author: Alina
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/ship/audit/**")
@RequestMapping("/ship/audit")
@AllArgsConstructor
public class OrderShipAuditController {

	private final OrderShipAuditQueryService orderShipAuditQueryService;

	/**
	 * 根据发货服务类型获取自建发货仓库
	 *
	 * @param fulfillmentServiceType 参数 发货服务类型
	 * @return R
	 */
	@GetMapping("/getSelfWarehouseInfo")
	public R<List<SelfWarehouseVO>> getSelfWarehouseInfo(@RequestParam Integer fulfillmentServiceType) {
		return R.success(orderShipAuditQueryService.getSelfWarehouseInfo(fulfillmentServiceType));
	}

	/**
	 * 根据所选仓库查询发货方式报价表
	 *
	 * @param query 参数
	 * @return R
	 */
	@PostMapping("/getShippingServices")
	@PreAuthorize(PermissionConstant.SaleOrder.CREATE_ORDER_SHIP_AUDIT)
	public R<List<ShippingServicesVO>> getShippingServices(@RequestBody @Valid ShippingServicesQuery query) {
		return R.success(orderShipAuditQueryService.getShippingServices(query));
	}

	/**
	 * TEMU线上面单是否允许修改发货审核信息接口
	 *
	 * @param query 参数
	 * @return R
	 */
	@PostMapping("/allowModifyShipAudit")
	public R<AllowModifyShipAuditVO> allowModifyShipAudit(@RequestBody @Valid AllowModifyShipAuditQuery query) {
		return R.success(orderShipAuditQueryService.allowModifyShipAudit(query));
	}

}
