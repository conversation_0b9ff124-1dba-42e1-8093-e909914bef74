package com.renpho.erp.oms.application.ordershipaudit.channel.wmt;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.*;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipExcelService;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.ordershipaudit.model.WfsShippingSpeedCategory;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.OrderItemStatus;
import com.renpho.erp.oms.domain.salemanagement.OrderStatus;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelListener;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderFulfillmentMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderPO;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Slf4j
@Component
public class WmtWfsChannelOrderShipExcelService implements ChannelOrderShipExcelService {
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private SkuMappingRepository skuMappingRepository;
    @Autowired
    private SaleOrderMapper saleOrderMapper;
    @Autowired
    private SaleOrderFulfillmentMapper saleOrderFulfillmentMapper;
    @Autowired
    private SaleOrderItemMapper saleOrderItemMapper;
    @Autowired
    private Validator validator;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private OrderShipAuditCreateService orderShipAuditCreateService;

    @Override
    public FulfillmentServiceType getFulfillmentServiceType() {
        return FulfillmentServiceType.WFS_MULTI_CHANNEL;
    }

    @Override
    public R<String> readExcel(InputStream inputStream) {
        List<WmtWfsChannelOrderShipExcelDto> allWmtWfsOrderShipExcelDtoList = EasyExcel.read(inputStream)
                .head(WmtWfsChannelOrderShipExcelDto.class)
                .registerReadListener(new LanguageExcelListener<>(List.of("errorInfo")))
                .sheet(0)
                .doReadSync();
        if (CollUtil.isEmpty(allWmtWfsOrderShipExcelDtoList)) {
            throw new BusinessException(I18nMessageKit.getMessage("FILE_CANNOT_BE_EMPTY"));
        }

        Map<String, List<WmtWfsChannelOrderShipExcelDto>> orderNoExcelDtoMap = allWmtWfsOrderShipExcelDtoList.stream().collect(Collectors.groupingBy(WmtWfsChannelOrderShipExcelDto::getOrderNo));
        Map<String, StoreVo> wmtStoreMap = new HashMap<>();
        Map<String, SaleOrderPO> saleOrderPOMap = new HashMap<>();
        // row orderNO, column channelMsku , value itemIdList
        Table<String, String, List<Long>> itemIdCahnnelMskuTable = HashBasedTable.create();

        boolean failFlag = checkData(orderNoExcelDtoMap, wmtStoreMap, saleOrderPOMap, itemIdCahnnelMskuTable);
        if (failFlag) {
            allWmtWfsOrderShipExcelDtoList.forEach(WmtWfsChannelOrderShipExcelDto::buildErrorInfo);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(byteArrayOutputStream)
                    .head(WmtWfsChannelOrderShipExcelDto.class)
                    .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                    .sheet()
                    .doWrite(allWmtWfsOrderShipExcelDtoList);
            return fileClient.uploadExcel(byteArrayOutputStream);
        }

        for (Map.Entry<String, List<WmtWfsChannelOrderShipExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
            String orderNo = entry.getKey();
            List<WmtWfsChannelOrderShipExcelDto> oneOrderExcelDtoList = entry.getValue();

            StoreVo amzStoreVo = wmtStoreMap.get(oneOrderExcelDtoList.get(0).getWmtShop());
            SaleOrderPO saleOrderPO = saleOrderPOMap.get(orderNo);

            OrderShipAuditDTO orderShipAuditDTO = new OrderShipAuditDTO();
            orderShipAuditDTO.setOrderId(saleOrderPO.getId());
            orderShipAuditDTO.setFulfillmentServiceType(FulfillmentServiceType.WFS_MULTI_CHANNEL.getValue());
            orderShipAuditDTO.setChannelStoreId(amzStoreVo.getId());
            orderShipAuditDTO.setWarehouseId(amzStoreVo.getChannelWarehouseLastId());
            orderShipAuditDTO.setWarehouseCode(amzStoreVo.getChannelWarehouseCode());
			orderShipAuditDTO.setShippingSpeedCategory(
					WfsShippingSpeedCategory.enumOfName(oneOrderExcelDtoList.get(0).getShipSpeedCategory()).getValue());
			orderShipAuditDTO.setNoticeEmail(oneOrderExcelDtoList.get(0).getEmail());

            List<OrderShipAuditDTO.Item> itemList = new ArrayList<>();
            Map<String, List<Long>> channelMskuItemIdMap = itemIdCahnnelMskuTable.row(orderNo);
            channelMskuItemIdMap.forEach((channelMsku, itemIdList) ->
                    itemIdList.forEach(itemId -> itemList.add(OrderShipAuditDTO.Item.builder().itemId(itemId).channelMsku(channelMsku).build())));
            orderShipAuditDTO.setItems(itemList);
            orderShipAuditCreateService.create(orderShipAuditDTO, true);
        }

        return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
    }

    private boolean checkData(Map<String, List<WmtWfsChannelOrderShipExcelDto>> orderNoExcelDtoMap, Map<String, StoreVo> wmtStoreMap, Map<String, SaleOrderPO> saleOrderPOMap, Table<String, String, List<Long>> itemIdCahnnelMskuTable) {
        boolean failFlag = false;
        Set<String> wmtStoreNameSet = new HashSet<>();
        Set<String> storeNameSet = new HashSet<>();

        // 获取所有店铺头的门店信息,后面校验
        orderNoExcelDtoMap.values().forEach(list -> list.forEach(amzFbaOrderShipExcelDto -> {
            storeNameSet.add(amzFbaOrderShipExcelDto.getShop());
            wmtStoreNameSet.add(amzFbaOrderShipExcelDto.getWmtShop());
        }));

        Map<String, StoreVo> storeNameMap = storeClient.getByStoreNames(new ArrayList<>(storeNameSet))
                .stream()
                .collect(Collectors.toMap(StoreVo::getStoreName, Function.identity()));
        storeClient.getByStoreNames(new ArrayList<>(wmtStoreNameSet)).forEach(storeVo -> wmtStoreMap.put(storeVo.getStoreName(), storeVo));

        // 按照orderNo归并,按订单号校验
        for (Map.Entry<String, List<WmtWfsChannelOrderShipExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
            String orderNo = entry.getKey();
            List<WmtWfsChannelOrderShipExcelDto> oneOrderExcelDtoList = entry.getValue();

            boolean oneOrderError = checkOneOrder(wmtStoreMap, saleOrderPOMap, itemIdCahnnelMskuTable, oneOrderExcelDtoList, failFlag, orderNo, storeNameMap);
            if (oneOrderError) {
                failFlag = true;
            }
        }

        return failFlag;
    }

    private boolean checkOneOrder(Map<String, StoreVo> wmtStoreMap, Map<String, SaleOrderPO> saleOrderPOMap, Table<String, String, List<Long>> itemIdCahnnelMskuTable, List<WmtWfsChannelOrderShipExcelDto> oneOrderExcelDtoList, boolean failFlag, String orderNo, Map<String, StoreVo> storeNameMap) {
        // 一个订单下订单msku跟订单psku只能填一个
        Map<String, Set<String>> mskuChannelMskuMap = oneOrderExcelDtoList.stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getMsku()))
                .collect(Collectors.groupingBy(WmtWfsChannelOrderShipExcelDto::getMsku, Collectors.mapping(WmtWfsChannelOrderShipExcelDto::getWmtMsku, Collectors.toSet())));
        Map<String, Set<String>> pskuChannelMskuMap = oneOrderExcelDtoList.stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getPsku()))
                .collect(Collectors.groupingBy(WmtWfsChannelOrderShipExcelDto::getPsku, Collectors.mapping(WmtWfsChannelOrderShipExcelDto::getWmtMsku, Collectors.toSet())));
        if (!mskuChannelMskuMap.isEmpty() && !pskuChannelMskuMap.isEmpty()) {
            failFlag = true;
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_MSKU_PSKU")));
        }

        // 校验沃尔玛门店填写是否一致
        Set<String> oneOrderAmzStoreNameSet = oneOrderExcelDtoList.stream()
                .map(WmtWfsChannelOrderShipExcelDto::getWmtShop)
                .collect(Collectors.toSet());
        if (oneOrderAmzStoreNameSet.size() > 1) {
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WMT_SHOP_NOT_CONSISTENT")));
        }

        // 获取sku映射信息用于后面校验
        Set<String> wmtMskuSet = new HashSet<>();
        mskuChannelMskuMap.values().forEach(wmtMskuSet::addAll);
        pskuChannelMskuMap.values().forEach(wmtMskuSet::addAll);
        SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
        skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
        skuMappingQuery.setSellerSkuList(new ArrayList<>(wmtMskuSet));
        List<Integer> oneOrderAmzStoreId = oneOrderAmzStoreNameSet.stream()
                .map(wmtStoreMap::get)
                .filter(Objects::nonNull)
                .map(StoreVo::getId)
                .distinct()
                .toList();
        skuMappingQuery.setStoreIdList(oneOrderAmzStoreId);
        Map<Integer, Set<String>> wmtStoreIdMskuMap = skuMappingRepository.list(skuMappingQuery)
                .stream()
                .collect(Collectors.groupingBy(SkuMappingFulfillmentVO::getStoreId, Collectors.mapping(SkuMappingFulfillmentVO::getSellerSku, Collectors.toSet())));

        // 订单是否存在
        SaleOrderPO saleOrderPO = saleOrderMapper.selectOne(Wrappers.<SaleOrderPO>lambdaQuery()
                .eq(SaleOrderPO::getOrderNo, orderNo)
                .eq(SaleOrderPO::getDeleted, false)
                .last("limit 1"));
        if (saleOrderPO == null) {
            failFlag = true;
            oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                    wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_EXISTS")));
        }
        //region 跟数据库里的订单数据对比开始
        if (saleOrderPO != null) {
            saleOrderPOMap.put(saleOrderPO.getOrderNo(), saleOrderPO);
            // 订单是否自发货
            SaleOrderFulfillmentPO saleOrderFulfillment = saleOrderFulfillmentMapper.selectOne(Wrappers.<SaleOrderFulfillmentPO>lambdaQuery()
                    .eq(SaleOrderFulfillmentPO::getOrderId, saleOrderPO.getId())
                    .eq(SaleOrderFulfillmentPO::getDeleted, false)
                    .last("limit 1"));
            if (saleOrderFulfillment != null && !FulfillmentType.MERCHANT.getValue().equals(saleOrderFulfillment.getFulfillmentType())) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_FULFILLMENT_BY_MERCHANT")));
            }
            // 订单状态是否可以创建发货审核
            boolean flag = OrderStatus.canCreateOrderShipAudit().stream().anyMatch(orderStatus -> orderStatus.getValue().equals(saleOrderPO.getOrderStatus()));
            if (!flag) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_STATUS_NOT_CANT_AUDIT")));
            }

            // 订单规格信息校验
            List<SaleOrderItemPO> saleOrderItemList = saleOrderItemMapper.selectList(Wrappers.<SaleOrderItemPO>lambdaQuery()
                    .eq(SaleOrderItemPO::getOrderId, saleOrderPO.getId())
                    .eq(SaleOrderItemPO::getDeleted, false));
            // excel的订单商品行比数据库多
            if (saleOrderItemList.size() < oneOrderExcelDtoList.size()) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ITEM_EXCEED_DB")));
            }

            ListMultimap<String, Long> orderMskuItemIdMap = ArrayListMultimap.create();
            ListMultimap<String, Long> orderPskuItemIdMap = ArrayListMultimap.create();
            Set<Integer> alreadyShipOrderItemStatus = OrderItemStatus.alreadyShipStatusSet().stream().map(OrderItemStatus::getValue).collect(Collectors.toSet());
            // 部分商品是否已发货
            flag = saleOrderItemList.stream().map(SaleOrderItemPO::getStatus).anyMatch(alreadyShipOrderItemStatus::contains);
            if (flag) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SOME_BE_SHIP")));
            }
            // 校验excel里的msku,psku是否跟系统里一致
            for (SaleOrderItemPO saleOrderItem : saleOrderItemList) {
                orderMskuItemIdMap.put(saleOrderItem.getMsku(), saleOrderItem.getId());
                orderPskuItemIdMap.put(saleOrderItem.getPsku(), saleOrderItem.getId());
            }
            for (WmtWfsChannelOrderShipExcelDto wmtWfsChannelOrderShipExcelDto : oneOrderExcelDtoList) {
                // excel填的msuk,数据库没有
                if (StrUtil.isNotBlank(wmtWfsChannelOrderShipExcelDto.getMsku())) {
                    if (!orderMskuItemIdMap.containsKey(wmtWfsChannelOrderShipExcelDto.getMsku())) {
                        failFlag = true;
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_EXCEL_ORDER_MSKU_NOT_EXISTS"));
                    } else {
                        itemIdCahnnelMskuTable.put(wmtWfsChannelOrderShipExcelDto.getOrderNo(), wmtWfsChannelOrderShipExcelDto.getWmtMsku(), orderMskuItemIdMap.get(wmtWfsChannelOrderShipExcelDto.getMsku()));
                    }
                }
                // excel填的psuk,数据库没有
                if (StrUtil.isNotBlank(wmtWfsChannelOrderShipExcelDto.getPsku())) {
                    if (!orderPskuItemIdMap.containsKey(wmtWfsChannelOrderShipExcelDto.getPsku())) {
                        failFlag = true;
                        wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_EXCEL_ORDER_PSKU_NOT_EXISTS"));
                    } else {
                        itemIdCahnnelMskuTable.put(wmtWfsChannelOrderShipExcelDto.getOrderNo(), wmtWfsChannelOrderShipExcelDto.getWmtMsku(), orderPskuItemIdMap.get(wmtWfsChannelOrderShipExcelDto.getPsku()));
                    }
                }
            }

            // 是否存在数据库订单行的msku没有匹配excel里的msku
            if (CollUtil.isNotEmpty(mskuChannelMskuMap)) {
                Collection<String> notMappingMskuCollection = CollUtil.subtract(orderMskuItemIdMap.keySet(), mskuChannelMskuMap.keySet());
                if (CollUtil.isNotEmpty(notMappingMskuCollection)) {
                    oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                            wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SYSTEM_ORDER_MSKU_NOT_EXISTS")));
                }
            }
            // 是否存在数据库订单行的psku没有匹配excel里的psku
            if (CollUtil.isNotEmpty(pskuChannelMskuMap)) {
                Collection<String> notMappingPskuCollection = CollUtil.subtract(orderPskuItemIdMap.keySet(), pskuChannelMskuMap.keySet());
                if (CollUtil.isNotEmpty(notMappingPskuCollection)) {
                    oneOrderExcelDtoList.forEach(wmtWfsChannelOrderShipExcelDto ->
                            wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SYSTEM_ORDER_PSKU_NOT_EXISTS")));
                }
            }
        }
        //endregion


        for (WmtWfsChannelOrderShipExcelDto wmtWfsChannelOrderShipExcelDto : oneOrderExcelDtoList) {
            StoreVo storeVo = storeNameMap.get(wmtWfsChannelOrderShipExcelDto.getShop());
            if (storeVo == null) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SHOP_NOT_EXISTS"));
            } else if (saleOrderPO != null && !Objects.equals(saleOrderPO.getStoreId(), storeVo.getId())) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_BELONG_SHOP"));
            }


            // jsr303校验必填项
            Set<ConstraintViolation<WmtWfsChannelOrderShipExcelDto>> errorInfoSet = validator.validate(wmtWfsChannelOrderShipExcelDto);
            if (CollUtil.isNotEmpty(errorInfoSet)) {
                failFlag = true;
                errorInfoSet.forEach(errorInfo -> wmtWfsChannelOrderShipExcelDto.addErrorInfo(errorInfo.getMessage()));
            }

            // 沃尔玛发货店铺
            StoreVo amzStore = wmtStoreMap.get(wmtWfsChannelOrderShipExcelDto.getWmtShop());
            if (amzStore == null) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WMT_SHOP_NOT_CONSISTENT"));
            } else if (!SaleChannelType.WALMART.getValue().equals(amzStore.getSalesChannelCode())){
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WMT_SHOP_NOT_WMT"));
            }
            if (amzStore != null && !wmtStoreIdMskuMap.getOrDefault(amzStore.getId(), Collections.emptySet()).contains(wmtWfsChannelOrderShipExcelDto.getWmtMsku())) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WMT_SHOP_NOT_SKU_MAPPING"));
            }

			// 校验发货速度是否正确
			if (ObjectUtil.isNull(WfsShippingSpeedCategory.enumOfName(wmtWfsChannelOrderShipExcelDto.getShipSpeedCategory()))) {
				failFlag = true;
				String speedCategories = Arrays.stream(WfsShippingSpeedCategory.values())
					.map(WfsShippingSpeedCategory::getEnglishName)
					.collect(Collectors.joining(","));
				wmtWfsChannelOrderShipExcelDto
					.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WMT_SHIPPING_SPEED_ERROR", speedCategories));
			}

            // 校验相同的msku与psku映射是否一致
            if (StrUtil.isNotBlank(wmtWfsChannelOrderShipExcelDto.getMsku()) && mskuChannelMskuMap.getOrDefault(wmtWfsChannelOrderShipExcelDto.getMsku(), Collections.emptySet()).size() > 1) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SAME_EXCEL_MSKU_NOT_CONSISTENT"));
            }
            if (StrUtil.isNotBlank(wmtWfsChannelOrderShipExcelDto.getPsku()) && pskuChannelMskuMap.getOrDefault(wmtWfsChannelOrderShipExcelDto.getPsku(), Collections.emptySet()).size() > 1) {
                failFlag = true;
                wmtWfsChannelOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SAME_EXCEL_PSKU_NOT_CONSISTENT"));
            }



        }
        return failFlag;
    }

    @Override
    public void writeExcelTemplate(OutputStream outputStream) {
        EasyExcelFactory.write()
                .file(outputStream)
                .head(WmtWfsChannelOrderShipExcelDto.class)
                .excludeColumnFieldNames(List.of("errorInfo"))
                .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                .sheet()
                .doWrite(Collections.emptyList());
    }
}
