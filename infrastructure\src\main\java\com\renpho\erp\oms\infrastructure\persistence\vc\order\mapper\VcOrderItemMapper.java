package com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderItemPO;

/**
 * VC-订单商品表 Mapper
 * <AUTHOR>
 */
@Mapper
public interface VcOrderItemMapper extends BaseMapper<VcOrderItemPO> {

    /**
     * 根据订单ID批量查询商品信息
     * @param orderIds 订单ID列表
     * @return 商品信息列表
     */
    List<VcOrderItemPO> getByOrderIds(@Param("orderIds") List<Long> orderIds);
    
    /**
     * 批量更新商品SKU解析结果
     * @param items 商品列表
     * @return 更新影响的行数
     */
    int batchUpdateSkuParseResult(@Param("items") List<VcOrderItemPO> items);

}
