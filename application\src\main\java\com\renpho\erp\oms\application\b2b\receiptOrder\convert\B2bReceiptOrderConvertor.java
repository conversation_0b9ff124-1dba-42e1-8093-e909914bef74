package com.renpho.erp.oms.application.b2b.receiptOrder.convert;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.renpho.erp.fms.client.ap.fundaccount.vo.FundAccountClientCurrencyVO;
import com.renpho.erp.fms.client.common.vo.LanguageClientVO;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.AddReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.ConfirmReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.ReceiptOrderDetailVO;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.SearchB2bOrderBaseInfoVO;
import com.renpho.erp.oms.domain.IdGeneratorCode;
import com.renpho.erp.oms.domain.IdGeneratorKit;
import com.renpho.erp.oms.domain.b2b.order.B2bReceiptOrderStatusEnum;
import com.renpho.erp.oms.domain.receiptOrder.model.B2bOrderBaseInfoDto;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po.B2bReceiptOrderPO;
import com.renpho.erp.security.util.SecurityUtils;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.springframework.context.i18n.LocaleContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/23 10:49
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface B2bReceiptOrderConvertor {

   default SearchB2bOrderBaseInfoVO toB2bOrderBaseInfoVo(B2bOrderBaseInfoDto b2bOrderBaseInfoDto, Optional<FundAccountClientCurrencyVO> bankAccountOptional, String paymentTermsName){
        SearchB2bOrderBaseInfoVO searchB2bOrderBaseInfoVO = new SearchB2bOrderBaseInfoVO();
        searchB2bOrderBaseInfoVO.setOrderNo(b2bOrderBaseInfoDto.getOrderNo());
        searchB2bOrderBaseInfoVO.setCurrency(b2bOrderBaseInfoDto.getCurrency());
        searchB2bOrderBaseInfoVO.setTotalAmount(b2bOrderBaseInfoDto.getTotalAmount());
        searchB2bOrderBaseInfoVO.setOrderReceiptAmount(b2bOrderBaseInfoDto.getReceiptAmount());
        searchB2bOrderBaseInfoVO.setOrderReceiptRate(multiplyOrderReceiptRate(b2bOrderBaseInfoDto.getReceiptRate()));
        searchB2bOrderBaseInfoVO.setPaymentTermsCode(b2bOrderBaseInfoDto.getPaymentTermsCode());
        searchB2bOrderBaseInfoVO.setPaymentTermsName(paymentTermsName);
        searchB2bOrderBaseInfoVO.setFmsFundAccountCurrencyId(getFmsFundAccountCurrencyId(bankAccountOptional));
        searchB2bOrderBaseInfoVO.setFmsFundAccountCurrencyName(getFmsFundAccountCurrencyName(bankAccountOptional));
        return searchB2bOrderBaseInfoVO;
    }

    default  ReceiptOrderDetailVO poToConfirmReceiptOrderDetailVO(B2bReceiptOrderPO b2bReceiptOrderPO, B2bOrderBaseInfoDto b2bOrderBaseInfoDto, String paymentTermsName){
        ReceiptOrderDetailVO receiptOrderDetailVO = new ReceiptOrderDetailVO();
        receiptOrderDetailVO.setId(b2bReceiptOrderPO.getId());
        receiptOrderDetailVO.setOrderId(b2bReceiptOrderPO.getId());
        receiptOrderDetailVO.setOrderNo(b2bReceiptOrderPO.getB2bOrderNo());
        receiptOrderDetailVO.setCurrency(b2bOrderBaseInfoDto.getCurrency());
        receiptOrderDetailVO.setTotalAmount(b2bOrderBaseInfoDto.getTotalAmount());
        receiptOrderDetailVO.setOrderReceiptRate(multiplyOrderReceiptRate(b2bOrderBaseInfoDto.getReceiptRate()));
        receiptOrderDetailVO.setOrderReceiptAmount(b2bOrderBaseInfoDto.getReceiptAmount());
        receiptOrderDetailVO.setFmsFundAccountCurrencyName(getFmsFundAccountCurrencyName(b2bReceiptOrderPO));
        receiptOrderDetailVO.setFmsFundAccountCurrencyId(b2bReceiptOrderPO.getFmsFundAccountCurrencyId());
        receiptOrderDetailVO.setPaymentTermsCode(b2bOrderBaseInfoDto.getPaymentTermsCode());
        receiptOrderDetailVO.setPaymentTermsName(paymentTermsName);
        receiptOrderDetailVO.setBankReferNo(b2bReceiptOrderPO.getBankReferNo());
        receiptOrderDetailVO.setCustomerPaidAmount(b2bReceiptOrderPO.getCustomerPaidAmount());
        receiptOrderDetailVO.setCustomerPaidTime(b2bReceiptOrderPO.getCustomerPaidTime());
        receiptOrderDetailVO.setCustomerPaidInfoFileUrl(b2bReceiptOrderPO.getCustomerPaidInfoFileUrl());
        receiptOrderDetailVO.setCustomerPaidInfoFileName(b2bReceiptOrderPO.getCustomerPaidInfoFileName());
        return  receiptOrderDetailVO;
    }



    /**
     * 添加的cmd转成po
     *
     * @param cmd
     * @param b2bOrderBaseInfoDto
     * @param bankAccountOptional
     * @return
     */
    default B2bReceiptOrderPO addReceiptOrderCmdToPo(AddReceiptOrderCmd cmd, B2bOrderBaseInfoDto b2bOrderBaseInfoDto, Optional<FundAccountClientCurrencyVO> bankAccountOptional) {
        B2bReceiptOrderPO b2bReceiptOrderPO = new B2bReceiptOrderPO();
        //生成自定义收款单号
        String receiptOrderNo = IdGeneratorKit.get(IdGeneratorCode.B2B_RECEIPT_ORDER_CODE).generateAsString();
        b2bReceiptOrderPO.setReceiptOrderNo(receiptOrderNo);
        //银行参考号
        b2bReceiptOrderPO.setBankReferNo(cmd.getBankReferNo());
        //b2b订单id
        b2bReceiptOrderPO.setB2bOrderId(b2bOrderBaseInfoDto.getId());
        //收款所属的订单号(B2B订单号)
        b2bReceiptOrderPO.setB2bOrderNo(b2bOrderBaseInfoDto.getOrderNo());
        if (bankAccountOptional.isPresent()) {
            FundAccountClientCurrencyVO fundAccountClientCurrencyVO = bankAccountOptional.get();
            //银行账号(fms_fund_account_currency表的id)
            b2bReceiptOrderPO.setFmsFundAccountCurrencyId(fundAccountClientCurrencyVO.getId().longValue());
            Map<String, String> fmsFundAccountCurrencyNameMap = getFmsFundAccountCurrencyNameMap(fundAccountClientCurrencyVO);
            //中文名称
            b2bReceiptOrderPO.setFmsFundAccountCurrencyCnname(fmsFundAccountCurrencyNameMap.get("zh-CN"));
            //英文名称
            b2bReceiptOrderPO.setFmsFundAccountCurrencyEnname(fmsFundAccountCurrencyNameMap.get("en-US"));
        }
        //客户付款金额
        b2bReceiptOrderPO.setCustomerPaidAmount(cmd.getCustomerPaidAmount());
        //客户付款时间
        b2bReceiptOrderPO.setCustomerPaidTime(cmd.getCustomerPaidTime());
        //客戶付款信息文件名
        b2bReceiptOrderPO.setCustomerPaidInfoFileName(cmd.getCustomerPaidInfoFileName());
        //客戶付款信息文件url
        b2bReceiptOrderPO.setCustomerPaidInfoFileUrl(cmd.getCustomerPaidInfoFileUrl());
        //创建人ID
        b2bReceiptOrderPO.setCreateBy(SecurityUtils.getUserId());
        //创建时间
        b2bReceiptOrderPO.setCreateTime(LocalDateTime.now());
        //更新人ID
        b2bReceiptOrderPO.setUpdateBy(SecurityUtils.getUserId());
        //更新时间
        b2bReceiptOrderPO.setUpdateTime(LocalDateTime.now());
        return b2bReceiptOrderPO;
    }



    /**
     * confirmCmdToPo
     *
     * @param b2bReceiptOrderPO
     * @param cmd
     * @param fundAccountClientCurrencyVO
     */
    default void confirmCmdToPo(B2bReceiptOrderPO b2bReceiptOrderPO, ConfirmReceiptOrderCmd cmd, FundAccountClientCurrencyVO fundAccountClientCurrencyVO) {
        Map<String, String> fmsFundAccountCurrencyNameMap = getFmsFundAccountCurrencyNameMap(fundAccountClientCurrencyVO);
        b2bReceiptOrderPO.setFmsFundAccountCurrencyId(cmd.getFmsFundAccountCurrencyId());
        b2bReceiptOrderPO.setFmsFundAccountCurrencyEnname(fmsFundAccountCurrencyNameMap.get("en-US"));
        b2bReceiptOrderPO.setFmsFundAccountCurrencyCnname(fmsFundAccountCurrencyNameMap.get("zh-CN"));
        b2bReceiptOrderPO.setReceiptAmount(cmd.getReceiptAmount());
        b2bReceiptOrderPO.setFeesAmount(cmd.getFeesAmount());
        b2bReceiptOrderPO.setReceiptTime(cmd.getReceiptTime());
        b2bReceiptOrderPO.setReceiptInfoFileUrl(cmd.getReceiptInfoFileUrl());
        b2bReceiptOrderPO.setReceiptInfoFileName(cmd.getReceiptInfoFileName());
        b2bReceiptOrderPO.setStatus(B2bReceiptOrderStatusEnum.CONFIRMED.getValue());
        b2bReceiptOrderPO.setUpdateBy(SecurityUtils.getUserId());
    }

    /**
     * accountCurrency对应的中英文
     *
     * @param fundAccountClientCurrencyVO
     * @return
     */
    default Map<String, String> getFmsFundAccountCurrencyNameMap(FundAccountClientCurrencyVO fundAccountClientCurrencyVO) {
        if (fundAccountClientCurrencyVO == null) {
            return Maps.newHashMap();
        }
        return fundAccountClientCurrencyVO.getFundAccountNames()
                .stream()
                .collect(Collectors.toMap(LanguageClientVO::getLanguage, x -> x.getName() + StrUtil.DASHED + fundAccountClientCurrencyVO.getCurrency(), (v1, v2) -> v1));
    }


    default BigDecimal multiplyOrderReceiptRate(BigDecimal orderReceiptRate) {
        if (orderReceiptRate == null) {
            return orderReceiptRate;
        }
        return orderReceiptRate.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
    }

    default String getFmsFundAccountCurrencyName(B2bReceiptOrderPO b2BReceiptOrderPO) {
        String language = Optional.of(LocaleContextHolder.getLocale()).map(java.util.Locale::toLanguageTag).orElse("zh-CN");
        return StrUtil.equalsIgnoreCase(language, "zh-CN") ? b2BReceiptOrderPO.getFmsFundAccountCurrencyCnname() : b2BReceiptOrderPO.getFmsFundAccountCurrencyEnname();
    }

    default Integer getFmsFundAccountCurrencyId(Optional<FundAccountClientCurrencyVO> bankAccountOptional) {
        if (!bankAccountOptional.isPresent()) {
            return null;
        }
        return bankAccountOptional.get().getId();
    }

    default String getFmsFundAccountCurrencyName(Optional<FundAccountClientCurrencyVO> bankAccountOptional) {
        if (!bankAccountOptional.isPresent()) {
            return null;
        }
        Map<String, String> fmsFundAccountCurrencyNameMap = getFmsFundAccountCurrencyNameMap(bankAccountOptional.get());
        String language = Optional.of(LocaleContextHolder.getLocale()).map(java.util.Locale::toLanguageTag).orElse("zh-CN");
        return fmsFundAccountCurrencyNameMap.get(language);
    }
}
