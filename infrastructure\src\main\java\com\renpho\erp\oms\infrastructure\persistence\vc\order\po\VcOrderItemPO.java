package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单商品表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order_item")
public class VcOrderItemPO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 渠道订单行号
	 */
	private String channelOrderLineNo;

	/**
	 * 销售SKU
	 */
	private String msku;

	/**
	 * ASIN
	 */
	private String asin;

	/**
	 * 采购SKU
	 */
	private String psku;

	/**
	 * 条码
	 */
	private String barcode;

	/**
	 * 装箱数量（每箱的psku数量）
	 */
	private Integer numberOfUnitsPerBox;

	/**
	 * 图片id
	 */
	private String imageId;

	/**
	 * 下单数量
	 */
	private Integer orderedQuantity;

	/**
	 * 接受数量
	 */
	private Integer acceptedQuantity;

	/**
	 * 拒绝数量
	 */
	private Integer rejectedQuantity;

	/**
	 * 拒绝原因
	 */
	private Integer rejectionReason;

	/**
	 * 发货数量
	 */
	private Integer shippedQuantity;

	/**
	 * 接收数量
	 */
	private Integer receivedQuantity;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 税额
	 */
	private BigDecimal tax;

	/**
	 * 小计，单价*数量+税额，如果已接单则为接单数量，否则为下单数量
	 */
	private BigDecimal subTotal;

}
