package com.renpho.erp.oms.application.channelmanagement.shopify.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpfOrderShippingLineLog {
	/**
	 * 发货id
	 */
	private Long shippingLineId;

	/**
	 * 发货标识符
	 */
	private String carrierIdentifier;

	/**
	 * 编码
	 */
	private String code;

	/**
	 * 折扣金额
	 */
	private BigDecimal discountedPrice;

	/**
	 * 折扣金额集合 json
	 */
	private String discountedPriceSet;

	/**
	 * 是否移动 0:否 1:是
	 */
	private Boolean isRemoved;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 价格集合 json
	 */
	private String priceSet;

	/**
	 * 请求履约服务id
	 */
	private String requestedFulfillmentServiceId;

	/**
	 * 来源
	 */
	private String source;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 税费集合 json
	 */
	private String taxLines;

	/**
	 * 折扣分配数组 json
	 */
	private String discountAllocations;

}
