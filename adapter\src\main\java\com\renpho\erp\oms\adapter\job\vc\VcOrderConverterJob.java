package com.renpho.erp.oms.adapter.job.vc;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.vc.order.service.VcOrderConvertService;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.RequiredArgsConstructor;

/**
 * VC订单转换 定时任务.
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class VcOrderConverterJob {

	private VcOrderConvertService vcOrderConvertService;

	/**
	 * 转换VC订单
	 */
	@XxlJob("convertVcOrder")
	public void convertVcOrder() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		VcOrderConvertService.VcOrderConvertCmd param = JSONKit.parseObject(jobParam, VcOrderConvertService.VcOrderConvertCmd.class);
		// 转换VC订单
		vcOrderConvertService.convert(param);
	}
}
