package com.renpho.erp.oms.application.vc.order.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * VC订单分页查询结果VO
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单分页查询结果")
public class VcOrderPageVO implements VO {

    @Schema(description = "订单ID")
    private Long id;

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "VC采购单号")
    private String vcPurchaseNo;

    @Schema(description = "店铺名称")
    private String storeName;

    @Schema(description = "业务类型")
    @Trans(type = TransType.DICTIONARY, key = "VcOrderBusinessType", ref = "businessTypeName")
    private Integer businessType;

    private String businessTypeName;

    @Schema(description = "发货地")
    private String shipFrom;

    @Schema(description = "收货地")
    private String shipTo;

    @Schema(description = "交货窗口-起始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime shippingWindowStart;

    @Schema(description = "交货窗口-结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime shippingWindowEnd;

    @Schema(description = "订单状态")
    @Trans(type = TransType.DICTIONARY, key = "VcOrderStatus", ref = "orderStatusName")
    private Integer orderStatus;

    private String orderStatusName;

    @Schema(description = "接单状态")
    @Trans(type = TransType.DICTIONARY, key = "VcOrderAcceptedStatus", ref = "acceptedStatusName")
    private Integer acceptedStatus;

    private String acceptedStatusName;

    @Schema(description = "异常原因")
    private String exceptionReason;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "币种")
    private String currency;

    @Schema(description = "商品收入")
    private BigDecimal productPrice;

    @Schema(description = "下单时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime orderedTime;

    @Schema(description = "接单时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime acceptedTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime updateTime;

    @Schema(description = "商品信息")
    private VcOrderItemVO item;
}
