package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.util.List;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单状态值对象 基于Amazon SP-API getPurchaseOrdersStatus接口设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.OrderStatus
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrdersStatus {

	/**
	 * 采购订单号 对应API字段：purchaseOrderNumber
	 */
	private String purchaseOrderNumber;

	/**
	 * 采购订单状态 对应API字段：purchaseOrderStatus 可能的值：OPEN, ACKNOWLEDGED, CLOSED, CANCELLED
	 */
	private String purchaseOrderStatus;

	/**
	 * 采购订单确认状态 对应API字段：purchaseOrderAcknowledgmentStatus 可能的值：ACKNOWLEDGED,
	 * PARTIALLY_ACKNOWLEDGED, UNACKNOWLEDGED
	 */
	private String purchaseOrderAcknowledgmentStatus;

	/**
	 * 商品接收状态 对应API字段：itemReceiptStatus 可能的值：NOT_RECEIVED, PARTIALLY_RECEIVED, RECEIVED
	 */
	private String itemReceiptStatus;

	/**
	 * 商品确认状态 对应API字段：itemConfirmationStatus 可能的值：ACCEPTED, PARTIALLY_ACCEPTED, REJECTED,
	 * UNCONFIRMED
	 */
	private String itemConfirmationStatus;

	/**
	 * 订单确认状态 对应API字段：orderAcknowledgmentStatus 可能的值：ACKNOWLEDGED, UNACKNOWLEDGED
	 */
	private String orderAcknowledgmentStatus;

	/**
	 * 订单商品状态 对应API字段：orderItemStatus 可能的值：ACCEPTED, BACKORDERED, CANCELLED
	 */
	private String orderItemStatus;

	/**
	 * 采购订单日期 对应API字段：purchaseOrderDate
	 */
	private String purchaseOrderDate;

	/**
	 * 最后更新日期 对应API字段：lastUpdatedDate
	 */
	private String lastUpdatedDate;

	/**
	 * 卖方信息 对应API字段：sellingParty
	 */
	private AmzVcPartyInfo sellingParty;

	/**
	 * 收货方信息 对应API字段：shipToParty
	 */
	private AmzVcPartyInfo shipToParty;

	/**
	 * 订单商品状态列表 对应API字段：itemStatuses
	 */
	private List<AmzVcOrderItemStatus> itemStatuses;

	// ========== 业务方法 ==========

	/**
	 * 检查采购订单是否已确认
	 * @return boolean
	 */
	public boolean isPurchaseOrderAcknowledged() {
		return "ACKNOWLEDGED".equals(purchaseOrderAcknowledgmentStatus);
	}

	/**
	 * 检查采购订单是否部分确认
	 * @return boolean
	 */
	public boolean isPurchaseOrderPartiallyAcknowledged() {
		return "PARTIALLY_ACKNOWLEDGED".equals(purchaseOrderAcknowledgmentStatus);
	}

	/**
	 * 检查采购订单是否未确认
	 * @return boolean
	 */
	public boolean isPurchaseOrderUnacknowledged() {
		return "UNACKNOWLEDGED".equals(purchaseOrderAcknowledgmentStatus);
	}

	/**
	 * 检查订单是否已确认
	 * @return boolean
	 */
	public boolean isOrderAcknowledged() {
		return "ACKNOWLEDGED".equals(orderAcknowledgmentStatus);
	}

	/**
	 * 检查订单是否未确认
	 * @return boolean
	 */
	public boolean isOrderUnacknowledged() {
		return "UNACKNOWLEDGED".equals(orderAcknowledgmentStatus);
	}

	/**
	 * 检查商品是否已完全接收
	 * @return boolean
	 */
	public boolean isItemsFullyReceived() {
		return "RECEIVED".equals(itemReceiptStatus);
	}

	/**
	 * 检查商品是否部分接收
	 * @return boolean
	 */
	public boolean isItemsPartiallyReceived() {
		return "PARTIALLY_RECEIVED".equals(itemReceiptStatus);
	}

	/**
	 * 检查商品是否未接收
	 * @return boolean
	 */
	public boolean isItemsNotReceived() {
		return "NOT_RECEIVED".equals(itemReceiptStatus);
	}

	/**
	 * 检查商品是否已完全确认
	 * @return boolean
	 */
	public boolean isItemsFullyConfirmed() {
		return "ACCEPTED".equals(itemConfirmationStatus);
	}

	/**
	 * 检查商品是否部分确认
	 * @return boolean
	 */
	public boolean isItemsPartiallyConfirmed() {
		return "PARTIALLY_ACCEPTED".equals(itemConfirmationStatus);
	}

	/**
	 * 检查商品是否被拒绝
	 * @return boolean
	 */
	public boolean isItemsRejected() {
		return "REJECTED".equals(itemConfirmationStatus);
	}

	/**
	 * 检查商品是否未确认
	 * @return boolean
	 */
	public boolean isItemsUnconfirmed() {
		return "UNCONFIRMED".equals(itemConfirmationStatus);
	}

	/**
	 * 检查订单商品是否已接受
	 * @return boolean
	 */
	public boolean isOrderItemsAccepted() {
		return "ACCEPTED".equals(orderItemStatus);
	}

	/**
	 * 检查订单商品是否缺货
	 * @return boolean
	 */
	public boolean isOrderItemsBackordered() {
		return "BACKORDERED".equals(orderItemStatus);
	}

	/**
	 * 检查订单商品是否已取消
	 * @return boolean
	 */
	public boolean isOrderItemsCancelled() {
		return "CANCELLED".equals(orderItemStatus);
	}

	/**
	 * 检查采购订单是否已关闭
	 * @return boolean
	 */
	public boolean isPurchaseOrderClosed() {
		return "CLOSED".equals(purchaseOrderStatus);
	}

	/**
	 * 检查采购订单是否已取消
	 * @return boolean
	 */
	public boolean isPurchaseOrderCancelled() {
		return "CANCELLED".equals(purchaseOrderStatus);
	}

	/**
	 * 检查采购订单是否开放状态
	 * @return boolean
	 */
	public boolean isPurchaseOrderOpen() {
		return "OPEN".equals(purchaseOrderStatus);
	}

	/**
	 * 检查是否有商品状态信息
	 * @return boolean
	 */
	public boolean hasItemStatuses() {
		return itemStatuses != null && !itemStatuses.isEmpty();
	}

	/**
	 * 获取商品状态数量
	 * @return int
	 */
	public int getItemStatusCount() {
		return hasItemStatuses() ? itemStatuses.size() : 0;
	}

	/**
	 * 根据商品序号获取商品状态
	 * @param itemSequenceNumber 商品序号
	 * @return AmzVcOrderItemStatus
	 */
	public AmzVcOrderItemStatus getItemStatusBySequenceNumber(String itemSequenceNumber) {
		if (!hasItemStatuses() || itemSequenceNumber == null) {
			return null;
		}

		return itemStatuses.stream().filter(status -> itemSequenceNumber.equals(status.getItemSequenceNumber())).findFirst().orElse(null);
	}

	/**
	 * 根据ASIN获取商品状态
	 * @param asin 买方商品标识符（ASIN）
	 * @return AmzVcOrderItemStatus
	 */
	public AmzVcOrderItemStatus getItemStatusByAsin(String asin) {
		if (!hasItemStatuses() || asin == null) {
			return null;
		}

		return itemStatuses.stream().filter(status -> asin.equals(status.getBuyerProductIdentifier())).findFirst().orElse(null);
	}

	/**
	 * 获取已接收的商品数量
	 * @return long
	 */
	public long getReceivedItemCount() {
		if (!hasItemStatuses()) {
			return 0;
		}

		return itemStatuses.stream().filter(AmzVcOrderItemStatus::isReceived).count();
	}

	/**
	 * 检查所有商品是否都已接收
	 * @return boolean
	 */
	public boolean areAllItemsReceived() {
		if (!hasItemStatuses()) {
			return false;
		}

		return itemStatuses.stream().allMatch(AmzVcOrderItemStatus::isReceived);
	}

}
