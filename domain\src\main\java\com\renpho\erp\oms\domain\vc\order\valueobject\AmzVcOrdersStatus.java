package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单状态值对象 基于Amazon SP-API getPurchaseOrdersStatus接口设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.OrderStatus
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrdersStatus {

	/**
	 * 采购订单号 对应API字段：purchaseOrderNumber
	 */
	private String purchaseOrderNumber;

	/**
	 * 采购订单状态 对应API字段：purchaseOrderStatus 可能的值：OPEN, ACKNOWLEDGED, CLOSED, CANCELLED
	 */
	private String purchaseOrderStatus;

	/**
	 * 采购订单确认状态 对应API字段：purchaseOrderAcknowledgmentStatus 可能的值：ACKNOWLEDGED,
	 * PARTIALLY_ACKNOWLEDGED, UNACKNOWLEDGED
	 */
	private String purchaseOrderAcknowledgmentStatus;

	/**
	 * 商品接收状态 对应API字段：itemReceiptStatus 可能的值：NOT_RECEIVED, PARTIALLY_RECEIVED, RECEIVED
	 */
	private String itemReceiptStatus;

	/**
	 * 商品确认状态 对应API字段：itemConfirmationStatus 可能的值：ACCEPTED, PARTIALLY_ACCEPTED, REJECTED,
	 * UNCONFIRMED
	 */
	private String itemConfirmationStatus;

	/**
	 * 订单确认状态 对应API字段：orderAcknowledgmentStatus 可能的值：ACKNOWLEDGED, UNACKNOWLEDGED
	 */
	private String orderAcknowledgmentStatus;

	/**
	 * 订单商品状态 对应API字段：orderItemStatus 可能的值：ACCEPTED, BACKORDERED, CANCELLED
	 */
	private String orderItemStatus;

	/**
	 * 采购订单日期 对应API字段：purchaseOrderDate
	 */
	private String purchaseOrderDate;

	/**
	 * 最后更新日期 对应API字段：lastUpdatedDate
	 */
	private String lastUpdatedDate;

	/**
	 * 卖方信息 对应API字段：sellingParty
	 */
	private AmzVcPartyInfo sellingParty;

	/**
	 * 收货方信息 对应API字段：shipToParty
	 */
	private AmzVcPartyInfo shipToParty;

	/**
	 * 订单商品状态列表 对应API字段：itemStatuses
	 */
	private List<AmzVcOrderItemStatus> itemStatus;

	// ========== 业务方法 ==========

	/**
	 * 获取确认时间（取第一个订单商品行）
	 * @return LocalDateTime
	 */
	public LocalDateTime getAcknowledgementDate() {
		// 确认时间（取第一个订单商品行）
		return Optional.ofNullable(itemStatus)
			.flatMap(f -> f.stream().findFirst())
			.map(AmzVcOrderItemStatus::getAcknowledgementStatus)
			.map(AmzVcAcknowledgementStatus::getAcknowledgementStatusDetails)
			.flatMap(f -> f.stream().findFirst())
			.map(AmzVcAcknowledgementStatusDetails::getAcknowledgementDate)
			.orElse(null);

	}

}
