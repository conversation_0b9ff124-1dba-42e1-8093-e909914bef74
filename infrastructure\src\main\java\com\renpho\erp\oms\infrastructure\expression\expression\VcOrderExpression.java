package com.renpho.erp.oms.infrastructure.expression.expression;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.infrastructure.expression.AbstractExpression;
import com.renpho.erp.oms.infrastructure.expression.chain.PskuExistsExpressionChain;
import com.renpho.erp.oms.infrastructure.expression.chain.StoreExpressionChain;

import lombok.RequiredArgsConstructor;
import net.sf.jsqlparser.expression.operators.relational.EqualsTo;
import net.sf.jsqlparser.schema.Column;

/**
 * VC订单-数据权限表达式
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class VcOrderExpression extends AbstractExpression {

	private final StoreExpressionChain storeExpressionChain;
	private final PskuExistsExpressionChain pskuExistsExpressionChain;

	@Override
	public void afterPropertiesSet() throws Exception {
		// 注册数据权限-表达式链
		// 店铺id，非空，且关系
		this.register("o.store_id", false, this.storeExpressionChain, true, null, null);
		// psku子查询-关联条件
		EqualsTo pskuCondition = new EqualsTo(new Column("order_id"), new Column("o.id"));
		// psku，为空，且关系，Exists子查询表名，关联条件
		this.register("psku", true, this.pskuExistsExpressionChain, true, "oms_vc_order_item", pskuCondition);
	}
}
