package com.renpho.erp.oms.infrastructure.manager;

import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcParseSourceOrderStatus;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.builder.AmzVcOrderBuilder;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.AmzVcOrderSourceJsonMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.AmzVcOrderSourceJsonPO;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class AmzVcOrderSourceJsonManager {

	private final AmzVcOrderSourceJsonMapper amzVcOrderSourceJsonMapper;
	private final AmzVcOrderBuilder amzVcOrderBuilder;

	/**
	 * 查询需要解析的亚马逊vc源订单主键集
	 * @param storeIds 店铺id集
	 * @param vcPurchaseNos 采购单号集
	 * @param maxFailCount 最大失败次数
	 * @param lastId 上一批最后一条的id
	 * @param size 分批的条数
	 * @return List
	 */
	public List<Long> findNeedParseAmzVcOrderIds(Set<Integer> storeIds, Set<String> vcPurchaseNos, Integer maxFailCount, Long lastId,
			Integer size) {
		// 需要解析的状态
		Set<Integer> needParseStatus = Set.of(VcParseSourceOrderStatus.WAIT_PARSE.getValue(),
				VcParseSourceOrderStatus.FAILED_PARSE.getValue());
		LambdaQueryWrapper<AmzVcOrderSourceJsonPO> qw = new LambdaQueryWrapper<AmzVcOrderSourceJsonPO>()
			.select(AmzVcOrderSourceJsonPO::getId)
			.in(AmzVcOrderSourceJsonPO::getParseOrderStatus, needParseStatus)
			.in(CollectionUtils.isNotEmpty(storeIds), AmzVcOrderSourceJsonPO::getStoreId, storeIds)
			.in(CollectionUtils.isNotEmpty(vcPurchaseNos), AmzVcOrderSourceJsonPO::getPurchaseOrderNumber, vcPurchaseNos)
			.lt(Objects.nonNull(maxFailCount), AmzVcOrderSourceJsonPO::getFailCount, maxFailCount)
			.gt(Objects.nonNull(lastId), AmzVcOrderSourceJsonPO::getId, lastId)
			.orderByAsc(AmzVcOrderSourceJsonPO::getId)
			.last("limit " + size);
		List<AmzVcOrderSourceJsonPO> sourceList = amzVcOrderSourceJsonMapper.selectList(qw);
		return sourceList.stream().map(AmzVcOrderSourceJsonPO::getId).toList();
	}

	/**
	 * 查询亚马逊VC订单-值对象
	 * @param id 亚马逊vc源订单主键
	 * @return AmzVcOrder
	 */
	public AmzVcOrder findById(Long id) {
		// 1. 根据ID查询PO对象
		AmzVcOrderSourceJsonPO po = amzVcOrderSourceJsonMapper.selectById(id);
		if (Objects.isNull(po)) {
			throw DomainException.of(String.format("amz_vc_order_source_json id[%s] not found", id));
		}
		// 2. 使用构建器解析orderJson和orderStatusJson，构建完整的亚马逊VC订单-值对象
		return amzVcOrderBuilder.build(po);
	}

	/**
	 * 更新为已解析
	 * @param id 亚马逊vc源订单主键
	 * @param parseOrderStatus 解析状态
	 * @param failCount 失败次数
	 */
	public void updateParseStatus(Long id, Integer parseOrderStatus, Integer failCount) {
		AmzVcOrderSourceJsonPO po = new AmzVcOrderSourceJsonPO();
		po.setId(id);
		// 解析状态
		po.setParseOrderStatus(parseOrderStatus);
		po.setFailCount(failCount);
		amzVcOrderSourceJsonMapper.updateById(po);
	}
}
