package com.renpho.erp.oms.application.salemanagement.cmd;

import java.util.Set;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 拆单cmd
 * <AUTHOR>
 */
@Data
public class MergeOrderCmd {

	/**
	 * 选择的订单id（作为新订单信息）
	 */
	@NotNull(message = "选择的订单id不能为空")
	private Long orderId;

	/**
	 * 合并的订单id集
	 */
	@NotEmpty(message = "合并的订单id集不能为空")
	private Set<Long> orderIds;

}
