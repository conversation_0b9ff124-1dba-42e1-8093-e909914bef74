package com.renpho.erp.oms.application.b2b.order.service;

import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderRemarkCmd;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderStatusEnum;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderPO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderRemarkPO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.service.B2bOrderDataPermissionService;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.service.B2bOrderRemarkPOService;

import lombok.RequiredArgsConstructor;

/**
 * @desc: B2B订单操作服务
 * @time: 2025-06-23 12:05:50
 * @author: Alina
 */
@Service
@RequiredArgsConstructor
public class B2bOrderOperateService {

	private final B2bOrderMapper b2bOrderMapper;
	private final B2bOrderRemarkPOService b2bOrderRemarkPOService;
	private final B2bOrderDataPermissionService b2bOrderDataPermissionService;

	/**
	 * 取消订单
	 *
	 * @param orderId 订单ID
	 */
	@Lock4j(name = Constants.B2B_ORDER_OPERATE, keys = "#orderId", acquireTimeout = 0L)
	public void cancelOrder(Long orderId) {
		// 查询订单状态
		B2bOrderPO order = b2bOrderMapper.selectById(orderId);
		// 校验订单是否存在
		if (order == null) {
			throw new BusinessException("B2B_ORDER_NOT_FOUND");
		}
		// 校验订单状态是否为待处理
		if (!B2bOrderStatusEnum.PENDING.getValue().equals(order.getOrderStatus())) {
			throw new BusinessException("B2B_ORDER_CANCEL_STATUS_ERROR");
		}
		// 更新订单状态为已取消
		order.setOrderStatus(B2bOrderStatusEnum.CANCELLED.getValue());
		b2bOrderMapper.updateById(order);
	}

	/**
	 * 新增备注
	 *
	 * @param cmd 备注命令
	 */
	@Transactional
	public void remark(B2bOrderRemarkCmd cmd) {
		// 新增到备注信息表
		B2bOrderRemarkPO remarkPO = new B2bOrderRemarkPO();
		remarkPO.setRemark(cmd.getRemark());
		remarkPO.setOrderId(cmd.getB2bOrderId());
		b2bOrderRemarkPOService.save(remarkPO);
		// 更新主表订单备注
		b2bOrderDataPermissionService.updateRemark(cmd.getB2bOrderId(), cmd.getRemark());
	}
}
