package com.renpho.erp.oms.application.ordershipaudit.chain.handler;

import java.util.List;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse.AllocateWarehouse;
import com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse.AllocateWarehouseFactory;
import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.AutoShipAuditConfig;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.repository.AutoShipAuditConfigRepository;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

import lombok.RequiredArgsConstructor;

/**
 * 订单-分仓处理器
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderAllocateWarehouseHandler implements OrderAutoShipAuditHandler {

	private final AutoShipAuditConfigRepository autoShipAuditConfigRepository;

	/**
	 * 处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建
	 * @return int
	 */
	@Override
	public int getOrder() {
		return 2;
	}

	/**
	 * 分仓
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return String 分仓失败原因
	 */
	@Override
	public String handle(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// 获取订单的sku组合类型
		SkuCombinationType skuCombinationType = orderAggRoot.getSkuCombinationType();
		orderAutoShipAudit.setSkuCombinationType(skuCombinationType);
		// 查询自动发货审核配置
		List<AutoShipAuditConfig> autoShipAuditConfigs = autoShipAuditConfigRepository.findByStoreAndSkus(orderAggRoot.getChannelCode(),
				orderAggRoot.getStoreId(), orderAggRoot.getNotExtendWarrantyPskus(), true);
		// 分仓策略
		AllocateWarehouse strategy = AllocateWarehouseFactory.get(skuCombinationType);
		return strategy.allocateWarehouse(orderAutoShipAudit, autoShipAuditConfigs);
	}

}
