package com.renpho.erp.oms.domain.vc.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC进口详情值对象
 * 基于Amazon SP-API getPurchaseOrder接口的importDetails字段
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcImportDetails {

	/**
	 * 付款方式
	 * PaidByBuyer, CollectOnDelivery, DefinedByBuyerAndSeller, FOBPortOfCall, PrepaidBySeller, PaidBySeller
	 */
	private String methodOfPayment;

	/**
	 * 国际商业条款 (Incoterms)
	 * ExWorks, FreeCarrier, FreeOnBoard, FreeAlongSideShip, CarriagePaidTo, CostAndFreight,
	 * CarriageAndInsurancePaidTo, CostInsuranceAndFreight, DeliveredAtTerminal, DeliveredAtPlace, DeliverDutyPaid
	 */
	private String internationalCommercialTerms;

	/**
	 * 交货港口
	 * 当internationalCommercialTerms为FOB时必须指定
	 */
	private String portOfDelivery;

	/**
	 * 进口集装箱信息
	 * 格式：$NUMBER_OF_CONTAINERS_OF_THIS_TYPE-$CONTAINER_TYPE
	 * 例如：2-40', 1-20'HC
	 */
	private String importContainers;

	/**
	 * 发货说明
	 * 关于货物的特殊说明
	 */
	private String shippingInstructions;

	/**
	 * 检查是否包含完整的贸易条款信息
	 * @return boolean
	 */
	public boolean hasCompleteTradeTerms() {
		return internationalCommercialTerms != null && !internationalCommercialTerms.trim().isEmpty()
			&& methodOfPayment != null && !methodOfPayment.trim().isEmpty();
	}

	/**
	 * 检查是否为FOB条款
	 * @return boolean
	 */
	public boolean isFOBTerms() {
		return "FOBPortOfCall".equals(internationalCommercialTerms)
			|| "FreeOnBoard".equals(internationalCommercialTerms);
	}

	/**
	 * 检查FOB条款是否包含必需的交货港口
	 * @return boolean
	 */
	public boolean hasFOBPortOfDelivery() {
		return isFOBTerms() && portOfDelivery != null && !portOfDelivery.trim().isEmpty();
	}

	/**
	 * 检查是否包含集装箱信息
	 * @return boolean
	 */
	public boolean hasContainerInfo() {
		return importContainers != null && !importContainers.trim().isEmpty();
	}

}
