package com.renpho.erp.oms.application.channelmanagement.ebay.service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.ebay.convert.EbyOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.ebay.convert.EbyOrderLineItemAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.ebay.dto.EbyOrderPushDTO;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.ebay.repository.EbyOrderRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@AllArgsConstructor
public class EbyOrderService {
    private final EbyOrderRepository ebyOrderRepository;

    @Lock4j(name = "oms:ebay:sync", keys = {"#ebyOrderPushDTO.orderId", "#ebyOrderPushDTO.ebaySellerId"})
    public void createOrUpdate(EbyOrderPushDTO ebyOrderPushDTO) {
        // 解析成平台单
        EbyOrderAggRoot ebyOrderAggRoot = EbyOrderAppConvertor.toDomain(ebyOrderPushDTO);
        ebyOrderAggRoot.setEbyOrderLineItemList(EbyOrderLineItemAppConvertor.toDomainList(ebyOrderPushDTO));
        // 查询有没有
        EbyOrderAggRoot exitsOrder = ebyOrderRepository.getByUniqueIndex(ebyOrderPushDTO.getOrderId(), ebyOrderPushDTO.getEbaySellerId());
        // 更新插入
        if (Objects.nonNull(exitsOrder)) {
            ebyOrderAggRoot.setId(exitsOrder.getId());
            ebyOrderAggRoot.setTransOmsStatus(TransOmsStatus.TO_BE_TRANS);
            ebyOrderRepository.update(ebyOrderAggRoot, exitsOrder);
        } else {
            ebyOrderRepository.save(ebyOrderAggRoot);
        }
    }
} 