package com.renpho.erp.oms.application.b2b.receiptOrder.command;

import com.renpho.erp.oms.infrastructure.common.validation.DecimalScale;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 确认收款单cmd
 * @date 2025/6/23 11:56
 */
@Data
public class ConfirmReceiptOrderCmd {
    /**
     * id
     */
    @NotNull(message = "ID_EMPTY")
    private Long id;

    /**
     * 订单id
     */
    @NotNull(message = "ORDER_ID_NOT_EMPTY")
    private Long orderId;

    /**
     * B2B订单号
     */
    @NotNull(message = "ORDER_NO_EMPTY")
    private String orderNo;

    /**
     * 银行账号
     */
    @NotNull(message = "BAND_ACCOUNT_NOT_EMPTY")
    private Long fmsFundAccountCurrencyId;

    /**
     * 收款金额
     */
    @NotNull(message = "RECEIPT_AMOUNT_NOT_EMPTY")
    @DecimalMin(value = "0.00", inclusive = false, message = "RECEIPT_AMOUNT_GT_ZERO_TWO_SCALE")
    @DecimalScale(scale = 2, message = "RECEIPT_AMOUNT_GT_ZERO_TWO_SCALE")
    private BigDecimal receiptAmount;


    /**
     * 费用金额
     */
    @NotNull(message = "FEES_AMOUNT_NOT_EMPTY")
    @DecimalMin(value = "0.00", inclusive = true, message = "FEES_AMOUNT_GE_ZERO_TWO_SCALE")
    @DecimalScale(scale = 2, message = "FEES_AMOUNT_GE_ZERO_TWO_SCALE")
    private BigDecimal feesAmount;

    /**
     * 收款时间
     */
    @NotNull(message = "RECEIPT_TIME_NOT_EMPTY")
    private LocalDateTime receiptTime;


    /**
     * 收款信息文件url,调/erp-ftm/file/upload接口，path传receiptInfoFile返回的url
     */
    private String receiptInfoFileUrl;

    /**
     * 收款信息文件名，调/erp-ftm/file/upload接口返回的originalFilename
     */
    private String receiptInfoFileName;


}
