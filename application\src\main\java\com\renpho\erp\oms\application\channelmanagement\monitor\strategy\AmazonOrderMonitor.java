package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * 亚马逊订单监视器
 * <AUTHOR>
 */
@Component
public class AmazonOrderMonitor extends AbstractChannelOrderMonitor {

	private final AmzOrderRepository amzOrderRepository;

	public AmazonOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			AmzOrderRepository amzOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.amzOrderRepository = amzOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.AMZ_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return amzOrderRepository.count(storeAuthorization.getAmzSellerId(), storeAuthorization.getAmzMarketplaceId(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		// 按照卖家+市场维度
		if (StringUtils.isNotBlank(storeAuthorization.getAmzMarketplaceId())) {
			return storeAuthorization.getAmzSellerId() + "-" + storeAuthorization.getAmzMarketplaceId();
		}
		// 按照卖家维度
		return storeAuthorization.getAmzSellerId();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 是否按照卖家维度统计
		if (monitor.isSeller()) {
			// 按照卖家维度统计
			return storeAuthorizationList.stream()
				.filter(s -> storeNames.contains(s.getStoreName()))
				.map(StoreAuthorizationVo::getAuthorization)
				.filter(Objects::nonNull)
				// 卖家id
				.map(StoreAuthorizationVo.Authorization::getAmzSellerId)
				.filter(StringUtils::isNotBlank)
				.distinct()
				.map(sellerId -> {
					StoreAuthorization storeAuthorization = new StoreAuthorization();
					storeAuthorization.setAmzSellerId(sellerId);
					// 店铺名称
					storeAuthorization.setStoreNames(storeAuthorizationList.stream()
						.filter(s -> Objects.nonNull(s.getAuthorization())
								&& StringUtils.equals(s.getAuthorization().getAmzSellerId(), sellerId))
						.map(StoreAuthorizationVo::getStoreName)
						.collect(Collectors.toSet()));
					return storeAuthorization;
				})
				.toList();
		}
		else {
			// 按照卖家和市场维度统计
			return storeAuthorizationList.stream()
				.filter(s -> storeNames.contains(s.getStoreName()))
				.map(StoreAuthorizationVo::getAuthorization)
				.filter(Objects::nonNull)
				// 卖家id和市场id都不能为空
				.filter(a -> StringUtils.isNotBlank(a.getAmzSellerId()) && StringUtils.isNotBlank(a.getAmzMarketplaceId()))
				.map(a -> {
					StoreAuthorization storeAuthorization = new StoreAuthorization();
					storeAuthorization.setAmzSellerId(a.getAmzSellerId());
					storeAuthorization.setAmzMarketplaceId(a.getAmzMarketplaceId());
					// 店铺名称
					storeAuthorization.setStoreNames(storeAuthorizationList.stream()
						.filter(s -> Objects.nonNull(s.getAuthorization())
								&& StringUtils.equals(s.getAuthorization().getAmzSellerId(), a.getAmzSellerId())
								&& StringUtils.equals(s.getAuthorization().getAmzMarketplaceId(), a.getAmzMarketplaceId()))
						.map(StoreAuthorizationVo::getStoreName)
						.collect(Collectors.toSet()));
					return storeAuthorization;
				})
				.toList();
		}
	}
}
