package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.math.BigDecimal;
import java.util.Optional;
import java.util.Optional;

import cn.hutool.core.util.ObjectUtil;
import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单商品金额值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class VcOrderItemAmount {

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 单价
	 */
	private BigDecimal unitPrice;

	/**
	 * 税额
	 */
	private BigDecimal tax;

	/**
	 * 商品总价（单价*数量）
	 */
	private BigDecimal productPrice;

	/**
	 * 小计，单价*数量+税额，如果已接单则为接单数量，否则为下单数量
	 */
	private BigDecimal subTotal;

	/**
	 * 计算订单商品金额
	 * @param quantity 数量
	 */
	public void calculateAmount(Integer quantity) {
		// 未开票时，税额为0
		this.tax = Optional.ofNullable(tax).orElse(BigDecimal.ZERO);
		if (unitPrice == null || quantity == null) {
			this.subTotal = tax;
			return;
		}
		// 商品总价（单价*数量）
		this.productPrice = unitPrice.multiply(BigDecimal.valueOf(quantity));
		// 小计 = 商品总价 + 税额
		this.subTotal = productPrice.add(tax);
	}

	public void invoiceTax(Integer quantity, BigDecimal invoiceTax) {
		this.tax = ObjectUtil.defaultIfNull(this.tax, BigDecimal.ZERO).add(invoiceTax);
		this.calculateAmount(quantity);
	}

	/**
	 * 验证金额信息是否有效
	 * @return boolean
	 */
	public boolean isValid() {
		return unitPrice != null && unitPrice.compareTo(BigDecimal.ZERO) >= 0;
	}

}
