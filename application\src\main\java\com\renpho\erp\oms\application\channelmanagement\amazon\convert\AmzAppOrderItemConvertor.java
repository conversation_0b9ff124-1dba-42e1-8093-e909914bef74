package com.renpho.erp.oms.application.channelmanagement.amazon.convert;

import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderItemLog;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderItemPushDTO;
import com.renpho.erp.oms.application.channelmanagement.amazon.vo.AmzOrderItemVO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItem;
import com.renpho.erp.oms.infrastructure.common.enums.EstimatedPriceStatusEnum;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;


@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface AmzAppOrderItemConvertor {
    default AmzOrderItem toDomain(AmzOrderItemPushDTO param, AmzOrder amzOrder) {
        if (param == null) {
            return null;
        }

        AmzOrderItem amzOrderItem = new AmzOrderItem();

        amzOrderItem.setOrderItemId(param.getOrderItemId());
        amzOrderItem.setTitle(param.getTitle());
        amzOrderItem.setQuantityOrdered(param.getQuantityOrdered());
        amzOrderItem.setQuantityShipped(param.getQuantityShipped());
        amzOrderItem.setConditionNote(param.getConditionNote());
        amzOrderItem.setConditionId(param.getConditionId());
        amzOrderItem.setConditionSubtypeId(param.getConditionSubtypeId());
        amzOrderItem.setPriceDesignation(param.getPriceDesignation());
        amzOrderItem.setSerialNumberRequired(param.getSerialNumberRequired());
        amzOrderItem.setIsTransparency(param.getIsTransparency());
        amzOrderItem.setIossNumber(param.getIossNumber());
        amzOrderItem.setStoreChainStoreId(param.getStoreChainStoreId());

        amzOrderItem.setAsin(param.getASIN());
        amzOrderItem.setAssociatedItems(BaseConvertor.convertJsonStr(param.getAssociatedItems()));
        amzOrderItem.setProductInfoNumberOfItems(
                param.getProductInfo() != null ? BaseConvertor.convertToInteger(param.getProductInfo().getNumberOfItems()) : null);
        amzOrderItem.setPointsGrantedPointsNumber(param.getPointsGranted() != null ? param.getPointsGranted().getPointsNumber() : null);
        amzOrderItem
                .setPointsGrantedCurrencyCode(param.getPointsGranted() != null && param.getPointsGranted().getPointsMonetaryValue() != null
                        ? param.getPointsGranted().getPointsMonetaryValue().getCurrencyCode() : null);
        amzOrderItem.setPointsGrantedAmount(param.getPointsGranted() != null && param.getPointsGranted().getPointsMonetaryValue() != null
                ? BaseConvertor.convertToBigDecimal(param.getPointsGranted().getPointsMonetaryValue().getAmount()) : null);
        amzOrderItem.setItemPriceCurrencyCode(param.getItemPrice() != null ? param.getItemPrice().getCurrencyCode() : null);
        amzOrderItem
                .setItemPriceAmount(param.getItemPrice() != null ? BaseConvertor.convertToBigDecimal(param.getItemPrice().getAmount()) : null);
        // 订单状态无需估算
        if (!amzOrder.needEstimatedPrice()) {
            amzOrderItem.setEstimatedPriceStatus(EstimatedPriceStatusEnum.NO_ESTIMATE_REQUIRED.getValue());
        } else {
            // 订单有价格就无需估算
            amzOrderItem.setEstimatedPriceStatus(param.getItemPrice() != null ? EstimatedPriceStatusEnum.NO_ESTIMATE_REQUIRED.getValue()
                    : EstimatedPriceStatusEnum.TO_BE_ESTIMATED.getValue());
        }
        amzOrderItem.setItemTaxCurrencyCode(param.getItemTax() != null ? param.getItemTax().getCurrencyCode() : null);
        amzOrderItem
                .setItemTaxAmount(param.getItemTax() != null ? BaseConvertor.convertToBigDecimal(param.getItemTax().getAmount()) : null);
        amzOrderItem.setShippingPriceCurrencyCode(param.getShippingPrice() != null ? param.getShippingPrice().getCurrencyCode() : null);
        amzOrderItem.setShippingPriceAmount(
                param.getShippingPrice() != null ? BaseConvertor.convertToBigDecimal(param.getShippingPrice().getAmount()) : null);
        amzOrderItem.setShippingTaxCurrencyCode(param.getShippingTax() != null ? param.getShippingTax().getCurrencyCode() : null);
        amzOrderItem.setShippingTaxAmount(
                param.getShippingTax() != null ? BaseConvertor.convertToBigDecimal(param.getShippingTax().getAmount()) : null);
        amzOrderItem
                .setShippingDiscountCurrencyCode(param.getShippingDiscount() != null ? param.getShippingDiscount().getCurrencyCode() : null);
        amzOrderItem.setShippingDiscountAmount(
                param.getShippingDiscount() != null ? BaseConvertor.convertToBigDecimal(param.getShippingDiscount().getAmount()) : null);
        amzOrderItem.setShippingDiscountTaxCurrencyCode(
                param.getShippingDiscountTax() != null ? param.getShippingDiscountTax().getCurrencyCode() : null);
        amzOrderItem.setShippingDiscountTaxAmount(param.getShippingDiscountTax() != null
                ? BaseConvertor.convertToBigDecimal(param.getShippingDiscountTax().getAmount()) : null);
        amzOrderItem
                .setPromotionDiscountCurrencyCode(param.getPromotionDiscount() != null ? param.getPromotionDiscount().getCurrencyCode() : null);
        amzOrderItem.setPromotionDiscountAmount(
                param.getPromotionDiscount() != null ? BaseConvertor.convertToBigDecimal(param.getPromotionDiscount().getAmount()) : null);
        amzOrderItem.setPromotionDiscountTaxCurrencyCode(
                param.getPromotionDiscountTax() != null ? param.getPromotionDiscountTax().getCurrencyCode() : null);
        amzOrderItem.setPromotionDiscountTaxAmount(param.getPromotionDiscountTax() != null
                ? BaseConvertor.convertToBigDecimal(param.getPromotionDiscountTax().getAmount()) : null);
        amzOrderItem.setPromotionIds(BaseConvertor.convertJsonStr(param.getPromotionIds()));
        amzOrderItem.setCodFeeCurrencyCode(param.getCoDFee() != null ? param.getCoDFee().getCurrencyCode() : null);
        amzOrderItem.setCodFeeAmount(param.getCoDFee() != null ? BaseConvertor.convertToBigDecimal(param.getCoDFee().getAmount()) : null);
        amzOrderItem.setCodFeeDiscountCurrencyCode(param.getCoDFeeDiscount() != null ? param.getCoDFeeDiscount().getCurrencyCode() : null);
        amzOrderItem.setCodFeeDiscountAmount(
                param.getCoDFeeDiscount() != null ? BaseConvertor.convertToBigDecimal(param.getCoDFeeDiscount().getAmount()) : null);
        amzOrderItem.setIsGift(param.getIsGift() != null ? BaseConvertor.stringToBoolean(param.getIsGift()) : null);
        amzOrderItem.setScheduledDeliveryStartDate(BaseConvertor.convertDate(param.getScheduledDeliveryStartDate()));
        amzOrderItem.setScheduledDeliveryEndDate(BaseConvertor.convertDate(param.getScheduledDeliveryEndDate()));
        amzOrderItem.setTaxCollectionModel(param.getTaxCollection() != null && param.getTaxCollection().getModel() != null
                ? param.getTaxCollection().getModel().getValue() : null);
        amzOrderItem
                .setTaxCollectionResponsibleParty(param.getTaxCollection() != null && param.getTaxCollection().getResponsibleParty() != null
                        ? param.getTaxCollection().getResponsibleParty().getValue() : null);
        amzOrderItem
                .setDeemedResellerCategory(param.getDeemedResellerCategory() != null ? param.getDeemedResellerCategory().getValue() : null);
        amzOrderItem.setIsBuyerRequestedCancel(param.getBuyerRequestedCancel() != null
                ? BaseConvertor.stringToBoolean(param.getBuyerRequestedCancel().getIsBuyerRequestedCancel()) : null);
        amzOrderItem
                .setBuyerCancelReason(param.getBuyerRequestedCancel() != null ? param.getBuyerRequestedCancel().getBuyerCancelReason() : null);
        amzOrderItem.setSerialNumbers(BaseConvertor.convertJsonStr(param.getSerialNumbers()));
        amzOrderItem.setSubstitutionPreferences(BaseConvertor.convertJsonStr(param.getSubstitutionPreferences()));
        amzOrderItem.setMeasurementUnit(param.getMeasurement() != null && param.getMeasurement().getUnit() != null
                ? param.getMeasurement().getUnit().getValue() : null);
        amzOrderItem.setMeasurementValue(param.getMeasurement() != null ? param.getMeasurement().getValue() : null);
        amzOrderItem.setShippingConstraintsPalletDelivery(
                param.getShippingConstraints() != null && param.getShippingConstraints().getPalletDelivery() != null
                        ? param.getShippingConstraints().getPalletDelivery().getValue() : null);
        amzOrderItem.setShippingConstraintsSignatureConfirmation(
                param.getShippingConstraints() != null && param.getShippingConstraints().getSignatureConfirmation() != null
                        ? param.getShippingConstraints().getSignatureConfirmation().getValue() : null);
        amzOrderItem.setShippingConstraintsRecipientIdentityVerification(
                param.getShippingConstraints() != null && param.getShippingConstraints().getRecipientIdentityVerification() != null
                        ? param.getShippingConstraints().getRecipientIdentityVerification().getValue() : null);
        amzOrderItem.setShippingConstraintsRecipientAgeVerification(
                param.getShippingConstraints() != null && param.getShippingConstraints().getRecipientAgeVerification() != null
                        ? param.getShippingConstraints().getRecipientAgeVerification().getValue() : null);
        amzOrderItem.setAmazonPrograms(BaseConvertor.convertJsonStr(param.getAmazonPrograms()));
        amzOrderItem.setSellerSku(param.getSellerSKU());

        return amzOrderItem;
    }

    AmzOrderItemLog toDTO(AmzOrderItem param);

    AmzOrderItemVO toVO(AmzOrderItem param);
}
