package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description VC源订单表
 * @date 2025/7/7 10:46
 */
@Setter
@Getter
@TableName(value = "oms_amz_vc_order_source_json")
public class AmzVcOrderSourceJsonPO {

    /**
     * id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 订单号
     */
    private String purchaseOrderNumber;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 订单状态
     */
    private String purchaseOrderState;

    /**
     * 订单json
     */
    private String orderJson;

    /**
     * 订单状态json
     */
    private String orderStatusJson;

    /**
     * 0:待解析订单,1:已解析,2解析失败,3无需处理
     */
    private Integer parseOrderStatus;

    /**
     * 解析失败次数
     */
    private Integer failCount;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
