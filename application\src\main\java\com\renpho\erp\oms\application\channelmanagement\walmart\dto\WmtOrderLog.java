package com.renpho.erp.oms.application.channelmanagement.walmart.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class WmtOrderLog {
	private Long id;
	/**
	 * 店铺id
	 */
	private String shopId;

	/**
	 * 卖家订单号
	 */
	private String purchaseOrderId;

	/**
	 * 与指定客户的销售订单关联的唯一 ID
	 */
	private String customerOrderId;

	/**
	 * 客户邮箱id
	 */
	private String customerEmailId;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 原始客户订单id
	 */
	private String originalCustomerOrderId;

	/**
	 * 客户提交销售订单的日期
	 */
	private LocalDateTime orderDate;

	/**
	 * 买家id
	 */
	private String buyerId;

	/**
	 * 市场信息
	 */
	private String mart;

	/**
	 * 是否是客人 0 否 1 是
	 */
	private Boolean isGuest;

	/**
	 * 发货手机号
	 */
	private String shippingInfoPhone;

	/**
	 * 预计送达时间
	 */
	private LocalDateTime shippingInfoEstimatedDeliveryDate;

	/**
	 * 预计发货时间
	 */
	private LocalDateTime shippingInfoEstimatedShipDate;

	/**
	 * 发货方式 Standard Express OneDay Freight WhiteGlove Value
	 */
	private String shippingInfoMethodCode;

	/**
	 * 发货城市
	 */
	private String postalAddressCity;

	/**
	 * 发货区
	 */
	private String postalAddressState;

	/**
	 * 发货邮政编码
	 */
	private String postalAddressPostalCode;

	/**
	 * 发国家
	 */
	private String postalAddressCountry;

	/**
	 * 支付方式数组json
	 */
	private String paymentTypes;

	/**
	 * 此订单的总费用
	 */
	private BigDecimal orderTotalAmount;

	/**
	 * 此订单的货币代码
	 */
	private String orderTotalCurrencyCode;

	/**
	 * 订单子支付信息json
	 */
	private String orderSubTotals;

	/**
	 * 订单接机人员名单json
	 */
	private String orderPickPersons;

	/**
	 * 发货方式类型
	 */
	private String shipNodeType;

	/**
	 * 发货方式名称
	 */
	private String shipNodeName;

	/**
	 * 发货方式id
	 */
	private String shipNodeId;

	private List<WmtOrderLineLog> wmtOrderLineLogList;
}
