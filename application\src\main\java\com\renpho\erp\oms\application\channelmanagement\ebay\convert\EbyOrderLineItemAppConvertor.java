package com.renpho.erp.oms.application.channelmanagement.ebay.convert;

import com.fasterxml.jackson.core.type.TypeReference;
import com.renpho.erp.apiproxy.ebay.model.orders.GetOrdersDetailResponse;
import com.renpho.erp.apiproxy.ebay.model.orders.GetOrdersListResponse;
import com.renpho.erp.oms.application.channelmanagement.ebay.dto.EbyOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayCancelRequestVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayOrderItemVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayPaymentDetailVO;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderLineItem;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.common.util.JsonUtil;
import com.renpho.karma.json.JSONKit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * eBay订单明细转换器
 */
@Component
public class EbyOrderLineItemAppConvertor {

    /**
     * 将DTO转换为订单明细列表
     * @param ebyOrderPushDTO eBay订单推送DTO
     * @return 订单明细列表
     */
    public static List<EbyOrderLineItem> toDomainList(EbyOrderPushDTO ebyOrderPushDTO) {
        if (ebyOrderPushDTO == null || ebyOrderPushDTO.getLineItems() == null) {
            return Collections.emptyList();
        }

        return ebyOrderPushDTO.getLineItems().stream()
                .map(EbyOrderLineItemAppConvertor::toDomain)
                .collect(Collectors.toList());
    }

    /**
     * 将单个明细DTO转换为领域模型
     * @param lineItem 明细DTO
     * @return 明细领域模型
     */
    public static EbyOrderLineItem toDomain(GetOrdersListResponse.LineItem lineItem) {
        if (lineItem == null) {
            return null;
        }

        EbyOrderLineItem ebyOrderLineItem = new EbyOrderLineItem();

        // 设置基本信息
        ebyOrderLineItem.setLineItemId(lineItem.getLineItemId());
        ebyOrderLineItem.setLegacyItemId(lineItem.getLegacyItemId());
        ebyOrderLineItem.setLegacyVariationId(lineItem.getLegacyVariationId());
        ebyOrderLineItem.setListingMarketplaceId(lineItem.getListingMarketplaceId());
        ebyOrderLineItem.setPurchaseMarketplaceId(lineItem.getPurchaseMarketplaceId());
        ebyOrderLineItem.setLineItemFulfillmentStatus(lineItem.getLineItemFulfillmentStatus());
        ebyOrderLineItem.setTitle(lineItem.getTitle());
        ebyOrderLineItem.setQuantity(StringUtils.isNotEmpty(lineItem.getQuantity()) ? Integer.valueOf(lineItem.getQuantity()) : null);
        ebyOrderLineItem.setSku(lineItem.getSku());
        ebyOrderLineItem.setSoldFormat(lineItem.getSoldFormat());

        // 处理价格信息
        if (lineItem.getLineItemCost() != null) {
            ebyOrderLineItem.setLineItemCostCurrency(lineItem.getLineItemCost().getCurrency());
            ebyOrderLineItem.setLineItemCostValue(new BigDecimal(lineItem.getLineItemCost().getValue()));
        }



        // 处理JSON格式数据
        if(lineItem.getDeliveryCost() != null) {
            ebyOrderLineItem.setDeliveryCost(JSONKit.toJSONString(lineItem.getDeliveryCost()));
        }

        if (lineItem.getDiscountedLineItemCost() != null) {
            ebyOrderLineItem.setDiscountedLineItemCost(JSONKit.toJSONString(lineItem.getDiscountedLineItemCost()));
        }

        if (lineItem.getTotal() != null) {
            ebyOrderLineItem.setTotal(JSONKit.toJSONString(lineItem.getTotal()));
        }

        if (lineItem.getLineItemFulfillmentInstructions() != null) {
            ebyOrderLineItem.setLineItemFulfillmentInstructions(JSONKit.toJSONString(lineItem.getLineItemFulfillmentInstructions()));
        }

        if (lineItem.getGiftDetails() != null) {
            ebyOrderLineItem.setGiftDetails(JSONKit.toJSONString(lineItem.getGiftDetails()));
        }

        if (lineItem.getItemLocation() != null) {
            ebyOrderLineItem.setItemLocation(JSONKit.toJSONString(lineItem.getItemLocation()));
        }

        if (lineItem.getProperties() != null) {
            ebyOrderLineItem.setProperties(JSONKit.toJSONString(lineItem.getProperties()));
        }

        if (lineItem.getLinkedOrderLineItems() != null) {
            ebyOrderLineItem.setLinkedOrderLineItems(JSONKit.toJSONString(lineItem.getLinkedOrderLineItems()));
        }

        if(lineItem.getAppliedPromotions() != null) {
            ebyOrderLineItem.setAppliedPromotions(JSONKit.toJSONString(lineItem.getAppliedPromotions()));
        }

        if (lineItem.getEbayCollectAndRemitTaxes() != null) {
            ebyOrderLineItem.setEbayCollectAndRemitTaxes(JSONKit.toJSONString(lineItem.getEbayCollectAndRemitTaxes()));
        }

        if (lineItem.getEbayCollectedCharges() != null){
            ebyOrderLineItem.setEbayCollectedCharges(JSONKit.toJSONString(lineItem.getEbayCollectedCharges()));
        }

        if (lineItem.getRefunds() != null){
            ebyOrderLineItem.setRefunds(JSONKit.toJSONString(lineItem.getRefunds()));
        }

        if (lineItem.getTaxes() != null){
            ebyOrderLineItem.setTaxes(JSONKit.toJSONString(lineItem.getTaxes()));
        }

        if(lineItem.getVariationAspects() != null){
            ebyOrderLineItem.setVariationAspects(JSONKit.toJSONString(lineItem.getVariationAspects()));
        }

        return ebyOrderLineItem;
    }

	/**
	 * 明细领域模型转订单行出参
	 * @param ebyOrderLineItem 明细领域模型
	 * @return 订单行出参
	 */
	public static EbayOrderItemVO toVO(EbyOrderLineItem ebyOrderLineItem) {
		EbayOrderItemVO ebayOrderItemVO = new EbayOrderItemVO();
		ebayOrderItemVO.setLineItemId(ebyOrderLineItem.getLineItemId());
		ebayOrderItemVO.setLegacyItemId(ebyOrderLineItem.getLegacyItemId());
		ebayOrderItemVO.setSku(ebyOrderLineItem.getSku());
		ebayOrderItemVO.setLineItemFulfillmentStatus(ebyOrderLineItem.getLineItemFulfillmentStatus());
		ebayOrderItemVO.setQuantity(ebyOrderLineItem.getQuantity());
		ebayOrderItemVO.setCurrency(ebyOrderLineItem.getLineItemCostCurrency());
		ebayOrderItemVO.setLineItemCostValue(ebyOrderLineItem.getLineItemCostValue());
		ebayOrderItemVO.setProperties(JsonUtil.parseJson(ebyOrderLineItem.getProperties(), EbayOrderItemVO.ItemProperties.class));

		// 折扣金额与币种（从 JSON 中解析）
		List<GetOrdersListResponse.AppliedPromotion> appliedPromotions = JsonUtil.parseArray(ebyOrderLineItem.getAppliedPromotions(),
				new TypeReference<List<GetOrdersListResponse.AppliedPromotion>>() {
				});
		if (CollectionUtils.isNotEmpty(appliedPromotions)) {
			BigDecimal discount = BigDecimal.ZERO;
			for (GetOrdersListResponse.AppliedPromotion appliedPromotion : appliedPromotions) {
                discount = discount.add(new BigDecimal(appliedPromotion.getDiscountAmount().getValue()));
			}
			ebayOrderItemVO.setDiscountAmount(discount);
		}

		// 运费金额与币种（从 JSON 中解析）
		GetOrdersListResponse.DeliveryCost deliveryCost = JsonUtil.parseJson(ebyOrderLineItem.getDeliveryCost(),
				GetOrdersListResponse.DeliveryCost.class);
		if (deliveryCost != null && deliveryCost.getShippingCost() != null && deliveryCost.getShippingCost().getValue() != null) {
			ebayOrderItemVO.setShippingPrice(new BigDecimal(deliveryCost.getShippingCost().getValue()));
		}

		// eBay代收税金额与币种（从 JSON 中解析）
		List<GetOrdersListResponse.EbayCollectAndRemitTaxes> ebayCollectAndRemitTaxes = JsonUtil.parseArray(
				ebyOrderLineItem.getEbayCollectAndRemitTaxes(), new TypeReference<List<GetOrdersListResponse.EbayCollectAndRemitTaxes>>() {
				});
		if (CollectionUtils.isNotEmpty(ebayCollectAndRemitTaxes)) {
			BigDecimal collectAndRemitTax = BigDecimal.ZERO;
			for (GetOrdersListResponse.EbayCollectAndRemitTaxes ebayCollectAndRemitTaxe : ebayCollectAndRemitTaxes) {
                collectAndRemitTax = collectAndRemitTax.add(new BigDecimal(ebayCollectAndRemitTaxe.getAmount().getValue()));
			}
			ebayOrderItemVO.setCollectAndRemitTax(collectAndRemitTax);
		}

		// 退款金额与币种（从 JSON 中解析）
		List<GetOrdersListResponse.Refund> refunds = JsonUtil.parseArray(ebyOrderLineItem.getRefunds(),
				new TypeReference<List<GetOrdersListResponse.Refund>>() {
				});
		if (CollectionUtils.isNotEmpty(refunds)) {
			BigDecimal refund = BigDecimal.ZERO;
			for (GetOrdersListResponse.Refund refund1 : refunds) {
                refund = refund.add(new BigDecimal(refund1.getAmount().getValue()));
			}
			ebayOrderItemVO.setRefundAmount(refund);
		}

		// 发货时间 从json 中解析
		GetOrdersListResponse.LineItemFulfillmentInstruction lineItemFulfillmentInstruction = JsonUtil.parseJson(ebyOrderLineItem.getLineItemFulfillmentInstructions(), GetOrdersListResponse.LineItemFulfillmentInstruction.class);
		if (lineItemFulfillmentInstruction != null) {
			ebayOrderItemVO.setShipmentTime(DateUtil.parse(lineItemFulfillmentInstruction.getShipByDate()));
		}
		return ebayOrderItemVO;
	}

	/**
	 * 转换付款明细出参
	 * @param ebyOrderAggRoot 订单聚合根
	 * @return 付款明细出参
	 */
	public static List<EbayPaymentDetailVO> toPaymentDetailVO(EbyOrderAggRoot ebyOrderAggRoot) {
		List<EbayPaymentDetailVO> ebayPaymentDetailVOs = new ArrayList<>();
		GetOrdersDetailResponse.PaymentSummary paymentSummary = JsonUtil.parseJson(ebyOrderAggRoot.getPaymentSummary(),
				GetOrdersDetailResponse.PaymentSummary.class);
		if (paymentSummary != null && !CollectionUtils.isEmpty(paymentSummary.getRefunds())) {
			for (GetOrdersDetailResponse.Refund refund : paymentSummary.getRefunds()) {
				EbayPaymentDetailVO vo = new EbayPaymentDetailVO();
				vo.setTime(DateUtil.parse(refund.getRefundDate()));
				vo.setType("Refund");
				vo.setStatus(refund.getRefundStatus());
				vo.setAmount(refund.getAmount().getValue());
				vo.setCurrency(refund.getAmount().getCurrency());
				vo.setReferId(refund.getRefundReferenceId());
				ebayPaymentDetailVOs.add(vo);
			}
		}
		if (paymentSummary != null && !CollectionUtils.isEmpty(paymentSummary.getPayments())) {
			for (GetOrdersDetailResponse.Payment payment : paymentSummary.getPayments()) {
				EbayPaymentDetailVO vo = new EbayPaymentDetailVO();
				vo.setTime(DateUtil.parse(payment.getPaymentDate()));
				vo.setType("Payment");
				vo.setStatus(payment.getPaymentStatus());
				vo.setPaymentMethod(payment.getPaymentMethod());
				vo.setAmount(payment.getAmount().getValue());
				vo.setCurrency(payment.getAmount().getCurrency());
				vo.setReferId(payment.getPaymentReferenceId());
				ebayPaymentDetailVOs.add(vo);
			}
		}
		ebayPaymentDetailVOs.sort(Comparator.comparing(EbayPaymentDetailVO::getTime));
		return ebayPaymentDetailVOs;
	}

	/**
	 * 转换取消状态列表
	 * @param ebyOrderAggRoot 订单聚合根
	 * @return 取消状态列表
	 */
	public static List<EbayCancelRequestVO> toCancelRequestVO(EbyOrderAggRoot ebyOrderAggRoot) {
		List<EbayCancelRequestVO> ebayCancelRequestVos = new ArrayList<>();
		GetOrdersListResponse.CancelStatus cancelStatus = JsonUtil.parseJson(ebyOrderAggRoot.getCancelStatus(),
				GetOrdersListResponse.CancelStatus.class);
		if (cancelStatus != null && CollectionUtils.isNotEmpty(cancelStatus.getCancelRequests())) {
			for (GetOrdersListResponse.CancelRequests cancelRequests: cancelStatus.getCancelRequests()){
				EbayCancelRequestVO ebayCancelRequestVO = getEbayCancelRequestVO(cancelRequests);
				ebayCancelRequestVos.add(ebayCancelRequestVO);
			}
		}
		return ebayCancelRequestVos;
	}

	private static @NotNull EbayCancelRequestVO getEbayCancelRequestVO(GetOrdersListResponse.CancelRequests cancelRequests) {
		EbayCancelRequestVO ebayCancelRequestVo = new EbayCancelRequestVO();
		ebayCancelRequestVo.setCancelRequestId(cancelRequests.getCancelRequestId());
		ebayCancelRequestVo.setCancelRequestedDate(DateUtil.parse(cancelRequests.getCancelRequestedDate()));
		ebayCancelRequestVo.setCancelInitiator(cancelRequests.getCancelInitiator());
		ebayCancelRequestVo.setCancelReason(cancelRequests.getCancelReason());
		ebayCancelRequestVo.setCancelRequestState(cancelRequests.getCancelRequestState());
		ebayCancelRequestVo.setCancelCompletedDate(DateUtil.parse(cancelRequests.getCancelCompletedDate()));
		return ebayCancelRequestVo;
	}
}