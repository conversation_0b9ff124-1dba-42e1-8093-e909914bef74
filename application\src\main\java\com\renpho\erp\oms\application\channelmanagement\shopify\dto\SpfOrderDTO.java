package com.renpho.erp.oms.application.channelmanagement.shopify.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class SpfOrderDTO {
	/**
	 * 同步时间
	 */
	private String syncTime;

	private Integer storeId;
	private String shopUrl;
	private Long id;
	private String admin_graphql_api_id;
	private Integer app_id;
	private String browser_ip;
	private boolean buyer_accepts_marketing;
	private String cancel_reason;
	private String cancelled_at;
	private String cart_token;
	private Long checkout_id;
	private String checkout_token;
	private ClientDetail client_details;
	private String closed_at;
	private String company;
	private String confirmation_number;
	private boolean confirmed;
	private String contact_email;
	private String created_at;
	private String currency;
	private String current_subtotal_price;
	private MoneySet current_subtotal_price_set;
	private MoneySet current_total_additional_fees_set;
	private String current_total_discounts;
	private MoneySet current_total_discounts_set;
	private MoneySet current_total_duties_set;
	private String current_total_price;
	private MoneySet current_total_price_set;
	private String current_total_tax;
	private MoneySet current_total_tax_set;
	private String customer_locale;
	private String device_id;
	private List<DiscountCode> discount_codes;
	private Boolean duties_included;
	private String email;
	private boolean estimated_taxes;
	private String financial_status;
	private String fulfillment_status;
	private String landing_site;
	private String landing_site_ref;
	private String location_id;
	private String merchant_business_entity_id;
	private String merchant_of_record_app_id;
	private String name;
	private String note;
	private List<Attribute> note_attributes;
	private Integer number;
	private Integer order_number;
	private String order_status_url;
	private MoneySet original_total_additional_fees_set;
	private MoneySet original_total_duties_set;
	private List<String> payment_gateway_names;
	private String phone;
	private String po_number;
	private String presentment_currency;
	private String processed_at;
	private String reference;
	private String referring_site;
	private String source_identifier;
	private String source_name;
	private String source_url;
	private String subtotal_price;
	private MoneySet subtotal_price_set;
	private String tags;
	private boolean tax_exempt;
	private List<Tax> tax_lines;
	private boolean taxes_included;
	private boolean test;
	private String token;
	private MoneySet total_cash_rounding_payment_adjustment_set;
	private MoneySet total_cash_rounding_refund_adjustment_set;
	private String total_discounts;
	private MoneySet total_discounts_set;
	private String total_line_items_price;
	private MoneySet total_line_items_price_set;
	private String total_outstanding;
	private String total_price;
	private MoneySet total_price_set;
	private String total_tax;
	private MoneySet total_tax_set;
	private String total_tip_received;
	private Integer total_weight;
	private String updated_at;
	private String user_id;
	private Address billing_address;
	private Customer customer;
	private List<DiscountApplication> discount_applications;
	private List<Fulfillment> fulfillments;
	private List<LineItem> line_items;
	private List<String> payment_terms;
	private List<Refund> refunds;
	private Address shipping_address;
	private List<ShippingItem> shipping_lines;

	@Data
	public static class ClientDetail {
		private String accept_language;
		private Integer browser_height;
		private String browser_ip;
		private Integer browser_width;
		private String session_hash;
		private String user_agent;
	}

	@Data
	public static class DiscountCode {
		private String code;
		private String amount;
		private String type;
	}

	@Data
	public static class MoneySet {
		private Money shop_money;
		private Money presentment_money;
	}

	@Data
	public static class Money {
		private String amount;
		private String currency_code;
	}

	@Data
	public static class Attribute {
		private String name;
		private String value;
	}

	@Data
	public static class Tax {
		private String price;
		private String rate;
		private String title;
		private MoneySet price_set;
		private boolean channel_liable;
	}

	@Data
	public static class Address {
		private String first_name;
		private String address1;
		private String phone;
		private String city;
		private String zip;
		private String province;
		private String country;
		private String last_name;
		private String address2;
		private String company;
		private String latitude;
		private String longitude;
		private String name;
		private String country_code;
		private String province_code;
	}

	@Data
	public static class Customer {
		private Long id;
		private String email;
		private String created_at;
		private String updated_at;
		private String first_name;
		private String last_name;
		private String state;
		private String note;
		private boolean verified_email;
		private String multipass_identifier;
		private boolean tax_exempt;
		private String phone;
		private MarketingConsent email_marketing_consent;
		private MarketingConsent sms_marketing_consent;
		private String tags;
		private String currency;
		private List<String> tax_exemptions;
		private String admin_graphql_api_id;
		private DefaultAddress default_address;
	}

	@Data
	public static class DiscountApplication {
		private String target_type;
		private String type;
		private String value;
		private String value_type;
		private String allocation_method;
		private String target_selection;
		private String code;
	}

	@Data
	public static class MarketingConsent {
		private String state;
		private String opt_in_level;
		private String consent_updated_at;
		private String consent_collected_from;
	}

	@Data
	public static class DefaultAddress extends Address {
		private Long id;
		private Long customer_id;
		private String city;
		private String country_name;

		@JsonProperty("default")
		private boolean defaulted;
	}

	@Data
	public static class Property {
		private String name;
		private String value;
	}

	@Data
	public static class DiscountAllocation {
		private String amount;
		private MoneySet amount_set;
		private Integer discount_application_index;
	}

	@Data
	public static class Fulfillment {
		private Long id;
		private String admin_graphql_api_id;
		private String created_at;
		private Long location_id;
		private String name;
		private Long order_id;
		private Map<String, Object> origin_address;
		private Receipt receipt;
		private String service;
		private String shipment_status;
		private String status;
		private String tracking_company;
		private String tracking_number;
		private List<String> tracking_numbers;
		private String tracking_url;
		private List<String> tracking_urls;
		private String updated_at;
		private List<LineItem> line_items;
	}

	@Data
	public static class LineItem {
		private Long id;
		private String admin_graphql_api_id;
		private List<String> attributed_staffs;
		private Integer current_quantity;
		private Integer fulfillable_quantity;
		private String fulfillment_service;
		private String fulfillment_status;
		private boolean gift_card;
		private Integer grams;
		private String name;
		private String pre_tax_price;
		private MoneySet pre_tax_price_set;
		private String price;
		private MoneySet price_set;
		private boolean product_exists;
		private Long product_id;
		private List<Property> properties;
		private Integer quantity;
		private boolean requires_shipping;
		private String sku;
		private boolean taxable;
		private String title;
		private String total_discount;
		private MoneySet total_discount_set;
		private String variant_id;
		private String variant_inventory_management;
		private String variant_title;
		private String vendor;
		private List<Tax> tax_lines;
		private List<String> duties;
		private List<DiscountAllocation> discount_allocations;
	}

	@Data
	public static class Refund {
		private Long id;
		private String admin_graphql_api_id;
		private String created_at;
		private String note;
		private String order_id;
		private String processed_at;
		private boolean restock;
		private MoneySet total_duties_set;
		private Long user_id;
		private List<Adjustment> order_adjustments;
		private List<RefundTransaction> transactions;
		private List<RefundItem> refund_line_items;
		private List<String> duties;
	}

	@Data
	public static class Adjustment {
		private Long id;
		private String amount;
		private MoneySet amount_set;
		private String kind;
		private Long order_id;
		private String reason;
		private Long refund_id;
		private String tax_amount;
		private MoneySet tax_amount_set;
	}

	@Data
	public static class RefundTransaction {
		private Long id;
		private String admin_graphql_api_id;
		private String amount;
		private String authorization;
		private String created_at;
		private String currency;
		private String device_id;
		private String error_code;
		private String gateway;
		private String kind;
		private String location_id;
		private String message;
		private Long order_id;
		private Long parent_id;
		private String payment_id;
		private String processed_at;
		private Receipt receipt;
		private String source_name;
		private String status;
		private boolean test;
		private Long user_id;
	}

	@Data
	public static class Receipt {
		private String reference_id;
		private String currency;
		private String amount;
		private String type;
		private Integer fee_refunded;
		private String created;
		private String id;
		private String seller_id;
	}

	@Data
	public static class RefundItem {
		private Long id;
		private Long line_item_id;
		private Long location_id;
		private Integer quantity;
		private String restock_type;
		private String subtotal;
		private MoneySet subtotal_set;
		private String total_tax;
		private MoneySet total_tax_set;
		private LineItem line_item;
	}

	@Data
	public static class ShippingItem {
		private Long id;
		private String carrier_identifier;
		private String code;
		private String discounted_price;
		private MoneySet discounted_price_set;
		private Boolean is_removed;
		private String phone;
		private String price;
		private MoneySet price_set;
		private String requested_fulfillment_service_id;
		private String source;
		private String title;
		private List<Tax> tax_lines;
		private List<DiscountAllocation> discount_allocations;
	}
}
