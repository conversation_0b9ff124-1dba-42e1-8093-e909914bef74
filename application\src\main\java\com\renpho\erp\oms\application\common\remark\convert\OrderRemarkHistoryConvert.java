package com.renpho.erp.oms.application.common.remark.convert;

import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderRemarkPO;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.po.ReturnOrderRemarkPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderRemarkPo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

/**
 * @desc: OrderRemarkHistoryConvert
 * @time: 2025-06-20 14:52:42
 * @author: Alina
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface OrderRemarkHistoryConvert {

    OrderRemarkHistoryVO toVO(SaleOrderRemarkPo remark);

    OrderRemarkHistoryVO toVO(ReturnOrderRemarkPO remark);

    @Mapping(source = "remark", target = "msg")
    OrderRemarkHistoryVO toVO(B2bOrderRemarkPO remark);

}
