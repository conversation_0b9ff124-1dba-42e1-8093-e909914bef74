package com.renpho.erp.oms.domain.vc.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * VC产品值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class VcProduct {

	/**
	 * 采购SKU
	 */
	private String psku;

	/**
	 * 条码
	 */
	private String barcode;

	/**
	 * 装箱数量（每箱的psku数量）
	 */
	private Integer numberOfUnitsPerBox;

	/**
	 * 图片id
	 */
	private String imageId;

}
