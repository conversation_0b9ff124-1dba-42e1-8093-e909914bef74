package com.renpho.erp.oms.application.channelmanagement.temu.convert;


import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.application.channelmanagement.temu.dto.TemOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.temu.vo.TemOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrder;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderStatus;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.karma.json.JSONKit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shenyu.common.utils.DateUtils;

import java.util.Objects;

public class TemOrderAppConvertor {
    public static TemOrderAggRoot toDomain(TemOrderPushDTO temOrderPushDTO) {
        if (Objects.nonNull(temOrderPushDTO)) {
            TemOrderAggRoot temOrderAggRoot = new TemOrderAggRoot();

            // 基本信息
            temOrderAggRoot.setStoreId(temOrderPushDTO.getStoreId());
            temOrderAggRoot.setShopId(temOrderPushDTO.getShopId());
            temOrderAggRoot.setParentOrderSn(temOrderPushDTO.getParentOrderMap().getParentOrderSn());
            temOrderAggRoot.setParentOrderStatus(TemOrderStatus.enumOf(temOrderPushDTO.getParentOrderMap().getParentOrderStatus()));
            temOrderAggRoot.setParentShippingTime(DateUtil.convertToDateTime(temOrderPushDTO.getParentOrderMap().getParentShippingTime()));
            temOrderAggRoot.setParentOrderPendingFinishTime(DateUtil.convertToDateTime(temOrderPushDTO.getParentOrderMap().getParentOrderPendingFinishTime()));
            temOrderAggRoot.setRegionName1(temOrderPushDTO.getParentOrderMap().getRegionName1());
            temOrderAggRoot.setRegionName2(temOrderPushDTO.getParentOrderMap().getRegionName2());
            temOrderAggRoot.setRegionName3(temOrderPushDTO.getParentOrderMap().getRegionName3());
            temOrderAggRoot.setLatestDeliveryTime(DateUtil.convertToDateTime(temOrderPushDTO.getParentOrderMap().getLatestDeliveryTime()));
            temOrderAggRoot.setParentOrderLabel(CollectionUtils.isNotEmpty(temOrderPushDTO.getParentOrderMap().getParentOrderLabel()) ? JSONKit.toJSONString(temOrderPushDTO.getParentOrderMap().getParentOrderLabel()) : null);
            temOrderAggRoot.setFulfillmentWarning(CollectionUtils.isNotEmpty(temOrderPushDTO.getParentOrderMap().getFulfillmentWarning()) ? JSONKit.toJSONString(temOrderPushDTO.getParentOrderMap().getFulfillmentWarning()) : null);
            temOrderAggRoot.setParentOrderTime(DateUtil.convertToDateTime(temOrderPushDTO.getParentOrderMap().getParentOrderTime()));
            temOrderAggRoot.setRegionId(temOrderPushDTO.getParentOrderMap().getRegionId().intValue());
            temOrderAggRoot.setSiteId(temOrderPushDTO.getParentOrderMap().getSiteId().intValue());
            temOrderAggRoot.setExpectShipLatestTime(DateUtil.convertToDateTime(temOrderPushDTO.getParentOrderMap().getExpectShipLatestTime()));
            temOrderAggRoot.setHasShippingFee(temOrderPushDTO.getParentOrderMap().getHasShippingFee());

            // 默认值设置
            temOrderAggRoot.setTransOmsStatus(TransOmsStatus.NOT_TRANS); // 默认为未转换状态
			temOrderAggRoot.setLastMpdsSyncOrderTime(
					StrUtil.isNotEmpty(temOrderPushDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(temOrderPushDTO.getSyncTime()) : null);

            return temOrderAggRoot;
        }
        return null;
    }

    public static TemOrderVO toVO(TemOrder temOrder) {
        if ( temOrder == null ) {
            return null;
        }

        TemOrderVO temOrderVO = new TemOrderVO();

        temOrderVO.setId( temOrder.getId() );
        temOrderVO.setParentOrderSn( temOrder.getParentOrderSn() );
        temOrderVO.setParentOrderStatus( temOrder.getParentOrderStatus() );
        temOrderVO.setRegionName1( temOrder.getRegionName1() );
        temOrderVO.setRegionName2( temOrder.getRegionName2() );
        temOrderVO.setRegionName3( temOrder.getRegionName3() );
        temOrderVO.setExpectShipLatestTime( temOrder.getExpectShipLatestTime() );
        temOrderVO.setLatestDeliveryTime( temOrder.getLatestDeliveryTime() );
        temOrderVO.setParentOrderTime( temOrder.getParentOrderTime() );
        temOrderVO.setParentShippingTime( temOrder.getParentShippingTime() );
        temOrderVO.setParentOrderPendingFinishTime(temOrder.getParentOrderPendingFinishTime());
        return temOrderVO;
    }
}