package com.renpho.erp.oms.adapter.web.controller.platform;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.adapter.manager.PlatformManager;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;

import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;

import com.renpho.erp.oms.domain.platform.query.PlatformQuery;

import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/platform/**")
@RequestMapping("/platform")
@AllArgsConstructor
public class PlatformOrderWebController {
	private final PlatformManager platformManager;

	/**
	 * 分页查询
	 * @param platformPageQuery
	 * @return R<Paging<Object>>
	 */
	@PostMapping("/page")
	public R<Paging<PlatformOrderVO>> page(@RequestBody @Valid PlatformPageQuery platformPageQuery) {
		return platformManager.getService(platformPageQuery.getChannleCode()).page(platformPageQuery);
	}

	/**
	 * 获取渠道单
	 * @param platformQuery
	 * @return R
	 */
	@PostMapping("/get")
	public R get(@RequestBody @Valid PlatformQuery platformQuery) {
        // 门店id和渠道订单id不为空将渠道表id设置成null,这两个值不为null说明是销售列表跳转到渠道单详情
		if (platformQuery.getStoreId() != null && StrUtil.isNotBlank(platformQuery.getChannelOrderNo())) {
            platformQuery.setOrderId(null);
        }

		return platformManager.getService(platformQuery.getChannelCode()).get(platformQuery);
	}

}
