package com.renpho.erp.oms.application.returnorder.converter;

import com.renpho.erp.oms.application.returnorder.command.ResendOrderAddCmd;
import com.renpho.erp.oms.domain.salemanagement.*;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @desc: 新建RAM补发订单转换器
 *
 * @time: 2025-03-19 15:58:46
 * @author: Alina
 */

public class ResendOrderAddConvertor {

	public static SaleOrderAggRoot convert(ResendOrderAddCmd resendOrderAddCmd, SaleOrderAggRoot saleOrderAggRoot) {

		Map<Long, SaleOrderItem> saleOrderItemMap = saleOrderAggRoot.getItems()
			.stream()
			.collect(Collectors.toMap(saleOrderItem -> saleOrderItem.getId().getId(), item -> item));

		// 构建订单明细
		List<SaleOrderItem> saleOrderItemList = resendOrderAddCmd.getItems().stream().map(resendOrderItemAddCmd -> {
			// 从缓存的 Map 中获取原始 SaleOrderItem
			SaleOrderItem originalItem = saleOrderItemMap.get(resendOrderItemAddCmd.getOrderItemId());
			if (originalItem == null) {
				throw new BusinessException("ORDER_ITEM_NOT_FOUND");
			}
			if (resendOrderItemAddCmd.getResendQuantity() <= 0
					|| resendOrderItemAddCmd.getResendQuantity() > originalItem.getQuantityShipment()) {
				throw new BusinessException("RESEND_QUANTITY_CHECK");
			}
			return SaleOrderItem.builder()
				.quantityShipment(resendOrderItemAddCmd.getResendQuantity())
				.quantityOrdered(resendOrderItemAddCmd.getResendQuantity())
				.channelOrderLineId(originalItem.getChannelOrderLineId())
				.msku(originalItem.getMsku())
				.currency(originalItem.getCurrency())
				.productPrice(BigDecimal.ZERO)
				.productTax(BigDecimal.ZERO)
				.productDiscount(BigDecimal.ZERO)
				.productDiscountTax(BigDecimal.ZERO)
				.freightAmount(BigDecimal.ZERO)
				.freightTax(BigDecimal.ZERO)
				.freightDiscount(BigDecimal.ZERO)
				.freightDiscountTax(BigDecimal.ZERO)
				.totalAmount(BigDecimal.ZERO)
				.status(OrderItemStatus.TO_BE_SHIPPED)
				.product(originalItem.getProduct())
				.build();
		}).filter(Objects::nonNull).collect(Collectors.toList());


		// 订单履约
		SaleOrderFulfillment saleOrderFulfillment = SaleOrderFulfillment.builder()
			// 最晚发货时间，平台要求履约时效
			.fulfillmentType(FulfillmentType.MERCHANT)
			// 自发货待上传
			.uploadTrackStatus(UploadTrackStatus.NOT_REQUIRED)
			.build();

		// 构建订单
		SaleOrderAggRoot newSaleOrderAggRoot = SaleOrderAggRoot.builder()
			.orderStatus(OrderStatus.PENDING)
			.orderType(OrderType.STANDARD)
			.orderSource(OrderSource.RMA)
			.channelOrderNo(saleOrderAggRoot.getChannelOrderNo())
			.referOrderNo(saleOrderAggRoot.getReferOrderNo())
			.sampleMark(saleOrderAggRoot.getSampleMark())
			.currency(saleOrderAggRoot.getCurrency())
			.orderedTime(saleOrderAggRoot.getOrderedTime())
			.paidTime(saleOrderAggRoot.getPaidTime())
			.store(saleOrderAggRoot.getStore())
			.fulfillment(saleOrderFulfillment)
			.customerServiceRemark(resendOrderAddCmd.getRemark())
			.address(saleOrderAggRoot.getAddress())
			.items(saleOrderItemList)
			.build();

		// 计算订单重量体积
		newSaleOrderAggRoot.getFulfillment().calculateSizeAndWeight(newSaleOrderAggRoot.getItems());

		// 计算金额
		newSaleOrderAggRoot.calculateAmount();
		return newSaleOrderAggRoot;
	}
}
