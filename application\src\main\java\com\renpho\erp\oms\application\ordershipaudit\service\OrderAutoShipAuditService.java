package com.renpho.erp.oms.application.ordershipaudit.service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.chain.OrderAutoShipAuditChain;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.common.exception.BizErrorCode;
import com.renpho.erp.oms.infrastructure.common.exception.DataInsufficiencyException;
import com.renpho.erp.oms.infrastructure.common.util.BatchUtils;
import com.renpho.erp.oms.infrastructure.config.NacosOrderConfig;
import com.xxl.job.core.context.XxlJobHelper;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单-自动发货审核（分仓分物流） 应用服务
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderAutoShipAuditService {

	private final SaleOrderRepository saleOrderRepository;
	private final NacosOrderConfig nacosOrderConfig;
	private final OrderAutoShipAuditChain orderAutoShipAuditChain;

	/**
	 * 订单-自动发货审核（分仓分物流）
	 * @param cmd 指令
	 */
	public void autoShipAudit(AutoShipAuditCmd cmd) {
		// 冷静期Map，key为渠道编码 value为冷静期（单位分钟）
		Map<String, Integer> coolingOffPeriodMap = nacosOrderConfig.getAutoAllocateFulfillmentCoolingOffPeriodMap();
		// 冷静期组Map，key为冷静期（单位分钟），value为渠道编码集
		Map<Integer, Set<String>> coolingOffPeriodGroupMap = this.buildCoolingOffPeriodGroupMap(coolingOffPeriodMap);
		// 按冷静期分组，分批查询
		coolingOffPeriodGroupMap.forEach((minutes, channelCodes) -> BatchUtils.batchDeal(
				// 根据 lastId 和 size 获取列表
				(Long lastId, Integer size) -> {
					// 构建待自动分仓分物流（自动发货审核）查询参数
					SaleOrderRepository.WaitingAutoAllocateFulfillmentParam param = this.buildParam(cmd, minutes, channelCodes, lastId,
							size);
					// 分批查询
					return saleOrderRepository.findOrderIdsOfWaitingAutoAllocateFulfillment(param);
				},
				// 对获取到的这一批列表进行处理
				this::autoShipAudit,
				// 恒等函数
				Function.identity(),
				// 批处理大小
				Optional.ofNullable(cmd.getSize()).orElse(1000)));
	}

	/**
	 * 订单-自动发货审核（分仓分物流）
	 * @param orderIds 订单id集
	 */
	private void autoShipAudit(List<Long> orderIds) {
		orderIds.forEach(orderId -> {
			// 订单聚合根
			SaleOrderAggRoot orderAggRoot = saleOrderRepository.findById(SaleOrderAggRoot.OrderId.of(orderId))
				.orElseThrow(() -> new DataInsufficiencyException(BizErrorCode.DATA_INSUFFICIENCY, orderId.toString()));
			try {
				// 订单自动发货审核-链处理
				orderAutoShipAuditChain.process(orderAggRoot);
			}
			catch (Exception e) {
				String msg = String.format("订单号:[%s]自动发货审核异常：", orderAggRoot.getOrderNo());
				XxlJobHelper.log(msg);
				XxlJobHelper.log(e);
				log.error(msg, e);
			}
		});
	}

	/**
	 * 构建待自动分仓分物流（自动发货审核）查询参数
	 * @param cmd 指令
	 * @param minutes 冷静期（分钟）
	 * @param channelCodes 渠道编码集
	 * @param lastId 上一批最后一条的id
	 * @param size 分批的条数
	 * @return WaitingAutoAllocateFulfillmentParam
	 */
	private @NotNull SaleOrderRepository.WaitingAutoAllocateFulfillmentParam buildParam(AutoShipAuditCmd cmd, Integer minutes,
			Set<String> channelCodes, Long lastId, Integer size) {
		// 构建待自动分仓分物流（自动发货审核）查询参数
		SaleOrderRepository.WaitingAutoAllocateFulfillmentParam param = new SaleOrderRepository.WaitingAutoAllocateFulfillmentParam();
		// 冷静期-时间
		param.setCoolingOffPeriodTime(LocalDateTime.now().minusMinutes(minutes));
		// 渠道编码
		param.setChannelCodes(channelCodes);
		// 销售单号集
		param.setOrderNos(cmd.getOrderNos());
		// 销售单号集是否非空
		boolean orderNosIsNotEmpty = CollectionUtils.isNotEmpty(cmd.getOrderNos());
		// 是否忽略自动分仓分物流（发货审核）状态
		param.setIgnoreAutoAllocateFulfillment(orderNosIsNotEmpty && cmd.isIgnoreAutoAllocateFulfillment());
		// 是否忽略冷静期
		param.setIgnoreCoolingOffPeriod(orderNosIsNotEmpty && cmd.isIgnoreCoolingOffPeriod());
		// 上一批最后一条的id
		param.setLastId(lastId);
		// 分批的条数
		param.setSize(size);
		return param;
	}

	/**
	 * 获取冷静期组Map
	 * @param coolingOffPeriodMap key为渠道编码 value为冷静期（单位分钟）
	 * @return Map，key为冷静期（单位分钟），value为渠道编码集
	 */
	public Map<Integer, Set<String>> buildCoolingOffPeriodGroupMap(Map<String, Integer> coolingOffPeriodMap) {
		Map<Integer, Set<String>> coolingOffPeriodGroupMap = new HashMap<>();
		// 冷静期配置-为空
		if (MapUtils.isEmpty(coolingOffPeriodMap)) {
			// 所有渠道编码
			Set<String> channelCodes = Arrays.stream(SaleChannelType.values()).map(SaleChannelType::getValue).collect(Collectors.toSet());
			// 冷静期默认为60min（1h）
			coolingOffPeriodGroupMap.put(60, channelCodes);
		}
		// 冷静期配置-非空
		else {
			for (Map.Entry<String, Integer> entry : coolingOffPeriodMap.entrySet()) {
				String channel = entry.getKey();
				Integer minutes = entry.getValue();
				coolingOffPeriodGroupMap.computeIfAbsent(minutes, k -> new HashSet<>()).add(channel);
			}
		}
		return coolingOffPeriodGroupMap;
	}

	/**
	 * 自动发货审核指令
	 */
	@Getter
	@Setter
	public static class AutoShipAuditCmd {
		/**
		 * 销售单号集
		 */
		private Set<String> orderNos;

		/**
		 * 是否忽略自动分仓分物流（发货审核）状态
		 */
		private boolean ignoreAutoAllocateFulfillment;

		/**
		 * 是否忽略冷静期
		 */
		private boolean ignoreCoolingOffPeriod;

		/**
		 * 分批的条数
		 */
		private Integer size;
	}
}
