package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLineLog;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLine;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface WmtOrderLineLogConvertor {

	WmtOrderLineLog toLog(WmtOrderLine wmtOrderLine);
}
