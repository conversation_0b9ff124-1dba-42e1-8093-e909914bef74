package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingUpdateCmd;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMapping;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SkuMappinngUpdateCmdConvertor {
	SkuMapping toDomain(SkuMappingUpdateCmd param);
}
