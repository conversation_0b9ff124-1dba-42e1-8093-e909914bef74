package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.apiproxy.ebay.model.ShopAccount;
import com.renpho.erp.apiproxy.ebay.model.logistics.CreateShippingQuoteRequest;
import com.renpho.erp.apiproxy.ebay.model.logistics.CreateShippingQuoteResponse;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrder;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.proxy.EbayClient;
import com.renpho.erp.oms.infrastructure.persistence.misc.service.RegionService;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @desc: Ebay 查询发货方式报价
 * @time: 2025-04-23 11:05:19
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class EbayShippingServiceStrategy extends AbstractShippingServiceStrategy {

	private final EbayClient ebayClient;
	private final SaleOrderQueryService saleOrderQueryService;
	private final RegionService regionService;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE);
	}

	@Override
	public List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query) {
		ShopAccount shopAccount = getShopAccount(query.getStoreId());
		CreateShippingQuoteRequest request = buildCreateShippingQuoteRequest(query);
		CreateShippingQuoteResponse response = ebayClient.createShippingQuote(shopAccount, request);
		List<CreateShippingQuoteResponse.Rate> rates = Optional.ofNullable(response)
			.map(CreateShippingQuoteResponse::getRates)
			.orElse(Collections.emptyList())
			.stream()
			.filter(rate -> {
				String company = Optional.ofNullable(rate.getShippingCarrierName()).orElse("").toLowerCase();
				return "ups".equals(company) || "usps".equals(company);
			})
			.toList();
		List<ShippingServicesVO> result = rates.stream().map(shipAuditConvertor::toShippingServicesVO).collect(Collectors.toList());
		result.forEach(e -> e.setShippingQuoteId(response.getShippingQuoteId()));
		return result;
	}

	/**
	 * 构建创建运输报价请求
	 * @param query 发货服务查询
	 * @return 运输报价请求
	 */
	private CreateShippingQuoteRequest buildCreateShippingQuoteRequest(ShippingServicesQuery query) {
		CreateShippingQuoteRequest request = new CreateShippingQuoteRequest();
		// 设置发件信息
		request.setShipFrom(buildShipFrom(query));
		SaleOrder saleOrder = saleOrderRepository.getById(query.getOrderId());
		// 设置收件信息
		request.setShipTo(buildShipTo(saleOrder));
		// 设置包裹信息
		request.setPackageSpecification(buildPackageSpecification(query));
		// 设置订单信息
		CreateShippingQuoteRequest.Order order = new CreateShippingQuoteRequest.Order();
		order.setOrderId(saleOrder.getChannelOrderNo());
		order.setChannel(SaleChannelType.EBAY.getName().toUpperCase());
		request.setOrders(List.of(order));
		return request;
	}

	/**
	 * 构建包裹信息
	 * @param query 发货服务查询
	 * @return 包裹信息
	 */
	private CreateShippingQuoteRequest.PackageSpecification buildPackageSpecification(ShippingServicesQuery query) {
		CreateShippingQuoteRequest.PackageSpecification pkg = new CreateShippingQuoteRequest.PackageSpecification();
		CreateShippingQuoteRequest.Dimension dim = new CreateShippingQuoteRequest.Dimension();
		dim.setLength(SizeUtil.formatToInUnit(query.getLength()));
		dim.setWidth(SizeUtil.formatToInUnit(query.getWidth()));
		dim.setHeight(SizeUtil.formatToInUnit(query.getHeight()));
		dim.setUnit(Constants.EBAY_DIMENSION_UNIT);
		pkg.setDimensions(dim);
		CreateShippingQuoteRequest.Weight weight = new CreateShippingQuoteRequest.Weight();
		weight.setUnit(Constants.EBAY_WEIGHT_UNIT);
		weight.setValue(SizeUtil.formatToLbUnit(query.getWeight()));
		pkg.setWeight(weight);
		return pkg;
	}

	/**
	 * 构建收件信息
	 * @param saleOrder 订单信息
	 * @return 收件信息
	 */
	private CreateShippingQuoteRequest.ShipTo buildShipTo(SaleOrder saleOrder) {
		// 查询敏感地址信息（从mpds）
		SaleOrderAddressVO receivingAddress = saleOrderQueryService.getReceivingAddress(saleOrder.getOrderNo(), false, false);
		// 构建收件信息
		CreateShippingQuoteRequest.ShipTo shipTo = new CreateShippingQuoteRequest.ShipTo();
		shipTo.setFullName(receivingAddress.getRecipientName());
		CreateShippingQuoteRequest.ContactAddress contactAddress = new CreateShippingQuoteRequest.ContactAddress();
		contactAddress.setAddressLine1(receivingAddress.getAddress1());
		contactAddress.setAddressLine2(receivingAddress.getAddress2());
		contactAddress.setCity(receivingAddress.getCity());
		contactAddress.setStateOrProvince(receivingAddress.getProvince());
		contactAddress.setPostalCode(receivingAddress.getPostalCode());
		contactAddress.setCountryCode(receivingAddress.getCountryCode());

		String countyName = StrUtil.blankToDefault(regionService.getCountyName(receivingAddress.getCity()), "Riverside");
		contactAddress.setCounty(countyName);
		shipTo.setContactAddress(contactAddress);
		CreateShippingQuoteRequest.PrimaryPhone primaryPhone1 = new CreateShippingQuoteRequest.PrimaryPhone();
		primaryPhone1.setPhoneNumber(receivingAddress.getRecipientPhone());
		shipTo.setPrimaryPhone(primaryPhone1);
		return shipTo;
	}

	/**
	 * 构建发件信息
	 * @param query 发货服务查询
	 * @return 发件信息
	 */
	private CreateShippingQuoteRequest.ShipFrom buildShipFrom(ShippingServicesQuery query) {
		// 构建发件信息
		CreateShippingQuoteRequest.ShipFrom shipFrom = new CreateShippingQuoteRequest.ShipFrom();
		WarehouseVo warehouse = warehouseClient.getById(Set.of(query.getWarehouseId())).get(query.getWarehouseId());
		shipFrom.setFullName(warehouse.getContactName());
		CreateShippingQuoteRequest.ContactAddress contactAddress = new CreateShippingQuoteRequest.ContactAddress();
		contactAddress.setAddressLine1(warehouse.getAddress());
		contactAddress.setCity(warehouse.getCity());
		contactAddress.setCountryCode(warehouse.getCountryCode());
		contactAddress.setPostalCode(warehouse.getZipCode());
		contactAddress.setStateOrProvince(warehouse.getProvince());

		String countyName = StrUtil.blankToDefault(regionService.getCountyName(warehouse.getCity()), "Riverside");
		contactAddress.setCounty(countyName);
		shipFrom.setContactAddress(contactAddress);
		CreateShippingQuoteRequest.PrimaryPhone primaryPhone = new CreateShippingQuoteRequest.PrimaryPhone();
		primaryPhone.setPhoneNumber(warehouse.getContactNumber());
		shipFrom.setPrimaryPhone(primaryPhone);
		return shipFrom;
	}

	/**
	 * 获取店铺授权信息
	 * @param storeId 店铺Id
	 * @return 店铺信息
	 */
	private ShopAccount getShopAccount(Integer storeId) {
		StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(storeId);
		ShopAccount shopAccount = new ShopAccount();
		if (StringUtils.isEmpty(storeAuthorizationVo.getAuthorization().getEbaySellerId())) {
			throw new BusinessException(I18nMessageKit.getMessage("STORE_ID_CHECK", "ebaySellerId"));
		}
		shopAccount.setSellerId(storeAuthorizationVo.getAuthorization().getEbaySellerId());
		shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());
		return shopAccount;
	}
}
