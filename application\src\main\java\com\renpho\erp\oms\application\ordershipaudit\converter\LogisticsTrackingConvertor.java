package com.renpho.erp.oms.application.ordershipaudit.converter;

import com.renpho.erp.apiproxy.ecom.model.logistics.RetrieveCarrierTrackingInfoResponse;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.EcomLogisticsTrackingPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.time.LocalDateTime;

/**
 * @desc:
 * @time: 2025-04-24 16:55:08
 * @author: Alina
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface LogisticsTrackingConvertor {

	@Mapping(target = "id", expression = "java(com.baomidou.mybatisplus.core.toolkit.IdWorker.getId())")
	@Mapping(target = "createTime", expression = "java(java.time.LocalDateTime.now())")
	@Mapping(target = "updateTime", expression = "java(java.time.LocalDateTime.now())")
	@Mapping(target = "isDeleted", constant = "0")
	@Mapping(target = "ecomId", source = "carrierTrackingInfo.id")
	@Mapping(target = "bookNumber", source = "bookNumber")
	@Mapping(target = "eventTime", expression = "java(safeConvertEventTime(carrierTrackingInfo.getEventTime()))")
	EcomLogisticsTrackingPO toEcomLogisticsTrackingPo(RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo carrierTrackingInfo,String bookNumber);

	default LocalDateTime safeConvertEventTime(String eventTimeStr) {
		if (eventTimeStr == null) {
			return null;
		}
		return DateUtil.convertStrToUTC(eventTimeStr);
	}
}
