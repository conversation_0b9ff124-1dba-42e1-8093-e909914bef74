package com.renpho.erp.oms.application.b2b.order.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;

import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

/**
 * B2B订单导出Excel
 * <AUTHOR> Assistant
 */
@Data
public class B2bOrderExportExcel implements VO {
	@ExcelIgnore
	private Long id;

	/**
	 * 订单号
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.ORDER_NO")
	private String orderNo;

	/**
	 * 客户公司
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.CUSTOMER_COMPANY")
	private String customerCompanyName;

	/**
	 * 客户参考号
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.REFER_NO")
	private String referNo;

	/**
	 * 销售公司
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.SALES_COMPANY")
	private String salesCompanyName;

	/**
	 * 贸易条款
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.INCOTERMS")
	private String incotermsName;

	/**
	 * 付款条款
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.PAYMENT_TERMS")
	private String paymentTermsName;

	/**
	 * 海外区域经理
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.COUNTRY_MANAGER")
	private String countryManagerName;

	/**
	 * 销售助理
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.SALES_ASSISTANT")
	private String salesAssistantName;

	/**
	 * 创建时间
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.CREATED_TIME")
	private LocalDateTime createTime;

	/**
	 * 发票时间
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.INVOICE_TIME")
	private LocalDateTime invoiceTime;

	/**
	 * 发货时间
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.SHIPPED_TIME")
	private LocalDateTime shippedTime;

	/**
	 * PSKU
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.PSKU")
	private String psku;

	/**
	 * PSKU名称
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.PSKU_NAME")
	private String pskuName;

	/**
	 * 条码
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.BARCODE")
	private String barcode;

	/**
	 * 下单数量
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.QUANTITY")
	private Integer orderedQuantity;

	/**
	 * 单价
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.UNIT_PRICE")
	private BigDecimal unitPrice;

	/**
	 * 税率
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.TAX_RATE")
	private BigDecimal taxRate;

	/**
	 * 税金
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.TAX")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal tax;

	/**
	 * 运费
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.SHIPPING_FEE")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingFee;

	/**
	 * 其他费用
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.OTHER_FEE")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal otherFee;

	/**
	 * 其他费用明细
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.OTHER_FEE_DETAIL")
	private String otherFeeDetail;

	/**
	 * 合计
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.TOTAL_AMOUNT")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal totalAmount;

	/**
	 * 已收款
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.RECEIPT_AMOUNT")
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal receiptAmount;

	/**
	 * 币种
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.CURRENCY")
	private String currency;

	/**
	 * 交货窗口期
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.SHIPPING_WINDOW")
	private LocalDate requestDeliveryDate;

	/**
	 * 是否托盘化
	 */
	@ExcelIgnore
	@Trans(type = TransType.DICTIONARY, key = "Tob_palletizationRequired", ref = "palletizationRequiredName")
	private Integer palletizationRequired;

	/**
	 * 托盘化要求名称
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.PALLETIZATION_REQUIRED")
	private String palletizationRequiredName;

	/**
	 * 提单类型
	 */
	@Trans(type = TransType.DICTIONARY, key = "Tob_blType", ref = "blTypeName")
	@ExcelIgnore
	private Integer blType;

	/**
	 * 提单类型名称
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.BL_TYPE")
	private String blTypeName;

	/**
	 * 其他要求
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.OTHER_REQUIREMENTS")
	private String otherRequirements;

	/**
	 * 备注
	 */
	@ExcelProperty(value = "B2B_ORDER_EXCEL_HEADER.REMARK")
	private String remark;

	// 以下字段用于内部处理，不导出到Excel
	@ExcelIgnore
	private Long orderId;

	@ExcelIgnore
	private String incotermsCode;

	@ExcelIgnore
	private String paymentTermsCode;
}
