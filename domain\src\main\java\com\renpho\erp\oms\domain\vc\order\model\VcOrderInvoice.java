package com.renpho.erp.oms.domain.vc.order.model;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单发票实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderInvoice {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * 发票号
	 */
	private String invoiceNo;

	/**
	 * 开票日期
	 */
	private LocalDate invoiceDate;

	/**
	 * 税率
	 */
	private BigDecimal taxRate;

	/**
	 * 发票金额
	 */
	private BigDecimal tax;

	/**
	 * 将税平分到每个订单行
	 */
	public void taxCalculate(List<VcOrderItem> orderItemList) {
		// 过滤掉没接单的
		orderItemList = orderItemList.stream().filter(VcOrderItem::isAccept).toList();

		BigDecimal totalProductPrice = BigDecimal.ZERO;
		Map<Long, BigDecimal> priceMap = new HashMap<>();
		for (VcOrderItem vcOrderItem : orderItemList) {
			// 计算商品总价格
			totalProductPrice = totalProductPrice.add(vcOrderItem.getAmount().getProductPrice());
			priceMap.put(vcOrderItem.getId(), vcOrderItem.getAmount().getProductPrice());
		}

		// todo 判断totalProductPrice=0抛错
		// 税=发票金额-商品总金额
		BigDecimal taxAmount = tax.subtract(totalProductPrice);
		// 摊分剩的数量
		BigDecimal remainTaxAmount = taxAmount;

		// 开始摊分
		for (int i = 0; i < orderItemList.size(); i++) {
			VcOrderItem vcOrderItem = orderItemList.get(i);
			if (Objects.equals(i, orderItemList.size() - 1)) {
				vcOrderItem.invoiceTax(remainTaxAmount);
			}

			// 根据商品总价摊分
			BigDecimal currentTax = priceMap.get(vcOrderItem.getId())
				.divide(totalProductPrice, 8, RoundingMode.HALF_UP)
				.multiply(taxAmount)
				.setScale(2, RoundingMode.HALF_UP);
			vcOrderItem.invoiceTax(currentTax);

			remainTaxAmount = remainTaxAmount.subtract(currentTax);
		}
	}
}
