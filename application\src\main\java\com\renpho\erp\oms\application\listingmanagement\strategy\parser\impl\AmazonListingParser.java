package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import com.renpho.erp.oms.application.listingmanagement.dto.AmzReportParseMerchantListingAllDataDto;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.DateFormatEnum;
import com.renpho.erp.oms.infrastructure.common.enums.SiteEnum;
import com.renpho.erp.oms.infrastructure.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 亚马逊 Listing解析器
 *
 * <AUTHOR>
 * @Date 2024/12/26 12:05
 **/
@Slf4j
@Component
public class AmazonListingParser extends AbstractListingParserTemplate<AmzReportParseMerchantListingAllDataDto> {

    @Override
    protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, AmzReportParseMerchantListingAllDataDto item) {
        // 获取sku映射Map key为 店铺id+销售SKU，value为List<SkuMapping>
        Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), Set.of(item.getSellerSku()));
        // 采购sku图片Map，key为采购sku，value为产品封面图片
        Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(
                skuMappingListMap.values().stream().flatMap(Collection::stream).toList());
        // 构建销售渠道Listing（基础数据）
        SalesChannelListing listing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, item.getSellerSku(),
                source.getStoreId());
        listing.setItem(item.getProductId());
        listing.setMsku(item.getSellerSku());
        listing.setTitle(item.getItemName());
        listing.setStatus(item.getStatus());
        // 价格
        if (StringUtils.isNotBlank(item.getPrice())) {
            listing.setPrice(new BigDecimal(item.getPrice()));
        }
        SiteEnum siteEnum = SiteEnum.getByCode(source.getSiteCode());
        if (siteEnum != null) {
            // 币种
            listing.setCurrency(siteEnum.getCurrency());
            // 链接 优先取asin1，取不到则取product-id
            String code = item.getAsin1();
            if (StringUtils.isBlank(code)) {
                code = item.getProductId();
            }
            if (StringUtils.isNotBlank(code)) {
                String link = siteEnum.getUrl() + "dp/" + code + "/";
                listing.setLink(link);
            }
        }
        // 上架时间
        if (StringUtils.isNotBlank(item.getOpenDate())) {
            // 截取时间，拿到时间字符串和时区格式
            String dateStr = item.getOpenDate().substring(0, item.getOpenDate().lastIndexOf(" "));
            String zoneId = item.getOpenDate().substring(item.getOpenDate().lastIndexOf(" ") + 1);
            DateFormatEnum dateFormatEnum = DateFormatEnum.getByCode(zoneId);
            if (dateFormatEnum != null) {
                try {
                    listing.setPublishedTime(DateUtils.parseDateToUtc(dateStr, dateFormatEnum.getFormat(), dateFormatEnum.getZoneId()));
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                    try {
                        //再根据默认时间格式转换
                        String format1 = "yyyy-MM-dd HH:mm:ss";
                        listing.setPublishedTime(DateUtils.parseDateToUtc(dateStr, format1, dateFormatEnum.getZoneId()));
                    } catch (Exception e1) {
                        log.error(e1.getMessage(), e1);
                        try {
                            //再根据默认时间格式转换
                            String format2 = "dd/MM/yyyy HH:mm:ss";
                            listing.setPublishedTime(DateUtils.parseDateToUtc(dateStr, format2, dateFormatEnum.getZoneId()));
                        } catch (Exception e2) {
                            log.error(e2.getMessage(), e2);
                        }
                    }
                }
            }
        }
        return List.of(listing);
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.AMZ_CHANNEL_CODE;
    }

}
