package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MclOrderLogConvertor {
	MclOrderLog toLog(MclOrder mclOrder);

	MclOrderVO toVO(MclOrder mclOrder);
}
