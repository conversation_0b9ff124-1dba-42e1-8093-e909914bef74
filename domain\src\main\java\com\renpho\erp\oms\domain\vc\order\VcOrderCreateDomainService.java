package com.renpho.erp.oms.domain.vc.order;

import org.jmolecules.ddd.annotation.Service;

import com.renpho.erp.oms.domain.vc.order.converter.VcOrderCreateConverter;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcOrderAggRoot;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单转换创建-领域服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderCreateDomainService {

	/**
	 * 转换创建VC订单
	 * @param amzVcOrder 亚马逊VC订单
	 * @param valueObject 值对象
	 *
	 * @return VcOrderAggRoot
	 */
	public VcOrderAggRoot convertAndCreate(AmzVcOrder amzVcOrder, VcOrderCreateConverter.ValueObject valueObject) {
		// 转换生成VC订单
		VcOrderAggRoot vcOrder = VcOrderCreateConverter.convertOrder(amzVcOrder, valueObject);
		// 计算订单状态
		vcOrder.calculateStatusOfCreate(amzVcOrder.getPurchaseOrderState());
		// 计算金额
		vcOrder.calculateAmount();
		// 解析sku
		vcOrder.parseSku(valueObject.getAsinToProductMap());
		return vcOrder;
	}

}
