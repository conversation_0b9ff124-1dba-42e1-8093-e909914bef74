package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import com.renpho.erp.oms.application.ordershipaudit.converter.ShipAuditConvertor;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodeLanguagePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceCodeLanguageService;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.ecom.model.logistics.CreateShippingQuoteRequest;
import com.renpho.erp.apiproxy.ecom.model.logistics.CreateShippingQuoteResponse;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrder;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.mdcThreadPool.MdcThreadPoolTaskExecutor;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import com.renpho.erp.oms.infrastructure.feign.proxy.EcomClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceConfigService;
import com.renpho.karma.dto.R;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: Ecom 查询发货方式报价
 * @time: 2025-04-23 09:53:52
 * @author: Alina
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class EcomShippingServiceStrategy extends AbstractShippingServiceStrategy {

	private final FulfillmentServiceConfigService fulfillmentServiceConfigService;
	private final EcomClient ecomClient;
	private final SaleOrderQueryService saleOrderQueryService;
	private final MdcThreadPoolTaskExecutor shippingServiceExecutor;
	private final FulfillmentServiceCodeLanguageService fulfillmentServiceCodeLanguageService;
	private final ShipAuditConvertor shipAuditConvertor;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE);
	}

	/**
	 * 根据所选仓库查询发货方式报价表
	 * @param query 参数
	 * @return List<ShippingServicesVO>
	 */
	@Override
	public List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query) {
		List<FulfillmentServiceCodePO> fulfillmentServiceCodePoList = fulfillmentServiceConfigService
			.getByCondition(query.getFulfillmentServiceType(), query.getWarehouseId());
		Set<Long> codeIdSet = fulfillmentServiceCodePoList.stream().map(FulfillmentServiceCodePO::getId).collect(Collectors.toSet());
		// 查询多语言表 只是为了规范承运商名称 默认都用 en-US
		Map<Long, FulfillmentServiceCodeLanguagePO> codeLanguageMap = fulfillmentServiceCodeLanguageService.getByCodeIdSet(codeIdSet,
				"en-US");
		List<ShippingServicesVO> servicesVoS = fulfillmentServiceCodePoList.stream()
			.map(e -> shipAuditConvertor.toShippingServicesVO(e,
					ObjectUtil.isNull(codeLanguageMap.get(e.getId())) ? e.getCarrierServiceCode()
							: codeLanguageMap.get(e.getId()).getName()))
			.collect(Collectors.toList());
		WarehouseVo warehouse = warehouseClient.getById(Set.of(query.getWarehouseId())).get(query.getWarehouseId());
		SaleOrder saleOrder = saleOrderRepository.getById(query.getOrderId());
		// 查询敏感地址信息（从mpds）
		SaleOrderAddressVO receivingAddress = saleOrderQueryService.getReceivingAddress(saleOrder.getOrderNo(), false, false);
		// 并发查询报价
		List<CompletableFuture<Void>> futures = servicesVoS.stream()
			.map(servicesVo -> CompletableFuture.runAsync(() -> setTotalAmount(servicesVo, receivingAddress, warehouse, query),
					shippingServiceExecutor))
			.toList();
		try {
			// 等待所有查询完成
			CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
		}
		catch (CompletionException ex) {
			Throwable cause = ex.getCause();
			throw cause instanceof BusinessException ? (BusinessException) cause : ex;
		}
		return servicesVoS;
	}

	/**
	 * 根据服务类型获取发货报价
	 * @param servicesVo 服务类型
	 */
	private void setTotalAmount(ShippingServicesVO servicesVo, SaleOrderAddressVO receivingAddress, WarehouseVo warehouse,
			ShippingServicesQuery query) {
		CreateShippingQuoteRequest createShippingQuoteRequest = new CreateShippingQuoteRequest();
		// 承运商信息
		createShippingQuoteRequest.setCarrierCode(servicesVo.getCarrierCode());
		createShippingQuoteRequest.setServiceCode(servicesVo.getCarrierServiceCode());
		createShippingQuoteRequest.setPackageTypeCode(servicesVo.getPackageTypeCode());
		// 发货地址
		CreateShippingQuoteRequest.Sender sender = new CreateShippingQuoteRequest.Sender();
		sender.setCountry(warehouse.getCountryCode());
		sender.setZip(warehouse.getZipCode());
		createShippingQuoteRequest.setSender(sender);
		// 收货地址
		createShippingQuoteRequest.setReceiver(buildReceiver(receivingAddress));
		// 包裹信息
		createShippingQuoteRequest.setDimUnit(Constants.DIMENSION_UNIT_IN);
		createShippingQuoteRequest.setWeightUnit(Constants.WEIGHT_UNIT_LB);
		createShippingQuoteRequest.setPieces(List.of(getPieces(query)));
		// 币种
		createShippingQuoteRequest.setCurrency(Constants.CURRENCY_USD);
		createShippingQuoteRequest.setCustomsCurrency(Constants.CURRENCY_USD);
		createShippingQuoteRequest.setSignatureOptionCode(null);
		createShippingQuoteRequest.setResidential(true);
		// 获取报价
		try {
			R<CreateShippingQuoteResponse> r = ecomClient.createShippingQuote(createShippingQuoteRequest);
			if (!r.isSuccess()) {
				servicesVo.setErrorMessage(r.getMessage().contains("No rate found") ? "No rate found" : r.getMessage());
				log.error("调用Ecom createShippingQuote接口查询发货方式报价失败: {}", r.getMessage());
			}
			else {
				CreateShippingQuoteResponse response = r.getData();
				servicesVo.setCurrency(response.getCurrency());
				servicesVo.setAmount(response.getTotalAmount());
				servicesVo.setShippingCost(response.getCurrency() + " " + response.getTotalAmount());
			}
		}
		catch (Exception e) {
			log.error("调用Ecom查询发货方式报价接口发生异常", e);
			throw new BusinessException("调用Ecom查询发货方式报价接口发生异常");
		}
	}

	/**
	 * 构建包裹信息
	 * @param query 参数
	 */
	private CreateShippingQuoteRequest.Pieces getPieces(ShippingServicesQuery query) {
		CreateShippingQuoteRequest.Pieces pieces = new CreateShippingQuoteRequest.Pieces();
		pieces.setLength(SizeUtil.formatToInUnit(query.getLength()));
		pieces.setWidth(SizeUtil.formatToInUnit(query.getWidth()));
		pieces.setHeight(SizeUtil.formatToInUnit(query.getHeight()));
		pieces.setWeight(SizeUtil.formatToLbUnit(query.getWeight()));
		pieces.setDeclaredValue(null);
		pieces.setInsuranceAmount(null);
		return pieces;
	}

	/**
	 * 构建收件人信息
	 * @param receivingAddress 收件人信息
	 */
	private CreateShippingQuoteRequest.Receiver buildReceiver(SaleOrderAddressVO receivingAddress) {
		// 构建收件地址
		CreateShippingQuoteRequest.Receiver receiver = new CreateShippingQuoteRequest.Receiver();
		receiver.setCountry(receivingAddress.getCountryCode());
		receiver.setCity(receivingAddress.getCity());
		receiver.setZip(receivingAddress.getPostalCode());
		return receiver;
	}
}
