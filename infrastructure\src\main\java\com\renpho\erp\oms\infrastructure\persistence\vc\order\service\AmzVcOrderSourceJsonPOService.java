package com.renpho.erp.oms.infrastructure.persistence.vc.order.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.renpho.erp.oms.domain.vc.order.enums.VcParseSourceOrderStatus;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.AmzVcOrderSourceJsonMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.AmzVcOrderSourceJsonPO;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/7 12:11
 */
@Service
@AllArgsConstructor
public class AmzVcOrderSourceJsonPOService extends ServiceImpl<AmzVcOrderSourceJsonMapper, AmzVcOrderSourceJsonPO> implements IService<AmzVcOrderSourceJsonPO> {

    /**
     * 保存拉取的数据
     *
     * @param saveVcOrderList
     * @param storeId
     */
    @Transactional(rollbackFor = Exception.class)
    public void savePullOrder(List<AmzVcOrderSourceJsonPO> saveVcOrderList, Integer storeId) {
        if (CollUtil.isEmpty(saveVcOrderList)) {
            return;
        }
        //1千条批量处理
        CollUtil.split(saveVcOrderList, 1000).forEach(tempSaveList -> {
            //1.修改旧数据待解析的为无需解析
            List<String> purchaseOrderNumberList = tempSaveList.stream()
                    .map(AmzVcOrderSourceJsonPO::getPurchaseOrderNumber)
                    .collect(Collectors.toList());
            this.update(null, Wrappers.<AmzVcOrderSourceJsonPO>lambdaUpdate()
                    .in(AmzVcOrderSourceJsonPO::getPurchaseOrderNumber, purchaseOrderNumberList)
                    .eq(AmzVcOrderSourceJsonPO::getStoreId, storeId)
                    .eq(AmzVcOrderSourceJsonPO::getParseOrderStatus, VcParseSourceOrderStatus.WAIT_PARSE.getValue())
                    .set(AmzVcOrderSourceJsonPO::getParseOrderStatus, VcParseSourceOrderStatus.NO_PARSE.getValue()));
            //2.保存新数据
            this.saveBatch(tempSaveList);
        });

    }
}
