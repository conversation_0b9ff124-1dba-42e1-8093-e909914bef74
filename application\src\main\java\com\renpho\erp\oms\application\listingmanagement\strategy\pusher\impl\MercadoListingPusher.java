package com.renpho.erp.oms.application.listingmanagement.strategy.pusher.impl;

import com.renpho.erp.apiproxy.mercado.model.items.GetMarketplaceItemsDetailResponse;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.AbstractListingPusherTemplate;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.message.MercadoListingSkuMessage;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class MercadoListingPusher extends AbstractListingPusherTemplate<GetMarketplaceItemsDetailResponse, List<MercadoListingSkuMessage>> {

	/**
	 * 美客多渠道编码
	 * @return String
	 */
	@Override
	public String getSalesChannelCode() {
		return ChannelCode.MCL_CHANNEL_CODE;
	}

	@Override
	protected List<MercadoListingSkuMessage> parseListingSource(GetMarketplaceItemsDetailResponse marketplaceItemsDetailResponse) {
		List<GetMarketplaceItemsDetailResponse.Variation> variations = Optional.of(marketplaceItemsDetailResponse)
			.map(GetMarketplaceItemsDetailResponse::getVariations)
			.orElse(List.of());
		List<MercadoListingSkuMessage> skuMessageList = new ArrayList<>();
		// 优先从变体(variations)取，取不到再从属性(attributes)里取
		if (CollectionUtils.isNotEmpty(variations)) {
			variations.forEach(variation -> {
				MercadoListingSkuMessage skuMessage = new MercadoListingSkuMessage();
				skuMessage.setInventoryId(variation.getInventory_id());
				skuMessage.setMSku(this.getSellerSkuByAttribute(variation.getAttributes()));
				skuMessageList.add(skuMessage);
			});
			return skuMessageList;
		}
		else {
			String inventoryId = Optional.of(marketplaceItemsDetailResponse)
				.map(GetMarketplaceItemsDetailResponse::getInventory_id)
				.orElse(null);
			List<GetMarketplaceItemsDetailResponse.Attribute> attributes = Optional.of(marketplaceItemsDetailResponse)
				.map(GetMarketplaceItemsDetailResponse::getAttributes)
				.orElse(List.of());
			MercadoListingSkuMessage skuMessage = new MercadoListingSkuMessage();
			skuMessage.setInventoryId(inventoryId);
			skuMessage.setMSku(this.getSellerSkuByAttribute(attributes));
			skuMessageList.add(skuMessage);
		}
		return skuMessageList;
	}

	/**
	 * 从属性获取销售sku
	 * @param attributes 属性
	 * @return 销售sku
	 */
	private String getSellerSkuByAttribute(List<GetMarketplaceItemsDetailResponse.Attribute> attributes) {
		// 取美克多渠道sku
		for (GetMarketplaceItemsDetailResponse.Attribute attribute : attributes) {
			String id = attribute.getId();
			if ("SELLER_SKU".equals(id)) {
				String mSku = attribute.getValue_name();
				if (StringUtils.isBlank(mSku)) {
					List<GetMarketplaceItemsDetailResponse.Value> values = attribute.getValues();
					if (CollectionUtils.isEmpty(values)) {
						log.error(
								"com.renpho.erp.apiproxy.mercado.model.items.GetMarketplaceItemsDetailResponse.Attribute.getValues is empty");
					}
				}
				else {
					return mSku;
				}
			}
		}
		return null;
	}
}
