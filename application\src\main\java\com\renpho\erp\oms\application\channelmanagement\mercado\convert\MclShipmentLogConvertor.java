package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentLog;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MclShipmentLogConvertor {
	MclShipmentLog toLog(MclShipment mclOrder);
}
