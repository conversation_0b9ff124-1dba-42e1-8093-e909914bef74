package com.renpho.erp.oms.application.channelmanagement.shopify.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpfOrderLineItemLog {
	/**
	 * Shopify商品id
	 */
	private Long itemId;

	/**
	 * 管理员 graphql api id
	 */
	private String adminGraphqlApiId;

	/**
	 * 属性集合 json
	 */
	private String attributedStaffs;

	/**
	 * 当前数量
	 */
	private Integer currentQuantity;

	/**
	 * 可履约数量
	 */
	private Integer fulfillableQuantity;

	/**
	 * 履约服务
	 */
	private String fulfillmentService;

	/**
	 * 履约状态
	 */
	private String fulfillmentStatus;

	/**
	 * 是否为礼物 0:否 1:是
	 */
	private Boolean giftCard;

	/**
	 * 克
	 */
	private Integer grams;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 税前价格
	 */
	private BigDecimal preTaxPrice;

	/**
	 * 税前价格集合 json
	 */
	private String preTaxPriceSet;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * 价格集合 json
	 */
	private String priceSet;

	/**
	 * 产品是否存在 0:否 1:是
	 */
	private Boolean productExists;

	/**
	 * product_id
	 */
	private Long productId;

	/**
	 * 特性集合 json
	 */
	private String properties;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 是否需要发货 0:否 1:是
	 */
	private Boolean requiresShipping;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 是否应纳税 0:否 1:是
	 */
	private Boolean taxable;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 优惠金额
	 */
	private BigDecimal totalDiscount;

	/**
	 * 价格集合 json
	 */
	private String totalDiscountSet;

	/**
	 * 变体id
	 */
	private String variantId;

	/**
	 * 变体库存管理
	 */
	private String variantInventoryManagement;

	/**
	 * 变体标题
	 */
	private String variantTitle;

	/**
	 * 供应商
	 */
	private String vendor;

	/**
	 * 税费行 json
	 */
	private String taxLines;

	/**
	 * 职责数组 json
	 */
	private String duties;

	/**
	 * 折扣分配数组 json
	 */
	private String discountAllocations;
}
