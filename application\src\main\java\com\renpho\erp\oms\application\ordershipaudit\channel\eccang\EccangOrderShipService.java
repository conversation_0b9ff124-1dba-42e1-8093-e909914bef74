package com.renpho.erp.oms.application.ordershipaudit.channel.eccang;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.eccang.yunwms.model.outstock.CreateOrderRequest;
import com.renpho.erp.apiproxy.eccang.yunwms.model.outstock.CreateOrderResponse;
import com.renpho.erp.apiproxy.eccang.yunwms.model.outstock.GetOrderStatusByCodeResponse;
import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractThirdWarehouseOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.strategy.query.EccangFulfillmentQueryStrategy;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.ChannelItemFulfillmentInfo;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditItem;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.dto.WarehouseConfigDto;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.EccangClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceConfigService;
import com.renpho.karma.dto.R;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 易仓-发货 应用服务
 * <AUTHOR>
 */
@Slf4j
@Component
public class EccangOrderShipService extends AbstractThirdWarehouseOrderShipService {

	/**
	 * 获取当前环境profile
	 */
	@Value("${spring.profiles.active}")
	private String env;

	private final EccangClient eccangClient;

	private final FulfillmentServiceConfigService fulfillmentServiceConfigService;

	protected EccangOrderShipService(SaleOrderQueryService saleOrderQueryService, WarehouseClient warehouseClient,
			EccangClient eccangClient, FulfillmentServiceConfigService fulfillmentServiceConfigService) {
		super(saleOrderQueryService, warehouseClient);
		this.eccangClient = eccangClient;
		this.fulfillmentServiceConfigService = fulfillmentServiceConfigService;
	}

	/**
	 * 履约服务类型集（极智佳，Kingspark）
	 * @return Set
	 */
	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.YPL_WAREHOUSE, FulfillmentServiceType.KING_SPARK_WAREHOUSE);
	}

	/**
	 * 创建履约单（仓储物流）
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param orderAggRoot 销售订单聚合根
	 * @param receivingAddress 敏感地址信息
	 * @param warehouseConfigDto ims仓库客户编码
	 */
	@Override
	protected void createFulfillmentOrder(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot orderAggRoot,
			SaleOrderAddressVO receivingAddress, WarehouseConfigDto warehouseConfigDto) {
		// 构建参数
		CreateOrderRequest request = this.buildCreateOrderRequest(shipAuditAggRoot, orderAggRoot, receivingAddress);
		// 请求创建出库单
		R<CreateOrderResponse> r;
		try {
			r = eccangClient.createOrder(warehouseConfigDto.getCustomerCode(), request);
		}
		catch (Exception e) {
			log.error("调用易仓-创建出库单接口异常：", e);
			shipAuditAggRoot.createFail(e.getMessage());
			return;
		}
		// 处理创建结果
		this.handleCreateResult(r, shipAuditAggRoot);
	}

	/**
	 * 构建参数
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param receivingAddress 敏感地址信息
	 * @return CreateOrderRequest
	 */
	private CreateOrderRequest buildCreateOrderRequest(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot orderAggRoot,
			SaleOrderAddressVO receivingAddress) {
		// 构建参数
		CreateOrderRequest request = new CreateOrderRequest();
		request.setPlatform("OTHER");
		// 自动分仓:0无,1自动分仓
		request.setAllocated_auto("0");
		// 极智佳的仓库编码（三方仓库编码）
		request.setWarehouse_code(shipAuditAggRoot.getThirdPartWarehouseCode());
		// 店铺单号
		request.setSw_order_number(orderAggRoot.getChannelOrderNo());
		// 承运商服务编码
		request.setShipping_method(shipAuditAggRoot.getCarrierServiceCode());
		// 发货审核编号
		request.setReference_no(shipAuditAggRoot.getAuditNo());
		// 国家
		request.setCountry_code(receivingAddress.getCountryCode());
		// 省份
		request.setProvince(receivingAddress.getProvince());
		// 市
		request.setCity(receivingAddress.getCity());
		// district收件人区，doorplate门牌号，不传
		// 地址1
		request.setAddress1(receivingAddress.getAddress1());
		// 地址2必传，如果没有地址2，传"/"
		String address2 = receivingAddress.getAddress2();
		request.setAddress2(StringUtils.isNotBlank(address2) ? address2 : "/");
		// 邮政编码
		request.setZipcode(receivingAddress.getPostalCode());
		// 客户姓名（收件人姓名）
		request.setName(receivingAddress.getRecipientName());
		// 客户手机号（收件人手机号）
		request.setPhone(receivingAddress.getRecipientPhone());
		// 货到付款，固定0
		request.setIs_order_cod(0);
		// item列表
		request.setItems(this.buildItems(shipAuditAggRoot.getItems()));
		request.setOrder_kind("BC");
		// api来源
		request.setApi_source("renpho_erp");
		// 自动发货审核，生产环境传1，其他传0
		request.setVerify("prod".equals(env) ? 1 : 0);
		// 是否强制审核(如欠费，缺货时是否审核到仓配系统), 0不强制，1强制， 默认为0 当verify==1时生效
		request.setForceVerify(0);
		return request;
	}

	/**
	 * 处理创建结果
	 * @param r 创建结果
	 */
	private void handleCreateResult(R<CreateOrderResponse> r, OrderShipAuditAggRoot shipAuditAggRoot) {
		if (!r.isSuccess()) {
			shipAuditAggRoot.createFail("[易仓]" + r.getMessage());
			return;
		}
		CreateOrderResponse response = r.getData();
		if (Objects.isNull(response)) {
			shipAuditAggRoot.createFail("[易仓]返回的data为空");
			return;
		}
		String ask = response.getAsk();
		if ("Success".equals(ask)) {
			// order_code为易仓的订单号
			shipAuditAggRoot.createSuccess(response.getOrder_code());
			return;
		}
		shipAuditAggRoot.createFail(String.format("[易仓]返回的ask为：%s", ask));
	}

	/**
	 * 构建item列表
	 * @param items 发货审核商品信息
	 * @return List
	 */
	private List<CreateOrderRequest.Item> buildItems(List<OrderShipAuditItem> items) {
		return items.stream().map(i -> {
			CreateOrderRequest.Item item = new CreateOrderRequest.Item();
			// FNSKU
			item.setProduct_sku(i.getFnSku());
			// 应发数量
			item.setQuantity(i.getQuantity());
			return item;
		}).toList();
	}

	@Override
	protected void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, WarehouseConfigDto warehouseConfigDto) {
		GetOrderStatusByCodeResponse orderStatusByCode = eccangClient.getOrderStatusByCode(warehouseConfigDto.getCustomerCode(),
				orderShipAuditAggRoot.getChannelStoreOrderId());
		if (orderStatusByCode == null) {
			log.info("易仓查询出库单获取不到数据, {}", orderShipAuditAggRoot.getChannelStoreOrderId());
			return;
		}
		if (orderStatusByCode.getData() == null) {
			log.info("易仓查询出库单获取数据,data是null, {}", orderShipAuditAggRoot.getChannelStoreOrderId());
			return;
		}

		GetOrderStatusByCodeResponse.OutStockOrder outStockOrder = orderStatusByCode.getData();
		// 发货异常
		if (EccangFulfillmentQueryStrategy.SHIP_EXCEPTION_STATUS.contains(outStockOrder.getOrder_status())) {
			orderShipAuditAggRoot.shipException();
			return;
		}
		// 没有物流单号
		if (StrUtil.isBlank(outStockOrder.getTracking_no())) {
			return;
		}

		FulfillmentServiceCodePO fulfillmentServiceConfig = fulfillmentServiceConfigService.getByFulfillmentServiceTypeAndServiceCode(
				orderShipAuditAggRoot.getFulfillmentServiceType().getValue(), outStockOrder.getShipping_method());
		String carrierName = Objects.nonNull(fulfillmentServiceConfig) ? fulfillmentServiceConfig.getCarrierName() : null;

		// 发货时间
		LocalDateTime shipDate = null;
		try {
			// 未发货状态下值是0000-00-00 00:00:00
			shipDate = DateUtil.convertStrToUTC(outStockOrder.getDate_shipping());
		}
		catch (Exception ignored) {
		}
		for (OrderShipAuditItem item : orderShipAuditAggRoot.getItems()) {
			orderShipAuditAggRoot.markShipping(item.getChannelStoreOrderItemId(), item.getOrderItemId(), item.getQuantity(),
					ChannelItemFulfillmentInfo.builder()
						.trackingNo(outStockOrder.getTracking_no())
						.carrierName(carrierName)
						.shippingDate(shipDate)
						.build());
		}
		orderShipAuditAggRoot.calculateStatus();

	}
}
