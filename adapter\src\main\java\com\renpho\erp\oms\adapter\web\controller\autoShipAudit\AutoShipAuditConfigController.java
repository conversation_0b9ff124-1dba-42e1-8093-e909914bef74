package com.renpho.erp.oms.adapter.web.controller.autoShipAudit;


import com.alibaba.excel.EasyExcel;
import com.renpho.erp.oms.adapter.web.controller.listener.AutoShipAuditConfigListener;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.command.SaveAutoShipAuditConfigCmd;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigImportExcel;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.service.AutoShipAuditConfigService;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.vo.AutoShipAuditWarehouseVO;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.query.AutoShipAuditConfigPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.vo.AutoShipAuditConfigPageVO;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 自动发货审核配置
 * <AUTHOR>
 * @description 自动发货审核配置
 * @date 2025/5/16 12:20
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/autoShipAuditConfig/**")
@RequestMapping("/autoShipAuditConfig")
@AllArgsConstructor
@Tag(name = "自动发货审核配置", description = "自动发货审核配置")
public class AutoShipAuditConfigController {

	private final AutoShipAuditConfigService autoShipAuditConfigService;

	/**
	 * 获取自动发货审核下拉仓库
	 * @return
	 */
	@GetMapping("/getAutoShipAuditWarehouse")
	public R<List<AutoShipAuditWarehouseVO>> getAutoShipAuditWarehouse() {
		return R.success(autoShipAuditConfigService.getAutoShipAuditWarehouseInfo());
	}

	/**
	 * 保存自动发货审核配置
	 * @param cmd
	 * @return
	 */
	@PreAuthorize(PermissionConstant.AutoShipAuditConfig.ADDOREDIT)
	@PostMapping("/save")
	public R saveAutoShipAuditConfig(@RequestBody @Valid SaveAutoShipAuditConfigCmd cmd) {
		autoShipAuditConfigService.saveUpdateShipAuditConfig(cmd);
		return R.success();
	}

    /**
     * 分页查询
     * @param autoShipAuditConfigPageQuery
     * @return
     */
	@PreAuthorize(PermissionConstant.AutoShipAuditConfig.PAGE)
	@PostMapping("/page")
	public R<Paging<AutoShipAuditConfigPageVO>> page(@RequestBody AutoShipAuditConfigPageQuery autoShipAuditConfigPageQuery) {
		Paging<AutoShipAuditConfigPageVO> page = autoShipAuditConfigService.page(autoShipAuditConfigPageQuery);
		return R.success(page);
	}


	/**
	 * 导入excel
	 * @param file
	 * @return
	 */
	@PreAuthorize(PermissionConstant.AutoShipAuditConfig.IMPORT)
	@PostMapping(value = "/importExcel")
	public R importExcel(@RequestParam("file") MultipartFile file) {
		try (InputStream inputStream = file.getInputStream()) {
			AutoShipAuditConfigListener autoShipAuditConfigListener = new AutoShipAuditConfigListener();
			EasyExcel.read(inputStream, AutoShipAuditConfigImportExcel.class, autoShipAuditConfigListener).sheet().doRead();
			List<AutoShipAuditConfigImportExcel> importExcelList = autoShipAuditConfigListener.getAutoShipAuditConfigImportExcelList();
			return autoShipAuditConfigService.importExcel(importExcelList);
		} catch (Exception e) {
			return R.fail(e.getMessage());
		}
	}

	/**
	 * 导出excel
	 * @param query
	 * @param response
	 */
	@PreAuthorize(PermissionConstant.AutoShipAuditConfig.EXPORT)
	@PostMapping("/exportExcel")
	public void export(@RequestBody AutoShipAuditConfigPageQuery query, HttpServletResponse response) {
		autoShipAuditConfigService.exportExcel(query, response);
	}

	/**
	 * 下载模板
	 * @param response
	 */
	@PostMapping("/downloadImportTemplate")
	public void getTemplateFile(HttpServletResponse response) {
		autoShipAuditConfigService.downloadImportTemplate(response);
	}
}
