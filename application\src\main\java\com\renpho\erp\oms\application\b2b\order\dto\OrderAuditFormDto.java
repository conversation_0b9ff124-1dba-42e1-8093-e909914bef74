package com.renpho.erp.oms.application.b2b.order.dto;

import com.renpho.erp.bpm.api.dto.KeyAndValueDto;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc: 订单审核表单DTO
 * @time: 2025-06-23 16:43:35
 * @author: Alina
 */
@Data
public class OrderAuditFormDto {
    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 客户名称
     */
    private List<KeyAndValueDto> customerCompanyName;

    /**
     * 订单金额
     */
    private BigDecimal totalAmount;

    /**
     * 已付款金额
     */
    private BigDecimal paymentAmount;

    /**
     * 保险公司
     */
    private String insuranceCompany;

    /**
     * 保险额度
     */
    private BigDecimal insuredAmount;

    /**
     * 已用额度
     */
    private BigDecimal usedCreditAmount;

    /**
     * 剩余额度
     */
    private BigDecimal remainingAmount;

    /**
     * 发货协议
     */
    private List<KeyAndValueDto> incotermsCode;

    /**
     * 付款协议
     */
    private List<KeyAndValueDto> paymentTermsCode;

    /**
     * 订单创建时间
     */
    private LocalDateTime createTime;

    /**
     * 海外区域经理用户id
     */
    private List<KeyAndValueDto> countryManagerUserId;

    /**
     * 销售助理用户id
     */
    private List<KeyAndValueDto> salesAssistantUserId;
}
