package com.renpho.erp.oms.application.ordershipaudit.channel.wmt;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import jakarta.validation.constraints.NotBlank;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Data
public class WmtWfsChannelOrderShipExcelDto {
    @NotBlank(message = "{ORDER_SHIP_AUDIT_STORE_NAME}")
    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String shop;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_ORDER_NO}")
    @ExcelHeadName(zhCnName = "销售单号", enName = "SO#")
    private String orderNo = "";
    @NotBlank(message = "{ORDER_SHIP_AUDIT_WMT_STORE_NAME}")
    @ExcelHeadName(zhCnName = "沃尔玛发货店铺", enName = "Walmart Shipment Shop")
    private String wmtShop;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_AMZ_SHIP_SPEED}")
    @ExcelHeadName(zhCnName = "运输速度", enName = "Shipping Speed")
    private String shipSpeedCategory;
    @ExcelHeadName(zhCnName = "通知邮箱", enName = "Notification Email")
    private String email;
    @ExcelHeadName(zhCnName = "订单MSKU", enName = "Order MSKU")
    private String msku;
    @ExcelHeadName(zhCnName = "订单PSKU", enName = "Order PSKU")
    private String psku;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_WMT_MSKU}")
    @ExcelHeadName(zhCnName = "沃尔玛MSKU", enName = "Walmart MSKU")
    private String wmtMsku;



    @ExcelHeadName(zhCnName = "错误信息", enName = "error info")
    private String errorInfo;

    @ExcelIgnore
    private List<String> errorInfoList = new ArrayList<>();

    public void addErrorInfo(String errorInfo) {
        this.errorInfoList.add(errorInfo);
    }

    public void buildErrorInfo() {
        errorInfo = String.join("\n", errorInfoList);
    }
}
