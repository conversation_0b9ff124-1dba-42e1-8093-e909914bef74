CREATE TABLE `oms_b2b_order`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_no`                varchar(32) NOT NULL COMMENT 'B2B订单号',
    `refer_no`                varchar(32)          DEFAULT NULL COMMENT '客户参考号',
    `invoice_no`              varchar(32)          DEFAULT NULL COMMENT '发票号',
    `customer_id`             bigint      NOT NULL COMMENT '客户id',
    `order_status`            int         NOT NULL COMMENT '订单状态 1-待处理 2-订单审核中 3-备货中 4-部分发货 5-已发货 6-已完结 7-已取消',
    `shipment_review_status`  int         NOT NULL COMMENT '发货审核状态 1-未发起 2-发货审核中 3-通过 4-未通过（拒绝or撤回）',
    `refuse_reason`           varchar(512)         DEFAULT NULL COMMENT '审批拒绝原因',
    `shipped_time`            datetime             DEFAULT NULL COMMENT '发货时间',
    `invoice_time`            datetime             DEFAULT NULL COMMENT '发票时间',
    `country_manager_user_id` int         NOT NULL COMMENT '海外区域经理用户id',
    `sales_assistant_user_id` int         NOT NULL COMMENT '销售助理用户id',
    `remark`                  varchar(256)         DEFAULT NULL COMMENT '备注信息',
    `create_time`             datetime    NOT NULL COMMENT '创建时间',
    `create_by`               int         NOT NULL COMMENT '创建人ID',
    `update_time`             datetime    NOT NULL COMMENT '更新时间',
    `update_by`               int         NOT NULL COMMENT '更新人ID',
    `is_deleted`              int         NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                       `idx_order_no` (`order_no`)
) COMMENT='B2B-订单表';

CREATE TABLE `oms_b2b_order_customer`
(
    `id`                    bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`              bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `sales_company_id`      int          NOT NULL COMMENT '销售公司id',
    `sales_company_name_cn` varchar(255) NOT NULL COMMENT '销售公司名称中文',
    `sales_company_name_en` varchar(255) NOT NULL COMMENT '销售公司名称英文',
    `incoterms_code`        varchar(64)  NOT NULL COMMENT '贸易条款编码',
    `payment_terms_code`    varchar(64)  NOT NULL COMMENT '付款条款编码',
    `customer_company_name` varchar(100) NOT NULL COMMENT '客户公司名称',
    `country`               varchar(10)           DEFAULT NULL COMMENT '国家',
    `country_manager_name`  varchar(64)  NOT NULL COMMENT '海外区域经理名称',
    `country_manager_code`  varchar(64)  NOT NULL COMMENT '海外区域经理工号',
    `sales_assistant_name`  varchar(64)  NOT NULL COMMENT '销售助理名称',
    `sales_assistant_code`  varchar(64)  NOT NULL COMMENT '销售助理工号',
    `create_time`           datetime     NOT NULL COMMENT '创建时间',
    `create_by`             int          NOT NULL COMMENT '创建人ID',
    `update_time`           datetime     NOT NULL COMMENT '更新时间',
    `update_by`             int          NOT NULL COMMENT '更新人ID',
    `is_deleted`            int          NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                     `idx_order_id` (`order_id`)
) COMMENT='B2B-订单客户表';

CREATE TABLE `oms_b2b_order_item`
(
    `id`                      bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`                bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `psku`                    varchar(64)    NOT NULL COMMENT '采购SKU',
    `image_id`                varchar(64)             DEFAULT NULL COMMENT '图片id',
    `psku_name_zh`            varchar(50)    NOT NULL COMMENT '采购SKU-中文名称',
    `psku_name_en`            varchar(110)   NOT NULL COMMENT '采购SKU-英文名称',
    `barcode`                 varchar(64)             DEFAULT NULL COMMENT '条码',
    `ordered_quantity`        int            NOT NULL COMMENT '下单数量',
    `shipped_quantity`        int            NOT NULL COMMENT '发货数量',
    `number_of_units_per_box` int                     DEFAULT NULL COMMENT '装箱数量（每箱的psku数量）',
    `unit_price`              decimal(14, 2) NOT NULL COMMENT '单价',
    `tax_rate`                decimal(7, 4)  NOT NULL COMMENT '税率，0.2代表20%',
    `tax`                     decimal(14, 2) NOT NULL COMMENT '税金',
    `sub_total`               decimal(14, 2) NOT NULL COMMENT '小计，下单数量*单价+税金',
    `create_time`             datetime       NOT NULL COMMENT '创建时间',
    `create_by`               int            NOT NULL COMMENT '创建人ID',
    `update_time`             datetime       NOT NULL COMMENT '更新时间',
    `update_by`               int            NOT NULL COMMENT '更新人ID',
    `is_deleted`              int            NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                       `idx_order_id` (`order_id`)
) COMMENT='B2B-订单商品表';

CREATE TABLE `oms_b2b_order_amount`
(
    `id`             bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`       bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `currency`       varchar(8)     NOT NULL COMMENT '币种',
    `product_price`  decimal(14, 2) NOT NULL COMMENT '商品收入',
    `tax`            decimal(14, 2) NOT NULL COMMENT '税金',
    `shipping_fee`   decimal(14, 2) NOT NULL COMMENT '运费收入',
    `other_fees`     decimal(14, 2)          DEFAULT NULL COMMENT '其他收入',
    `total_amount`   decimal(14, 2) NOT NULL COMMENT '金额合计',
    `receipt_amount` decimal(14, 2)          DEFAULT NULL COMMENT '已收款的金额',
    `receipt_rate`   decimal(7, 4)  NOT NULL COMMENT '已收款比例，0.2代表20%',
    `fees_detail`    json                    DEFAULT NULL COMMENT '费用明细，json数组',
    `create_time`    datetime       NOT NULL COMMENT '创建时间',
    `create_by`      int            NOT NULL COMMENT '创建人ID',
    `update_time`    datetime       NOT NULL COMMENT '更新时间',
    `update_by`      int            NOT NULL COMMENT '更新人ID',
    `is_deleted`     int            NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY              `idx_order_id` (`order_id`)
) COMMENT='B2B-订单金额表';

CREATE TABLE `oms_b2b_order_customer_contact`
(
    `id`                  bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`            bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `contact_type`        int          NOT NULL COMMENT '联系人类型，1-主联系人 2-采购 3-物流 4-财务',
    `customer_contact_id` bigint       NOT NULL COMMENT '客户联系信息id',
    `contact_position`    varchar(100) NOT NULL COMMENT '岗位',
    `contact_name`        varchar(100)          DEFAULT NULL COMMENT '名字',
    `contact_email`       varchar(100)          DEFAULT NULL COMMENT '邮箱',
    `contact_phone`       varchar(64)           DEFAULT NULL COMMENT '电话',
    `remark`              varchar(200)          DEFAULT NULL COMMENT '备注',
    `create_time`         datetime     NOT NULL COMMENT '创建时间',
    `create_by`           int          NOT NULL COMMENT '创建人ID',
    `update_time`         datetime     NOT NULL COMMENT '更新时间',
    `update_by`           int          NOT NULL COMMENT '更新人ID',
    `is_deleted`          int          NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                   `idx_order_id` (`order_id`)
) COMMENT='B2B-订单客户联系表';

CREATE TABLE `oms_b2b_order_customer_invoice`
(
    `id`              bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`        bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `attn_to`         varchar(100) NOT NULL COMMENT '接收人',
    `invoice_company` varchar(100) NOT NULL COMMENT '开票公司',
    `address`         varchar(255) NOT NULL COMMENT '客户地址',
    `zip_code`        varchar(20)  NOT NULL COMMENT '邮政编码',
    `city`            varchar(100) NOT NULL COMMENT '城市',
    `country`         varchar(100) NOT NULL COMMENT '国家',
    `contact_number`  varchar(50)  NOT NULL COMMENT '联系号码',
    `vat_number`      varchar(50)  NOT NULL COMMENT '税号',
    `create_time`     datetime     NOT NULL COMMENT '创建时间',
    `create_by`       int          NOT NULL COMMENT '创建人ID',
    `update_time`     datetime     NOT NULL COMMENT '更新时间',
    `update_by`       int          NOT NULL COMMENT '更新人ID',
    `is_deleted`      int          NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY               `idx_order_id` (`order_id`)
) COMMENT='B2B-订单客户发票表';

CREATE TABLE `oms_b2b_order_address`
(
    `id`               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`         bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `attn_to`          varchar(100) NOT NULL COMMENT '收货人',
    `shipping_company` varchar(100) NOT NULL COMMENT '收货方公司',
    `address`          varchar(256) NOT NULL COMMENT '收货地址',
    `zip_code`         varchar(20)  NOT NULL COMMENT '邮编',
    `city`             varchar(100) NOT NULL COMMENT '城市',
    `country`          varchar(100) NOT NULL COMMENT '国家',
    `contact_number`   varchar(50)  NOT NULL COMMENT '联系号码',
    `comment`          varchar(256)          DEFAULT NULL COMMENT '备注',
    `create_time`      datetime     NOT NULL COMMENT '创建时间',
    `create_by`        int          NOT NULL COMMENT '创建人ID',
    `update_time`      datetime     NOT NULL COMMENT '更新时间',
    `update_by`        int          NOT NULL COMMENT '更新人ID',
    `is_deleted`       int          NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                `idx_order_id` (`order_id`)
) COMMENT='B2B-订单收货地址表';

CREATE TABLE `oms_b2b_order_shipment_requirement`
(
    `id`                       bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`                 bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `request_delivery_date`    date     NOT NULL COMMENT '货物最晚交期',
    `palletization_required`   int      NOT NULL DEFAULT '0' COMMENT '是否打托，0-否 1-是，默认0',
    `bl_type`                  int               DEFAULT NULL COMMENT '提单类型，1-无要求 2-电放提单 3-正本提单 4-海运单 ',
    `master_carton_label_name` varchar(128)      DEFAULT NULL COMMENT '外箱标签文件名',
    `master_carton_label_url`  varchar(256)      DEFAULT NULL COMMENT '外箱标签文件url',
    `other_requirements`       varchar(512)      DEFAULT NULL COMMENT '其他要求',
    `create_time`              datetime NOT NULL COMMENT '创建时间',
    `create_by`                int      NOT NULL COMMENT '创建人ID',
    `update_time`              datetime NOT NULL COMMENT '更新时间',
    `update_by`                int      NOT NULL COMMENT '更新人ID',
    `is_deleted`               int      NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                        `idx_order_id` (`order_id`)
) COMMENT='B2B-订单发货要求表';

CREATE TABLE `oms_b2b_order_remark`
(
    `id`          bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `order_id`    bigint unsigned NOT NULL COMMENT 'B2B订单ID',
    `remark`      varchar(256) NOT NULL COMMENT '备注信息',
    `create_time` datetime     NOT NULL COMMENT '创建时间',
    `create_by`   int          NOT NULL COMMENT '创建人ID',
    `update_time` datetime     NOT NULL COMMENT '更新时间',
    `update_by`   int          NOT NULL COMMENT '更新人ID',
    `is_deleted`  int          NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY           `idx_order_id` (`order_id`)
) COMMENT='B2B-订单备注表';

-- 公司添加B2B收款账号
ALTER TABLE renpho_erp_mdm.mdm_company
    ADD b2b_fms_fund_account_id int(11) NULL COMMENT 'B2B收款账号(fms_fund_account的表id)' after status;

-- b2b客户 信息表修改
ALTER TABLE renpho_erp_oms.oms_customer_sales_company_terms_info
    ADD sales_company_id int(11) NULL COMMENT '销售公司id' after customer_id;

ALTER TABLE renpho_erp_oms.oms_customer_sales_company_terms_info DROP COLUMN sales_company;

ALTER TABLE renpho_erp_oms.oms_customer_sales_company_terms_info DROP COLUMN sales_company_country;

ALTER TABLE renpho_erp_oms.oms_customer_sales_company_terms_info DROP COLUMN receiving_account_id;

DROP TABLE oms_b2b_receiving_account_config;


-- b2b收款单
CREATE TABLE renpho_erp_oms.`oms_b2b_receipt_order`
(
    `id`                               bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
    `receipt_order_no`                 varchar(32) NOT NULL COMMENT '自定义收款单号',
    `bank_refer_no`                    varchar(256)         DEFAULT NULL COMMENT '银行参考号',
    `b2b_order_id`                     bigint unsigned DEFAULT NULL COMMENT 'b2b订单id',
    `b2b_order_no`                     varchar(32) NOT NULL COMMENT '收款所属的订单号(B2B订单号)',
    `status`                           int         NOT NULL DEFAULT '1' COMMENT '状态 1-待确认,2-已确认,3-已取消',
    `fms_fund_account_currency_id`     bigint unsigned DEFAULT NULL COMMENT '银行账号(fms_fund_account_currency表的id)',
    `fms_fund_account_currency_cnname` varchar(256)         DEFAULT NULL COMMENT '银行账号中文名称',
    `fms_fund_account_currency_enname` varchar(256)         DEFAULT NULL COMMENT '银行账号英文名称',
    `customer_paid_amount`             decimal(18, 2)       DEFAULT NULL COMMENT '客户付款金额',
    `customer_paid_time`               datetime             DEFAULT NULL COMMENT '客户付款时间',
    `customer_paid_info_file_url`      varchar(300)         DEFAULT NULL COMMENT '客戶付款信息文件url',
    `customer_paid_info_file_name`     varchar(300)         DEFAULT NULL COMMENT '客戶付款信息文件名',
    `receipt_info_file_url`            varchar(300)         DEFAULT NULL COMMENT '收款信息文件url',
    `receipt_info_file_name`           varchar(300)         DEFAULT NULL COMMENT '收款信息文件名',
    `receipt_amount`                   decimal(18, 2)       DEFAULT NULL COMMENT '收款金额',
    `fees_amount`                      decimal(18, 2)       DEFAULT NULL COMMENT '费用金额',
    `receipt_time`                     datetime             DEFAULT NULL COMMENT '收款时间',
    `create_by`                        int         NOT NULL COMMENT '创建人ID',
    `create_time`                      datetime    NOT NULL COMMENT '创建时间',
    `update_by`                        int         NOT NULL COMMENT '更新人ID',
    `update_time`                      datetime    NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_deleted`                       int         NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
    PRIMARY KEY (`id`),
    KEY                                `idx_receipt_order_no` (`receipt_order_no`) USING BTREE,
    KEY                                `idx_update_time` (`update_time`) USING BTREE,
    KEY                                `idx_create_time` (`create_time`) USING BTREE,
    KEY                                `idx_customer_paid_time` (`customer_paid_time`) USING BTREE,
    KEY                                `idx_receipt_time` (`receipt_time`) USING BTREE,
    KEY                                `idx_bank_refer_no` (`bank_refer_no`) USING BTREE,
    KEY                                `idx_fms_fund_account_currency_id` (`fms_fund_account_currency_id`) USING BTREE,
    KEY                                `idx_b2b_order_id` (`b2b_order_id`) USING BTREE,
    KEY                                `idx_b2b_order_no` (`b2b_order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='b2b收款单';