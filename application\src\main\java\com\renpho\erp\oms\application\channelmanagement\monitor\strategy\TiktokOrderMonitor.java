package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.repository.TtOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * Tiktok订单监视器
 * <AUTHOR>
 */
@Component
public class TiktokOrderMonitor extends AbstractChannelOrderMonitor {

	private final TtOrderRepository ttOrderRepository;

	public TiktokOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			TtOrderRepository ttOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.ttOrderRepository = ttOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.TT_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return ttOrderRepository.count(storeAuthorization.getTtkShopId(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		return storeAuthorization.getTtkShopId();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 按照Tiktok，店铺ID统计
		return storeAuthorizationList.stream()
			.filter(s -> storeNames.contains(s.getStoreName()))
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			// Tiktok，店铺ID
			.map(StoreAuthorizationVo.Authorization::getTtkShopId)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.map(ttkShopId -> {
				StoreAuthorization storeAuthorization = new StoreAuthorization();
				storeAuthorization.setTtkShopId(ttkShopId);
				// 店铺名称
				storeAuthorization.setStoreNames(storeAuthorizationList.stream()
					.filter(s -> Objects.nonNull(s.getAuthorization())
							&& StringUtils.equals(s.getAuthorization().getTtkShopId(), ttkShopId))
					.map(StoreAuthorizationVo::getStoreName)
					.collect(Collectors.toSet()));
				return storeAuthorization;
			})
			.toList();

	}
}
