package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics;

import java.util.Set;

import org.springframework.beans.factory.InitializingBean;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

import cn.hutool.extra.spring.SpringUtil;

/**
 * 抽象-分物流模板
 * <AUTHOR>
 */
public abstract class AbstractAllocateLogisticsTemplate implements AllocateLogistics, InitializingBean {

	@Override
	public void afterPropertiesSet() throws Exception {
		Set<FulfillmentServiceType> fulfillmentServiceTypes = this.getFulfillmentServiceTypes();
		// 使用代理对象，不用this，避免事务失效等问题
		fulfillmentServiceTypes.forEach(t -> AllocateLogisticsFactory.register(t, SpringUtil.getBean(this.getClass())));
	}
}
