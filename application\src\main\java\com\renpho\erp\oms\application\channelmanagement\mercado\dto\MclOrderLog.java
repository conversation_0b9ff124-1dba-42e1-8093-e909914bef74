package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class MclOrderLog {

	/**
	 * 美客多用户id
	 */
	private String mclUserId;

	/**
	 * 美客多订单id
	 */
	private Long mercadoOrderId;

	/**
	 * 订单创建时间
	 */
	private LocalDateTime dateCreated;

	/**
	 * 订单关闭时间
	 */
	private LocalDateTime dateClosed;

	/**
	 * 订单最新更新时间
	 */
	private LocalDateTime lastUpdated;

	/**
	 * 生产终止日期
	 */
	private LocalDateTime manufacturingEndingDate;

	/**
	 * feedback json
	 */
	private String feedBack;

	/**
	 * mediations json
	 */
	private String mediations;

	/**
	 * 备注
	 */
	private String comments;

	private Long packId;

	private String pickupId;

	/**
	 * order_request json
	 */
	private String orderRequest;

	/**
	 * 是否自发货 0:否 1:是
	 */
	private Boolean fulfilled;

	/**
	 * 支付金额
	 */
	private BigDecimal paidAmount;

	/**
	 * 优惠信息json
	 */
	private String coupon;

	private LocalDateTime expirationDate;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 运单id
	 */
	private Long shippingId;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 状态明细码
	 */
	private String statusDetailCode;

	/**
	 * 状态明细描述
	 */
	private String statusDetailDescription;

	/**
	 * 买家信息json
	 */
	private String buyer;

	/**
	 * 卖家id
	 */
	private Long sellerId;

	/**
	 * 税费
	 */
	private BigDecimal taxAmount;

	/**
	 * 税费币种id
	 */
	private String taxCurrencyId;

	/**
	 * context json
	 */
	private String context;

	private List<MclOrderItemLog> mclOrderItemLogList;

	private List<MclOrderPaymentLog> mclOrderPaymentLogList;
}
