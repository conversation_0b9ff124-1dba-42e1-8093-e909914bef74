package com.renpho.erp.oms.application.returnorder.converter;

import com.renpho.erp.oms.application.returnorder.command.ReturnOrderAddCmd;
import com.renpho.erp.oms.application.returnorder.dto.ReturnOrderExportExcel;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderItemVO;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderPageVO;
import com.renpho.erp.oms.domain.returnorder.model.*;
import org.mapstruct.*;


/**
 * @desc:
 * @time: 2025-03-18 10:05:52
 * @author: Alina
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface ReturnOrderVoConvertor {

	ReturnOrderPageVO toPageVO(ReturnOrder returnOrder);

	@AfterMapping
	default void setItem(@MappingTarget ReturnOrderPageVO vo, ReturnOrder returnOrder) {
		if (returnOrder.getReturnOrderItems() != null && !returnOrder.getReturnOrderItems().isEmpty()) {
			ReturnOrderItem firstItem = returnOrder.getReturnOrderItems().get(0);
			ReturnOrderPageVO.Item item = new ReturnOrderPageVO.Item();
			item.setId(firstItem.getId().getId());
			item.setItem(firstItem.getItem());
			item.setMsku(firstItem.getMsku());
			item.setReturnQuantity(firstItem.getReturnQuantity());
			item.setPsku(firstItem.getPsku());
			item.setFnSku(firstItem.getFnSku());
			item.setImageId(firstItem.getImageId());
			item.setItemCount(returnOrder.getItemCount());
			vo.setItem(item);
		}
	}

	ReturnOrderAggRoot toDomain(ReturnOrderAddCmd cmd);

	@Mapping(target = "id", expression = "java(returnOrderItem.getId().getId())")
	ReturnOrderItemVO toReturnOrderItemVO(ReturnOrderItem returnOrderItem);

	ReturnOrderExportExcel toReturnOrderExportExcel(ReturnOrderExcel returnOrderExcel);


}
