package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderVO;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;

import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLogConvertor;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrder;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.walmart.mapper.WmtOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.walmart.po.WmtOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service(ChannelCode.WMT_CHANNEL_CODE)
@AllArgsConstructor
public class WmtOrderPlatformService extends AbstractDefaultPlatfomService {
	private final WmtOrderRepository wmtOrderRepository;
	private final PlatformOrderConvertor platformOrderConvertor;
	private final WmtOrderLogConvertor wmtOrderLogConvertor;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private WmtOrderMapper wmtOrderMapper;

	@Override
	protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<WmtOrderPage> page = wmtOrderRepository.page(platformPageQuery);
		List<WmtOrderPage> wmtOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(wmtOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}
		List<PlatformOrderVO> records = wmtOrderPageList.stream().map(wmOrderPage -> {
			return platformOrderConvertor.walmartOrderToVO(wmOrderPage);
		}).collect(Collectors.toList());

		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		WmtOrder wmtOrder = wmtOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(wmtOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		WmtOrderVO wmtOrderVO = wmtOrderLogConvertor.toVO(wmtOrder);
		// 查询店铺
		wmtOrderVO.setStoreName(getStoreName(wmtOrder.getStoreId()));
		return R.success(wmtOrderVO);
	}

	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> platformOrderDtos = wmtOrderMapper
			.selectList(Wrappers.<WmtOrderPO> lambdaQuery()
				.select(WmtOrderPO::getPurchaseOrderId, WmtOrderPO::getLastMpdsSyncOrderTime, WmtOrderPO::getStoreId)
				.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, WmtOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
				.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, WmtOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
			.stream()
			.map(x -> monitorPlatformOrderConvertor.walmartOrderToDto(x))
			.collect(Collectors.toList());
		return platformOrderDtos;
	}
}
