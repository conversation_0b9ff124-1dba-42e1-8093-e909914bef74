package com.renpho.erp.oms.application.ordershipaudit;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.karma.dto.R;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

import javax.annotation.PostConstruct;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.EnumMap;
import java.util.Map;

/**
 * 销售单审核excel操作
 *
 * <AUTHOR>
 * @since 2025/2/27
 */
public interface ChannelOrderShipExcelService {
    FulfillmentServiceType getFulfillmentServiceType();

    /**
     * 通过excel流读取并创建销售单发货审核
     *
     * @return
     */
    R<String> readExcel(InputStream inputStream);

    /**
     * 输出excel模板到流
     */
    void writeExcelTemplate(OutputStream outputStream);


    @PostConstruct
    default void init() {
        ChannelOrderShipExcelService.Factory.MAP.put(this.getFulfillmentServiceType(), this);
    }

    @AllArgsConstructor(access = AccessLevel.PRIVATE)
    class Factory {
        private static final Map<FulfillmentServiceType, ChannelOrderShipExcelService> MAP = new EnumMap<>(FulfillmentServiceType.class);

        public static ChannelOrderShipExcelService getInstance(FulfillmentServiceType fulfillmentServiceType) {
            return MAP.get(fulfillmentServiceType);
        }
    }
}
