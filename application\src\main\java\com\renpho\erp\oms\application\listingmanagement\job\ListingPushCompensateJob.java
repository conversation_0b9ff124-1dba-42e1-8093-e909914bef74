package com.renpho.erp.oms.application.listingmanagement.job;

import com.renpho.erp.oms.application.listingmanagement.dto.ListingCompensateParam;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.ListingPusher;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.ListingPusherRegister;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourcePushMqStatusEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * Listing推送-补偿job
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class ListingPushCompensateJob {

	private final SalesChannelListingSourceRepository channelListingSourceRepository;
	private final ListingPusherRegister listingPusherRegister;

	/**
	 * 补偿推送Listing Job
	 */
	@XxlJob("compensatePushListingJob")
	public void compensateParseListingJob() {
		// 通过XXL-JOB参数
		ListingCompensateParam listingCompensateParam = ListingJobHelper.getListingCompensateParam();
		// 销售渠道编码集合
		Set<String> salesChannelCodeSet = listingCompensateParam.getSalesChannelCodeSet();
		// 如果为空，设置默认的销售渠道
		if (CollectionUtils.isEmpty(salesChannelCodeSet)) {
			salesChannelCodeSet = Set.of(ChannelCode.MCL_CHANNEL_CODE, ChannelCode.TT_CHANNEL_CODE);
		}
		// 分批查询的条数，如果为空，默认200
		Integer pageSize = Optional.ofNullable(listingCompensateParam.getPageSize()).orElse(200);
		// 补偿推送Listing
		this.compensatePushListing(salesChannelCodeSet, pageSize);
	}

	/**
	 * 补偿推送Listing
	 * @param salesChannelCodeSet 销售渠道编码
	 */
	public void compensatePushListing(Set<String> salesChannelCodeSet, Integer pageSize) {
		// 推送状态集合
		Set<Integer> pushStatusSet = Set.of(ListingSourcePushMqStatusEnum.WAIT_PUSH.getStatus(),
				ListingSourcePushMqStatusEnum.PUSH_FAIL.getStatus());
		// 循环分批查询（直到无数据）
		Long lastId = null;
		while (true) {
			// 分批查询原始Listing
			List<SalesChannelListingSource> listingSourceList = channelListingSourceRepository
				.findBySalesChannelCodeAndPushStatus(salesChannelCodeSet, lastId, pageSize, pushStatusSet);
			// 无数据，结束循环
			if (CollectionUtils.isEmpty(listingSourceList)) {
				break;
			}
			// 遍历，推送Listing
			listingSourceList.forEach(this::pushListing);
			// 上一页最后一条的id，用于检索下一页
			lastId = listingSourceList.stream()
				.max(Comparator.comparingLong(SalesChannelListingSource::getId))
				.map(SalesChannelListingSource::getId)
				.orElse(null);
		}
	}

	/**
	 * 推送Listing
	 * @param listingSource 原始Listing
	 */
	private void pushListing(SalesChannelListingSource listingSource) {
		// 获取推送器
		ListingPusher pusher = listingPusherRegister.getPusher(listingSource.getSalesChannelCode());
		if (Objects.isNull(pusher)) {
			XxlJobHelper.log("销售渠道编码：{}，对应的推送器不存在", listingSource.getSalesChannelCode());
			return;
		}
		// 推送Listing
		pusher.pushListing(listingSource);
	}
}
