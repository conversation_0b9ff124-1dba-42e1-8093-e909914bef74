package com.renpho.erp.oms.application.vc.order.service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.domain.vc.order.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcParseSourceOrderStatus;
import com.renpho.erp.oms.infrastructure.common.util.BatchUtils;
import com.renpho.erp.oms.infrastructure.manager.AmzVcOrderSourceJsonManager;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单转换 定时任务.
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderConvertService {

	private final AmzVcOrderSourceJsonManager amzVcOrderSourceJsonManager;
	private final VcOrderRepository vcOrderRepository;
	private final VcOrderConvertCreateService vcOrderConvertCreateService;
	private final VcOrderConvertModifyService vcOrderConvertModifyService;

	/**
	 * 转换VC订单.
	 *
	 * @param cmd 转换指令
	 */
	public void convert(VcOrderConvertCmd cmd) {
		// 分批的条数
		Integer batchSize = Optional.ofNullable(cmd.getSize()).orElse(1000);
		BatchUtils.batchDeal(
				// 根据 lastId 和 size 获取列表
				(Long lastId, Integer size) -> amzVcOrderSourceJsonManager.findNeedParseAmzVcOrderIds(cmd.getStoreIds(),
						cmd.getVcPurchaseNos(), cmd.getMaxFailCount(), lastId, batchSize),
				// 对获取到的这一批列表进行处理
				this::convert,
				// 恒等函数
				Function.identity(),
				// 批处理大小
				batchSize);
	}

	/**
	 * 转换VC订单.
	 * @param ids 亚马逊vc源订单主键集
	 */
	public void convert(List<Long> ids) {
		ids.forEach(this::convert);
	}

	/**
	 * 转换VC订单.
	 * @param id 亚马逊vc源订单主键
	 */
	public void convert(Long id) {
		// 查询亚马逊vc源订单-值对象
		AmzVcOrder amzVcOrder = amzVcOrderSourceJsonManager.findById(id);
		try {
			// 转换VC订单
			SpringUtil.getBean(this.getClass()).convert(amzVcOrder);
		}
		catch (Exception e) {
			log.error(String.format("convert vc order error: %s", id), e);
			// 更新为解析失败
			amzVcOrderSourceJsonManager.updateParseStatus(amzVcOrder.getId(), VcParseSourceOrderStatus.FAILED_PARSE.getValue(),
					Optional.ofNullable(amzVcOrder.getFailCount()).orElse(0) + 1);
		}
	}

	/**
	 * 转换VC订单.
	 * @param amzVcOrder 亚马逊VC订单
	 */
	@Lock4j(name = "vc:order:convert", keys = { "#amzVcOrder.storeId", "#amzVcOrder.purchaseOrderNumber" }, acquireTimeout = 0L)
	public void convert(AmzVcOrder amzVcOrder) {
		// 查询VC订单ID
		Long orderId = vcOrderRepository.findIdByVcPurchaseNoAndStoreId(amzVcOrder.getPurchaseOrderNumber(), amzVcOrder.getStoreId());
		// 不存在（新增）
		if (Objects.isNull(orderId)) {
			vcOrderConvertCreateService.create(amzVcOrder);
		}
		// 修改
		else {
			vcOrderConvertModifyService.modify(amzVcOrder, orderId);
		}
		// 更新为已解析
		amzVcOrderSourceJsonManager.updateParseStatus(amzVcOrder.getId(), VcParseSourceOrderStatus.HAS_PARSED.getValue(), null);
	}

	@Getter
	@Setter
	public static class VcOrderConvertCmd {

		/**
		 * 最大失败次数
		 */
		private Integer maxFailCount;

		/**
		 * 店铺id集
		 */
		private Set<Integer> storeIds;

		/**
		 * vc采购单号集
		 */
		private Set<String> vcPurchaseNos;

		/**
		 * 分批的条数
		 */
		private Integer size;
	}
}
