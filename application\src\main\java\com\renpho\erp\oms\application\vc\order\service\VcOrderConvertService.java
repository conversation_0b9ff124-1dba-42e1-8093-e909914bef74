package com.renpho.erp.oms.application.vc.order.service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;

import org.springframework.stereotype.Service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.repository.AmzVcOrderSourceJsonRepository;
import com.renpho.erp.oms.domain.vc.order.repository.VcOrderRepository;
import com.renpho.erp.oms.infrastructure.common.util.BatchUtils;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单转换 定时任务.
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderConvertService {

	private final AmzVcOrderSourceJsonRepository amzVcOrderSourceJsonRepository;
	private final VcOrderRepository vcOrderRepository;
	private final VcOrderConvertCreateService vcOrderConvertCreateService;
	private final VcOrderConvertModifyService vcOrderConvertModifyService;

	/**
	 * 转换VC订单.
	 *
	 * @param cmd 转换指令
	 */
	public void convert(VcOrderConvertCmd cmd) {
		BatchUtils.batchDeal(
				// 根据 lastId 和 size 获取列表
				(Long lastId, Integer size) -> amzVcOrderSourceJsonRepository.findNeedParseAmzVcOrderIds(cmd.getStoreIds(),
						cmd.getVcPurchaseNos(), cmd.getSize()),
				// 对获取到的这一批列表进行处理
				this::convert,
				// 恒等函数
				Function.identity(),
				// 批处理大小
				Optional.ofNullable(cmd.getSize()).orElse(1000));
	}

	/**
	 * 转换VC订单.
	 * @param ids 亚马逊vc源订单主键集
	 */
	private void convert(List<Long> ids) {
		ids.forEach(id -> {
			AmzVcOrderAggRoot amzVcOrderAggRoot = amzVcOrderSourceJsonRepository.findById(id);
			try {
				SpringUtil.getBean(this.getClass()).convert(amzVcOrderAggRoot);
			}
			catch (Exception e) {
				log.error(String.format("convert vc order error: %s", id), e);
			}
		});
	}

	/**
	 * 转换VC订单.
	 * @param amzVcOrder 亚马逊VC订单
	 */
	@Lock4j(name = "vc:order:convert", keys = { "#amzVcOrder.storeId", "#amzVcOrder.purchaseOrderNumber" }, acquireTimeout = 0L)
	public void convert(AmzVcOrderAggRoot amzVcOrder) {
		// 查询VC订单ID
		Long orderId = vcOrderRepository.findIdByVcPurchaseNoAndStoreId(amzVcOrder.getPurchaseOrderNumber(), amzVcOrder.getStoreId());
		// 不存在（新增）
		if (Objects.isNull(orderId)) {
			vcOrderConvertCreateService.create(amzVcOrder);
		}
		// 修改
		else {
			vcOrderConvertModifyService.modify(amzVcOrder, orderId);
		}
	}

	@Getter
	@Setter
	public static class VcOrderConvertCmd {

		/**
		 * 店铺id集
		 */
		private Set<Integer> storeIds;

		/**
		 * vc采购单号集
		 */
		private Set<String> vcPurchaseNos;

		/**
		 * 分批的条数
		 */
		private Integer size;
	}
}
