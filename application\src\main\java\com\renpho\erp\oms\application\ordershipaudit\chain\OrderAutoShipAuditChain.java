package com.renpho.erp.oms.application.ordershipaudit.chain;

import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.chain.handler.OrderAutoShipAuditHandler;
import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.xxl.job.core.context.XxlJobHelper;

import lombok.RequiredArgsConstructor;

/**
 * 订单自动发货审核-链
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class OrderAutoShipAuditChain {

	/**
	 * Spring会自动将Bean按Ordered接口排序注入（处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建）
	 */
	private final List<OrderAutoShipAuditHandler> orderedHandlers;
	private final SaleOrderRepository saleOrderRepository;

	/**
	 * 订单自动发货审核-链处理
	 * @param orderAggRoot 订单聚合根
	 */
	public void process(SaleOrderAggRoot orderAggRoot) {
		// 组装参数
		OrderAutoShipAuditDTO orderAutoShipAudit = new OrderAutoShipAuditDTO();
		orderAutoShipAudit.setSaleOrderAggRoot(orderAggRoot);
		// 执行处理器链（1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建）
		for (OrderAutoShipAuditHandler handler : orderedHandlers) {
			String failMsg = handler.handle(orderAutoShipAudit);
			// 自动发货审核失败
			if (StringUtils.isNotBlank(failMsg)) {
				this.autoAllocateFulfillmentFail(orderAggRoot, failMsg);
				XxlJobHelper.log("订单号：{}，自动发货审核失败，原因：{}", orderAutoShipAudit.getOrderNo(), failMsg);
				break;
			}
		}
	}

	/**
	 * 订单自动分仓分物流（发货审核）失败
	 * @param orderAggRoot 订单聚合根
	 * @param failMsg 失败原因
	 */
	private void autoAllocateFulfillmentFail(SaleOrderAggRoot orderAggRoot, String failMsg) {
		// 更新自动分仓分物流（发货审核）状态为失败、记录异常信息
		orderAggRoot.autoAllocateFulfillmentFail(failMsg);
		saleOrderRepository.updateAutoAllocateFulfillmentFail(orderAggRoot);
	}
}
