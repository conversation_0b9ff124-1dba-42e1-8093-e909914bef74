package com.renpho.erp.oms.application.listingmanagement.strategy.pusher;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Listing推送器注册
 * <AUTHOR>
 */
@Component
public class ListingPusherRegister implements ApplicationContextAware, InitializingBean {

	private ApplicationContext context;

	private static final Map<String, ListingPusher> CACHE = new HashMap<>(1 << 5);

	@Override
	public void setApplicationContext(@NotNull @Nonnull ApplicationContext applicationContext) throws BeansException {
		this.context = applicationContext;
	}

	@Override
	public void afterPropertiesSet() {
		// 从容器获取ListingPusher
		String[] beanNamesForTypes = context.getBeanNamesForType(ListingPusher.class);
		for (String beanNamesForType : beanNamesForTypes) {
			ListingPusher listingPusher = (ListingPusher) context.getBean(beanNamesForType);
			// 销售渠道编码
			String salesChannelCode = listingPusher.getSalesChannelCode();
			// 注册
			this.register(salesChannelCode, listingPusher);
		}
	}

	/**
	 * 注册推送器
	 * @param salesChannelCode 销售渠道编码
	 * @param listingPusher Listing推送器
	 */
	private void register(String salesChannelCode, ListingPusher listingPusher) {
		if (CACHE.containsKey(salesChannelCode)) {
			return;
		}
		CACHE.put(salesChannelCode, listingPusher);
	}

	/**
	 * 获取推送器
	 * @param salesChannelCode 销售渠道编码
	 * @return ListingPuller
	 */
	public ListingPusher getPusher(String salesChannelCode) {
		return CACHE.get(salesChannelCode);
	}
}
