package com.renpho.erp.oms.application.channelmanagement.shopify.optlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.channelmanagement.shopify.service.SpfOrderService;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderLog;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import org.springframework.stereotype.Component;

@Component
public class SpfOrderUpdateSnapSource implements SnapshotDatatSource {
	private final SpfOrderService spfOrderService;

	public JSONObject getOldData(Object[] args) {
		Long id = ((JSONObject) JSONObject.toJSON(args[0])).getObject("id", Long.class);
		return JSON.parseObject(JSON.toJSONString(this.spfOrderService.getLogById(id)));
	}

	public JSONObject getNewData(Object[] args, JSONObject result) {
		SpfOrderLog spfOrderLog = this.spfOrderService.getLogById((Long) result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(spfOrderLog));
	}

	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

	public SpfOrderUpdateSnapSource(final SpfOrderService spfOrderService) {
		this.spfOrderService = spfOrderService;
	}
}
