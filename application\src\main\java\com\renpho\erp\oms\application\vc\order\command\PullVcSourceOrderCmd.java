package com.renpho.erp.oms.application.vc.order.command;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 拉取VC源订单cmd
 * @date 2025/7/7 11:12
 */
@Data
public class PullVcSourceOrderCmd {

    /**
     * 指定开始时间,格式yyyy-MM-dd HH:mm:ss
     */
    private String changedAfter;

    /**
     * 指定结束时间,格式yyyy-MM-dd HH:mm:ss
     */
    private String changedBefore;

    /**
     * 拉取最近多少天,默认7天
     */
    private Integer lastDay;

    /**
     * 店铺id
     */
    private List<Integer> storeIds;
}
