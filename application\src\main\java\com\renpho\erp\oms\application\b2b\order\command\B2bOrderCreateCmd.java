package com.renpho.erp.oms.application.b2b.order.command;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.renpho.erp.oms.infrastructure.common.validation.AllOrNoneNotBlank;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Getter;

/**
 * B2B订单创建命令
 * <AUTHOR>
 */
@Getter
@Builder
public class B2bOrderCreateCmd {

	/**
	 * 客户参考号
	 */
	@Size(max = 32, message = "{B2B_ORDER_REFER_NO_LENGTH_EXCEEDS_LIMIT}")
	private String referNo;

	/**
	 * 订单商品
	 */
	@NotEmpty(message = "{B2B_ORDER_ITEMS_NOT_EMPTY}")
	@Valid
	private List<Item> items;

	/**
	 * 订单金额
	 */
	@Valid
	@NotNull(message = "{B2B_ORDER_AMOUNT_NOT_NULL}")
	private Amount amount;

	/**
	 * 订单收货地址
	 */
	@Valid
	private Address address;

	/**
	 * 订单发货要求
	 */
	@NotNull(message = "{B2B_ORDER_SHIPMENT_REQUIREMENT_NOT_NULL}")
	@Valid
	private ShipmentRequirement shipmentRequirement;

	/**
	 * 订单客户信息
	 */
	@NotNull(message = "{B2B_ORDER_CUSTOMER_NOT_NULL}")
	@Valid
	private Customer customer;

	/**
	 * 客户联系人
	 */
	@NotEmpty(message = "customerContacts cannot be empty")
	@Valid
	private List<CustomerContact> customerContacts;

	/**
	 * 订单客户发票
	 */
	@NotNull(message = "{B2B_ORDER_CUSTOMER_INVOICE_NOT_NULL}")
	@Valid
	private CustomerInvoice customerInvoice;

	@Getter
	@Builder
	public static class Item {
		/**
		 * 采购SKU
		 */
		@NotBlank(message = "{B2B_ORDER_ITEM_PSKU_NOT_BLANK}")
		@Size(max = 64, message = "{B2B_ORDER_ITEM_PSKU_LENGTH_EXCEEDS_LIMIT}")
		private String psku;

		/**
		 * 下单数量
		 */
		@NotNull(message = "{B2B_ORDER_ITEM_ORDERED_QUANTITY_NOT_NULL}")
		@Positive(message = "{B2B_ORDER_ITEM_ORDERED_QUANTITY_MUST_BE_GREATER_THAN_ZERO}")
		private Integer orderedQuantity;

		/**
		 * 单价
		 */
		@NotNull(message = "{B2B_ORDER_ITEM_UNIT_PRICE_NOT_NULL}")
		@Positive(message = "{B2B_ORDER_ITEM_UNIT_PRICE_MUST_BE_GREATER_THAN_ZERO}")
		private BigDecimal unitPrice;

		/**
		 * 税率
		 */
		@NotNull(message = "{B2B_ORDER_ITEM_TAX_RATE_NOT_NULL}")
		private BigDecimal taxRate;

		/**
		 * 税金
		 */
		@NotNull(message = "{B2B_ORDER_ITEM_TAX_NOT_NULL}")
		private BigDecimal tax;
	}

	@Getter
	@Builder
	public static class Amount {
		/**
		 * 币种
		 */
		@NotBlank(message = "{B2B_ORDER_AMOUNT_CURRENCY_NOT_BLANK}")
		@Size(max = 8, message = "{B2B_ORDER_AMOUNT_CURRENCY_LENGTH_EXCEEDS_LIMIT}")
		private String currency;

		/**
		 * 运费收入
		 */
		private BigDecimal shippingFee;

		/**
		 * 费用明细
		 */
		@Valid
		private List<FreeDetail> feesDetails;
	}

	@Getter
	@Builder
	public static class FreeDetail {
		/**
		 * 费用类型，1-推广费 2-广告费
		 */
		@NotNull(message = "{B2B_ORDER_AMOUNT_FEE_TYPE_NOT_NULL}")
		private Integer feeType;

		/**
		 * 费用金额
		 */
		@NotNull(message = "{B2B_ORDER_AMOUNT_FEE_AMOUNT_NOT_NULL}")
		private BigDecimal feeAmount;
	}

	@Getter
	@Builder
	@AllOrNoneNotBlank(fields = { "attnTo", "shippingCompany", "address", "zipCode", "city", "country", "contactNumber" },
			message = "{ORDER_SHIPPING_ADDRESS_ALL_OR_NONE_FIELDS_REQUIRED}")
	public static class Address {
		/**
		 * 收货人
		 */
		@Size(max = 100, message = "{B2B_ORDER_ADDRESS_ATTN_TO_LENGTH_EXCEEDS_LIMIT}")
		private String attnTo;

		/**
		 * 收货方公司
		 */
		@Size(max = 100, message = "{B2B_ORDER_ADDRESS_SHIPPING_COMPANY_LENGTH_EXCEEDS_LIMIT}")
		private String shippingCompany;

		/**
		 * 收货地址
		 */
		@Size(max = 256, message = "{B2B_ORDER_ADDRESS_ADDRESS_LENGTH_EXCEEDS_LIMIT}")
		private String address;

		/**
		 * 邮编
		 */
		@Size(max = 20, message = "{B2B_ORDER_ADDRESS_ZIP_CODE_LENGTH_EXCEEDS_LIMIT}")
		private String zipCode;

		/**
		 * 城市
		 */
		@Size(max = 100, message = "{B2B_ORDER_ADDRESS_CITY_LENGTH_EXCEEDS_LIMIT}")
		private String city;

		/**
		 * 国家
		 */
		@Size(max = 100, message = "{B2B_ORDER_ADDRESS_COUNTRY_LENGTH_EXCEEDS_LIMIT}")
		private String country;

		/**
		 * 联系号码
		 */
		@Size(max = 50, message = "{B2B_ORDER_ADDRESS_CONTACT_NUMBER_LENGTH_EXCEEDS_LIMIT}")
		private String contactNumber;

		/**
		 * 备注
		 */
		@Size(max = 256, message = "{B2B_ORDER_ADDRESS_COMMENT_LENGTH_EXCEEDS_LIMIT}")
		private String comment;
	}

	@Getter
	@Builder
	public static class ShipmentRequirement {
		/**
		 * 货物最晚交期
		 */
		@NotNull(message = "{B2B_ORDER_SHIPMENT_REQUEST_DELIVERY_DATE_NOT_NULL}")
		private LocalDate requestDeliveryDate;

		/**
		 * 是否打托，0-否 1-是
		 */
		@NotNull(message = "{B2B_ORDER_SHIPMENT_PALLETIZATION_REQUIRED_NOT_NULL}")
		private Integer palletizationRequired;

		/**
		 * 提单类型，1-Original 2-Seaway 3-Express 4-Telex
		 */
		@NotNull(message = "{B2B_ORDER_SHIPMENT_BL_TYPE_NOT_NULL}")
		private Integer blType;

		/**
		 * 外箱标签名称
		 */
		@Size(max = 128, message = "{B2B_ORDER_SHIPMENT_MASTER_CARTON_LABEL_NAME_LENGTH_EXCEEDS_LIMIT}")
		private String masterCartonLabelName;

		/**
		 * 外箱标签链接
		 */
		@Size(max = 256, message = "{B2B_ORDER_SHIPMENT_MASTER_CARTON_LABEL_URL_LENGTH_EXCEEDS_LIMIT}")
		private String masterCartonLabelUrl;

		/**
		 * 其他要求
		 */
		@Size(max = 512, message = "{B2B_ORDER_SHIPMENT_OTHER_REQUIREMENTS_LENGTH_EXCEEDS_LIMIT}")
		private String otherRequirements;
	}

	@Getter
	@Builder
	public static class Customer {
		/**
		 * 客户ID
		 */
		@NotNull(message = "{B2B_ORDER_CUSTOMER_ID_NOT_NULL}")
		private Long customerId;

		/**
		 * 销售公司ID
		 */
		@NotNull(message = "{B2B_ORDER_CUSTOMER_SALES_COMPANY_ID_NOT_NULL}")
		private Integer salesCompanyId;

		/**
		 * 贸易条款编码
		 */
		@NotBlank(message = "{B2B_ORDER_CUSTOMER_INCOTERMS_CODE_NOT_BLANK}")
		@Size(max = 64, message = "{B2B_ORDER_CUSTOMER_INCOTERMS_CODE_LENGTH_EXCEEDS_LIMIT}")
		private String incotermsCode;

		/**
		 * 付款条款编码
		 */
		@NotBlank(message = "{B2B_ORDER_CUSTOMER_PAYMENT_TERMS_CODE_NOT_BLANK}")
		@Size(max = 64, message = "{B2B_ORDER_CUSTOMER_PAYMENT_TERMS_CODE_LENGTH_EXCEEDS_LIMIT}")
		private String paymentTermsCode;
	}

	@Getter
	@Builder
	public static class CustomerContact {
		/**
		 * 联系人类型，1-主联系人 2-采购 3-物流 4-财务
		 */
		@NotNull(message = "contactType cannot be null")
		private Integer contactType;

		/**
		 * 客户联系信息id
		 */
		@NotNull(message = "customerContactId cannot be null")
		private Long customerContactId;
	}

	@Getter
	@Builder
	@AllOrNoneNotBlank(fields = { "attnTo", "invoiceCompany", "address", "zipCode", "city", "country", "contactNumber", "vatNumber" },
			message = "{ORDER_INVOICE_ALL_OR_NONE_FIELDS_REQUIRED}")
	public static class CustomerInvoice {
		/**
		 * 收件人
		 */
		@Size(max = 100, message = "{B2B_ORDER_CUSTOMER_INVOICE_ATTN_TO_LENGTH_EXCEEDS_LIMIT}")
		private String attnTo;

		/**
		 * 发票公司
		 */
		@Size(max = 100, message = "{B2B_ORDER_CUSTOMER_INVOICE_INVOICE_COMPANY_LENGTH_EXCEEDS_LIMIT}")
		private String invoiceCompany;

		/**
		 * 地址
		 */
		@Size(max = 255, message = "{B2B_ORDER_CUSTOMER_INVOICE_ADDRESS_LENGTH_EXCEEDS_LIMIT}")
		private String address;

		/**
		 * 邮编
		 */
		@Size(max = 20, message = "{B2B_ORDER_CUSTOMER_INVOICE_ZIP_CODE_LENGTH_EXCEEDS_LIMIT}")
		private String zipCode;

		/**
		 * 城市
		 */
		@Size(max = 100, message = "{B2B_ORDER_CUSTOMER_INVOICE_CITY_LENGTH_EXCEEDS_LIMIT}")
		private String city;

		/**
		 * 国家
		 */
		@Size(max = 100, message = "{B2B_ORDER_CUSTOMER_INVOICE_COUNTRY_LENGTH_EXCEEDS_LIMIT}")
		private String country;

		/**
		 * 联系号码
		 */
		@Size(max = 50, message = "{B2B_ORDER_CUSTOMER_INVOICE_CONTACT_NUMBER_LENGTH_EXCEEDS_LIMIT}")
		private String contactNumber;

		/**
		 * 增值税号码
		 */
		@Size(max = 50, message = "{B2B_ORDER_CUSTOMER_INVOICE_VAT_NUMBER_LENGTH_EXCEEDS_LIMIT}")
		private String vatNumber;
	}
}