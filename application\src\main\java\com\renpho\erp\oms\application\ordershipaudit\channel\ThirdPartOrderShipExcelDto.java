package com.renpho.erp.oms.application.ordershipaudit.channel;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 三方仓excel导入dto  京东仓 kingspark 极智佳
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Data
public class ThirdPartOrderShipExcelDto {
    @NotBlank(message = "{ORDER_SHIP_AUDIT_STORE_NAME}")
    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String shop;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_ORDER_NO}")
    @ExcelHeadName(zhCnName = "销售单号", enName = "SO#")
    private String orderNo = "";
    @NotBlank(message = "{ORDER_SHIP_AUDIT_WAREHOUSE_CODE}")
    @ExcelHeadName(zhCnName = "发货仓库编码", enName = "Shipping Warehouse Code")
    private String imsWarehouseCode;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_CARRIER_CODE}")
    @ExcelHeadName(zhCnName = "承运商服务编码", enName = "Carrier Server Code")
    private String platformWarehouseServiceCode;

    @ExcelIgnore
    private Integer jdOrderType;

    @ExcelHeadName(zhCnName = "错误信息", enName = "error info")
    private String errorInfo;

    @ExcelIgnore
    private List<String> errorInfoList = new ArrayList<>();

    public void addErrorInfo(String errorInfo) {
        this.errorInfoList.add(errorInfo);
    }

    public void buildErrorInfo() {
        errorInfo = String.join("\n", errorInfoList);
    }

    @Data
    public static class PlatformWarehouse {
        @ExcelHeadName(zhCnName = "发货仓库编码", enName = "Shipping Warehouse Code")
        private String platformWarehouseCode;
        @ExcelHeadName(zhCnName = "仓库名", enName = "warehouse")
        private String platformWarehouseName;
    }

    @Data
    public static class PlatformWarehouseServiceCode {
        @ExcelHeadName(zhCnName = "仓库名", enName = "warehouse")
        private String platformWarehouseName;

        @ExcelHeadName(zhCnName = "承运商服务编码", enName = "Carrier Server Code")
        private String platformWarehouseServiceCode;

        @ExcelHeadName(zhCnName = "承运商服务描述", enName = "Carrier Server Desc")
        private String platformWarehouseServiceDesc;
    }


}
