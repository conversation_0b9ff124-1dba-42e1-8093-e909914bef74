package com.renpho.erp.oms.application.channelmanagement.walmart.task;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtFulfillmentPullService;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 沃尔玛拉取履约信息定时任务
 */
@Component
@AllArgsConstructor
public class WmtPullFulfillmentTask {

    private final WmtFulfillmentPullService wmtFulfillmentPullService;

    @XxlJob("WmtPullFulfillmentTask")
    public void wmtPullFulfillmentTask() throws Exception {
        // 获取参数
        String param = XxlJobHelper.getJobParam();
        if (StringUtils.isNotEmpty(param)) {
            XxlJobHelper.log("沃尔玛主动拉取履约参数: {}", param);
            // 构建参数
            PullFulfillmentParam pullFulfillmentParam = JsonUtils.jsonToObject(param, PullFulfillmentParam.class);
            if (Objects.isNull(pullFulfillmentParam.getStoreId())) {
                XxlJobHelper.log("手动拉取输入店铺参数为空");
                return;
            }
            boolean timeRange = StringUtils.isNotEmpty(pullFulfillmentParam.getCreateTimeAfter())
                    && StringUtils.isNotEmpty(pullFulfillmentParam.getCreateTimeBefore());
            if (!timeRange && CollectionUtil.isEmpty(pullFulfillmentParam.getOrderNums())) {
                XxlJobHelper.log("手动拉取输入时间范围或订单号为空");
                return;
            }

            wmtFulfillmentPullService.pullFulfillment(pullFulfillmentParam);

        } else {
            // 获取分片参数
            Integer shardIndex = XxlJobHelper.getShardIndex(); // 当前分片
            Integer shardTotal = XxlJobHelper.getShardTotal(); // 总分片数
            // 将分片参数存入ThreadLocal
            JobShardContext.JobShardInfo shardInfo = new JobShardContext.JobShardInfo();
            shardInfo.setShardIndex(shardIndex);
            shardInfo.setShardTotal(shardTotal);
            JobShardContext.setShardInfo(shardInfo);
            try {
                wmtFulfillmentPullService.pullFulfillment(null);

            } finally {
                // 清除ThreadLocal中的分片信息
                JobShardContext.clear();
            }
        }

    }

    @Data
    public static class PullFulfillmentParam {

        /**
         * 店铺id
         */
        private Integer storeId;

        /**
         * 订单号
         */
        private List<String> orderNums;

        /**
         * 创建时间之后
         */
        private String createTimeAfter;

        /**
         * 创建时间之前
         */
        private String createTimeBefore;

        public LocalDateTime getStartDateUTC() {
            if (StringUtils.isEmpty(this.createTimeAfter)) {
                return null;
            }
            return DateUtil.convertStrToUTC(this.createTimeAfter);
        }

        public LocalDateTime getEndDateUTC() {
            if (StringUtils.isEmpty(this.createTimeBefore)) {
                return null;
            }
            return DateUtil.convertStrToUTC(this.createTimeBefore);
        }
    }
}
