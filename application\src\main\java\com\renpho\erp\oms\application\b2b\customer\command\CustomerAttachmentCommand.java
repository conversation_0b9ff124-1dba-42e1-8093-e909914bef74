package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 客户附件信息命令
 */
@Data
public class CustomerAttachmentCommand {

    private Long id;

    /**
     * 附件类型
     */
    @NotNull(message = "CUSTOMER_ATTACHMENT_TYPE_REQUIRE")
    private Integer attachmentType;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件URL
     */
    private String fileUrl;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 备注
     */
    private String remark;
} 