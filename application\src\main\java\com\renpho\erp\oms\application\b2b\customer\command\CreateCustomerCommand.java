package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * 创建客户命令
 */
@Data
public class CreateCustomerCommand {
    /**
     * 客户公司名称
     */
    @NotEmpty(message = "CUSTOMER_CREATE_COMPANY_NAME_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_CREATE_COMPANY_NAME_TOO_LONG")
    private String customerCompanyName;

    /**
     * 国家
     */
    @NotEmpty(message = "CUSTOMER_CREATE_COUNTRY_REQUIRE")
    private String country;

    /**
     * 海外区域经理用户id
     */
    @NotNull(message = "CUSTOMER_CREATE_COUNTRY_MANAGER_USER_ID_REQUIRE")
    private Integer countryManagerUserId;

    /**
     * 销售助理用户id
     */
    @NotNull(message = "CUSTOMER_CREATE_SALES_ASSISTANT_USER_ID_REQUIRE")
    private Integer salesAssistantUserId;

    /**
     * 备注
     */
    @Size(max = 1024, message = "CUSTOMER_CREATE_REMARK_TOO_LONG")
    private String remark;

    /**
     * 客户联系人列表
     */
    @NotEmpty(message = "CUSTOMER_CREATE_CONTACT_INFOS_REQUIRE")
    @Valid
    private List<CustomerContactCommand> contactInfos;

    /**
     * 客户发票信息列表
     */
    @Valid
    private List<CustomerInvoiceCommand> invoiceInfos;

    /**
     * 客户收货信息列表
     */
    @Valid
    private List<CustomerShippingCommand> shippingInfos;

    /**
     * 客户附件信息列表
     */
    @Valid
    private List<CustomerAttachmentCommand> attachmentInfos;

    /**
     * 客户销售公司和协议信息
     */
    @Valid
    private CustomerSalesCompanyTermsCommand salesCompanyTermsInfo;

    /**
     * 客户保险授信信息
     */
    @Valid
    private CustomerInsuranceCreditCommand insuranceCreditInfo;

} 