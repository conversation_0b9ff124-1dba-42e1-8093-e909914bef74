package com.renpho.erp.oms.application.b2b.order.convert;

import java.util.List;
import java.util.Optional;

import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.mdm.client.vo.LanguageNameVo;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bCompany;

/**
 * <AUTHOR>
 */
@Component
public class B2bCompanyConvert {

	/**
	 * 转换
	 * @param comany 公司
	 * @return B2bCompany
	 */
	public B2bCompany convert(CompanyVo comany) {
		// 销售公司名称
		List<LanguageNameVo> companyNameList = Optional.ofNullable(comany.getCompanyName()).orElse(List.of());
		return B2bCompany.builder()
			// 销售公司ID
			.salesCompanyId(comany.getId())
			// 销售公司名称中文
			.salesCompanyNameCn(companyNameList.stream()
				.filter(c -> "zh-CN".equals(c.getLanguage()))
				.findFirst()
				.map(LanguageNameVo::getName)
				.orElse(null))
			// 销售公司名称英文
			.salesCompanyNameEn(companyNameList.stream()
				.filter(c -> "en-US".equals(c.getLanguage()))
				.findFirst()
				.map(LanguageNameVo::getName)
				.orElse(null))
			.build();
	}
}
