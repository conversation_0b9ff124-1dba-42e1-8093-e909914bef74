package com.renpho.erp.oms.application.listingmanagement.convert;

import java.util.List;

import com.renpho.erp.oms.domain.SaleChannelType;
import org.mapstruct.*;
import org.mapstruct.factory.Mappers;

import com.renpho.erp.oms.application.listingmanagement.vo.SalesChannelListingExportExcel;
import com.renpho.erp.oms.application.listingmanagement.vo.SalesChannelListingVO;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.infrastructure.persistence.listing.po.SalesChannelListingPO;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SalesChannelListingAppConvertor {

	/**
	 *
	 */
	SalesChannelListingAppConvertor INSTANCE = Mappers.getMapper(SalesChannelListingAppConvertor.class);

	List<SalesChannelListingExportExcel> toExcel(List<SalesChannelListingPO> poList);

	List<SalesChannelListingVO> toVoList(List<SalesChannelListing> domainList);

	@Mapping(source = ".", target = "status", qualifiedByName = "mapStatus")
	SalesChannelListingVO toVo(SalesChannelListing domain);

	@Named("mapStatus")
	default String mapStatus(SalesChannelListing listing) {
		String status = listing.getStatus();

		if (SaleChannelType.TEMU.getValue().equals(listing.getSalesChannelCode())) {
			if ("1".equals(status)) {
				return "Active";
			} else {
				return "Inactive";
			}
		}

		// 默认情况
		return status;
	}
}
