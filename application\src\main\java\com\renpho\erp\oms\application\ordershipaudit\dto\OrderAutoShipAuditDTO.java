package com.renpho.erp.oms.application.ordershipaudit.dto;

import java.util.Optional;

import com.renpho.erp.oms.domain.ordershipaudit.model.Carrier;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.Warehouse;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单-自动发货审核DTO
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderAutoShipAuditDTO {

	/**
	 * 订单聚合根
	 */
	private SaleOrderAggRoot saleOrderAggRoot;

	/**
	 * 订单sku组合类型
	 */
	private SkuCombinationType skuCombinationType;

	/**
	 * 分仓成功的仓库
	 */
	private Warehouse warehouse;

	/**
	 * 履约服务类型
	 */
	private FulfillmentServiceType fulfillmentServiceType;

	/**
	 * 分物流成功的承运商
	 */
	private Carrier carrier;

	/**
	 * 获取订单号
	 * @return String
	 */
	public String getOrderNo() {
		return Optional.ofNullable(saleOrderAggRoot).map(SaleOrderAggRoot::getOrderNo).orElse(null);
	}
}
