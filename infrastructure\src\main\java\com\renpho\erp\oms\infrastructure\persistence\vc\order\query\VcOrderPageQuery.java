package com.renpho.erp.oms.infrastructure.persistence.vc.order.query;

import java.time.LocalDateTime;
import java.util.List;

import com.renpho.karma.dto.PageQuery;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * VC订单分页查询参数
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "VC订单分页查询参数")
public class VcOrderPageQuery extends PageQuery {

    @Schema(description = "订单号")
    private List<String> orderNos;

    @Schema(description = "VC采购单号")
    private List<String> vcPurchaseNos;

    @Schema(description = "店铺ID")
    private Integer storeId;

    @Schema(description = "订单状态")
    private Integer orderStatus;

    @Schema(description = "业务类型")
    private Integer businessType;

    @Schema(description = "站点")
    private String siteCode;

    @Schema(description = "接单时间-开始")
    private LocalDateTime acceptedTimeStart;

    @Schema(description = "接单时间-结束")
    private LocalDateTime acceptedTimeEnd;

    @Schema(description = "创建时间-开始")
    private LocalDateTime createTimeStart;

    @Schema(description = "接单时间-结束")
    private LocalDateTime createTimeEnd;

    @Schema(description = "PSKU")
    private String psku;

    @Schema(description = "ASIN")
    private String asin;

    @Schema(description = "条码")
    private String barcode;
}
