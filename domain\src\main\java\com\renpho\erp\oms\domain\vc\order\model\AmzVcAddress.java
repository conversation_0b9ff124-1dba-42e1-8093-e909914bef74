package com.renpho.erp.oms.domain.vc.order.model;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC地址值对象 基于Amazon SP-API Address模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcAddress {

	/**
	 * 参与方名称 对应API字段：name
	 */
	private String name;

	/**
	 * 地址行1 对应API字段：addressLine1
	 */
	private String addressLine1;

	/**
	 * 地址行2 对应API字段：addressLine2
	 */
	private String addressLine2;

	/**
	 * 地址行3 对应API字段：addressLine3
	 */
	private String addressLine3;

	/**
	 * 城市 对应API字段：city
	 */
	private String city;

	/**
	 * 县/区 对应API字段：county
	 */
	private String county;

	/**
	 * 区/地区 对应API字段：district
	 */
	private String district;

	/**
	 * 州/省 对应API字段：stateOrRegion
	 */
	private String stateOrRegion;

	/**
	 * 邮政编码 对应API字段：postalCode
	 */
	private String postalCode;

	/**
	 * 国家代码 对应API字段：countryCode ISO 3166-1 alpha-2国家代码
	 */
	private String countryCode;

	/**
	 * 电话号码 对应API字段：phone
	 */
	private String phone;

	/**
	 * 验证地址信息是否完整
	 * @return boolean
	 */
	public boolean isComplete() {
		return name != null && !name.trim().isEmpty() && addressLine1 != null && !addressLine1.trim().isEmpty() && city != null
				&& !city.trim().isEmpty() && countryCode != null && !countryCode.trim().isEmpty();
	}

	/**
	 * 获取完整地址字符串
	 * @return String
	 */
	public String getFullAddress() {
		StringBuilder address = new StringBuilder();

		if (name != null && !name.trim().isEmpty()) {
			address.append(name);
		}

		if (addressLine1 != null && !addressLine1.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(addressLine1);
		}

		if (addressLine2 != null && !addressLine2.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(addressLine2);
		}

		if (addressLine3 != null && !addressLine3.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(addressLine3);
		}

		if (city != null && !city.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(city);
		}

		if (county != null && !county.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(county);
		}

		if (district != null && !district.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(district);
		}

		if (stateOrRegion != null && !stateOrRegion.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(stateOrRegion);
		}

		if (postalCode != null && !postalCode.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(" ");
			address.append(postalCode);
		}

		if (countryCode != null && !countryCode.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(countryCode);
		}

		return address.toString();
	}

	/**
	 * 获取简短地址（仅包含主要信息）
	 * @return String
	 */
	public String getShortAddress() {
		StringBuilder address = new StringBuilder();

		if (city != null && !city.trim().isEmpty()) {
			address.append(city);
		}

		if (stateOrRegion != null && !stateOrRegion.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(stateOrRegion);
		}

		if (countryCode != null && !countryCode.trim().isEmpty()) {
			if (address.length() > 0)
				address.append(", ");
			address.append(countryCode);
		}

		return address.toString();
	}

	/**
	 * 检查是否为美国地址
	 * @return boolean
	 */
	public boolean isUSAddress() {
		return "US".equals(countryCode);
	}

	/**
	 * 检查是否有联系电话
	 * @return boolean
	 */
	public boolean hasPhone() {
		return phone != null && !phone.trim().isEmpty();
	}

}
