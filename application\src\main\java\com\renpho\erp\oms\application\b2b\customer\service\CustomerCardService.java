package com.renpho.erp.oms.application.b2b.customer.service;

import java.util.List;

import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.openai.gpt.service.GptService;
import com.renpho.erp.openai.gpt.vo.BusinessCardVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 客户名片-应用服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CustomerCardService {

	private final GptService gptService;

	/**
	 * 识别客户名片
	 * @param files 附件集
	 * @return BusinessCardVo
	 */
	public BusinessCardVo identify(MultipartFile[] files) {
		BusinessCardVo cardVo;
		try {
			cardVo = gptService.cardRecognition(List.of(files));
		}
		catch (Exception e) {
			log.warn("识别名片异常：", e);
			throw DomainException.of("识别失败");
		}
		// 是否识别成功
		if (BooleanUtils.isNotTrue(cardVo.getSuccess())) {
			throw DomainException.of(cardVo.getFailMsg());
		}
		return cardVo;
	}

}
