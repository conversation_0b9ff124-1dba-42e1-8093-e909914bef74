package com.renpho.erp.oms.application.listingmanagement.job;

import com.renpho.erp.oms.application.listingmanagement.executor.MercadoGlobalItemMappingPullExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 美客多全球/市场Item映射拉取Job
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class MercadoGlobalItemMappingPullJob {

	private MercadoGlobalItemMappingPullExecutor mercadoGlobalItemMappingPullExecutor;

	@XxlJob("pullMercadoGlobalItemMappingJob")
	public void pullMercadoGlobalItemMappingJob() {
		mercadoGlobalItemMappingPullExecutor.pullMercadoGlobalItemMapping();
	}

}
