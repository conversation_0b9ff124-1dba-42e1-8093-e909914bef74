package com.renpho.erp.oms.adapter.web.controller.tiktok;

import com.renpho.erp.oms.application.channelmanagement.tiktok.service.TtOrderLineItemService;
import com.renpho.erp.oms.application.channelmanagement.tiktok.vo.TtOrderLineItemVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/tiktok/**")
@RequestMapping("/tiktok/item")
@AllArgsConstructor
public class TtOrderLineItemWebController {

	private final TtOrderLineItemService ttOrderLineItemService;

	/**
	 * Tiktok订单商品列表
	 * @param orderId
	 * @return R<List<TtOrderLineItemVO>>
	 */
	@GetMapping("/list")
	public R<List<TtOrderLineItemVO>> listByOrderId(@RequestParam Long orderId) {
		return ttOrderLineItemService.listByOrderId(orderId);
	}
}
