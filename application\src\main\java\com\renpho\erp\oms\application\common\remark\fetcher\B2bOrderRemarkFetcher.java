package com.renpho.erp.oms.application.common.remark.fetcher;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.common.remark.convert.OrderRemarkHistoryConvert;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderRemarkMapper;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderRemarkPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @desc: B2B订单备注获取器
 * @time: 2025-06-20 15:06:20
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class B2bOrderRemarkFetcher implements RemarkFetcher<B2bOrderRemarkPO, OrderRemarkHistoryVO> {

    private final B2bOrderRemarkMapper b2bOrderRemarkMapper;

    private final OrderRemarkHistoryConvert convert;

    @Override
    public List<B2bOrderRemarkPO> fetchRemarks(Long bizId) {
        return b2bOrderRemarkMapper.selectList(
                Wrappers.<B2bOrderRemarkPO>lambdaQuery()
                        .eq(B2bOrderRemarkPO::getOrderId, bizId)
                        .orderByAsc(B2bOrderRemarkPO::getId));
    }

    @Override
    public OrderRemarkHistoryVO toVO(B2bOrderRemarkPO remark) {
        return convert.toVO(remark);
    }
}
