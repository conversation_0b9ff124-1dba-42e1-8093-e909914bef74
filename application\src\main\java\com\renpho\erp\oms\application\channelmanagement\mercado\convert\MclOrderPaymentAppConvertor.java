package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPayment;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.compress.utils.Lists;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.List;
import java.util.Objects;

public class MclOrderPaymentAppConvertor {
	public static List<MclOrderPayment> toDomainList(List<MclOrderDTO.Payment> payments) {
		List<MclOrderPayment> mclOrderPaymentList = Lists.newArrayList();
		payments.forEach(payment -> {
			mclOrderPaymentList.add(toDomain(payment));
		});
		return mclOrderPaymentList;
	}

	public static MclOrderPayment toDomain(MclOrderDTO.Payment payment) {
		if (Objects.nonNull(payment)) {
			MclOrderPayment mclOrderPayment = new MclOrderPayment();
			mclOrderPayment.setMclPaymentId(payment.getId());
			mclOrderPayment.setMclOrderId(payment.getOrder_id());
			mclOrderPayment.setPayerId(payment.getPayer_id());
			mclOrderPayment.setCollectorId(Objects.nonNull(payment.getCollector()) ? payment.getCollector().getId() : null);
			mclOrderPayment.setCardId(payment.getCard_id());
			mclOrderPayment.setSiteId(payment.getSite_id());
			mclOrderPayment.setReason(payment.getReason());
			mclOrderPayment.setPaymentMethodId(payment.getPayment_method_id());
			mclOrderPayment.setCurrencyId(payment.getCurrency_id());
			mclOrderPayment.setInstallments(payment.getInstallments());
			mclOrderPayment.setIssuerId(payment.getIssuer_id());
			mclOrderPayment.setAtmTransferReference(
					Objects.nonNull(payment.getAtm_transfer_reference()) ? JsonUtils.toJson(payment.getAtm_transfer_reference()) : null);
			mclOrderPayment.setCouponId(payment.getCoupon_id());
			mclOrderPayment.setActivationUri(payment.getActivation_uri());
			mclOrderPayment.setOperationType(payment.getOperation_type());
			mclOrderPayment.setPaymentType(payment.getPayment_type());
			mclOrderPayment.setAvailableActions(
					CollectionUtil.isNotEmpty(payment.getAvailable_actions()) ? JsonUtils.toJson(payment.getAvailable_actions()) : null);
			mclOrderPayment.setStatus(payment.getStatus());
			mclOrderPayment.setStatusDetail(payment.getStatus_detail());
			mclOrderPayment.setTransactionAmount(payment.getTransaction_amount());
			mclOrderPayment.setTaxesAmount(payment.getTaxes_amount());
			mclOrderPayment.setShippingCost(payment.getShipping_cost());
			mclOrderPayment.setCouponAmount(payment.getCoupon_amount());
			mclOrderPayment.setTotalPaidAmount(payment.getTotal_paid_amount());
			mclOrderPayment.setDeferredPeriod(payment.getDeferred_period());
			mclOrderPayment.setDateApproved(DateUtil.parseISOStr(payment.getDate_approved()));
			mclOrderPayment.setAuthorizationCode(payment.getAuthorization_code());
			mclOrderPayment.setTransactionOrderId(payment.getTransaction_order_id());
			mclOrderPayment.setDateCreated(DateUtil.parseISOStr(payment.getDate_created()));
			mclOrderPayment.setDateLastModified(DateUtil.parseISOStr(payment.getDate_last_modified()));
			return mclOrderPayment;
		}
		return null;
	}
}
