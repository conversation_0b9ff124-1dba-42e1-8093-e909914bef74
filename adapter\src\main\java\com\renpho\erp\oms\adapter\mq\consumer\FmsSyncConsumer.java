package com.renpho.erp.oms.adapter.mq.consumer;

import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.ebay.dto.EbyOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentDTO;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.temu.dto.TemOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.MQConstant;
import com.renpho.erp.oms.infrastructure.common.mq.MqMessageHandlerDecorator;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.service.SaleOrderService;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.function.Consumer;

/**
 * 消费MPDS同步发回来的三方订单信息
 */
@Component
@AllArgsConstructor
@Slf4j
public class FmsSyncConsumer {

     private final SaleOrderService saleOrderService;

    @Bean
    public Consumer<Message<String>> syncFms() {
        return MqMessageHandlerDecorator.wrap(msg -> {
            String tag = msg.getHeaders().get("msgTag").toString();
            String message = msg.getPayload();
            log.info("接收到FMS系统同步至OMS的信息{} ,tag为{}", message, tag);
            switch (tag) {
                // 确认消息
                case MQConstant.TAG.FMS_ACK_CONSUME:
                    List<String> orderNos = JSONKit.parseObject(message, List.class);
                    saleOrderService.ackFmsConsume(orderNos);
                    break;
                default:
                    throw new RuntimeException("未知tag");
            }
        });
    }

}
