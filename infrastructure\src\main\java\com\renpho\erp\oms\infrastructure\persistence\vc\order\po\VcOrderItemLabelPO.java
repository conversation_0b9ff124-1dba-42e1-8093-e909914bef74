package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单商品箱唛表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order_item_label")
public class VcOrderItemLabelPO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * vc订单行id
	 */
	private Long orderItemId;

	/**
	 * 物流标-文件url
	 */
	private String asinLabelUrl;

	/**
	 * 物流标-文件名
	 */
	private String asinLabelName;

	/**
	 * 外箱标-文件url
	 */
	private String cartonLabelUrl;

	/**
	 * 外箱标-文件名
	 */
	private String cartonLabelName;

}
