package com.renpho.erp.oms.application.b2b.order.vo;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * B2B订单分页查询VO
 */
@Data
public class B2bOrderPageVO implements VO {

    /**
     * 主键
     */
    private Long id;

    /**
     * B2B订单号
     */
    private String orderNo;

    /**
     * 客户参考号
     */
    private String referNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 发票时间
     */
    private LocalDateTime invoiceTime;

    /**
     * 发货时间
     */
    private LocalDateTime shippedTime;

    /**
     * 订单状态 1-待处理 2-订单审核中 3-备货中 4-部分发货 5-已发货 6-已完结 7-已取消
     */
    @Trans(type = TransType.DICTIONARY, key = "ToB_order_status", ref = "orderStatusName")
    private Integer orderStatus;

    /**
     * 订单状态名称
     */
    private String orderStatusName;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 国家
     */
    private String country;

    /**
     * 客户公司名称
     */
    private String customerCompanyName;

    /**
     * 海外区域经理名称
     */
    private String countryManagerName;

    /**
     * 销售助理名称
     */
    private String salesAssistantName;

    /**
     * 贸易条款编码
     */
    private String incotermsCode;

    /**
     * 贸易条款名称
     */
    private String incotermsName;

    /**
     * 付款条款编码
     */
    private String paymentTermsCode;

    /**
     * 付款条款名称
     */
    private String paymentTermsName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 金额合计
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;

    /**
     * 已收款的金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal receiptAmount;

    /**
     * 已收款比例
     */
    private BigDecimal receiptRate;

    /**
     * 订单商品信息
     */
    private B2bOrderItemVO item;

}
