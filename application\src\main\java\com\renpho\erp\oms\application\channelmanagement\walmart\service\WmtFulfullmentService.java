package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtFulfillmentAppconvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtFulfillmentShipmentAppconvertor;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillment;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillmentShipment;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtFulfillmentRepository;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtFulfillmentShipmentRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class WmtFulfullmentService {

    private final WmtFulfillmentRepository wmtFulfillmentRepository;
    private final WmtFulfillmentShipmentRepository wmtFulfillmentShipmentRepository;

    /**
     * 履约信息批量插入或更新
     *
     * @param orderList
     * @param shopCode
     * @param siteCode
     * @param storeId
     */
    @Transactional
    public void batchInsertOrUpdate(List<GetFulfillmentOrdersStatusResponse.Order> orderList, String shopCode, String siteCode, Integer storeId) {
        Map<String, WmtFulfillment> wmtFulfillmentMap = wmtFulfillmentRepository.mapByStoreIdAnSellerOrderIds(storeId, orderList.stream().map(GetFulfillmentOrdersStatusResponse.Order::getSellerOrderId).collect(Collectors.toList()));
        orderList.forEach(order -> {
            WmtFulfillment wmtFulfillment = WmtFulfillmentAppconvertor.toDomain(order);
            wmtFulfillment.setShopId(shopCode);
            wmtFulfillment.setSiteCode(siteCode);
            wmtFulfillment.setStoreId(storeId);
            List<WmtFulfillmentShipment> wmtFulfillmentShipmentList = WmtFulfillmentShipmentAppconvertor.toDomainList(order.getShipments());
            // 存在进行更新
            if (wmtFulfillmentMap.containsKey(order.getSellerOrderId())) {
                wmtFulfillment.setId(wmtFulfillmentMap.get(order.getSellerOrderId()).getId());
                wmtFulfillmentRepository.update(wmtFulfillment);
                // 先删除原有发货行
                wmtFulfillmentShipmentRepository.deleteByWmtFulfillmentId(wmtFulfillment.getId());
                if (CollectionUtil.isNotEmpty(wmtFulfillmentShipmentList)) {
                    // 再插入
                    wmtFulfillmentShipmentRepository.batchInsert(wmtFulfillment.getId(), wmtFulfillmentShipmentList);
                }
            } else {
                Long wmtFulfillmentId = wmtFulfillmentRepository.insert(wmtFulfillment);
                if (CollectionUtil.isNotEmpty(wmtFulfillmentShipmentList)) {
                    // 插入
                    wmtFulfillmentShipmentRepository.batchInsert(wmtFulfillmentId, wmtFulfillmentShipmentList);
                }
            }
        });


    }
}
