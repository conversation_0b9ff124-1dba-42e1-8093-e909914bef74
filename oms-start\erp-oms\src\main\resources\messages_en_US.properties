SUCCESS=Success
SYSTEM_EXCEPTION=System Exception
BUSY_OPERATION_PLEASE_TRY_AGAIN_LATER=Busy operation, please try again later
FILE_IMPORT_EXCEPTION=File import exception.
FILE_EXPORT_EXCEPTION=File export exception.
FILE_UPLOAD_FAILED=FileUploadFailed
FILE_DOWNLOAD_FAILED=FileDownloadFailed
GET_UPLOADED_FILE_FAILED=GetUploadedFileFailed
FILE_IMPORT_ERROR_CHECK=FileImportErrorCheck
BUSINESS_FAILED_INDEX={0}
SKUMAPPING_EXITS=This MSKU is already exists, please update it.
SKUMAPPING_NO_EXITS=The mapping relationship not exists
SKUMAPPING_LINE_EMPTY=The SkuMapping Line is empty
SKUMAPPING_LINE_TOO_MANY=The SkuMapping Line too many
SKUMAPPING_LINE_REPEAT=The SkuMapping Line is repeat
RETAILPRICE_IS_EMPTY=retailprice is empty
RETAILPRICE_LESS_THAN_ZERO=Retailprice less than zero
FNSKUQUANTITY_IS_EMPTY=FnskuQuantity is empty
FNSKUQUANTITY_LESS_THAN_ONE=FnskuQuantity less than one
STORE_NO_EXITS=The store does not exist
STORE_EMPTY=The store cannot be empty
CHANNLE_EMPTY=The channel cannot be empty
CHANNEL_NO_EXITS=The channel does not exist
OPERATOR_NO_EXITS=The operator does not exist
PURCHASE_SKU_NO_EXITS=The purchase sku does not exist
PURCHASE_SKU_EMPTY=The purchase sku is empty
PURCHASE_SKU_TOO_LONG=The purchase sku length cannot be greater than 64
ASIN_REQUIRE=Amazon requires ASIN
ASIN_REQUIRED_FOR_AMZ_VC=Amazon VC store requires ASIN
TIKTOK_ASIN_REQUIRE=TikTok's ASIN requires (Fill Product ID)
MERCADO_ASIN_REQUIRE=Mercado's ASIN required (Fill in Item ID)
EBY_ASIN_REQUIRE=Ebay's ASIN required (Fill in Item ID)
ASIN_TOO_LONG=ASIN length cannot be greater than 64
FBA_INVENTORY_COUNTED_REQUIRE=Amazon requires fbaInventoryCounted
FBA_NOT_REQUIRE=Non-Amazon channels do not count FBA inventory
FBA_ERROR=FBA error
UPDATE_FAIL=Update failed
DATA_NOT_EXITS=Data does not exist
DATA_EMPTY=Data is empty
HEAD_NOT_MATCH=The header does not match the template
REMOTE_CALL_FAILED=Remote call failed
FN_SKU_REQUIRE=FnSku require
FN_SKU_TOO_LONG=FnSku length cannot be greater than 64
IMPORT_SUCCESS=Import Success
SELLER_SKU_EMPTY=Seller SKU cannot be empty
SELLER_SKU_TOO_LONG=Seller SKU length cannot be greater than 64
STATUS_ERROR=Status error
STATUS_EMPTY=Status is empty
SKU_REPEATED=SkuMapping repeated
SKU_TEMPLATE_FILE_NAME=SellerSKUImportTemplate
ID_EMPTY=Id is empty
FULFILMENTTYPE_EMPTY=FulfilmentType is empty
FULFILMENTTYPE_NOT_EXITS=FulfilmentType not exits
MULTI_CHANNEL_TYPE_NO_EXITS=Multichannel type no exits
STORE_PSKU_NOT_MATCH=Store Don't match psku
STORE_MSKU_NOT_MATCH=Store Don't match msku
SKU_MAPPING_EXCEL.STORE=Store
SKU_MAPPING_EXCEL.SELLERSKU=SellerSKU
SKU_MAPPING_EXCEL.FNSKU=FNSKU
SKU_MAPPING_EXCEL.ASIN=ASIN
SKU_MAPPING_EXCEL.PURCHASESKU=PurchaseSKU
SKU_MAPPING_EXCEL.OPERATORNO=OperatorNo
SKU_MAPPING_EXCEL.COUNTFBAINVENTORY=Count FBA inventory? (1 yes 0 no)
SKU_MAPPING_EXCEL.STATUS=Status (1 enabled 0 disabled)
SKU_MAPPING_EXCEL.ERRORMESSAGE=Error Message
SKU_MAPPING_EXCEL.CHANNEL=Sales Channel
SKU_MAPPING_EXCEL.OPERATOR=Operator
SKU_MAPPING_EXCEL.FULFILLMENTTYPE=FulfillmentType (1 platform FulfillmentType 2 self FulfillmentType)
SKU_MAPPING_EXCEL.FNSKUQUANTITY=Fnskuquantity
SKU_MAPPING_EXCEL.RETAILPRICE=Retailprice
SKU_MAPPING_EXCEL.VERSION=Version
SKU_MAPPING_EXCEL_EXPORT.COUNTFBAINVENTORY=Count FBA inventory
SKU_MAPPING_EXCEL_EXPORT.STATUS=Status
SKU_MAPPING_EXCEL_EXPORT.UPDATER=Updater
SKU_MAPPING_EXCEL_EXPORT.UPDATETIME=UpdateTime
SKU_MAPPING_EXCEL_EXPORT.FULFILLMENTTYPE=FulfilmentType
#
SALES_CHANNEL_LISTING_EXPORT_EXCEL.ITEM=ITEM/ASIN
SALES_CHANNEL_LISTING_EXPORT_EXCEL.MSKU=MSKU
SALES_CHANNEL_LISTING_EXPORT_EXCEL.PSKU=PSKU
SALES_CHANNEL_LISTING_EXPORT_EXCEL.TITLE=Title
SALES_CHANNEL_LISTING_EXPORT_EXCEL.LINK=Link
SALES_CHANNEL_LISTING_EXPORT_EXCEL.STATUS=Status
SALES_CHANNEL_LISTING_EXPORT_EXCEL.STORE=Store
SALES_CHANNEL_LISTING_EXPORT_EXCEL.CHANNEL=Sales Channel
SALES_CHANNEL_LISTING_EXPORT_EXCEL.CURRENCY=Currency
SALES_CHANNEL_LISTING_EXPORT_EXCEL.PRICE=Price
SALES_CHANNEL_LISTING_EXPORT_EXCEL.PUBLISHEDTIME=Published Time
SALES_CHANNEL_LISTING_EXPORT_EXCEL.ITEMUPDATEDTIME=Updated Time
SALES_CHANNEL_LISTING_EXPORT_EXCEL.CREATETIME=Fetched Time
UNSUPPORT=UNSUPPORT
DATA_INSUFFICIENCY_HIT_MSG={0} Data loading failed,please try again later
SALE_ORDER_SPLIT_MERGE_ORDER_STATUS_CHECK=The current order status is not pending, on hold, or ship exception
ORDER_STORE_CHANNEL_NOT_MATCH=OrderStore Don't match OrderChannel
MULTI_CHANNEL_TYPE_EMPTY=Multichannel type is empty
MULTI_CHANNEL_MSKU_EMPTY=Multichannel MSKU is empty
MULTI_CHANNEL_MSKU_NO_EXITS=Multichannel MSKU no exits
MULTI_CHANNEL_STORE_NO_EXITS=Multichannel OrderStore not exits
MULTI_CHANNEL_STORE_EMPTY=Multichannel Store is empty
MUL_SKU_ORDER_PSKU_EMPTY=The OrderPSKU is empty
MULTI_CHANNEL_STORE_NOT_MATCH=Multichannel Don't match MultiStore
MUL_SKU_MAPPING_EXCEL.ORDER_STORE=OrderStore
MUL_SKU_MAPPING_EXCEL.ORDER_CHANNEL=OrderChannel
MUL_SKU_MAPPING_EXCEL.ORDER_PSKU=OrderPSKU
MUL_SKU_MAPPING_EXCEL.MUL_CHANNEL_TYPE=MultiChannelType
MUL_SKU_MAPPING_EXCEL.MUL_CHANNEL_STORE=Multi-channel store
MUL_SKU_MAPPING_EXCEL.MUL_CHANNEL_MSKU=Multi-channel MSKU
MUL_SKU_MAPPING_EXCEL.PRIORITY=Priority
MUL_SKU_MAPPING_EXCEL.STATUS=Status
MUL_SKU_MAPPING_EXCEL_EXPORT.UPDATER=Updater
MUL_SKU_MAPPING_EXCEL_EXPORT.UPDATETIME=UpdateTime
MUL_SKU_MAPPING_EXCEL_IMPORT.MUL_CHANNEL_TYPE=Multi-channel type (7: FBA multi-channel 8: WFS multi-channel)
MUL_SKU_MAPPING_EXCEL_IMPORT.ERRORMESSAGE=Error Message
MUL_SKU_TEMPLATE_FILE_NAME=MultiSKUMappingImportTemplate
SALE_ORDER_SPLIT_ORDER_LIME_EMPTY_CHECK=Split order lines cannot be empty
SALE_ORDER_SPLIT_ORDER_QUANTITY_MIN=The quantity split must be greater than or equal to 1
SALE_ORDER_SPLIT_ORDER_EXTEND_WARRANTY_CHECK=The split line cannot be an extended warranty product
SALE_ORDER_SPLIT_ORDER_QUANTITY_SHIPMENT_CHECK=The split quantity cannot be greater than the quantity to be shipped on the order line
SALE_ORDER_SPLIT_ORDER_EMPTY_ORDER_CHECK=The original order must contain at least 1 item and 1 quantity, and cannot be split into empty order
SALE_ORDER_UPDATE_ADDRESS_NOT_MERCHANT_DELIVERY=The current order fulfillment service is not fulfilled by the merchant
SALE_ORDER_UPDATE_ADDRESS_STATUS_CHECK=The current order status is not pending review, frozen, or abnormal delivery
SALE_ORDER_MERGE_ORDER_CHANNEL_ORDER_NO_SAME=The store order number must be the same
SALE_ORDER_MERGE_ORDER_SELECTED_ORDER_CHECK=The selected order does not exist
SALE_ORDER_INTERCEPT_ORDER_STATUS_CHECK={0} The current order status is not To-be-shipped
SALE_ORDER_INTERCEPT_ORDER_FULFILLMENT_TYPE_CHECK={0} The current fulfillment type is not merchant self shipment
SALE_ORDER_HOLD_STATUS_CHECK={0} The current order status is not pending
SALE_ORDER_RELEASE_STATUS_CHECK={0} The current order status is not on hold
SALE_ORDER_CANCEL_REASON_EMPTY_CHECK=Cancellation reason cannot be empty
SALE_ORDER_CANCEL_STATUS_CHECK=The current order status is not pending, on hold, or ship exception
SALE_ORDER_CANCEL_ITEM_STATUS_CHECK=There are already shipped product lines, please cancel after splitting the order
ORDER_STATUS_NOT_EXCEPTION=The order is not a resolution exception
EMAIL_FORMAT_ERROR=The mailbox is malformed
NOT_MERCHANT_Fulfillment_ERROR=Non-merchant fulfilled orders
FULFILLMENT_SERVICE_TYPE_NOT_SUPPORT=This fulfillment service type doesn't support
ORDER_NOT_OFFLINE_FULFILMENT=The current order does not support offline shipping
ORDER_STATUS_NOT_SHIPPING=The current order status is not shipped
DATA_FORMAT_ERROR=The data format is incorrect
ORDER_SHIP_AUDIT_EXIT=The related order fulfillment review has been created
ORDER_SHIP_AUDIT_NON_EDITABLE=Only the previous shipping review info is allowed. All fields are non-editable
ORDER_SHIP_AUDIT_SKU_NOT_FILL=The SKU information for order shipment review is required
ORDER_SHIP_AUDIT_SHIP_COMPANY_ID_NOT_FILL=Ship company id and Ship channel id are required
ORDER_SHIP_AUDIT_SHIP_QUOTE_ID_NOT_FILL=Quote id and Rate id are required
ORDER_SHIP_AUDIT_EXTWARRANTY_ITEM_CAN_NOT_SHIP_AUDIT=The extended warranty product cannot be shipped
ORDER_SHIP_AUDIT_ITEM_NOT_EXIST=The order line does not exist
FAILED_TO_CREATE_FULFILLMENT_ORDER=Failed to create fulfillment order: {0}
SALE_ORDER_CREATE_ORDER_SHIP_HAS_SHIPMENT=There are already shipped product lines\uFF0Cplease splitting the order after operate it
AMAZON_OUTBOUND_SHIPPING_SPEED_CATEGORY_REQUIRE=FBA multi channel shipping speed category is required
AMAZON_DELIVERY_TIME_ERROR=Amazon delivery time error
AMAZON_DELIVERY_START_AFTER_END=The delivery start time cannot be later than the delivery end time
AMAZON_DELIVERY_INSTRUCTION_ERROR=Amazon delivery instructions error
AMAZON_JP_NEIGHBOR_REQUIRE=Neighbor information is required for 'Leave with a neighbor'
WFS_SALE_CHANNEL_MAPPING_NOT_FOUND=WFS sales channel mapping not found
OFFLINE_FULFILLMENT_TIME_ERROR=The shipping time must be less than or equal to the delivery time
OFFLINE_FULFILLMENT_TEMPLATE_FILE_NAME=Import_mark_as_shipped_template
ORDER_STATUS_INCORRECT=The order status is incorrect
MODIFY_ITEM_EMPTY=The modified items cannot be empty
ORDER_ITEM_EXTEND_WARRANTY_CANNOT_MODIFY=The extended warranty product cannot be modified
ORDER_ID_EMPTY=The orderId list cannot be empty
ORDER_ID_NOT_EMPTY=The orderId cannot be empty
REPLACE_ITEM_DETAIL_EMPTY=The replacement item details cannot be empty
ORIGINAL_PSKU_EMPTY=The original PSKU cannot be empty
ORIGINAL_FNSKU_EMPTY=The original FNSKU cannot be empty
REPLACE_PSKU_EMPTY=The replacement PSKU cannot be empty
REPLACE_FNSKU_EMPTY=The replacement FNSKU cannot be empty
REPLACE_ITEM_DUPLICATE=Replacement item has duplicate original PSKU and FNSKU
REPLACE_SAME=Original PSKU and replacement PSKU And Original FNSKU and replacement FNSKU cannot be the same
OFFLINE_FULFILLMENT_IMPORT_EXCEL.SALE_ORDER=Sales Order #
OFFLINE_FULFILLMENT_IMPORT_EXCEL.SHIPPED_TIME=Shipped Time
OFFLINE_FULFILLMENT_IMPORT_EXCEL.DELIVERED_TIME=Delivered Time
OFFLINE_FULFILLMENT_IMPORT_EXCEL.WAREHOUSE_ORDER=Warehouse Order #
OFFLINE_FULFILLMENT_IMPORT_EXCEL.CARRIER=Carrier
OFFLINE_FULFILLMENT_IMPORT_EXCEL.TRACKING=Tracking #
OFFLINE_FULFILLMENT_IMPORT_EXCEL.ERROR_MESSAGE=Error Message
OFFLINE_FULFILLMENT_IMPORT_SALE_ORDER_EMPTY=Sales Order is empty
OFFLINE_FULFILLMENT_IMPORT_SALE_ORDER_REPEAT=Sales Order is repeated
OFFLINE_FULFILLMENT_IMPORT_SHIPPED_TIME_EMPTY=Shipped Time is empty
OFFLINE_FULFILLMENT_IMPORT_DELIVERED_TIME_EMPTY=Delivered Time is empty
ORDER_SHIP_AUDIT_STORE_NAME=Store name required
ORDER_SHIP_AUDIT_ORDER_NO=SO# required
ORDER_SHIP_AUDIT_AMZ_STORE_NAME=Amazon Shipment Shop required
ORDER_SHIP_AUDIT_AMZ_SHIP_SPEED=Shipping Speed error
ORDER_SHIP_AUDIT_AMZ_PACKING_REMARK=Packing Remarks required
ORDER_SHIP_AUDIT_AMZ_BLANK_BOX=Blank Box error
ORDER_SHIP_AUDIT_AMZ_BLOCK_AMZL=Block AMZL error
ORDER_SHIP_AUDIT_AMZ_MSKU=Amazon MSKU required
ORDER_SHIP_AUDIT_WMT_STORE_NAME=Walmart Shipment Shop required
ORDER_SHIP_AUDIT_WMT_MSKU=Walmart MSKU required
ORDER_SHIP_AUDIT_WAREHOUSE_CODE=Shipping Warehouse Code required
ORDER_SHIP_AUDIT_CARRIER_CODE=Carrier Server Code required
ORDER_SHIP_AUDIT_MSKU_PSKU=msku psku one of them is required
ORDER_SHIP_AUDIT_FBA_CONFIG=amazon fba config should be consistent
ORDER_SHIP_AUDIT_SHOP_NAME_CONSISTENT=amazon store name should be consistent
ORDER_SHIP_AUDIT_ORDER_NOT_EXISTS=The order does not exist
ORDER_SHIP_AUDIT_ORDER_NOT_FULFILLMENT_BY_MERCHANT=Orders are not fulfilled by the merchant
ORDER_SHIP_AUDIT_ORDER_STATUS_NOT_CANT_AUDIT=The order status cannot create a shipping review
ORDER_SHIP_AUDIT_ITEM_EXCEED_DB=Order product line exceeds the system
ORDER_SHIP_AUDIT_SOME_BE_SHIP=Some orders have been shipped
ORDER_SHIP_AUDIT_EXCEL_ORDER_MSKU_NOT_EXISTS=The excel order product does not correspond to msku
ORDER_SHIP_AUDIT_EXCEL_ORDER_PSKU_NOT_EXISTS=The excel order product does not correspond to psku
ORDER_SHIP_AUDIT_SYSTEM_ORDER_MSKU_NOT_EXISTS=The system order product does not correspond to msku
ORDER_SHIP_AUDIT_SYSTEM_ORDER_PSKU_NOT_EXISTS=The system order product does not correspond to psku
ORDER_SHIP_AUDIT_SHOP_NOT_EXISTS=The store does not exist
ORDER_SHIP_AUDIT_DELIVERY_TIME_FORMAT=The delivery time format is incorrect
ORDER_SHIP_AUDIT_ORDER_NOT_BELONG_SHOP=The order is not from this store
ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_EXISTS=The amazon store does not exist
ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_AMZ=Amazon Shipment Shop NOT amazon
ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_SKU_MAPPING=Amazon shipping channel store has no related msku map
ORDER_SHIP_AUDIT_SAME_EXCEL_MSKU_NOT_CONSISTENT=The corresponding shipping channels of the same excel msku are inconsistent
ORDER_SHIP_AUDIT_SAME_EXCEL_PSKU_NOT_CONSISTENT=The corresponding shipping channels of the same excel psku are inconsistent
AMAZON_DELIVERY_TIME_PAIR_REQUIRED=Delivery start time and end time must be either both filled or both left blank
ORDER_SHIP_AUDIT_WMT_SHOP_NOT_CONSISTENT=Walmart shop name not consistent
ORDER_SHIP_AUDIT_WMT_SHOP_NOT_EXISTS=The walmart store does not exist
ORDER_SHIP_AUDIT_WMT_SHOP_NOT_WMT=Walmart Shipment Shop NOT walmart
ORDER_SHIP_AUDIT_WMT_SHOP_NOT_SKU_MAPPING=Walmart shipping channel store has no related msku map
ORDER_SHIP_AUDIT_WMT_SHIPPING_SPEED_ERROR=Walmart shipping speed must be one of {0}
ORDER_SHIP_AUDIT_ORDER_NO_REPEAT=SO NO repeat
ORDER_SHIP_AUDIT_WAREHOUSE_CODE_INVALIDATE=warehouse code invalidate
ORDER_SHIP_AUDIT_CARRIER_SERVICE_CODE_INVALIDATE=carrier service code invalidate
SALE_ORDER_EXCEL_HEADER.BASE_INFO=Base Info.
SALE_ORDER_EXCEL_HEADER.ADDRESS_INFO=Address Info.
SALE_ORDER_EXCEL_HEADER.FULFILLMENT_INFO=Fulfillment Info.
SALE_ORDER_EXCEL_HEADER.PRODUCT_INFO=Product Info.
SALE_ORDER_EXCEL_HEADER.ERROR_INFO=Error Info.
SALE_ORDER_EXCEL_HEADER.SO=SO#
SALE_ORDER_EXCEL_HEADER.SALES_CHANNEL=Sales Channel
SALE_ORDER_EXCEL_HEADER.SHOP=Shop
SALE_ORDER_EXCEL_HEADER.SITE=Site
SALE_ORDER_EXCEL_HEADER.SHOPORDER=Shop Order #
SALE_ORDER_EXCEL_HEADER.REFER=Refer #
SALE_ORDER_EXCEL_HEADER.SOURCE=Source #
SALE_ORDER_EXCEL_HEADER.ORDER_TAGS=Order Tags
SALE_ORDER_EXCEL_HEADER.CURRENCY=Currency
SALE_ORDER_EXCEL_HEADER.ORDERED_TIME=Ordered Time
SALE_ORDER_EXCEL_HEADER.PAID_TIME=Paid Time
SALE_ORDER_EXCEL_HEADER.SHIPPED_TIME=Shipped Time
SALE_ORDER_EXCEL_HEADER.DELIVERED_TIME=Delivered Time
SALE_ORDER_EXCEL_HEADER.BUYER_NOTE=Buyer Note
SALE_ORDER_EXCEL_HEADER.SALES_NOTE=Sales Note
SALE_ORDER_EXCEL_HEADER.EXCEPTION=Exception
SALE_ORDER_EXCEL_HEADER.COUNTRY_OR_REGION=Country or Region
SALE_ORDER_EXCEL_HEADER.STATE=State
SALE_ORDER_EXCEL_HEADER.CITY=City
SALE_ORDER_EXCEL_HEADER.POSTAL_CODE=Postal Code
SALE_ORDER_EXCEL_HEADER.FUFILLMENT_SERVICE_TYPE=Fufillment Service Type
SALE_ORDER_EXCEL_HEADER.WAREHOUSE=Warehouse
SALE_ORDER_EXCEL_HEADER.WAREHOUSE_ORDER=Warehouse Order #
SALE_ORDER_EXCEL_HEADER.LOGISTICS_ORDER=Logistics Order #
SALE_ORDER_EXCEL_HEADER.PACKAGE_SIZE=Package Size (theoretical)
SALE_ORDER_EXCEL_HEADER.PACKAGE_WEIGHT=Package Weight (theoretical)
SALE_ORDER_EXCEL_HEADER.LASTEST_SHIPMENT_TIME=Lastest Shipment Time
SALE_ORDER_EXCEL_HEADER.UPLOAD_TRACK=Upload Track #
SALE_ORDER_EXCEL_HEADER.ASIN_ITEMID=ASIN/ItemID
SALE_ORDER_EXCEL_HEADER.MSKU=MSKU
SALE_ORDER_EXCEL_HEADER.PSKU=PSKU
SALE_ORDER_EXCEL_HEADER.FNSKU=FNSKU
SALE_ORDER_EXCEL_HEADER.QUANTITY=Quantity
SALE_ORDER_EXCEL_HEADER.ITEM_LINE_STATUS=Item Line Status
SALE_ORDER_EXCEL_HEADER.PRODUCT_PRINCIPAL=Product Principal
SALE_ORDER_EXCEL_HEADER.PRODUCT_DISCOUNT=Product Discount
SALE_ORDER_EXCEL_HEADER.PRODUCT_TAX=Product Tax
SALE_ORDER_EXCEL_HEADER.PRODUCT_TAX_DISCOUNT=Product Tax Discount
SALE_ORDER_EXCEL_HEADER.SHIPPING_CHARGE=Shipping Charge
SALE_ORDER_EXCEL_HEADER.SHIPPING_DISCOUNT=Shipping Discount
SALE_ORDER_EXCEL_HEADER.SHIPPING_TAX=Shipping  Tax
SALE_ORDER_EXCEL_HEADER.SHIPPING_TAX_DISCOUNT=Shipping  Tax Discount
SALE_ORDER_EXCEL_HEADER.SUB_TOTAL=Sub Total
SALE_ORDER_EXCEL_HEADER.ERRORMESSAGE=Error Message
SALE_ORDER_EXCEL_HEADER.RECIPIENT_NAME=Receiver
SALE_ORDER_EXCEL_HEADER.RECIPIENT_PHONE=Tel.
SALE_ORDER_EXCEL_HEADER.EMAIL=Email
SALE_ORDER_EXCEL_HEADER.ADDRESS1=Address1
SALE_ORDER_EXCEL_HEADER.ADDRESS2=Address2
SALE_ORDER_EXCEL_HEADER.CARRIER=Carrier
SALE_ORDER_EXCEL_HEADER.TRACKING_NO=Tracking #
SALE_ORDER_EXCEL_HEADER.ERROR_MSG=Error Message
SALE_ORDER_CREATE_STORE_ID_REQUIRE=Store ID is required
SALE_ORDER_CREATE_STORE_REQUIRE=Store is required
ORDER_TAG_NOT_SUPPORTED=Order Tag is not supported
SALE_ORDER_CREATE_CHANNEL_CODE_REQUIRE=Channel Code is required
SALE_ORDER_CREATE_CHANNEL_ORDER_NO_REQUIRE=Channel Order No is required
SALE_ORDER_CREATE_CHANNEL_ORDER_NO_TOO_LONG=Channel Order No is too long
SALE_ORDER_CREATE_CURRENCY_REQUIRE=Currency is required
SALE_ORDER_CREATE_ORDERED_TIME_REQUIRE=Ordered Time is required
SALE_ORDER_CREATE_PAID_TIME_REQUIRE=Paid Time is required
SALE_ORDER_CREATE_RECIPIENT_NAME_REQUIRE=Recipient Name is required
SALE_ORDER_CREATE_RECIPIENT_NAME_TOO_LONG=Recipient Name is too long
SALE_ORDER_CREATE_RECIPIENT_PHONE_REQUIRE=Recipient Phone is required
SALE_ORDER_CREATE_RECIPIENT_PHONE_TOO_LONG=Recipient Phone is too long
SALE_ORDER_CREATE_POSTAL_CODE_REQUIRE=Postal Code is required
SALE_ORDER_CREATE_POSTAL_CODE_TOO_LONG=Postal Code is too long
SALE_ORDER_CREATE_EMAIL_REQUIRE=Email is required
SALE_ORDER_CREATE_EMAIL_TOO_LONG=Email is too long
SALE_ORDER_CREATE_COUNTRY_CODE_REQUIRE=Country Code is required
SALE_ORDER_CREATE_COUNTRY_CODE_TOO_LONG=Country Code is too long
SALE_ORDER_CREATE_CITY_REQUIRE=City is required
SALE_ORDER_CREATE_CITY_TOO_LONG=City is too long
SALE_ORDER_CREATE_ADDRESS1_EMPTY=Address1 is empty
SALE_ORDER_CREATE_ADDRESS1_TOO_LONG=Address1 is too long
SALE_ORDER_CREATE_EMAIL_PATTERN_ERROR=Email format error
SALE_ORDER_CREATE_PSKU_REQUIRE=PSKU is required
SALE_ORDER_CREATE_PSKU_TOO_LONG=PSKU is too long
SALE_ORDER_CREATE_FNSKU_REQUIRE=FNSKU is required
SALE_ORDER_CREATE_FNSKU_TOO_LONG=FNSKU is too long
SALE_ORDER_CREATE_QUANTITY_SHIPMENT_REQUIRE=Quantity Shipment is required
SALE_ORDER_CREATE_QUANTITY_SHIPMENT_LESS_THAN_ONE=Quantity is less than one
SALE_ORDER_CREATE_QUANTITY_SHIPMENT_MUST_BE_INTEGER=Quantity must be integer
SALE_ORDER_CREATE_PRDOCUT_PRICE_LESS_THAN_ZERO=Product Price is less than zero
SALE_ORDER_CREATE_PRDOCUT_TAX_LESS_THAN_ZERO=Product Tax is less than zero
SALE_ORDER_CREATE_PRDOCUT_DISCOUNT_LESS_THAN_ZERO=Product Discount is less than zero
SALE_ORDER_CREATE_PRDOCUT_DISCOUNT_TAX_LESS_THAN_ZERO=Product Discount Tax is less than zero
SALE_ORDER_CREATE_FREIGHT_AMOUNT_LESS_THAN_ZERO=Freight Amount is less than zero
SALE_ORDER_CREATE_FREIGHT_TAX_LESS_THAN_ZERO=Freight Tax is less than zero
SALE_ORDER_CREATE_FREIGHT_TAX_DISCOUNT_LESS_THAN_ZERO=Freight Tax Discount is less than zero
SALE_ORDER_CREATE_FREIGHT_DISCOUNT_TAX_LESS_THAN_ZERO=Freight Discount Tax is less than zero
SALE_ORDER_CREATE_CHANNEL_ERROR=Channel is not supported
SALE_ORDER_CREATE_CHANNEL_NO_EXIST=ChannelNo is  exist
SALE_ORDER_CREATE_CURRENCY_ERROR=Currency is not supported
SALE_ORDER_CREATE_PSKU_ERROR=PSKU is not exist
SALE_ORDER_CREATE_WAREHOUSE_REQUIRE=Warehouse is required
SALE_ORDER_CREATE_WAREHOUSE_ERROR=Warehouse is not exist
SALE_ORDER_CREATE_WAREHOUSE_ORDER_REQUIRE=Warehouse Order is required
SALE_ORDER_CREATE_WAREHOUSE_ORDER_TOO_LONG=Warehouse Order is too long
SALE_ORDER_CREATE_CARRIER_REQUIRE=Carrier is required
SALE_ORDER_CREATE_CARRIER_TOO_LONG=Carrier is too long
SALE_ORDER_CREATE_TRACKING_NUMBER_REQUIRE=Tracking Number is required
SALE_ORDER_CREATE_TRACKING_NUMBER_TOO_LONG=Tracking Number is too long
SALE_ORDER_CREATE_SHIPPED_TIME_REQUIRE=Shipped Time is required
SALE_ORDER_CREATE_DELIVERD_TIME_ERROR=Delivered Time is error
SALE_ORDER_CREATE_DELIVERD_TIME_REQUIRE=Delivered Time is required
SALE_ORDER_CREATE_DELIVERD_TIME_LESS_THAN_SHIPPED_TIME=Delivered Time is less than Shipped Time
SALE_ORDER_CREATE_INCONSISTENT_ORDER_INFORMATION=Inconsistent order information
RETURN_ORDER_CHANNEL_ORDER_NO_NOT_EXIST=ChannelNo is not exist
ITEM_DETAIL_NOT_EMPTY=Item details cannot be empty
SALE_ORDER_ITEM_ID_REQUIRE=Order itemId cannot ne null
RETURN_QUANTITY_REQUIRE=At least one return quantity exists
RESEND_QUANTITY_REQUIRE=At least one resend quantity exists
RETURN_ORDER_ID_NOT_NULL=Return order id cannot be null
RECEIVE_TIME_NOT_NULL=Receive time cannot be null
RETURN_ORDER_NOT_FOUND=Return order not found
ORDER_ITEM_NOT_FOUND=Order item not found
RETURN_ORDER_STATUS_CHECK=The current order status is not await receipt
RETURN_QUANTITY_CHECK=The return quantity should be greater than 0 and less than or equal to the quantity
RESEND_QUANTITY_CHECK=The resend quantity should be greater than 0 and less than or equal to the quantity
REMARK_NOT_NULL=Remark cannot be null
SALE_ORDER_NOT_DRAFT=The current order is not a draft status
RETURN_ORDER_EXCEL_HEADER.SR=RMA Order
RETURN_ORDER_EXCEL_HEADER.WAREHOUSE=Return To
RETURN_ORDER_EXCEL_HEADER.STATUS=Status
RETURN_ORDER_EXCEL_HEADER.RECEIVE_TIME=Received Time
RETURN_ORDER_EXCEL_HEADER.CREATE_TIME=Create Time
IMPORT_SALES_ORDER_TEMPLATE=Import_sales_order_template
IMPORT_SHIPPED_SALES_ORDER_TEMPLATE=Import_shipped_sales_order_template
PACKAGE_LENGTH_CHECK=Package length cannot be empty
PACKAGE_WIDTH_CHECK=Package width cannot be empty
PACKAGE_HEIGHT_CHECK=Package height cannot be empty
PACKAGE_WEIGHT_CHECK=Package weight cannot be empty
SHIPPING_WAREHOUSE_CHECK=Shipping warehouse id cannot be empty
PACKAGE_WEIGHT_SIZE_CHECK=Package weight and size cannot be empty
TEMU_STORE_ID_CHECK=This store is not allowed to use TEMU online label
WAREHOUSE_SETTING_NOT_FOUND=Warehouse setting not found in fulfillment warehouse table
FULFILLMENT_SERVICE_TYPE_CHECK=The fulfillment service type cannot be null
STORE_ID_CHECK=The {0} of this store is empty
ORDER_ID_NOT_NULL=Order id cannot be null
# Customer Create Command Validation
CUSTOMER_CREATE_COMPANY_NAME_REQUIRE=Customer company name is required
CUSTOMER_CREATE_COMPANY_NAME_TOO_LONG=Customer company name cannot be longer than 100 characters
CUSTOMER_CREATE_COUNTRY_REQUIRE=Country is required
CUSTOMER_CREATE_COUNTRY_MANAGER_USER_ID_REQUIRE=Country manager user ID is required
CUSTOMER_CREATE_SALES_ASSISTANT_USER_ID_REQUIRE=Sales assistant user ID is required
CUSTOMER_CREATE_REMARK_TOO_LONG=Remark cannot be longer than 1024 characters
CUSTOMER_CREATE_CONTACT_NAME_REQUIRE=Contact name is required
CUSTOMER_CREATE_CONTACT_NAME_TOO_LONG=Contact name cannot be longer than 100 characters
CUSTOMER_CREATE_CONTACT_POSITION_REQUIRE=Contact position is required
CUSTOMER_CREATE_CONTACT_POSITION_TOO_LONG=Contact position cannot be longer than 100 characters
CUSTOMER_CREATE_CONTACT_INFOS_REQUIRE=Contact information list cannot be empty
CUSTOMER_CREATE_INCOTERMS_NOT_EXIST=Incoterms does not exist
CUSTOMER_CREATE_PAYMENT_TERMS_NOT_EXIST=Payment terms does not exist
CUSTOMER_CREATE_COMPANY_NOT_EXIST=Company does not exist
CUSTOMER_CREATE_CONTACT_IS_DEFAULT_REQUIRE=Contact is default is required
CUSTOMER_ATTACHMENT_TYPE_REQUIRE=Attachment type is required
CUSTOMER_FILE_LARGER_THAN_10MB=File is larger than 10MB
CUSTOMER_NAME_EXIST=Customer name already exists
CUSTOMER_CREATE_INSURANCE_CREDIT_INSURED_AMOUNT_LESS_THAN_ZERO=Insurance Credit Insured Amount is less than zero
# Customer Operation Service Messages
CUSTOMER_USER_NOT_EXIST=User does not exist
CUSTOMER_PRIMARY_CONTACT_MISSING=Primary contact is missing
CUSTOMER_CONTACT_INFO_MISSING=Contact email or phone is missing
CUSTOMER_NOT_EXIST=Customer does not exist
# Customer Invoice Command Validation
CUSTOMER_INVOICE_ATTN_TO_REQUIRE=Invoice recipient is required
CUSTOMER_INVOICE_ATTN_TO_TOO_LONG=Invoice recipient cannot be longer than 100 characters
CUSTOMER_INVOICE_COMPANY_REQUIRE=Invoice company name is required
CUSTOMER_INVOICE_COMPANY_TOO_LONG=Invoice company name cannot be longer than 100 characters
CUSTOMER_INVOICE_ADDRESS_REQUIRE=Invoice address is required
CUSTOMER_INVOICE_ADDRESS_TOO_LONG=Invoice address cannot be longer than 255 characters
CUSTOMER_INVOICE_ZIP_CODE_REQUIRE=Invoice zip code is required
CUSTOMER_INVOICE_ZIP_CODE_TOO_LONG=Invoice zip code cannot be longer than 20 characters
CUSTOMER_INVOICE_CITY_REQUIRE=Invoice city is required
CUSTOMER_INVOICE_CITY_TOO_LONG=Invoice city cannot be longer than 100 characters
CUSTOMER_INVOICE_COUNTRY_REQUIRE=Invoice country is required
CUSTOMER_INVOICE_COUNTRY_TOO_LONG=Invoice country cannot be longer than 100 characters
CUSTOMER_INVOICE_CONTACT_NUMBER_REQUIRE=Invoice contact number is required
CUSTOMER_INVOICE_CONTACT_NUMBER_TOO_LONG=Invoice contact number cannot be longer than 50 characters
CUSTOMER_INVOICE_VAT_NUMBER_REQUIRE=VAT number is required
CUSTOMER_INVOICE_VAT_NUMBER_TOO_LONG=VAT number cannot be longer than 50 characters
# Customer Shipping Command Validation
CUSTOMER_SHIPPING_ATTN_TO_REQUIRE=Shipping recipient is required
CUSTOMER_SHIPPING_ATTN_TO_TOO_LONG=Shipping recipient cannot be longer than 100 characters
CUSTOMER_SHIPPING_COMPANY_REQUIRE=Shipping company name is required
CUSTOMER_SHIPPING_COMPANY_TOO_LONG=Shipping company name cannot be longer than 100 characters
CUSTOMER_SHIPPING_ADDRESS_REQUIRE=Shipping address is required
CUSTOMER_SHIPPING_ADDRESS_TOO_LONG=Shipping address cannot be longer than 255 characters
CUSTOMER_SHIPPING_ZIP_CODE_REQUIRE=Shipping zip code is required
CUSTOMER_SHIPPING_ZIP_CODE_TOO_LONG=Shipping zip code cannot be longer than 20 characters
CUSTOMER_SHIPPING_CITY_REQUIRE=Shipping city is required
CUSTOMER_SHIPPING_CITY_TOO_LONG=Shipping city cannot be longer than 100 characters
CUSTOMER_SHIPPING_COUNTRY_REQUIRE=Shipping country is required
CUSTOMER_SHIPPING_COUNTRY_TOO_LONG=Shipping country cannot be longer than 100 characters
CUSTOMER_SHIPPING_CONTACT_NUMBER_REQUIRE=Shipping contact number is required
CUSTOMER_SHIPPING_CONTACT_NUMBER_TOO_LONG=Shipping contact number cannot be longer than 50 characters
FULFILLMENT_SERVICE_CODE_NOT_FOUND=Carrier service code not configured
AUDIT_SWITCH_EMPTY=The auto switch cannot be empty
PSKU_EMPTY=The PSKU cannot be empty
WAREHOUSE_SELECTOR_PRIORITY_EMPTY=The warehouse selector priority cannot be empty
PRIORITY_EMPTY=The priority cannot be empty
ONE_SKU_ONE_PC=One SKU One PC
ONE_SKU_MULTI_PCS=One SKU Multi PCs
MULTI_SKUS=Multi SKUs
SWITCH_ON=On
SWITCH_OFF=Off
AUTO_SHIP_AUDIT_CONFIG_EXCEL.CHANNEL=Sales Channel
AUTO_SHIP_AUDIT_CONFIG_EXCEL.STORE=Shop
AUTO_SHIP_AUDIT_CONFIG_EXCEL.PSKU=PSKU
AUTO_SHIP_AUDIT_CONFIG_EXCEL.WAREHOUSECODE=Warehouse Code List(Split with comma)
AUTO_SHIP_AUDIT_CONFIG_EXCEL.ONESKUONEPC=SKU Comb Type:One SKU One PC(Y/N)
AUTO_SHIP_AUDIT_CONFIG_EXCEL.ONESKUMULTIPCS=SKU Comb Type:One SKU Multi PCs(Y/N)
AUTO_SHIP_AUDIT_CONFIG_EXCEL.MULTISKUS=SKU Comb Type:Multi SKUs(Y/N)
AUTO_SHIP_AUDIT_CONFIG_IMPORT_EXCEL.AUTOSWITCH=Auto Shipment Review Switch(On/Off)
AUTO_SHIP_AUDIT_CONFIG_EXCEL.WAREHOUSESELECTORPRIORITY=Warehouse Selection Priority
AUTO_SHIP_AUDIT_CONFIG_EXCEL.SKUCOMBINATIONTYPE=SKU Combination Type
AUTO_SHIP_AUDIT_CONFIG_EXPORT_EXCEL.AUTOSWITCH=Auto Shipment Review Switch
AUTO_SHIP_AUDIT_CONFIG_EXCEL.ERRORMESSAGE=Error Message
WAREHOUSE_CODE_LIST_EMPTY=The warehouse code list cannot be empty
PSKU_NO_EXITS=The psku does not exist
SKU_COMBINATION_TYPE_YES_NOT=The sku combination type can only be Y/N
SKU_COMBINATION_TYPE_EMPTY=The sku combination type cannot be empty
SKU_COMBINATION_TYPE_ALL_FALSE=The sku combination type cannot be all false
AUDIT_SWITCH_ON_OF=The auto shipment review switch can only be On/Off
CHANNEL_SHOP_PSKU_EXISTS=The channel+shop+psku had exists
WAREHOUSE_CODE_LIST_EXIST=The warehouse code does not exist
STORE_NO_DATA_PERMISSION=The store not data permission
PSKU_NO_DATA_PERMISSION=The psku not data permission
WAREHOUSE_TEMPLATE_EXCEL.WAREHOUSENAME=Warehouse Name
WAREHOUSE_TEMPLATE_EXCEL.WAREHOUSECODE=Warehouse Code
AUTO_SHIP_AUDIT_CONFIG_EXPORT_NAME=Auto Shipment Review Conf Export
AUTO_SHIP_AUDIT_CONFIG_TEMPLATE_NAME=Auto Shipment Review Conf Template
WAREHOUSE_ID_EMPTY=The warehouse id cannot be empty
WAREHOUSE_CODE_EMPTY=The warehouse code cannot be empty
WAREHOUSE_CUSTOMER_CODE_CONFIG_NOT_FOUND=IMS customer code is not set for this warehouse
WAREHOUSE_REPEAT=The warehouse cannot be repeat 
STORE_SALE_CHANNEL_NOT_MATCH=The store is not a sales channel
# Order base info
B2B_ORDER_ITEMS_NOT_EMPTY=Order items cannot be empty
B2B_ORDER_CUSTOMER_NOT_NULL=Customer information cannot be empty
B2B_ORDER_CUSTOMER_INVOICE_NOT_NULL=Customer invoice information cannot be empty
B2B_ORDER_AMOUNT_NOT_NULL=Order amount information cannot be empty
B2B_ORDER_SHIPMENT_REQUIREMENT_NOT_NULL=Shipment requirement cannot be empty
# Order item
B2B_ORDER_ITEM_PSKU_NOT_BLANK=PSKU cannot be empty
B2B_ORDER_ITEM_ORDERED_QUANTITY_NOT_NULL=Ordered quantity cannot be empty
B2B_ORDER_ITEM_UNIT_PRICE_NOT_NULL=Unit price cannot be empty
B2B_ORDER_ITEM_TAX_RATE_NOT_NULL=Tax rate cannot be empty
B2B_ORDER_ITEM_TAX_NOT_NULL=Item tax amount cannot be empty
B2B_ORDER_ITEM_PSKU_LENGTH_EXCEEDS_LIMIT=PSKU length cannot exceed 64 characters
B2B_ORDER_ITEM_ORDERED_QUANTITY_MUST_BE_GREATER_THAN_ZERO=Ordered quantity must be greater than zero
B2B_ORDER_ITEM_UNIT_PRICE_MUST_BE_GREATER_THAN_ZERO=Unit price must be greater than zero
# Order amount
B2B_ORDER_AMOUNT_CURRENCY_NOT_BLANK=Currency cannot be empty
B2B_ORDER_AMOUNT_FEE_TYPE_NOT_NULL=Fee type cannot be empty
B2B_ORDER_AMOUNT_FEE_AMOUNT_NOT_NULL=Fee amount cannot be empty
# Order customer
B2B_ORDER_CUSTOMER_ID_NOT_NULL=Customer ID cannot be empty
B2B_ORDER_CUSTOMER_SALES_COMPANY_ID_NOT_NULL=Sales company ID cannot be null
B2B_ORDER_CUSTOMER_INCOTERMS_CODE_NOT_BLANK=Incoterms code cannot be empty
B2B_ORDER_CUSTOMER_PAYMENT_TERMS_CODE_NOT_BLANK=Payment terms code cannot be empty
# Order shipment requirement
B2B_ORDER_SHIPMENT_REQUEST_DELIVERY_DATE_NOT_NULL=Request delivery date cannot be empty
B2B_ORDER_SHIPMENT_PALLETIZATION_REQUIRED_NOT_NULL=Palletization required cannot be empty
# Order customer invoice
ORDER_INVOICE_ALL_OR_NONE_FIELDS_REQUIRED=All invoice fields must be either all filled or all empty
# Order shipping address
ORDER_SHIPPING_ADDRESS_ALL_OR_NONE_FIELDS_REQUIRED=All shipping fields must be either all filled or all empty
ORDER_SHIPPING_ADDRESS_CANNOT_BE_FILLED_IN_WITH_JUST_THE_COMMENT=shipping cannot be filled in with just the comment
#Receipt order
ORDER_NO_EMPTY=The orderNo cannot be empty
CUSTOMER_PAID_AMOUNT_NO_EMPTY=The customer paid amount cannot be empty
CUSTOMER_PAID_TIME_NO_EMPTY=The customer_paid_time cannot be empty
ORDER_NOT_EXISTS=The order does not exist
BAND_ACCOUNT_NOT_EMPTY=The band account cannot be empty
RECEIPT_AMOUNT_NOT_EMPTY=The receipt amount cannot be empty
FEES_AMOUNT_NOT_EMPTY=The fees amount cannot be empty
RECEIPT_TIME_NOT_EMPTY=The receipt time cannot be empty
ADD_RECEIPT_ORDER_B2B_STATUS_CHECK=The current b2bOrder status is not pending, in review,preparing,partially shipped, or shipped
B2B_ORDER_STATUS_NOT_PENDING=Order status is not pending for processing
BMP_PROCESS_CREATE_ERROR=Failed to create BPM approval process
B2B_ORDER_NOT_FOUND=B2B order not found
B2B_ORDER_DATA_INCOMPLETE=B2B order data incomplete
INSURED_CURRENCY_EMPTY=Insured currency cannot be empty
INSURED_AMOUNT_EMPTY=Insured amount cannot be empty
CALCULATE_USED_CREDIT_AMOUNT_ERROR=Failed to calculate used credit amount
B2B_ORDER_CUSTOMER_INVOICE_NOT_FOUND=B2B order customer invoice information not found
BMP_PROCESS_BIZ_ID_EMPTY=BMP process business ID is empty
BMP_PROCESS_BIZ_ID_FORMAT_ERROR=BMP process business ID format error
BMP_PROCESS_RESULT_EMPTY=BMP process result is empty
BMP_PROCESS_RESULT_UNKNOWN=Unknown BMP process result type
B2B_ORDER_STATUS_UPDATE_FAILED=B2B order status update failed
CUSTOMER_PAID_AMOUNT_GT_ZERO_TWO_SCALE=The customer paid amount must be greater than zero and with two decimal places
RECEIPT_AMOUNT_GT_ZERO_TWO_SCALE=The receipt amount must be greater than zero and with two decimal places
FEES_AMOUNT_GE_ZERO_TWO_SCALE=The fees amount must be greater equal zero and with two decimal places
# B2B Order Export Excel Headers
B2B_ORDER_EXCEL_HEADER.ORDER_NO=Order #
B2B_ORDER_EXCEL_HEADER.CUSTOMER_COMPANY=Customer Company
B2B_ORDER_EXCEL_HEADER.REFER_NO=Refer #
B2B_ORDER_EXCEL_HEADER.SALES_COMPANY=Sales Company
B2B_ORDER_EXCEL_HEADER.INCOTERMS=Incoterms
B2B_ORDER_EXCEL_HEADER.PAYMENT_TERMS=Payment Terms
B2B_ORDER_EXCEL_HEADER.COUNTRY_MANAGER=Country Manager
B2B_ORDER_EXCEL_HEADER.SALES_ASSISTANT=Sales Assistant
B2B_ORDER_EXCEL_HEADER.CREATED_TIME=Created Time
B2B_ORDER_EXCEL_HEADER.INVOICE_TIME=Invoice Time
B2B_ORDER_EXCEL_HEADER.SHIPPED_TIME=Shipped Time
B2B_ORDER_EXCEL_HEADER.PSKU=PSKU
B2B_ORDER_EXCEL_HEADER.PSKU_NAME=Name
B2B_ORDER_EXCEL_HEADER.BARCODE=Barcode
B2B_ORDER_EXCEL_HEADER.QUANTITY=Quantity
B2B_ORDER_EXCEL_HEADER.UNIT_PRICE=Unit Price
B2B_ORDER_EXCEL_HEADER.TAX_RATE=Tax Rate
B2B_ORDER_EXCEL_HEADER.TAX=Tax
B2B_ORDER_EXCEL_HEADER.SHIPPING_FEE=Shipping Fee
B2B_ORDER_EXCEL_HEADER.OTHER_FEE=Other Fee
B2B_ORDER_EXCEL_HEADER.OTHER_FEE_DETAIL=Other Fee Detail
B2B_ORDER_EXCEL_HEADER.TOTAL_AMOUNT=Total Amount
B2B_ORDER_EXCEL_HEADER.CURRENCY=Currency
B2B_ORDER_EXCEL_HEADER.RECEIPT_AMOUNT=Receipt Amount
B2B_ORDER_EXCEL_HEADER.SHIPPING_WINDOW=Shipping Window
B2B_ORDER_EXCEL_HEADER.PALLETIZATION_REQUIRED=Palletization required
B2B_ORDER_EXCEL_HEADER.BL_TYPE=B/L Type
B2B_ORDER_EXCEL_HEADER.OTHER_REQUIREMENTS=Other Requirements
B2B_ORDER_EXCEL_HEADER.REMARK=Remark
B2B_ORDER_CANCEL_STATUS_ERROR=Orders that are not in pending status cannot be canceled
B2B_ORDER_STATUS_NOT_PREPARING=The order status is not in Purchasing
B2B_ORDER_SHIPMENT_REVIEW_STATUS_ERROR=Please do not submit the shipping review multiple times
# B2B order validation
B2B_ORDER_REFER_NO_LENGTH_EXCEEDS_LIMIT=Reference number length cannot exceed 32 characters
B2B_ORDER_AMOUNT_CURRENCY_LENGTH_EXCEEDS_LIMIT=Currency length cannot exceed 8 characters
B2B_ORDER_ADDRESS_ATTN_TO_LENGTH_EXCEEDS_LIMIT=Recipient length cannot exceed 100 characters
B2B_ORDER_ADDRESS_SHIPPING_COMPANY_LENGTH_EXCEEDS_LIMIT=Shipping company length cannot exceed 100 characters
B2B_ORDER_ADDRESS_ADDRESS_LENGTH_EXCEEDS_LIMIT=Address length cannot exceed 256 characters
B2B_ORDER_ADDRESS_ZIP_CODE_LENGTH_EXCEEDS_LIMIT=Zip code length cannot exceed 20 characters
B2B_ORDER_ADDRESS_CITY_LENGTH_EXCEEDS_LIMIT=City length cannot exceed 100 characters
B2B_ORDER_ADDRESS_COUNTRY_LENGTH_EXCEEDS_LIMIT=Country length cannot exceed 100 characters
B2B_ORDER_ADDRESS_CONTACT_NUMBER_LENGTH_EXCEEDS_LIMIT=Contact number length cannot exceed 50 characters
B2B_ORDER_ADDRESS_COMMENT_LENGTH_EXCEEDS_LIMIT=Comments length cannot exceed 256 characters
B2B_ORDER_SHIPMENT_MASTER_CARTON_LABEL_NAME_LENGTH_EXCEEDS_LIMIT=Master carton label name length cannot exceed 128 characters
B2B_ORDER_SHIPMENT_MASTER_CARTON_LABEL_URL_LENGTH_EXCEEDS_LIMIT=Master carton label URL length cannot exceed 256 characters
B2B_ORDER_SHIPMENT_OTHER_REQUIREMENTS_LENGTH_EXCEEDS_LIMIT=Other requirements length cannot exceed 512 characters
B2B_ORDER_CUSTOMER_SALES_COMPANY_ID_LENGTH_EXCEEDS_LIMIT=Sales company ID length cannot exceed 100 characters
B2B_ORDER_CUSTOMER_INCOTERMS_CODE_LENGTH_EXCEEDS_LIMIT=Incoterms code length cannot exceed 64 characters
B2B_ORDER_CUSTOMER_PAYMENT_TERMS_CODE_LENGTH_EXCEEDS_LIMIT=Payment terms code length cannot exceed 64 characters
B2B_ORDER_CUSTOMER_INVOICE_ATTN_TO_LENGTH_EXCEEDS_LIMIT=Invoice recipient length cannot exceed 100 characters
B2B_ORDER_CUSTOMER_INVOICE_INVOICE_COMPANY_LENGTH_EXCEEDS_LIMIT=Invoice company length cannot exceed 100 characters
B2B_ORDER_CUSTOMER_INVOICE_ADDRESS_LENGTH_EXCEEDS_LIMIT=Invoice address length cannot exceed 255 characters
B2B_ORDER_CUSTOMER_INVOICE_ZIP_CODE_LENGTH_EXCEEDS_LIMIT=Invoice zip code length cannot exceed 20 characters
B2B_ORDER_CUSTOMER_INVOICE_CITY_LENGTH_EXCEEDS_LIMIT=Invoice city length cannot exceed 100 characters
B2B_ORDER_CUSTOMER_INVOICE_COUNTRY_LENGTH_EXCEEDS_LIMIT=Invoice country length cannot exceed 100 characters
B2B_ORDER_CUSTOMER_INVOICE_CONTACT_NUMBER_LENGTH_EXCEEDS_LIMIT=Invoice contact number length cannot exceed 50 characters
B2B_ORDER_CUSTOMER_INVOICE_VAT_NUMBER_LENGTH_EXCEEDS_LIMIT=VAT number length cannot exceed 50 characters
CUSTOMER_INVOICE_INFO_DUPLICATE=Duplicate invoice information found
CUSTOMER_CONTACT_INFO_DUPLICATE=Duplicate contact information found
CUSTOMER_SHIPPING_INFO_DUPLICATE=Duplicate shipping information found
B2B_ORDER_CUSTOMER_CONTACT_INFO_NOT_FOUND=B2B order customer contact info not found
PSKU_NOT_FOUND=psku not found
SALES_COMPANY_NOT_FOUND=Sales company not found
ORDER_STATUS_NOT_PENDING=Order status is not Pending
VC_ORDER_HAS_NOT_SUCCESS_PARSE=SKU mapping error, maintain mapping and re-parse.
VC_ORDER_STATUS_ERROR=vc order status error
VC_ORDER_NOT_FOUND=B2B order not found
VC_ORDER_BUSINESS_TYPE_NOT_DO=Shipping Labels are not required for DI orders
VC_ORDER_PARSE_STATUS_NOT_SUCCESS=SKU mapping is abnormal. Please first maintain the mapping relationship and reparse
VC_ORDER_ACCEPTED_STATUS_INVALID=The accepted status must be pending asn label or accept exception

VC_ORDER_STATUS_ACCEPT_EXCEPTION=The order status must be accept exception
VC_ORDER_LABEL_NOT_UPLOADED=Shipping labels have not been uploaded

VC_ORDER_LABEL_UPLOAD_ORDER_ID_NOTNULL=Order ID cannot be null
VC_ORDER_LABEL_UPLOAD_ITEM_LABEL_LIST_NOT_EMPTY=Item label list cannot be empty
VC_ORDER_LABEL_UPLOAD_ITEM_ID_NOTNULL=Order item ID cannot be null
VC_ORDER_LABEL_UPLOAD_ITEM_ASIN_LABEL_URL_NOT_EMPTY=ASIN label URL cannot be empty
VC_ORDER_LABEL_UPLOAD_ITEM_ASIN_LABEL_NAME_NOT_EMPTY=ASIN label name cannot be empty
VC_ORDER_LABEL_UPLOAD_ITEM_CARTON_LABEL_URL_NOT_EMPTY=Carton label URL cannot be empty
VC_ORDER_LABEL_UPLOAD_ITEM_CARTON_LABEL_NAME_NOT_EMPTY=Carton label name cannot be empty
VC_ORDER_SUBMIT_ACKNOWLEDGEMENT_FAIL=The reason for failure to submit acknowledgement is {0}
VC_ORDER_INVOICE_NO_DUPLICATED=The same invoice number already exists

VC_ORDER_ITEM_NOT_FOUND=VC order item line does not exist
VC_ORDER_ITEM_EMPTY=VC order item line cannot be empty
VC_ORDER_ALL_ITEMS_ZERO=No need to upload labels when all order acceptance quantities are 0
VC_ORDER_OPERATE_TYPE_NOT_FOUND=The operation type is not found