package com.renpho.erp.oms.adapter.client;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.oms.client.spf.RemoteSpfOrderService;
import com.renpho.erp.oms.client.spf.query.GetOrderIdPageQuery;
import com.renpho.erp.oms.client.spf.vo.GetOrderIdPageVo;
import com.renpho.erp.oms.infrastructure.persistence.shopify.mapper.SpfOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.shopify.po.SpfOrderPO;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;

@RestController
public class RemoteSpfOrderServiceImpl implements RemoteSpfOrderService {
    @Autowired
    private SpfOrderMapper spfOrderMapper;

    @Inner
    @Override
    public R<Paging<GetOrderIdPageVo>> getOrderIdPage(GetOrderIdPageQuery query) {
        SFunction<SpfOrderPO, LocalDateTime> dateField = StrUtil.equalsIgnoreCase(query.getDateType(), "createDate") ? SpfOrderPO::getCreatedAt : SpfOrderPO::getUpdatedAt;
        Wrapper<SpfOrderPO> wrapper = Wrappers.<SpfOrderPO>lambdaQuery()
                .select(SpfOrderPO::getShopifyOrderId, SpfOrderPO::getStoreId, SpfOrderPO::getName)
                .ge(query.getStartDate() !=null, dateField, query.getStartDate())
                .le(query.getEndDate() !=null, dateField, query.getEndDate())
                .eq(query.getStoreId() != null, SpfOrderPO::getStoreId, query.getStoreId())
                .in(CollUtil.isNotEmpty(query.getSpfOrderIdList()), SpfOrderPO::getShopifyOrderId, query.getSpfOrderIdList())
                .orderByAsc(SpfOrderPO::getId);
        Page<SpfOrderPO> page = new Page<>(query.getPageIndex(), query.getPageSize());
        spfOrderMapper.selectPage(page, wrapper);
        IPage<GetOrderIdPageVo> resultPage = page.convert(spfOrder -> {
            GetOrderIdPageVo getOrderIdPageVo = new GetOrderIdPageVo();
            getOrderIdPageVo.setShopifyOrderId(spfOrder.getShopifyOrderId());
            getOrderIdPageVo.setName(spfOrder.getName());
            getOrderIdPageVo.setStoreId(spfOrder.getStoreId());
            return getOrderIdPageVo;
        });
        return R.success(Paging.of(resultPage.getRecords(), (int) resultPage.getTotal(), (int) resultPage.getSize(), (int) resultPage.getCurrent()));
    }
}
