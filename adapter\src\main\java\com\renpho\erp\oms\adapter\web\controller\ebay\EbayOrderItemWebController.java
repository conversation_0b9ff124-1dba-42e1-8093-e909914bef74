package com.renpho.erp.oms.adapter.web.controller.ebay;

import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayCancelRequestVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayOrderItemVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbayPaymentDetailVO;
import com.renpho.erp.oms.application.channelmanagement.ebay.service.EbyOrderPlatformService;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Ebay订单明细
 * @time: 2025-04-14 09:44:54
 * @author: <PERSON><PERSON>
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/ebay/**")
@RequestMapping("/ebay/item")
@AllArgsConstructor
public class EbayOrderItemWebController {

	private final EbyOrderPlatformService ebyOrderPlatformService;

	/**
	 * Ebay 订单行
	 * @param orderId 订单id
	 * @return R<List<EbayOrderItemVO>>
	 */
	@GetMapping("/list")
	public R<List<EbayOrderItemVO>> listByOrderId(@RequestParam Long orderId) {
		return ebyOrderPlatformService.listItemByOrderId(orderId);
	}

	/**
	 * Ebay 付款明细
	 * @param orderId 订单id
	 * @return R<List<EbayPaymentDetailVO>>
	 */
	@GetMapping("/listPaymentDetailByOrderId")
	public R<List<EbayPaymentDetailVO>> listPaymentDetailByOrderId(@RequestParam Long orderId) {
		return R.success(ebyOrderPlatformService.listPaymentDetailByOrderId(orderId));
	}

	/**
	 * Ebay 取消状态列表
	 * @param orderId 订单id
	 * @return R<List<EbayCancelRequestVO>>
	 */
	@GetMapping("/listCancelRequestByOrderId")
	public R<List<EbayCancelRequestVO>> listCancelRequestByOrderId(@RequestParam Long orderId) {
		return R.success(ebyOrderPlatformService.listCancelRequestByOrderId(orderId));
	}
}
