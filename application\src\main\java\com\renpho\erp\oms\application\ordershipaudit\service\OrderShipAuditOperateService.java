package com.renpho.erp.oms.application.ordershipaudit.service;

import org.springframework.stereotype.Service;

import com.renpho.erp.oms.application.ordershipaudit.cmd.OrderShipAuditCmd;
import com.renpho.erp.oms.application.salemanagement.converter.OrderShipAuditConverter;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.karma.json.JSONKit;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单发货审核-操作 应用服务
 *
 * <AUTHOR>
 * @since 2025/2/17
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderShipAuditOperateService {

	private final OrderShipAuditCreateService orderShipAuditCreateService;
	private final OrderShipAuditService orderShipAuditService;
	private final OrderShipAuditConverter orderShipAuditConverter;

	/**
	 * 发货审核
	 * @param cmd 指令
	 */
	public void shipAudit(OrderShipAuditCmd cmd) {
		log.info("发货审核请求：{}", JSONKit.toJSONString(cmd));
		// 转换为订单发货审核 DTO
		OrderShipAuditDTO shipAuditDto = orderShipAuditConverter.convertShipAuditDto(cmd);
		// 发货审核创建
		OrderShipAuditAggRoot shipAuditAggRoot = orderShipAuditCreateService.create(shipAuditDto, false);
		// 发货审核流程执行
		orderShipAuditService.shipAudit(shipAuditAggRoot.getAuditNo(), false);
	}
}
