package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.query;


import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Data
public class MulSkuMappingQuery {
    @NotNull(message = "{MULTI_CHANNEL_TYPE_EMPTY}")
    private Integer mulChannelType;
    @NotNull(message = "{MULTI_CHANNEL_STORE_EMPTY}")
    private Integer mulChannelStoreId;
    @NotEmpty(message = "{MUL_SKU_ORDER_PSKU_EMPTY}")
    private List<String> orderPsku;
    @NotEmpty(message = "{MUL_SKU_ORDER_STORE_ID_EMPTY}")
    private Integer orderStoreId;

}
