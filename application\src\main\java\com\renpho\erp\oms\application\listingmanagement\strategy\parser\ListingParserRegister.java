package com.renpho.erp.oms.application.listingmanagement.strategy.parser;

import com.alibaba.nacos.shaded.javax.annotation.Nonnull;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * Listing解析器注册
 * <AUTHOR>
 */
@Component
public class ListingParserRegister implements ApplicationContextAware, InitializingBean {

	private ApplicationContext context;

	private static final Map<String, ListingParser> CACHE = new HashMap<>(1 << 5);

	@Override
	public void setApplicationContext(@NotNull @Nonnull ApplicationContext applicationContext) throws BeansException {
		this.context = applicationContext;
	}

	@Override
	public void afterPropertiesSet() {
		// 从容器获取ListingParser
		String[] beanNamesForTypes = context.getBeanNamesForType(ListingParser.class);
		for (String beanNamesForType : beanNamesForTypes) {
			ListingParser listingParser = (ListingParser) context.getBean(beanNamesForType);
			// 销售渠道编码
			String salesChannelCode = listingParser.getSalesChannelCode();
			// 注册
			this.register(salesChannelCode, listingParser);
		}
	}

	/**
	 * 注册解析器
	 * @param salesChannelCode 销售渠道编码
	 * @param listingParser Listing解析器
	 */
	private void register(String salesChannelCode, ListingParser listingParser) {
		if (CACHE.containsKey(salesChannelCode)) {
			return;
		}
		CACHE.put(salesChannelCode, listingParser);
	}

	/**
	 * 获取解析器
	 * @param salesChannelCode 销售渠道编码
	 * @return ListingParser
	 */
	public ListingParser getParser(String salesChannelCode) {
		return CACHE.get(salesChannelCode);
	}

}
