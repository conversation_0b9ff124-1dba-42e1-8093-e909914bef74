package com.renpho.erp.oms.domain.vc.order.entity;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import com.baomidou.mybatisplus.core.toolkit.IdWorker;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单备注实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderRemark {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 备注信息
	 */
	private String remark;

	/**
	 * 生成id
	 * @param orderId 订单id
	 */
	public void generateId(Long orderId) {
		this.id = IdWorker.getId();
		this.orderId = orderId;
	}

}
