package com.renpho.erp.oms.application.b2b.customer.vo;

import java.util.List;

import lombok.Data;

/**
 * 客户视图对象
 */
@Data
public class CustomerDetailVO {
	/**
	 * 客户ID
	 */
	private Long id;

	/**
	 * 客户公司名称
	 */
	private String customerCompanyName;

	/**
	 * 多语言国家名称
	 */
	private String languageConutryName;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 海外区域经理名称
	 */
	private String countryManagerName;

	/**
	 * 海外区域经理工号
	 */
	private String countryManagerCode;

	/**
	 * 海外区域经理用户id
	 */
	private Integer countryManagerUserId;

	/**
	 * 销售助理名称
	 */
	private String salesAssistantName;

	/**
	 * 销售助理工号
	 */
	private String salesAssistantCode;

	/**
	 * 销售助理用户id
	 */
	private Integer salesAssistantUserId;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 状态 0 禁用 1启用
	 */
	private Integer status;

	/**
	 * 联系人列表
	 */
	private List<CustomerContactVO> contacts;

	/**
	 * 客户发票信息列表
	 */
	private List<CustomerInvoiceVO> invoiceInfos;

	/**
	 * 客户收货信息列表
	 */
	private List<CustomerShippingVO> shippingInfos;

	/**
	 * 客户销售公司和协议信息
	 */
	private CustomerSalesCompanyTermsVO salesCompanyTermsInfo;

}