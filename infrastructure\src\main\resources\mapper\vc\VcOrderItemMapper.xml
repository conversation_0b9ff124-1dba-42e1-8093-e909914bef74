<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderItemMapper">

    <select id="getByOrderIds" resultType="com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderItemPO">
        SELECT
            id,
            order_id,
            channel_order_line_no,
            msku,
            asin,
            psku,
            barcode,
            number_of_units_per_box,
            image_id,
            ordered_quantity,
            accepted_quantity,
            rejected_quantity,
            rejection_reason,
            shipped_quantity,
            received_quantity,
            currency,
            unit_price,
            tax,
            sub_total,
            create_time,
            update_time
        FROM
            oms_vc_order_item
        WHERE
            is_deleted = 0
            <if test="orderIds != null and orderIds.size() > 0">
                AND order_id IN
                <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
                    #{orderId}
                </foreach>
            </if>
        ORDER BY order_id, id
    </select>
    
    <!-- 批量更新商品SKU解析结果 -->
    <update id="batchUpdateSkuParseResult">
        <foreach collection="items" item="item" separator=";" open="" close="">
            UPDATE oms_vc_order_item
            SET
                psku = #{item.psku},
                barcode = #{item.barcode},
                number_of_units_per_box = #{item.numberOfUnitsPerBox},
                image_id = #{item.imageId},
                update_time = #{item.updateTime}
            WHERE
                order_id = #{item.orderId}
                AND asin = #{item.asin}
                AND is_deleted = 0
        </foreach>
    </update>

</mapper>
