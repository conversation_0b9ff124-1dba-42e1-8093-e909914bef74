package com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper;

import com.renpho.erp.oms.infrastructure.persistence.vc.order.query.VcOrderPageQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;

import com.renpho.erp.oms.infrastructure.expression.expression.VcOrderExpression;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderPO;

import java.util.List;

/**
 * VC-订单表 Mapper
 * <AUTHOR>
 */
@Mapper
public interface VcOrderMapper extends BaseMapper<VcOrderPO> {

    /**
     * 连表分页查询（带数据权限）
     * @param page 分页参数
     * @param query 查询参数
     * @return IPage
     */
    @DataPermission(condition = VcOrderExpression.class)
    IPage<VcOrderPO> selectPermissionPage(@Param("page") IPage<VcOrderPO> page, @Param("query") VcOrderPageQuery query);

    /**
     * 查询不重复的站点编码列表（带数据权限）
     * @return 站点编码列表
     */

    List<String> selectDistinctSiteCodes();

}
