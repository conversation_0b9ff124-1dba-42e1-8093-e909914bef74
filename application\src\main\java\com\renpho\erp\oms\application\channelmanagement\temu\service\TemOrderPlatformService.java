package com.renpho.erp.oms.application.channelmanagement.temu.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.type.TypeReference;
import com.renpho.erp.apiproxy.temu.model.orders.OrderLabel;
import com.renpho.erp.apiproxy.temu.model.orders.Product;
import com.renpho.erp.oms.application.channelmanagement.temu.convert.TemOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.temu.vo.TemOrderItemVO;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.application.channelmanagement.temu.vo.TemOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderAggRoot;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrder;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.temu.repository.TemOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.temu.mapper.TemOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.temu.po.TemOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.renpho.erp.oms.application.channelmanagement.temu.convert.TemOrderItemAppConvertor;

@AllArgsConstructor
@Service(ChannelCode.TEM_CHANNEL_CODE)
@Slf4j
public class TemOrderPlatformService extends AbstractDefaultPlatfomService {
    private final TemOrderRepository temOrderRepository;
    private final PlatformOrderConvertor platformOrderConvertor;

    private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

    private TemOrderMapper temOrderMapper;

    @Override
    protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
        IPage<TemOrderPage> page = temOrderRepository.page(platformPageQuery);
        List<TemOrderPage> temOrderPageList = page.getRecords();
        if (CollectionUtil.isEmpty(temOrderPageList)) {
            return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
        }
        List<PlatformOrderVO> records = temOrderPageList.stream().map(temOrderPage -> {
            PlatformOrderVO platformOrderVO = platformOrderConvertor.temuOrderToVO(temOrderPage);
            parseProduct(platformOrderVO, temOrderPage.getProductList());
            return platformOrderVO;
        }).collect(Collectors.toList());

        return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
    }

    private void parseProduct(PlatformOrderVO platformOrderVO, String productList) {
        if (StringUtils.isNotEmpty(productList)) {
            List<Product> productLists = JSONKit.parseObject(productList, new TypeReference<List<Product>>() {
            });
            if (CollectionUtil.isNotEmpty(productLists)) {
                Product product = productLists.get(0);
                platformOrderVO.setOrderItemId(product.getProductSkuId().toString());
                platformOrderVO.setSellerSku(product.getExtCode());
            }
        }
    }

    @Override
    public R get(PlatformQuery platformQuery) {
        TemOrder temOrder = temOrderRepository.getByIdOrChannelOrderId(platformQuery);
        if (Objects.isNull(temOrder)) {
            throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
        }
        TemOrderVO temOrderVO = TemOrderAppConvertor.toVO(temOrder);
        // 查询店铺
        temOrderVO.setStoreName(getStoreName(temOrder.getStoreId()));
        parseLable(temOrderVO, temOrder.getParentOrderLabel(), temOrder.getFulfillmentWarning());
        return R.success(temOrderVO);
    }

    private void parseLable(TemOrderVO temOrderVO, String parentOrderLabel, String fulfillmentWarning) {
        if (StringUtils.isNotEmpty(parentOrderLabel)) {
            List<OrderLabel> orderLabelList = JSONKit.parseObject(parentOrderLabel, new TypeReference<List<OrderLabel>>() {
            });
            if (CollectionUtil.isNotEmpty(orderLabelList)) {
                orderLabelList.forEach(orderLabel -> {
                    if (Objects.equals(orderLabel.getName(), "soon_to_be_overdue")) {
                        temOrderVO.setSoonToBeOverdue(orderLabel.getValue());
                    } else if (Objects.equals(orderLabel.getName(), "past_due")) {
                        temOrderVO.setPastDue(orderLabel.getValue());
                    } else if (Objects.equals(orderLabel.getName(), "pending_buyer_cancellation")) {
                        temOrderVO.setPendingBuyerCancellation(orderLabel.getValue());
                    } else if (Objects.equals(orderLabel.getName(), "pending_buyer_address_change")) {
                        temOrderVO.setPendingBuyerAddressChange(orderLabel.getValue());
                    } else if (Objects.equals(orderLabel.getName(), "pending_risk_control_alert")) {
                        temOrderVO.setPendingRiskControlAlert(orderLabel.getValue());
                    }
                });
            }
        }
        if (StringUtils.isNotEmpty(fulfillmentWarning)) {
            temOrderVO.setFulfillmentWarnings(JSONKit.parseObject(fulfillmentWarning, new TypeReference<List<String>>() {
            }));
        }
    }

    public R<List<TemOrderItemVO>> listItemByOrderId(Long orderId) {
        TemOrderAggRoot temOrderAggRoot = temOrderRepository.getById(orderId);
        if (Objects.isNull(temOrderAggRoot)) {
            throw new BusinessException("DATA_NOT_EXITS");
        }
        return R.success(
                temOrderAggRoot.getTemOrderItemList().stream().map(temOrderItem -> {
                    return TemOrderItemAppConvertor.toVO(temOrderItem);
                }).collect(Collectors.toList())
        );
    }

    @Override
    public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
        List<MonitorPlatformOrderDto> monitorPlatformOrderDtos = temOrderMapper.selectList(Wrappers.<TemOrderPO>lambdaQuery()
                        .select(TemOrderPO::getParentOrderSn, TemOrderPO::getLastMpdsSyncOrderTime, TemOrderPO::getStoreId)
                        .ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, TemOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
                        .le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, TemOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
                .stream()
                .map(x -> monitorPlatformOrderConvertor.temuOrderToDto(x))
                .collect(Collectors.toList());
        return monitorPlatformOrderDtos;
    }
}