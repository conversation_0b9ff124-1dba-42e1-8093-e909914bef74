package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.renpho.erp.oms.application.ordershipaudit.converter.ShipAuditConvertor;
import com.renpho.erp.oms.domain.ordershipaudit.CreateWmsOutboundStatus;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.renpho.erp.apiproxy.temu.model.ShopAccount;
import com.renpho.erp.apiproxy.temu.model.TemuResponse;
import com.renpho.erp.apiproxy.temu.model.fulfillment.*;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.service.UploadPdfService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentStatus;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderFulfillment;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import com.renpho.erp.oms.infrastructure.feign.proxy.TemuClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceWarehousePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.LogisticsShipmentPO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.LogisticsShipmentPOService;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: Temu物流下单
 * @time: 2025-04-03 14:36:22
 * @author: Alina
 */

@Slf4j
@Component
@RequiredArgsConstructor
public class TemuLogisticsStrategy extends AbstractPlatformLogisticsStrategy {

	private final TemuClient temuClient;
	private final UploadPdfService uploadPdfService;
	private final LogisticsShipmentPOService logisticsShipmentPOService;
	private final ShipAuditConvertor shipAuditConvertor;

	/**
	 * 返回发货服务类型
	 *
	 * @return Set<FulfillmentServiceType> Temu线上线上面单(自建仓)
	 */
	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceType() {
		return Set.of(FulfillmentServiceType.TEMU_ONLINE_LABEL);
	}

	/**
	 * 物流下单
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	@Override
	protected void createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			StoreAuthorizationVo storeAuthorizationVo, LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		// 是否存在物流下单记录
		LogisticsShipmentPO lastLogisticsShipmentPo = logisticsShipmentPOService
			.getLastLogisticsShipmentByOrderId(shipAuditAggRoot.getOrderId());
		if (ObjectUtils.isEmpty(lastLogisticsShipmentPo) || StringUtils.isEmpty(lastLogisticsShipmentPo.getLogisticsOrderNo())) {
			// 不存在物流下单记录，创建物流下单记录
			createFirstLogisticsShipment(shipAuditAggRoot, saleOrderAggRoot, storeAuthorizationVo, logisticsShipmentAggRoot);
		}
		else if (LogisticsShipmentStatus.FAILED.getValue().equals(lastLogisticsShipmentPo.getStatus())) {
			// 物流下单失败，重新创建物流下单记录
			recreateLogisticsOrderShipment(shipAuditAggRoot, saleOrderAggRoot, storeAuthorizationVo, logisticsShipmentAggRoot,
					lastLogisticsShipmentPo);
		}
		else {
			// 物流下单成功，无需再次创建, 保存上次的物流信息
			saveToNewLogisticsShipment(shipAuditAggRoot, saleOrderAggRoot, logisticsShipmentAggRoot, lastLogisticsShipmentPo);
		}
	}

	/**
	 * 保存上次的物流信息到新物流记录
	 * @param lastLogisticsShipmentPo 上次的物流信息
	 */
	private void saveToNewLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			LogisticsShipmentAggRoot logisticsShipmentAggRoot, LogisticsShipmentPO lastLogisticsShipmentPo) {
		// 物流单号
		saleOrderAggRoot.updateLogisticsOrderNo(lastLogisticsShipmentPo.getLogisticsOrderNo());
		logisticsShipmentAggRoot.updateFromLastShipment(shipAuditConvertor.toLogisticsShipment(lastLogisticsShipmentPo));
		// 物流下单成功，设置发货审核为创建状态
		shipAuditAggRoot.createSuccess(null);
		// 修改发货审核为待创建WMS出库单
		shipAuditAggRoot.updateCreateWmsOutboundStatus(CreateWmsOutboundStatus.WAIT_CREATE);
	}

	/**
	 * TEMU 第一次物流下单
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	private void createFirstLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			StoreAuthorizationVo storeAuthorizationVo, LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		// warehouseID 映射
		FulfillmentServiceWarehousePO fulfillmentWarehouse = fulfillmentWarehouseService.getByWarehouse(shipAuditAggRoot.getWarehouseId(),
				shipAuditAggRoot.getFulfillmentServiceType().getValue());
		ShopAccount shopAccount = this.getShopAccount(storeAuthorizationVo);
		CreateShipmentRequest request = this.buildCreateShipmentRequest(shipAuditAggRoot, saleOrderAggRoot, fulfillmentWarehouse);
		TemuResponse<CreateShipmentData> response = temuClient.createShipment(shopAccount, request);
		if (!CollectionUtils.isEmpty(response.getResult().getPackageSnList())) {
			String logisticsOrderNo = response.getResult().getPackageSnList().get(0);
			saleOrderAggRoot.updateLogisticsOrderNo(logisticsOrderNo);
			logisticsShipmentAggRoot.updateLogisticsOrderNo(logisticsOrderNo);
			// 物流下单成功，设置发货审核为创建状态
			shipAuditAggRoot.createSuccess(null);
		}
		else {
			shipAuditAggRoot.createFail("temu返回的物流单号为空");
		}
	}

	/**
	 * 构建物流下单参数
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param fulfillmentWarehouse warehouseID 映射
	 * @return CreateShipmentRequest
	 */
	private CreateShipmentRequest buildCreateShipmentRequest(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			FulfillmentServiceWarehousePO fulfillmentWarehouse) {
		CreateShipmentRequest request = new CreateShipmentRequest();
		// 0：一个订单中的所有产品都装在一个包裹中，使用一个跟踪号
		request.setSendType(0);

		SaleOrderFulfillment fulfillment = saleOrderAggRoot.getFulfillment();

		// 创建包裹信息
		CreateShipmentRequest.Package shipPackage = new CreateShipmentRequest.Package();
		shipPackage.setShipCompanyId(shipAuditAggRoot.getShipCompanyId());
		shipPackage.setChannelId(shipAuditAggRoot.getShipChannelId());
		shipPackage.setWarehouseId(fulfillmentWarehouse.getPlatformWarehouseId());
		shipPackage.setLength(SizeUtil.parseSizeToIn(fulfillment.getSize(), 0));
		shipPackage.setWidth(SizeUtil.parseSizeToIn(fulfillment.getSize(), 1));
		shipPackage.setHeight(SizeUtil.parseSizeToIn(fulfillment.getSize(), 2));
		shipPackage.setWeight(SizeUtil.formatToLbUnit(fulfillment.getWeight()));
		shipPackage.setDimensionUnit(Constants.TEMU_DIMENSION_UNIT);
		shipPackage.setWeightUnit(Constants.TEMU_WEIGHT_UNIT);

		// 商品行信息
		List<OrderSendInfo> orderSendInfoList = new ArrayList<>();
		Set<String> orderSnSet = new HashSet<>();
		saleOrderAggRoot.getItems().forEach(e -> {
			String orderSn = e.getChannelOrderLineId();
			if (orderSnSet.add(orderSn)) {
				OrderSendInfo orderSendInfo = new OrderSendInfo();
				orderSendInfo.setOrderSn(orderSn);
				orderSendInfo.setParentOrderSn(saleOrderAggRoot.getChannelOrderNo());
				orderSendInfo.setQuantity(e.getQuantityShipment());
				orderSendInfoList.add(orderSendInfo);
			}
		});
		shipPackage.setOrderSendInfoList(orderSendInfoList);

		request.setSendRequestList(List.of(shipPackage));
		return request;
	}

	/**
	 * 获取店铺授权信息
	 *
	 * @param storeAuthorizationVo 授权信息
	 * @return ShopAccount
	 */
	private ShopAccount getShopAccount(StoreAuthorizationVo storeAuthorizationVo) {
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setShopId(storeAuthorizationVo.getAuthorization().getTemuShopId());
		shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());
		return shopAccount;
	}

	/**
	 * 获取并处理物流下单结果
	 *
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	@Override
	protected void processLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot, StoreAuthorizationVo storeAuthorizationVo) {
		ShopAccount shopAccount = getShopAccount(storeAuthorizationVo);
		// 创建请求对象，用于查询物流下单结果
		GetShipmentResultRequest getShipmentResultRequest = new GetShipmentResultRequest();
		getShipmentResultRequest.setPackageSnList(List.of(logisticsShipmentAggRoot.getLogisticsOrderNo()));
		// 调用远程API获取物流单的状态信息
		TemuResponse<GetShipmentResultData> response = null;
		try {
			response = temuClient.getShipmentResult(shopAccount, getShipmentResultRequest);
		}
		catch (Exception e) {
			log.error("temu获取物流单失败", e);
		}
		if (response == null || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getPackageInfoResultList())) {
			logisticsShipmentAggRoot.markAsPending();
			// 无返回结果直接等待下次定时任务继续查询
			return;
		}
		GetShipmentResultData.PackageInfo packageInfo = response.getResult().getPackageInfoResultList().get(0);
		handlePackageStatus(shopAccount, packageInfo, logisticsShipmentAggRoot);
	}

	/**
	 * 处理包裹状态
	 */
	private void handlePackageStatus(ShopAccount shopAccount, GetShipmentResultData.PackageInfo packageInfo,
			LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		switch (packageInfo.getShippingLabelStatus()) {
			case 0:
				// 待处理状态，不做任何操作，等待下次定时任务查询
				logisticsShipmentAggRoot.markAsPending();
				break;
			case 1:
				handleSuccessStatus(shopAccount, logisticsShipmentAggRoot, packageInfo);
				break;
			default:
				handleFailedStatus(logisticsShipmentAggRoot, packageInfo.getFailReasonText());
				break;
		}
	}

	/**
	 * 处理成功状态
	 */
	private void handleSuccessStatus(ShopAccount shopAccount, LogisticsShipmentAggRoot root,
			GetShipmentResultData.PackageInfo packageInfo) {
		// 更新物流单号
		root.updateTrackNo(packageInfo.getTrackingNumber());
		GetShipmentDocumentRequest getShipmentDocumentRequest = new GetShipmentDocumentRequest();
		getShipmentDocumentRequest.setPackageSnList(List.of(root.getLogisticsOrderNo()));
		getShipmentDocumentRequest.setDocumentType("SHIPPING_LABEL_PDF");
		// 调用获取面单地址
		TemuResponse<GetShipmentDocumentData> response = temuClient.getShipmentDocument(shopAccount, getShipmentDocumentRequest);
		// 调用【下载面单】，获得PDF文件，保存此文件
		if (ObjectUtils.isNotEmpty(response) && ObjectUtils.isNotEmpty(response.getResult())
				&& !CollectionUtils.isEmpty(response.getResult().getShippingLabelUrlList())) {
			GetShipmentDocumentData.ShippingLabelUrl shippingLabelUrl = response.getResult().getShippingLabelUrlList().get(0);
			// 根据获取到的文件url 查询文件信息
			DownloadShipmentDocumentRequest request = new DownloadShipmentDocumentRequest();
			request.setUrl(shippingLabelUrl.getUrl());
			TemuResponse<Byte[]> rs = temuClient.downShipmentDocument(shopAccount, request);
			String waybillFileUrl = uploadPdfService.uploadPdfFromBytes(rs.getResult(), shippingLabelUrl.getUrl());
			root.markSuccess(waybillFileUrl, shippingLabelUrl.getUrl());
		}
		else {
			log.info("TEMU 获取面单信息为空{}", root.getLogisticsOrderNo());
		}
	}

	/**
	 * 处理失败状态
	 */
	private void handleFailedStatus(LogisticsShipmentAggRoot root, String failureReason) {
		root.markAsFailed(failureReason);
	}

	/**
	 * 重新创建物流下单
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param storeAuthorizationVo 店铺授权信息
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 */
	private void recreateLogisticsOrderShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			StoreAuthorizationVo storeAuthorizationVo, LogisticsShipmentAggRoot logisticsShipmentAggRoot,
			LogisticsShipmentPO lastLogisticsShipmentPo) {
		UpdateShipmentRequest updateShipmentRequest = buildUpdateShipmentRequest(shipAuditAggRoot, saleOrderAggRoot,
				lastLogisticsShipmentPo);
		ShopAccount shopAccount = this.getShopAccount(storeAuthorizationVo);
		TemuResponse<Boolean> response = temuClient.updateShipment(shopAccount, updateShipmentRequest);
		if (response.getSuccess() && response.getResult()) {
			saleOrderAggRoot.updateLogisticsOrderNo(lastLogisticsShipmentPo.getLogisticsOrderNo());
			logisticsShipmentAggRoot.updateLogisticsOrderNo(lastLogisticsShipmentPo.getLogisticsOrderNo());
			// 物流下单成功，设置发货审核为创建状态
			shipAuditAggRoot.createSuccess(null);
		}
		else {
			shipAuditAggRoot.createFail("重新物流下单失败:" + response.getErrorMsg());
		}
	}

	/**
	 * 构建更新请求
	 *
	 * @param shipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param lastLogisticsShipmentPo 上次物流下单信息
	 * @return UpdateShipmentRequest
	 */
	private UpdateShipmentRequest buildUpdateShipmentRequest(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			LogisticsShipmentPO lastLogisticsShipmentPo) {
		UpdateShipmentRequest request = new UpdateShipmentRequest();
		// warehouseID 映射
		FulfillmentServiceWarehousePO fulfillmentWarehouse = fulfillmentWarehouseService.getByWarehouse(shipAuditAggRoot.getWarehouseId(),
				shipAuditAggRoot.getFulfillmentServiceType().getValue());
		SaleOrderFulfillment fulfillment = saleOrderAggRoot.getFulfillment();

		// 创建包裹信息
		UpdateShipmentRequest.Package shipPackage = new UpdateShipmentRequest.Package();
		shipPackage.setShipCompanyId(shipAuditAggRoot.getShipCompanyId());
		shipPackage.setChannelId(shipAuditAggRoot.getShipChannelId());
		shipPackage.setWarehouseId(fulfillmentWarehouse.getPlatformWarehouseId());
		shipPackage.setLength(SizeUtil.parseSizeToIn(fulfillment.getSize(), 0));
		shipPackage.setWidth(SizeUtil.parseSizeToIn(fulfillment.getSize(), 1));
		shipPackage.setHeight(SizeUtil.parseSizeToIn(fulfillment.getSize(), 2));
		shipPackage.setWeight(SizeUtil.formatToLbUnit(fulfillment.getWeight()));
		shipPackage.setDimensionUnit(Constants.TEMU_DIMENSION_UNIT);
		shipPackage.setWeightUnit(Constants.TEMU_WEIGHT_UNIT);

		// 商品行信息
		List<OrderSendInfo> orderSendInfoList = new ArrayList<>();
		Set<String> orderSnSet = new HashSet<>();
		saleOrderAggRoot.getItems().forEach(e -> {
			String orderSn = e.getChannelOrderLineId();
			if (orderSnSet.add(orderSn)) {
				OrderSendInfo orderSendInfo = new OrderSendInfo();
				orderSendInfo.setOrderSn(orderSn);
				orderSendInfo.setParentOrderSn(saleOrderAggRoot.getChannelOrderNo());
				orderSendInfo.setQuantity(e.getQuantityShipment());
				orderSendInfoList.add(orderSendInfo);
			}
		});
		shipPackage.setOrderSendInfoList(orderSendInfoList);
		shipPackage.setPackageSn(lastLogisticsShipmentPo.getLogisticsOrderNo());
		request.setRetrySendPackageRequestList(List.of(shipPackage));
		return request;
	}

}
