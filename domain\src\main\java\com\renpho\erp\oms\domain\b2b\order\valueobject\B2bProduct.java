package com.renpho.erp.oms.domain.b2b.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class B2bProduct {

	/**
	 * 采购SKU
	 */
	private String psku;

	/**
	 * 图片ID，来自PDS，用于换取临时访问地址
	 */
	private String imageId;

	/**
	 * 装箱数量（每箱的psku数量）
	 */
	private Integer numberOfUnitsPerBox;

	/**
	 * 采购SKU-中文名称
	 */
	private String pskuNameZh;

	/**
	 * 采购SKU-英文名称
	 */
	private String pskuNameEn;
}
