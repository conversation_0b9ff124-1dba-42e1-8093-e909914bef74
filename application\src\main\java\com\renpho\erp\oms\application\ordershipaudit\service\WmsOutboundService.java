package com.renpho.erp.oms.application.ordershipaudit.service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.renpho.erp.oms.infrastructure.persistence.carriermapping.service.CarrierMappingService;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.apiproxy.wms.model.outbound.CreateOutBoundSaleOrderReq;
import com.renpho.erp.apiproxy.wms.model.outbound.CreateOutBoundSaleOrderRsp;
import com.renpho.erp.apiproxy.wms.model.outbound.QueryOutBoudSaleOrderRsp;
import com.renpho.erp.apiproxy.wms.model.outbound.SaleOutBoundStatus;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditInventoryService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.strategy.intercept.AbstractSelfWarehouseOrderInterceptStrategy;
import com.renpho.erp.oms.application.salemanagement.strategy.intercept.OrderInterceptFactory;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.application.wms.WmsTokenService;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.ordershipaudit.CreateWmsOutboundStatus;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditItem;
import com.renpho.erp.oms.domain.ordershipaudit.ShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.repository.LogisticsShipmentRepository;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.domain.salemanagement.*;
import com.renpho.erp.oms.infrastructure.common.exception.BizErrorCode;
import com.renpho.erp.oms.infrastructure.common.exception.DataInsufficiencyException;
import com.renpho.erp.oms.infrastructure.common.util.BatchUtils;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.config.WmsOutboundConfig;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.oms.infrastructure.feign.wms.WmsClient;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.R;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

/**
 * 创建wms出库单-应用服务
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class WmsOutboundService {

	private final OrderShipAuditRepository orderShipAuditRepository;
	private final SaleOrderRepository saleOrderRepository;
	private final LogisticsShipmentRepository logisticsShipmentRepository;
	private final SaleOrderQueryService saleOrderQueryService;
	private final WmsClient wmsClient;
	private final UserClient userClient;
	private final WmsOutboundConfig wmsOutboundConfig;
	private final OrderShipAuditInventoryService orderShipAuditInventoryService;
	private final WmsTokenService wmsTokenService;
	private final TransactionTemplate transactionTemplate;
	private final CarrierMappingService carrierMappingService;

	/**
	 * 分批创建wms出库单
	 * @param param 参数
	 */
	public void batchCreateWmsOutbound(WmsOutboundCreateParam param) {
		// 分批的条数
		Integer size = Optional.ofNullable(param.getSize()).orElse(1000);
		BatchUtils.batchDeal(
				// 根据 lastId 和 size 获取列表
				(Long lastId, Integer pageSize) -> {
					// 待创建，创建结果未知，待重试
					Set<Integer> createWmsOutboundStatus = Set.of(CreateWmsOutboundStatus.WAIT_CREATE.getValue(),
							CreateWmsOutboundStatus.CREATE_UNKNOWN.getValue());
					// 自建仓履约服务类型
					Set<Integer> selfWarehouseFulfillmentType = FulfillmentServiceType.getSelfWarehouseFulfillmentType();
					// 分批查询
					return orderShipAuditRepository.findIdsByCreateWmsOutboundStatus(ShipmentStatus.CREATE.getValue(),
							createWmsOutboundStatus, selfWarehouseFulfillmentType, param.getAuditNos(), lastId, size);
				},
				// 对获取到的这一批列表进行处理
				this::createWmsOutbound,
				// 恒等函数
				Function.identity(),
				// 批处理大小
				size);
	}

	/**
	 * 同步wms出库单
	 * @param orderShipAuditAggRoot 发货审核根
	 * @param saleOrderAggRoot 销售单根
	 */
	public void syncWmsOutbound(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot) {
		// 出库单号
		String outBoundNo = Optional.ofNullable(saleOrderAggRoot.getFulfillment()).map(SaleOrderFulfillment::getOutboundNo).orElse(null);
		// wms仓库编码
		String thirdPartWarehouseCode = orderShipAuditAggRoot.getThirdPartWarehouseCode();

		String token = wmsTokenService.getToken(thirdPartWarehouseCode);
		R<QueryOutBoudSaleOrderRsp> r;
		try {
			r = wmsClient.queryOutbound(token, outBoundNo);
		}
		catch (Exception e) {
			log.error("查询wms出库单发生异常,发货审核id是{},订单id是{},outBoundNo={}", orderShipAuditAggRoot.getId(), saleOrderAggRoot.getId().getId(),
					outBoundNo, e);
			throw e;
		}
		if (!r.isSuccess()) {
			log.info("查询wms出库单响应错误,发货审核id是{},订单id是{},outBoundNo={},错误信息是{}", orderShipAuditAggRoot.getId(),
					saleOrderAggRoot.getId().getId(), outBoundNo, r.getMessage());
			return;
		}
		QueryOutBoudSaleOrderRsp queryOutBoudSaleOrderRsp = r.getData();
		SaleOutBoundStatus saleOutBoundStatus = SaleOutBoundStatus.of(queryOutBoudSaleOrderRsp.getStatus());

		// 取消状态
		if (Objects.equals(SaleOutBoundStatus.CANCELLED, saleOutBoundStatus)) {
			// 取消面单
			this.cancelShippingLabel(saleOrderAggRoot, orderShipAuditAggRoot);
			// 库存解锁
			orderShipAuditInventoryService.unlockInventory(orderShipAuditAggRoot);
			// 订单发货异常、发货审核失败（事务）
			SpringUtil.getBean(this.getClass()).syncOutboundFail(saleOrderAggRoot, orderShipAuditAggRoot);
			return;
		}
		// 出库
		if (SaleOutBoundStatus.OUT.equals(saleOutBoundStatus)) {
			LocalDateTime outBoundTime = null;
			if (StrUtil.isNotBlank(queryOutBoudSaleOrderRsp.getOutBoundTimeStr())) {
				outBoundTime = LocalDateTime.parse(queryOutBoudSaleOrderRsp.getOutBoundTimeStr(), DatePattern.NORM_DATETIME_FORMATTER);
			}
			saleOrderAggRoot.selfShip(outBoundTime);
			orderShipAuditAggRoot.selfShip();

			transactionTemplate.executeWithoutResult(status -> {
				saleOrderRepository.selfShip(saleOrderAggRoot);
				orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
			});
		}
	}

	/**
	 * 创建wms出库单
	 * @param auditIds 发货审核id集合
	 */
	private void createWmsOutbound(List<Long> auditIds) {
		// 承运商映射Map，key为承运商编码，value为wms的承运商id
		Map<String, Integer> carrierMap = getCarrierMap();
		// 创建wms出库单
		auditIds.forEach(auditId -> {
			try {
				SpringUtil.getBean(this.getClass()).createWmsOutbound(auditId, carrierMap);
			}
			catch (Exception e) {
				log.error("创建wms出库单异常：", e);
			}
		});
	}

	/**
	 * 创建wms出库单
	 * @param auditId 发货审核id
	 * @param carrierMap 承运商映射Map，key为承运商编码，value为wms的承运商id
	 */
	@Lock4j(name = "sale:order:shipAudit:outboundOperation", keys = "#auditId", acquireTimeout = 3L)
	public void createWmsOutbound(Long auditId, Map<String, Integer> carrierMap) {
		// 发货审核聚合根
		OrderShipAuditAggRoot auditAggRoot = Optional.ofNullable(orderShipAuditRepository.getById(auditId))
			.orElseThrow(() -> new DataInsufficiencyException(BizErrorCode.DATA_INSUFFICIENCY, auditId.toString()));
		if (auditAggRoot.isNoNeedCreateWmsOutbound()) {
			log.info("发货审核id：{}，无需创建wms出库单", auditId);
			return;
		}
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = saleOrderRepository.findById(SaleOrderAggRoot.OrderId.of(auditAggRoot.getOrderId()))
			.orElseThrow(() -> new DataInsufficiencyException(BizErrorCode.DATA_INSUFFICIENCY, auditAggRoot.getOrderId().toString()));
		// 物流下单聚合根
		LogisticsShipmentAggRoot logisticsShipmentAggRoot = logisticsShipmentRepository.findByAuditIdAndStatus(auditAggRoot.getId(),
				LogisticsShipmentStatus.DOWNLOAD_WAYBILL_FILE_SUCCESS.getValue());
		// 查询数据并构建出库单请求
		CreateOutBoundSaleOrderReq req = this.findDataAndBuildReq(orderAggRoot, logisticsShipmentAggRoot, carrierMap, auditAggRoot);
		// 请求wms创建出库单
		R<CreateOutBoundSaleOrderRsp> r = this.createWmsOutbound(req, auditAggRoot);
		// 创建结果未知，待下次重试
		if (Objects.isNull(r)) {
			return;
		}
		// 处理创建出库单结果
		this.handleCreateOutboundR(r, auditAggRoot, orderAggRoot, logisticsShipmentAggRoot);
	}

	/**
	 * 处理创建出库单结果
	 * @param r 结果
	 * @param auditAggRoot 发货审核
	 * @param orderAggRoot 订单聚合根
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 */
	private void handleCreateOutboundR(R<CreateOutBoundSaleOrderRsp> r, OrderShipAuditAggRoot auditAggRoot, SaleOrderAggRoot orderAggRoot,
			LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		CreateOutBoundSaleOrderRsp data = r.getData();
		// 处理创建结果
		boolean success = this.handleCreateR(r, data, auditAggRoot);
		// 更新创建出库单结果（事务）
		SpringUtil.getBean(this.getClass()).updateCreateOutboundR(auditAggRoot, orderAggRoot, logisticsShipmentAggRoot, success, data);
		// 创建失败
		if (!success) {
			// 取消面单
			this.cancelShippingLabel(orderAggRoot, auditAggRoot);
			// 库存解锁
			orderShipAuditInventoryService.unlockInventory(auditAggRoot);
		}
	}

	/**
	 * 更新创建出库单结果（事务）
	 * @param auditAggRoot 发货审核聚合根
	 * @param orderAggRoot 订单聚合根
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param success 是否创建成功
	 * @param data 数据
	 */
	@Transactional(rollbackFor = Exception.class)
	public void updateCreateOutboundR(OrderShipAuditAggRoot auditAggRoot, SaleOrderAggRoot orderAggRoot,
			LogisticsShipmentAggRoot logisticsShipmentAggRoot, boolean success, CreateOutBoundSaleOrderRsp data) {
		// 更新创建wms出库单状态
		orderShipAuditRepository.updateCreateWmsOutboundStatus(auditAggRoot);
		// 创建成功
		if (success) {
			String standardCarrier = carrierMappingService.getCarrier(orderAggRoot.getChannelType(), orderAggRoot.getFulfillment().getFulfillmentServiceType(), auditAggRoot.getCarrierCode());
			standardCarrier = StrUtil.blankToDefault(standardCarrier, auditAggRoot.getCarrierCode());
			// 填充仓库单号，跟踪号，承运商，上传跟踪号状态：待上传
			orderAggRoot.fillOutboundNoAndTrackingNo(data.getOrderNo(), logisticsShipmentAggRoot.getTrackingNo(),
					auditAggRoot.getCarrierCode(), standardCarrier, UploadTrackStatus.WAITING_UPLOAD);
			// 更新
			saleOrderRepository.updateOutboundNoAndTrackingNo(orderAggRoot);
		}
		// 创建失败，重新走发货审核流程
		else {
			// 订单发货异常、发货审核失败
			SpringUtil.getBean(this.getClass()).createOutboundFail(orderAggRoot, auditAggRoot);
		}
	}

	/**
	 * 请求wms创建出库单
	 * @param req 请求
	 * @param auditAggRoot 发货审核聚合根
	 * @return R
	 */
	private @Nullable R<CreateOutBoundSaleOrderRsp> createWmsOutbound(CreateOutBoundSaleOrderReq req, OrderShipAuditAggRoot auditAggRoot) {
		String token = wmsTokenService.getToken(auditAggRoot.getThirdPartWarehouseCode());
		R<CreateOutBoundSaleOrderRsp> r;
		try {
			r = wmsClient.createOutbound(token, req);
		}
		catch (Exception e) {
			log.error("请求wms创建出库单异常：", e);
			// 创建结果未知,，待下次重试
			auditAggRoot.createWmsOutboundUnknown(e.getMessage());
			orderShipAuditRepository.updateCreateWmsOutboundStatus(auditAggRoot);
			return null;
		}
		return r;
	}

	/**
	 * 订单发货异常、发货审核失败
	 * @param orderAggRoot 订单聚合根
	 * @param auditAggRoot 发货审核聚合根
	 */
	@Transactional(rollbackFor = Exception.class)
	public void createOutboundFail(SaleOrderAggRoot orderAggRoot, OrderShipAuditAggRoot auditAggRoot) {
		// 订单发货异常
		orderAggRoot.markShipException("创建wms出库单失败，" + auditAggRoot.getCreateWmsOutboundFailMsg());
		saleOrderRepository.updateShipException(orderAggRoot);
		// 清空物流单号
		saleOrderRepository.clearLogisticsOrderNo(orderAggRoot);
		// 发货审核失败
		auditAggRoot.createFail("创建wms出库单失败，" + auditAggRoot.getCreateWmsOutboundFailMsg());
		orderShipAuditRepository.updateStatus(auditAggRoot);
	}

	/**
	 * 订单发货异常、发货审核失败
	 * @param orderAggRoot 订单聚合根
	 * @param auditAggRoot 发货审核聚合根
	 */
	@Transactional(rollbackFor = Exception.class)
	public void syncOutboundFail(SaleOrderAggRoot orderAggRoot, OrderShipAuditAggRoot auditAggRoot) {
		// 订单发货异常
		orderAggRoot.markShipException("wms出库单已被取消");
		saleOrderRepository.updateShipException(orderAggRoot);
		// 清空物流单号和出库单号
		saleOrderRepository.clearLogisticsOrderNoAndOutboundNo(orderAggRoot);
		// 发货审核失败
		auditAggRoot.cancel("wms出库单已被取消");
		orderShipAuditRepository.updateStatus(auditAggRoot);
	}

	/**
	 * 取消面单
	 * @param orderAggRoot 订单聚合根
	 * @param auditAggRoot 发货审核聚合根
	 */
	private void cancelShippingLabel(SaleOrderAggRoot orderAggRoot, OrderShipAuditAggRoot auditAggRoot) {
		// 获取面单取消策略（自建仓）
		AbstractSelfWarehouseOrderInterceptStrategy strategy = (AbstractSelfWarehouseOrderInterceptStrategy) OrderInterceptFactory
			.get(auditAggRoot.getFulfillmentServiceType());
		// 取消面单
		try {
			strategy.cancelShippingLabel(orderAggRoot);
		}
		catch (Exception e) {
			log.error("取消面单异常：", e);
		}
	}

	/**
	 * 查询数据并构建出库单请求
	 * @param orderAggRoot 订单聚合根
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param carrierMap 承运商映射Map，key为承运商编码，value为wms的承运商id
	 * @param auditAggRoot 发货审核聚合根
	 * @return CreateOutBoundSaleOrderReq
	 */
	private @NotNull CreateOutBoundSaleOrderReq findDataAndBuildReq(SaleOrderAggRoot orderAggRoot,
			LogisticsShipmentAggRoot logisticsShipmentAggRoot, Map<String, Integer> carrierMap, OrderShipAuditAggRoot auditAggRoot) {
		// 订单收货地址
		SaleOrderAddressVO receivingAddress = null;
		// 非Temu线上面单（获取Temu面单后，订单收货地址查不到）
		if (!FulfillmentServiceType.TEMU_ONLINE_LABEL.equals(auditAggRoot.getFulfillmentServiceType())) {
			// 查询敏感地址信息（从mpds）
			receivingAddress = saleOrderQueryService.getReceivingAddress(orderAggRoot.getOrderNo(), false, false);
		}
		// 用户信息
		OumUserInfoRes user = userClient.getUserById(auditAggRoot.getCreateBy());
		// 构建创建出库单请求
		return this.buildCreateOutBoundReq(carrierMap, auditAggRoot, orderAggRoot, user, logisticsShipmentAggRoot, receivingAddress);
	}

	/**
	 * 处理创建结果
	 * @param r 结果
	 * @param data 结果数据
	 * @param auditAggRoot 发货审核聚合根
	 * @return boolean
	 */
	private boolean handleCreateR(R<CreateOutBoundSaleOrderRsp> r, CreateOutBoundSaleOrderRsp data, OrderShipAuditAggRoot auditAggRoot) {
		// success为false 并且 非重复创建状态码
		if (!r.isSuccess() && !"10-501-100001".equals(r.getCode())) {
			auditAggRoot.createWmsOutboundFail("返回的success为false（状态码不是重复创建），失败原因：" + r.getMessage());
			return false;
		}
		if (Objects.isNull(data)) {
			auditAggRoot.createWmsOutboundFail("返回的data为空");
			return false;
		}
		if (Objects.isNull(data.getOrderNo())) {
			auditAggRoot.createWmsOutboundFail("返回的出库单号orderNo为空");
			return false;
		}
		// 创建成功（重复创建，也认为创建成功）
		auditAggRoot.createWmsOutboundSuccess();
		return true;
	}

	/**
	 * 构建创建出库单请求
	 * @param carrierMap 承运商映射Map，key为承运商编码，value为wms的承运商id
	 * @param auditAggRoot 发货审核聚合根
	 * @param orderAggRoot 订单聚合根
	 * @param user 发货审核创建人
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 * @param receivingAddress 订单收货地址
	 * @return CreateOutBoundSaleOrderReq
	 */
	private CreateOutBoundSaleOrderReq buildCreateOutBoundReq(Map<String, Integer> carrierMap, OrderShipAuditAggRoot auditAggRoot,
			SaleOrderAggRoot orderAggRoot, OumUserInfoRes user, LogisticsShipmentAggRoot logisticsShipmentAggRoot,
			SaleOrderAddressVO receivingAddress) {
		CreateOutBoundSaleOrderReq req = new CreateOutBoundSaleOrderReq();
		// 数据源 1-新ERP
		req.setDataSource(1);
		// 发货审核单号
		req.setSoNo(auditAggRoot.getAuditNo());
		// 店铺单号
		req.setShopOrder(orderAggRoot.getChannelOrderNo());
		// 店铺信息
		Optional<Store> store = Optional.ofNullable(orderAggRoot.getStore());
		// 销售平台编码：传销售渠道全称
		req.setSalePlatformName(store.map(Store::getChannelName).orElseThrow(() -> DomainException.of("销售渠道名称不能为空")));
		// 销售负责人:发货审核的人名字
		req.setSalesPic(user.getName());
		// erp货主ID
		req.setErpOwnerId(store.map(Store::getCompanyId).orElseThrow(() -> DomainException.of("货主ID不能为空")));
		// 承运商编码（大写）
		String carrierCode = Optional.ofNullable(auditAggRoot.getCarrierCode())
			.map(String::toUpperCase)
			.orElseThrow(() -> DomainException.of("承运商编码不能为空"));
		// wms的承运商id
		req.setLogisticsChannelId(Optional.ofNullable(carrierMap.get(carrierCode))
			.orElseThrow(() -> DomainException.of(String.format("承运商映射配置-承运商编码：【%s】不存在", carrierCode))));
		// 跟踪号
		req.setTrackingNo(logisticsShipmentAggRoot.getTrackingNo());
		// 面单打印文件(下载链接)
		req.setPrintFileUrl(logisticsShipmentAggRoot.getWaybillFileUrl());
		// 最晚签收时间(最迟送达时间)
		req.setLastDelivery(Optional.ofNullable(orderAggRoot.getFulfillment())
			.map(SaleOrderFulfillment::getLastDeliveryTime)
			.map(DateUtil::convertToStr)
			.orElse(null));
		// 商品明细
		req.setDetails(this.buildDetails(auditAggRoot.getItems()));
		// 收货信息
		Optional.ofNullable(receivingAddress).ifPresent(r -> req.setReceiveInfo(this.buildReceiveInfo(r)));
		return req;
	}

	/**
	 * 构建收货信息
	 * @param orderAddress 订单地址
	 * @return ReceiveInfoDTO
	 */
	private CreateOutBoundSaleOrderReq.ReceiveInfoDTO buildReceiveInfo(SaleOrderAddressVO orderAddress) {
		// 收货信息
		CreateOutBoundSaleOrderReq.ReceiveInfoDTO receiveInfo = new CreateOutBoundSaleOrderReq.ReceiveInfoDTO();
		// 收件人姓名
		receiveInfo.setRecipientName(orderAddress.getRecipientName());
		// 电话
		receiveInfo.setRecipientPhone(orderAddress.getRecipientPhone());
		// 国家
		receiveInfo.setReceivingCountryRegion(orderAddress.getCountryCode());
		// 州
		receiveInfo.setState(orderAddress.getProvince());
		// 城市
		receiveInfo.setCity(orderAddress.getCity());
		// 邮编
		receiveInfo.setZip(orderAddress.getPostalCode());
		// 地址1
		receiveInfo.setAddress1(orderAddress.getAddress1());
		// 地址2
		receiveInfo.setAddress2(orderAddress.getAddress2());
		return receiveInfo;
	}

	/**
	 * 构建商品明细
	 * @param items 商品明细
	 * @return List
	 */
	private List<CreateOutBoundSaleOrderReq.DetailsDTO> buildDetails(List<OrderShipAuditItem> items) {
		if (CollectionUtils.isEmpty(items)) {
			throw DomainException.of("商品明细为空");
		}
		return items.stream().map(item -> {
			// 商品信息
			CreateOutBoundSaleOrderReq.DetailsDTO detailsDTO = new CreateOutBoundSaleOrderReq.DetailsDTO();
			// PSKU
			detailsDTO.setPsku(item.getPsku());
			// FNSKU
			detailsDTO.setBarcode(item.getFnSku());
			// 应发数量
			detailsDTO.setPlanQty(item.getQuantity());
			return detailsDTO;
		}).toList();
	}

	/**
	 * 获取承运商映射Map，key为承运商编码，value为wms的承运商id
	 * @return Map
	 */
	private Map<String, Integer> getCarrierMap() {
		// 承运商映射列表
		List<WmsOutboundConfig.CarrierMapping> carrierMappings = wmsOutboundConfig.getCarrierMappings();
		if (CollectionUtils.isEmpty(carrierMappings)) {
			throw DomainException.of("承运商映射列表未配置");
		}
		// 承运商映射Map，key为承运商编码，value为wms的承运商id
		return carrierMappings.stream()
			.collect(Collectors.toMap(
					mapping -> Optional.ofNullable(mapping.getCarrierCode())
						.map(String::toUpperCase)
						.orElseThrow(() -> DomainException.of("承运商映射配置-承运商编码不能为空")),
					WmsOutboundConfig.CarrierMapping::getWmsCarrierId, (x1, x2) -> x1));
	}

	@Getter
	@Setter
	public static class WmsOutboundCreateParam {

		/**
		 * 发货审核单号
		 */
		private Set<String> auditNos;

		/**
		 * 分批的条数
		 */
		private Integer size;
	}

}
