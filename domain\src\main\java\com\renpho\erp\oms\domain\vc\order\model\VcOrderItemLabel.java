package com.renpho.erp.oms.domain.vc.order.model;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单商品箱唛实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderItemLabel {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * vc订单行id
	 */
	private Long orderItemId;

	/**
	 * 物流标-文件url
	 */
	private String asinLabelUrl;

	/**
	 * 物流标-文件名
	 */
	private String asinLabelName;

	/**
	 * 外箱标-文件url
	 */
	private String cartonLabelUrl;

	/**
	 * 外箱标-文件名
	 */
	private String cartonLabelName;

}
