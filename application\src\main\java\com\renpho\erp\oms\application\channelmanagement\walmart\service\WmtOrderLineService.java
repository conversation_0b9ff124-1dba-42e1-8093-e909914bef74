package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineVO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLine;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderLineRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLineAppConvertor;

import java.util.List;

@Service
@AllArgsConstructor
public class WmtOrderLineService {
	private final WmtOrderLineRepository wmtOrderLineRepository;

	public R<List<WmtOrderLineVO>> listByOrderId(Long orderId) {
		List<WmtOrderLine> wmtOrderLineList = wmtOrderLineRepository.listByOrderId(orderId);
		return R.success(WmtOrderLineAppConvertor.toVOList(wmtOrderLineList));
	}
}
