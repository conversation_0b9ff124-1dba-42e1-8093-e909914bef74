package com.renpho.erp.oms.application.channelmanagement.mercado.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MclOrderVO {

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 美客多订单id
	 */
	private Long mercadoOrderId;

	/**
	 * 用户id
	 */
	private String mclUserId;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 美客多运单id
	 */
	private Long shippingId;

	/**
	 * 状态
	 */
	private String shipMentStatus;

	/**
	 * 物流模式
	 */
	private String logisticMode;

	/**
	 * 物流类型
	 */
	private String logisticType;

	/**
	 * 追踪号码
	 */
	private String trackingNumber;

	/**
	 * 追踪方式
	 */
	private String trackingMethod;

	/**
	 * 运费金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal cost;

	/**
	 * 运费定价
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal list_cost;

	/**
	 * 运费类型
	 */
	private String cost_type;

	/**
	 * 高
	 */
	private Integer height;

	/**
	 * 长
	 */
	private Integer length;

	/**
	 * 宽
	 */
	private Integer width;

	/**
	 * 重量
	 */
	private Integer weight;

	/**
	 * 区
	 */
	private String stateName;

	/**
	 * 国家
	 */
	private String countryName;

	/**
	 * 城市
	 */
	private String cityName;

	/**
	 * zip_code
	 */
	private String zipCode;

	/**
	 * 订单创建时间
	 */
	private LocalDateTime dateCreated;

	/**
	 * 订单关闭时间
	 */
	private LocalDateTime dateClosed;

	/**
	 * 订单最新更新时间
	 */
	private LocalDateTime lastUpdated;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;
}
