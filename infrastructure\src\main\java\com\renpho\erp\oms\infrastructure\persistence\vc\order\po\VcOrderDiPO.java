package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单DI表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order_di")
public class VcOrderDiPO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 付款条款
	 */
	private String paymentMethod;

	/**
	 * 贸易条款
	 */
	private String incoterms;

	/**
	 * 集装箱规格
	 */
	private String containerType;

	/**
	 * 发货描述
	 */
	private String shippingInstructions;

}
