package com.renpho.erp.oms.adapter.web.controller.skuMapping;

import java.util.List;

import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.PskuQueryCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.PurchaseSkuQueryService;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.B2bPurchaseSkuVO;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.PurchaseSkuVO;
import com.renpho.karma.dto.R;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 采购SKU
 * <AUTHOR>
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/purchaseSku/**")
@RequestMapping("/purchaseSku")
@AllArgsConstructor
public class PurchaseSkuController {

	private final PurchaseSkuQueryService purchaseSkuQueryService;

	/**
	 * B2C-PSKU列表
	 * @return R
	 */
	@PostMapping("/listPermission")
	public R<List<PurchaseSkuVO>> listPermissionPurchaseSku() {
		List<PurchaseSkuVO> list = purchaseSkuQueryService.listPermissionPurchaseSku();
		return R.success(list);
	}

	/**
	 * B2B-PSKU列表
	 * @param queryCmd 查询参数
	 * @return R
	 */
	@PostMapping("/b2b/list")
	public R<List<B2bPurchaseSkuVO>> b2bList(@RequestBody PskuQueryCmd queryCmd) {
		List<B2bPurchaseSkuVO> list = purchaseSkuQueryService.b2bList(queryCmd);
		return R.success(list);
	}
}
