package com.renpho.erp.oms.application.b2b.receiptOrder.service.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.domain.b2b.order.B2bReceiptOrderStatusEnum;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import com.renpho.erp.oms.infrastructure.common.excel.TimeZoneLocalDateTimeStringConverter;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderAmountPO;
import com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderCustomerPO;
import com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageDto;
import lombok.Data;
import org.springframework.context.i18n.LocaleContextHolder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;

@Data
public class B2bReceiptOrderPageVo implements VO {
    @ExcelIgnore
    private Long id;
    /**
     * 银行参考号
     */
    @ExcelHeadName(zhCnName = "银行参考号", enName = "Bank Refer No#")
    private String bankReferNo;

    /**
     * 自定义收款单号
     */
    @ExcelIgnore
    private String receiptOrderNo;


    /**
     * 收款所属的订单号
     */
    @ExcelHeadName(zhCnName = "订单号", enName = "SO#")
    private String b2bOrderNo;

    @ExcelIgnore
    private Long b2bOrderId;

    /**
     * 客户id(oms_customer_info表的id)
     */
    @ExcelIgnore
    private Long customerId;
    /**
     * 公司名称
     */
    @ExcelHeadName(zhCnName = "公司", enName = "Company")
    private String customerCompanyName;

    /**
     * 客户国家
     */
    @ExcelHeadName(zhCnName = "地区", enName = "Region")
    private String customerCountry;

    /**
     * 贸易条款编码
     */
    @ExcelHeadName(zhCnName = "贸易条款", enName = "Incoterms")
    private String incotermsCode;

    /**
     * 付款条款编码
     */
    @ExcelHeadName(zhCnName = "付款条款", enName = "Payment Terms")
    private String paymentTermsCode;

    /**
     * 币种
     */
    @ExcelHeadName(zhCnName = "币种", enName = "Currency")
    private String currency;

    /**
     * 客户付款金额
     */
    @ExcelIgnore
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal customerPaidAmount;

    /**
     * 收款金额
     */
    @ExcelHeadName(zhCnName = "收款金额", enName = "Receive Amount")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal receiptAmount;

    /**
     * 收款金额比例
     */
    @ExcelIgnore
    private BigDecimal receiptAmountRate;

    /**
     * 订单总金额
     */
    @ExcelHeadName(zhCnName = "合计", enName = "Total")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal orderTotalAmount;

    /**
     * 订单已收款的金额
     */
    @ExcelHeadName(zhCnName = "已收", enName = "Received")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal orderReceiptAmount;
    /**
     * 状态 1-待确认,2-已确认,3-已取消
     */
    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "btowb_receipt_order_status", ref = "statusStr")
    private Integer status;
    /**
     * 状态
     */
    @ExcelHeadName(zhCnName = "状态", enName = "Status")
    private String statusStr;

    /**
     * 创建时间
     */
    @ExcelHeadName(zhCnName = "创建时间", enName = "Create Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime createTime;
    /**
     * 客户付款时间
     */
    @ExcelHeadName(zhCnName = "付款时间", enName = "Paid Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime customerPaidTime;
    /**
     * 收款时间
     */
    @ExcelHeadName(zhCnName = "收款时间", enName = "Receipt Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime receiptTime;

    /**
     * 收款账号
     */
    @ExcelIgnore
    private String bankAccountCurrencyName;

    /**
     * 订单已收款的占比，0.2代表20%
     */
    @ExcelIgnore
    private BigDecimal orderReceiptRate;


    public static B2bReceiptOrderPageVo form(B2BReceiptOrderPageDto b2BReceiptOrderPageDto, B2bOrderAmountPO b2bOrderAmount, B2bOrderCustomerPO b2bOrderCustomer, Map<String, String> paymentTermsNameMap) {
        B2bReceiptOrderPageVo b2BReceiptOrderPageVo = new B2bReceiptOrderPageVo();
        b2BReceiptOrderPageVo.setId(b2BReceiptOrderPageDto.getId());
        b2BReceiptOrderPageVo.setReceiptOrderNo(b2BReceiptOrderPageDto.getReceiptOrderNo());
        b2BReceiptOrderPageVo.setBankReferNo(b2BReceiptOrderPageDto.getBankReferNo());
        b2BReceiptOrderPageVo.setB2bOrderNo(b2BReceiptOrderPageDto.getB2bOrderNo());
        b2BReceiptOrderPageVo.setB2bOrderId(b2BReceiptOrderPageDto.getB2bOrderId());
        b2BReceiptOrderPageVo.setCustomerId(b2BReceiptOrderPageDto.getCustomerId());
        b2BReceiptOrderPageVo.setCustomerPaidAmount(b2BReceiptOrderPageDto.getCustomerPaidAmount());
        b2BReceiptOrderPageVo.setStatus(b2BReceiptOrderPageDto.getStatus());
        b2BReceiptOrderPageVo.setReceiptAmount(b2BReceiptOrderPageDto.getReceiptAmount());

        BigDecimal receiptRate = null;
        if (Set.of(B2bReceiptOrderStatusEnum.WAIT_CONFIRM.getValue(),B2bReceiptOrderStatusEnum.CANCELLED.getValue()).contains(b2BReceiptOrderPageDto.getStatus())) {
            BigDecimal amount = b2BReceiptOrderPageDto.getCustomerPaidAmount();
            if (amount != null) {
                receiptRate = amount.divide(b2bOrderAmount.getTotalAmount(), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
            }
        } else if (B2bReceiptOrderStatusEnum.CONFIRMED.getValue().equals(b2BReceiptOrderPageDto.getStatus())) {
            BigDecimal amount = b2BReceiptOrderPageDto.getReceiptAmount();
            if (amount != null) {
                receiptRate = amount.divide(b2bOrderAmount.getTotalAmount(), 4, RoundingMode.HALF_UP)
                        .multiply(BigDecimal.valueOf(100));
            }
        }
        b2BReceiptOrderPageVo.setReceiptAmountRate(receiptRate);

        b2BReceiptOrderPageVo.setCurrency(b2bOrderAmount.getCurrency());
        b2BReceiptOrderPageVo.setOrderTotalAmount(b2bOrderAmount.getTotalAmount());
        b2BReceiptOrderPageVo.setOrderReceiptAmount(b2bOrderAmount.getReceiptAmount());
        b2BReceiptOrderPageVo.setOrderReceiptRate(Optional.ofNullable(b2bOrderAmount.getReceiptRate()).orElse(BigDecimal.ZERO).multiply(BigDecimal.valueOf(100)));

        String language = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");
        if ("zh-CN".equalsIgnoreCase(language)) {
            b2BReceiptOrderPageVo.setBankAccountCurrencyName(b2BReceiptOrderPageDto.getFmsFundAccountCurrencyCnname());
        } else {
            b2BReceiptOrderPageVo.setBankAccountCurrencyName(b2BReceiptOrderPageDto.getFmsFundAccountCurrencyEnname());
        }

        b2BReceiptOrderPageVo.setCreateTime(b2BReceiptOrderPageDto.getCreateTime());
        b2BReceiptOrderPageVo.setCustomerPaidTime(b2BReceiptOrderPageDto.getCustomerPaidTime());
        b2BReceiptOrderPageVo.setReceiptTime(b2BReceiptOrderPageDto.getReceiptTime());

        if (b2bOrderCustomer != null) {
            b2BReceiptOrderPageVo.setCustomerCompanyName(b2bOrderCustomer.getCustomerCompanyName());
            b2BReceiptOrderPageVo.setCustomerCountry(b2bOrderCustomer.getCountry());
            b2BReceiptOrderPageVo.setIncotermsCode(b2bOrderCustomer.getIncotermsCode());
            b2BReceiptOrderPageVo.setPaymentTermsCode(paymentTermsNameMap.get(b2bOrderCustomer.getPaymentTermsCode()));
        }

        return b2BReceiptOrderPageVo;

    }
}
