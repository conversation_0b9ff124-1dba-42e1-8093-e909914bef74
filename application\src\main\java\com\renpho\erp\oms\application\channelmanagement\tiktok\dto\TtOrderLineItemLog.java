package com.renpho.erp.oms.application.channelmanagement.tiktok.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class TtOrderLineItemLog {

	private Long id;

	private Long omsTtOrderId;

	/**
	 * Tiktok商品id
	 */
	private String itemId;

	/**
	 * sku id
	 */
	private String skuId;

	/**
	 * 组合列表 SKU 的相关产品 json列表
	 */
	private String combinedListingSkus;

	/**
	 * 商品状态
	 * UNPAID、AWAITING_SHIPMENT、AWAITING_COLLECTION、IN_TRANSIT、DELIVERED、COMPLETED、CANCELLED
	 */
	private String displayStatus;

	/**
	 * 商品名称
	 */
	private String productName;

	/**
	 * 卖家sku
	 */
	private String sellerSku;

	/**
	 * sku图片
	 */
	private String skuImage;

	/**
	 * sku名称
	 */
	private String skuName;

	/**
	 * product id
	 */
	private String productId;

	/**
	 * 商品售价
	 */
	private BigDecimal salePrice;

	/**
	 * 平台折扣
	 */
	private BigDecimal platformDiscount;

	/**
	 * 卖家折扣
	 */
	private BigDecimal sellerDiscount;

	/**
	 * sku类型
	 */
	private String skuType;

	/**
	 * 取消原因
	 */
	private String cancelReason;

	/**
	 * 原始价格
	 */
	private BigDecimal originalPrice;

	/**
	 * 卖家发货订单的时间
	 */
	private LocalDateTime rtsTime;

	/**
	 * 包裹状态 TO_FULFILL、PROCESSING、FULFILLING、COMPLETED、CANCELLED
	 */
	private String packageStatus;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 运输供应商名称
	 */
	private String shippingProviderName;

	/**
	 * 取消用户
	 */
	private String cancelUser;

	/**
	 * 运输供应商id
	 */
	private String shippingProviderId;

	/**
	 * 是否为礼品 0:否 1:是
	 */
	private Boolean isGift;

	/**
	 * 税费信息json
	 */
	private String taxInfo;

	/**
	 * 追踪号码
	 */
	private String trackingNumber;

	/**
	 * 包裹id
	 */
	private String packageId;

	/**
	 * 零售送货费
	 */
	private BigDecimal retailDeliveryFee;

	/**
	 * 服务费
	 */
	private String buyerServiceFee;

	/**
	 * 小额订单费
	 */
	private String smallOrderFee;

	/**
	 * 订单下达后，卖家处理订单并将其交给运输公司所需的工作日数
	 */
	private String handlingDurationDays;

}
