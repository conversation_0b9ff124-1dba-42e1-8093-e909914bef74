package com.renpho.erp.oms.domain.vc.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单地址值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class VcOrderAddress {

	/**
	 * 发货地
	 */
	private String shipFrom;

	/**
	 * 收货地
	 */
	private String shipTo;

	/**
	 * 账单地址
	 */
	private String billTo;

	/**
	 * 买家地址
	 */
	private String buying;

}
