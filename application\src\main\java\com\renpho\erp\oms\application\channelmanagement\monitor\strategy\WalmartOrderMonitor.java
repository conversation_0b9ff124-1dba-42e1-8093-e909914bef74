package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * 沃尔玛订单监视器
 * <AUTHOR>
 */
@Component
public class WalmartOrderMonitor extends AbstractChannelOrderMonitor {

	private final WmtOrderRepository wmtOrderRepository;

	public WalmartOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			WmtOrderRepository wmtOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.wmtOrderRepository = wmtOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.WMT_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return wmtOrderRepository.count(storeAuthorization.getWmtShopId(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		return storeAuthorization.getWmtShopId();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 按照Walmart，店铺ID统计
		return storeAuthorizationList.stream()
			.filter(s -> storeNames.contains(s.getStoreName()))
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			// Walmart，店铺ID
			.map(StoreAuthorizationVo.Authorization::getWmtShopId)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.map(wmtShopId -> {
				StoreAuthorization storeAuthorization = new StoreAuthorization();
				storeAuthorization.setWmtShopId(wmtShopId);
				// 店铺名称
				storeAuthorization.setStoreNames(storeAuthorizationList.stream()
					.filter(s -> Objects.nonNull(s.getAuthorization())
							&& StringUtils.equals(s.getAuthorization().getWmtShopId(), wmtShopId))
					.map(StoreAuthorizationVo::getStoreName)
					.collect(Collectors.toSet()));
				return storeAuthorization;
			})
			.toList();

	}
}
