package com.renpho.erp.oms.application.channelmanagement.mercado.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MclOrderPaymentVO {

	/**
	 * 美客多支付id
	 */
	private Long mclPaymentId;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 交易金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal transactionAmount;

	/**
	 * 税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal taxesAmount;

	/**
	 * 运费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingCost;

	/**
	 * 优惠金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal couponAmount;

	/**
	 * 多付金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal overpaidAmount;

	/**
	 * 总支付金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal totalPaidAmount;

	/**
	 * 审核日期
	 */
	private LocalDateTime dateApproved;

	/**
	 * 支付创建时间
	 */
	private LocalDateTime dateCreated;

	/**
	 * 支付更新时间
	 */
	private LocalDateTime dateLastModified;
}
