package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.time.LocalDateTime;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC接收状态值对象 基于Amazon SP-API OrderItemStatusReceivingStatus模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcReceivingStatus {

	/**
	 * 接收状态 对应API字段：receiveStatus 可能的值：NOT_RECEIVED, PARTIALLY_RECEIVED, RECEIVED
	 */
	private String receiveStatus;

	/**
	 * 已接收数量 对应API字段：receivedQuantity
	 */
	private AmzVcQuantity receivedQuantity;

	/**
	 * 最后接收日期 对应API字段：lastReceiveDate
	 */
	private LocalDateTime lastReceiveDate;

	// ========== 业务方法 ==========

	/**
	 * 检查是否已接收
	 * @return boolean
	 */
	public boolean isReceived() {
		return "RECEIVED".equals(receiveStatus);
	}

	/**
	 * 检查是否部分接收
	 * @return boolean
	 */
	public boolean isPartiallyReceived() {
		return "PARTIALLY_RECEIVED".equals(receiveStatus);
	}

	/**
	 * 检查是否未接收
	 * @return boolean
	 */
	public boolean isNotReceived() {
		return "NOT_RECEIVED".equals(receiveStatus);
	}

	/**
	 * 检查是否有接收日期
	 * @return boolean
	 */
	public boolean hasReceiveDate() {
		return lastReceiveDate != null;
	}

}
