package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VC订单接单子状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VcOrderAcceptedStatus {

	/**
	 * 未接单
	 */
	NOT_ACCEPTED(1, "未接单"),

	/**
	 * 接单确认中
	 */
	ACCEPTING(2, "接单确认中"),

	/**
	 * 已接单
	 */
	ACCEPTED(3, "已接单"),

	/**
	 * 接单异常
	 */
	ACCEPT_EXCEPTION(4, "接单异常");

	private final Integer value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return VcOrderAcceptedStatus
	 */
	public static VcOrderAcceptedStatus getByValue(Integer value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
