package com.renpho.erp.oms.application.vc.order.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class VcOrderOperatorDetailVo {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 店铺id
     */
    private Integer storeId;

    /**
     * 店铺名
     */
    private String storeName;

    /**
     * 采购单号
     */
    private String vcPurchaseNo;

    /**
     * 发货窗口开始时间
     */
    private LocalDateTime shippingWindowStart;

    /**
     * 发货窗口结束时间
     */
    private LocalDateTime shippingWindowEnd;

    /**
     * 订单商品列表
     */
    private List<VcOperatorDetailItemVo> items;

    @Data
    public static class VcOperatorDetailItemVo {
        private Long id;
        /**
         * 图片url
         */
        private String imageUrl;

        /**
         * 采购SKU
         */
        private String psku;

        /**
         * ASIN
         */
        private String asin;

        /**
         * 条码
         */
        private String barcode;

        /**
         * 下单数量
         */
        private Integer orderedQuantity;

        /**
         * 下单箱数
         */
        private Integer orderedBoxQuantity;

        /**
         * 接单数量
         */
        private Integer acceptedQuantity;

        /**
         * 接单箱数
         */
        private Integer acceptedBoxQuantity;

        /**
         * 拒单数量
         */
        private Integer rejectedQuantity;

        /**
         * 币种
         */
        private String currency;

        /**
         * 单价
         */
        private BigDecimal unitPrice;
        /**
         * 装箱数量（每箱的psku数量）
         */
        private Integer numberOfUnitsPerBox;
    }
}
