package com.renpho.erp.oms.infrastructure.common.constant;

public interface PermissionConstant {

	interface AutoShipAuditConfig {
		/**
		 * 新增或者修改
		 */
		String ADDOREDIT = "hasPermission('oms:autoShipAuditConfig:addOrEdit')";

		/**
		 * 分页查询
		 */
		String PAGE = "hasPermission('oms:autoShipAuditConfig:page')";

		/**
		 * 导出
		 */
		String EXPORT = "hasPermission('oms:autoShipAuditConfig:export')";

		/**
		 * 导入
		 */
		String IMPORT = "hasPermission('oms:autoShipAuditConfig:import')";
	}

	interface SkuMapping {
		/**
		 * 添加
		 */
		String ADD = "hasPermission('oms:skuMapping:add')";

		/**
		 * 修改状态
		 */
		String CHANGESTATUS = "hasPermission('oms:skuMapping:changeStatus')";

		/**
		 * 分页查询
		 */
		String PAGE = "hasPermission('oms:skuMapping:page')";

		/**
		 * 编辑
		 */
		String EDIT = "hasPermission('oms:skuMapping:edit')";

		/**
		 * 导出
		 */
		String EXPORT = "hasPermission('oms:skuMapping:export')";

		/**
		 * 导入
		 */
		String IMPORT = "hasPermission('oms:skuMapping:import')";
	}

	interface SaleOrder {

		String SHOW_ADDRESS_DETAIL = "hasPermission('oms:saleOrder:showAddressDetail')";
		String SPLIT_ORDER = "hasPermission('oms:saleOrder:spiltOrder')";
		String MERGE_ORDER = "hasPermission('oms:saleOrder:mergeOrder')";
		String INTERCEPT_ORDER = "hasPermission('oms:saleOrder:interceptOrder')";
		String RELEASE = "hasPermission('oms:saleOrder:release')";
		String HOLD = "hasPermission('oms:saleOrder:hold')";
		String CANCEL = "hasPermission('oms:saleOrder:cancel')";

		String SHOW_DETAIL = "hasPermission('oms:saleOrder:showDetail')";

		String SHOW_ITEM_LIST = "hasPermission('oms:saleOrder:showItemList')";
		String PAGE = "hasPermission('oms:saleOrder:page')";

		String REMARK = "hasPermission('oms:saleOrder:remark')";
		String ORDER_REMARK_HISTORY = "hasPermission('oms:saleOrder:orderRemarkHistory')";
		String UPDATE_ADDRESS = "hasPermission('oms:saleOrder:updateAddress')";
		String REPARSE_ORDER = "hasPermission('oms:saleOrder:reparseOrder')";
		String CREATE_ORDER_SHIP_AUDIT = "hasPermission('oms:saleOrder:createOrderShipAudit')";
		String OFFLINE_FULFILLMENT = "hasPermission('oms:saleOrder:offlineFulfillment')";
		String OFFLINE_FULFILLMENT_IMPORT = "hasPermission('oms:saleOrder:offlineFulfillmentImport')";
		String MODIFY_ITEM = "hasPermission('oms:saleOrder:modifyItem')";
		String BATCH_REPLACE_ITEM = "hasPermission('oms:saleOrder:batchReplaceItem')";
		String ORDER_SHIP_AUDIT_EXCEL_IMPORT = "hasPermission('oms:saleOrder:orderShipAuditExcelImport')";
		String EXPORT = "hasPermission('oms:saleOrder:export')";
		String CREATE = "hasPermission('oms:saleOrder:create')";
		String DELETE = "hasPermission('oms:saleOrder:delete')";
		String SUBMIT = "hasPermission('oms:saleOrder:submit')";
		String IMPORT_TO_BE_SHIPPED = "hasPermission('oms:saleOrder:importToBeShipped')";
		String IMPORT_TO_SHIPPED = "hasPermission('oms:saleOrder:importToShipped')";
		String FILE_UPLOAD_RECORD_PAGE = "hasPermission('oms:saleOrder:fileUploadRecordPage')";
	}

	interface MulSkuMapping {
		/**
		 * 添加
		 */
		String ADD = "hasPermission('oms:mulSkuMapping:add')";

		/**
		 * 修改状态
		 */
		String CHANGESTATUS = "hasPermission('oms:mulSkuMapping:changeStatus')";

		/**
		 * 分页查询
		 */
		String PAGE = "hasPermission('oms:mulSkuMapping:page')";

		/**
		 * 编辑
		 */
		String EDIT = "hasPermission('oms:mulSkuMapping:edit')";

		/**
		 * 导出
		 */
		String EXPORT = "hasPermission('oms:mulSkuMapping:export')";

		/**
		 * 导入
		 */
		String IMPORT = "hasPermission('oms:mulSkuMapping:import')";
	}

	interface ReturnOrder {
		/**
		 * 分页查询
		 */
		String PAGE = "hasPermission('oms:returnOrder:page')";
		/**
		 * 新建RAM退货订单/新建RAM补发订单
		 */
		String ADD = "hasPermission('oms:returnOrder:add')";
		/**
		 * 手动收货
		 */
		String MANUAL_RECEIVE = "hasPermission('oms:returnOrder:manualReceive')";
		/**
		 * 取消
		 */
		String CANCEL = "hasPermission('oms:returnOrder:cancel')";
		/**
		 * 备注
		 */
		String REMARK = "hasPermission('oms:returnOrder:remark')";
		/**
		 * 导出
		 */
		String EXPORT = "hasPermission('oms:returnOrder:export')";
	}

	interface AmzFbaReturn {
		/**
		 * 分页列表
		 */
		String PAGE = "hasPermission('oms:amzRefundReport:list')";

		/**
		 * 导出
		 */
		String EXPORT = "hasPermission('oms:amzRefundReport:export')";
		/**
		 * 重新解析
		 */
		String REPARSE = "hasPermission('oms:amzRefundReport:reParse')";
	}

	interface FileUploadRecord {

	}

	interface Customer {

		String PAGE = "hasPermission('oms:customer:page')";

		String ADD = "hasPermission('oms:customer:add')";

		String UPDATE = "hasPermission('oms:customer:update')";

		String CHANGE_STATUS = "hasPermission('oms:customer:changeStatus')";

		/**
		 * 保险权限
		 */
		String INSURANCE_GET = "oms:customer:insurance:get";

		String INSURANCE_UPDATE = "oms:customer:insurance:update";
	}

	interface B2bOrder {
		String PAGE = "hasPermission('oms:b2bOrder:page')";
		String REMARK = "hasPermission('oms:b2bOrder:remark')";
		String AUDIT = "hasPermission('oms:b2bOrder:audit')";
		String CANCEL = "hasPermission('oms:b2bOrder:cancel')";
		String EXPORT = "hasPermission('oms:b2bOrder:export')";
		String SHIPMENT_AUDIT = "hasPermission('oms:b2bOrder:shipmentAudit')";
		String CREATE = "hasPermission('oms:b2bOrder:create')";
		String EDIT = "hasPermission('oms:b2bOrder:edit')";
	}

	interface B2BReceiptOrder {
		String PAGE = "hasPermission('oms:b2BReceiptOrder:page')";
		String EXPORT = "hasPermission('oms:b2BReceiptOrder:export')";

		String ADD = "hasPermission('oms:b2BReceiptOrder:add')";

		String CONFIRM = "hasPermission('oms:b2BReceiptOrder:confirm')";

		String CANCEL = "hasPermission('oms:b2BReceiptOrder:cancel')";
	}

	interface VcOrder {
		String PAGE = "hasPermission('oms:vcOrder:page')";
		String EXPORT = "hasPermission('oms:vcOrder:export')";
		String SUBMIT_ORDER = "hasPermission('oms:vcOrder:submitOrder')";
		String REPARSE_SKUS = "hasPermission('oms:vcOrder:reparseSkus')";
		String REMARK = "hasPermission('oms:vcOrder:remark')";
		String UPLOAD_LABEL = "hasPermission('oms:vcOrder:uploadLabel')";
		String SUBMIT_SUPPLIER_CHAIN = "hasPermission('oms:vcOrder:submitSupplierChain')";
		String INVOICE = "hasPermission('oms:vcOrder:invoice')";
	}

}
