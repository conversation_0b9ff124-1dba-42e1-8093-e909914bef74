package com.renpho.erp.oms.domain.vc.order.service;

import org.jmolecules.ddd.annotation.Service;

import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;

import lombok.extern.slf4j.Slf4j;

/**
 * @desc: 提交供应链服务
 * @time: 2025-07-08 16:31:10
 * @author: <PERSON><PERSON>
 */
@Slf4j
@Service
public class SubmitSupplierChainService {

	/**
	 * 提交供应链
	 * @param vcOrderAggRoot VC订单聚合根
	 */
	public SuccessR submit(VcOrderAggRoot vcOrderAggRoot) {
		return new SuccessR(true, null);
	}

	/**
	 * 提交供应链结果
	 *
	 * @param success 是否成功
	 * @param failMsg 失败原因
	 */
	public record SuccessR(boolean success, String failMsg) {
	}

	/**
	 * 提交供应链（有异常直接抛出异常）
	 * @param vcOrderAggRoot VC订单聚合根
	 */
	public void submitSupplierChain(VcOrderAggRoot vcOrderAggRoot) {

	}
}
