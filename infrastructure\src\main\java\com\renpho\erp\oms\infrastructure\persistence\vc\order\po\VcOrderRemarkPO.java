package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单备注表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order_remark")
public class VcOrderRemarkPO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * vc订单id
	 */
	private Long orderId;

	/**
	 * 备注信息
	 */
	private String remark;

}
