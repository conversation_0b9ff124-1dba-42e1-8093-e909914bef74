package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import com.renpho.erp.apiproxy.mercado.model.items.GetMarketplaceItemsDetailResponse;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 美客多Listing解析器
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MercadoListingParser extends AbstractListingParserTemplate<GetMarketplaceItemsDetailResponse> {

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.MCL_CHANNEL_CODE;
    }

    @Override
    protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source,
                                                           GetMarketplaceItemsDetailResponse marketplaceItemsDetailResponse) {
        List<GetMarketplaceItemsDetailResponse.Variation> variations = Optional.of(marketplaceItemsDetailResponse)
                .map(GetMarketplaceItemsDetailResponse::getVariations)
                .orElse(List.of());
        List<SalesChannelListing> listingList = new ArrayList<>();
        // 优先从变体(variations)取，取不到再从属性(attributes)里取
        if (CollectionUtils.isNotEmpty(variations)) {
            // 从变体(variations)取
            variations.forEach(variation -> this.buildListingList(source, marketplaceItemsDetailResponse, variation,
                    variation.getAttributes(), listingList));
        } else {
            // 从属性(attributes)里取
            List<GetMarketplaceItemsDetailResponse.Attribute> attributes = Optional.of(marketplaceItemsDetailResponse)
                    .map(GetMarketplaceItemsDetailResponse::getAttributes)
                    .orElse(List.of());
            this.buildListingList(source, marketplaceItemsDetailResponse, null, attributes, listingList);
        }
        return listingList;
    }

    /**
     * @param source                         原始Listing
     * @param marketplaceItemsDetailResponse 美客多item
     * @param variation                      变体
     * @param attributes                     属性
     * @param listingList                    Listing列表
     */
    private void buildListingList(SalesChannelListingSource source, GetMarketplaceItemsDetailResponse marketplaceItemsDetailResponse,
                                  GetMarketplaceItemsDetailResponse.Variation variation, List<GetMarketplaceItemsDetailResponse.Attribute> attributes,
                                  List<SalesChannelListing> listingList) {
        if (CollectionUtils.isEmpty(attributes)) {
            return;
        }
        // 销售sku集合
        Set<String> sellerSkuSet = attributes.stream().map(this::getSellerSkuByAttribute).collect(Collectors.toSet());
        // 获取sku映射Map key为 店铺id+销售SKU，value为List<SkuMapping>
        Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), sellerSkuSet);
        // 采购sku图片Map，key为采购sku，value为产品封面图片
        Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(
                skuMappingListMap.values().stream().flatMap(Collection::stream).toList());
        attributes.forEach(attribute -> {
            // 获取销售SKU（在variations的attributes里id=SELLER_SKU的value_name）
            String mSku = this.getSellerSkuByAttribute(attribute);
            if (StringUtils.isBlank(mSku)) {
                return;
            }
            // 构建销售渠道Listing
            SalesChannelListing listing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, mSku,
                    source.getStoreId());
            // item编码
            listing.setItem(marketplaceItemsDetailResponse.getId());
            // 标题
            listing.setTitle(marketplaceItemsDetailResponse.getTitle());
            // Item状态
            listing.setStatus(marketplaceItemsDetailResponse.getStatus());
            // Item link
            listing.setLink(marketplaceItemsDetailResponse.getPermalink());
            // Item平台更新时间
            listing.setItemUpdatedTime(ZonedDateTime.parse(marketplaceItemsDetailResponse.getLast_updated()).toLocalDateTime());
            // 销售SKU
            listing.setMsku(mSku);
            // 价格
            listing.setPrice(Objects.nonNull(variation) ? variation.getPrice() : marketplaceItemsDetailResponse.getPrice());
            // 币种
            listing.setCurrency(Objects.nonNull(variation) ? variation.getCurrency_id() : marketplaceItemsDetailResponse.getCurrency_id());
            listingList.add(listing);

        });
    }

    /**
     * 获取销售SKU（在variations的attributes里id=SELLER_SKU的value_name）
     *
     * @param attribute 属性
     * @return mSku
     */
    private String getSellerSkuByAttribute(GetMarketplaceItemsDetailResponse.Attribute attribute) {
        // id=SELLER_SKU的value_name
        if ("SELLER_SKU".equals(attribute.getId())) {
            return attribute.getValue_name();
        }
        return null;
    }
}
