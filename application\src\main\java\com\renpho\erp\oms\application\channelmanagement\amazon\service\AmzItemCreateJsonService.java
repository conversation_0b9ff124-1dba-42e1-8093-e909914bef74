package com.renpho.erp.oms.application.channelmanagement.amazon.service;


import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItemsSourceJson;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderSyncLog;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderItemsSourceRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSourceJsonRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSyncLogRepository;
import com.renpho.karma.json.JSONKit;
import io.swagger.client.model.orders.OrderItem;
import lombok.AllArgsConstructor;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import static com.renpho.erp.oms.infrastructure.common.enums.SyncTypeEnum.SYNC_AMAZON_ORDER_ITEMS;

@AllArgsConstructor
@Service
public class AmzItemCreateJsonService {
    private final AmzOrderItemsSourceRepository amzOrderItemsSourceRepository;
    private final AmzOrderSyncLogRepository amzOrderSyncLogRepository;
    private final AmzOrderSourceJsonRepository amzOrderSourceJsonRepository;

    @Transactional
    public void insert(List<OrderItem> orderItemList, String amazonOrderId, String sellerId, Integer storeId) {
        // 先删除已有的
        amzOrderItemsSourceRepository.deleteByAmazonOrderId(amazonOrderId);

        List<AmzOrderItemsSourceJson> amzOrderItemsSourceJsonList = orderItemList.stream().map(orderItem -> {
            AmzOrderItemsSourceJson amzOrderItemsSourceJson = new AmzOrderItemsSourceJson();
            amzOrderItemsSourceJson.setPlatOrderId(amazonOrderId);
            amzOrderItemsSourceJson.setStoreId(storeId);
            amzOrderItemsSourceJson.setOrderItemsJson(JSONKit.toJSONString(orderItem));
            return amzOrderItemsSourceJson;

        }).collect(Collectors.toList());


        amzOrderItemsSourceRepository.insert(JSONKit.toJSONString(amzOrderItemsSourceJsonList), amazonOrderId);
        // 改变订单同步状态
        amzOrderSourceJsonRepository.toSyncItemsStatus(amazonOrderId);
        // 记录日志
        amzOrderSyncLogRepository.batchInsert(Arrays.asList(build(orderItemList, amazonOrderId, sellerId, storeId)));

    }

    private AmzOrderSyncLog build(List<OrderItem> orderItemList, String amazonOrderId, String sellerId, Integer storeId) {
        AmzOrderSyncLog amzOrderSyncLog = new AmzOrderSyncLog();
        amzOrderSyncLog.setDetailLog(JsonUtils.toJson(orderItemList));
        amzOrderSyncLog.setPlatOrderId(amazonOrderId);
        amzOrderSyncLog.setStoreId(storeId);
        amzOrderSyncLog.setSyncType(SYNC_AMAZON_ORDER_ITEMS.getValue());
        amzOrderSyncLog.setSellerId(sellerId);
        return amzOrderSyncLog;
    }
}
