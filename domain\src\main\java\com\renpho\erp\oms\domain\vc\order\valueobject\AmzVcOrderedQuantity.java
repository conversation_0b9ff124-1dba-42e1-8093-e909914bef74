package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.util.List;
import java.util.Optional;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订购数量值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderedQuantity {

	/**
	 * 订购数量 此值不应为零
	 */
	private AmzVcQuantity orderedQuantity;

	/**
	 * 订购数量详情
	 */
	private List<AmzVcOrderedQuantityDetails> orderedQuantityDetails;

	/**
	 * 获取数量
	 * @return Integer
	 */
	public Integer getQuantity() {
		return Optional.ofNullable(orderedQuantity).map(AmzVcQuantity::getQuantity).orElse(null);
	}

}
