package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderShippingLine;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;

public class SpfOrderShippingLineAppConvertor {
	public static List<SpfOrderShippingLine> toDomainList(List<SpfOrderDTO.ShippingItem> shippinLines) {
		List<SpfOrderShippingLine> spfOrderShippingLineList = Lists.newArrayList();
		shippinLines.forEach((line) -> {
			spfOrderShippingLineList.add(toDomain(line));
		});
		return spfOrderShippingLineList;
	}

	public static SpfOrderShippingLine toDomain(SpfOrderDTO.ShippingItem line) {
		if (Objects.nonNull(line)) {
			SpfOrderShippingLine spfOrderShippingLine = new SpfOrderShippingLine();
			spfOrderShippingLine.setShippingLineId(line.getId());
			spfOrderShippingLine.setCarrierIdentifier(line.getCarrier_identifier());
			spfOrderShippingLine.setDiscountedPriceSet(
					Objects.nonNull(line.getDiscounted_price_set()) ? JsonUtils.toJson(line.getDiscounted_price_set()) : null);
			spfOrderShippingLine.setCode(line.getCode());
			spfOrderShippingLine
				.setDiscountedPrice(StringUtils.isNotEmpty(line.getDiscounted_price()) ? new BigDecimal(line.getDiscounted_price()) : null);
			spfOrderShippingLine.setIsRemoved(line.getIs_removed());
			spfOrderShippingLine.setPhone(line.getPhone());
			spfOrderShippingLine.setPrice(StringUtils.isNotEmpty(line.getPrice()) ? new BigDecimal(line.getPrice()) : null);
			spfOrderShippingLine.setPriceSet(Objects.nonNull(line.getPrice_set()) ? JsonUtils.toJson(line.getPrice_set()) : null);
			spfOrderShippingLine.setRequestedFulfillmentServiceId(line.getRequested_fulfillment_service_id());
			spfOrderShippingLine.setSource(line.getSource());
			spfOrderShippingLine.setTitle(line.getTitle());
			spfOrderShippingLine.setTaxLines(CollectionUtil.isNotEmpty(line.getTax_lines()) ? JsonUtils.toJson(line.getTax_lines()) : null);
			spfOrderShippingLine.setDiscountAllocations(
					CollectionUtil.isNotEmpty(line.getDiscount_allocations()) ? JsonUtils.toJson(line.getDiscount_allocations()) : null);
			return spfOrderShippingLine;
		}
		else {
			return null;
		}
	}
}
