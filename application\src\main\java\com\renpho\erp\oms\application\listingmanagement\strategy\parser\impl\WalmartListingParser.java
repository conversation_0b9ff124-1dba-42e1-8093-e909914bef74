package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import com.renpho.erp.apiproxy.walmart.model.items.GetAllItemsResponse;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 沃尔玛 Listing解析器
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class WalmartListingParser extends AbstractListingParserTemplate<GetAllItemsResponse.Item> {

    @Override
    protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, GetAllItemsResponse.Item item) {
        // 原始报文解析
        SalesChannelListing salesChannelListing = this.walAnalysis(source, item);

        ArrayList<SalesChannelListing> list = new ArrayList<>();
        list.add(salesChannelListing);
        return list;
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.WMT_CHANNEL_CODE;
    }

    /**
     * 沃尔玛 原始报文解析逻辑
     *
     * @param source 原始Listing
     * @param item   沃尔玛item
     * @return SalesChannelListing
     */
    private SalesChannelListing walAnalysis(SalesChannelListingSource source, GetAllItemsResponse.Item item) {
        // 获取sku映射Map key为 店铺id+销售SKU，value为List<SkuMapping>
        Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), Set.of(item.getSku()));
        // 采购sku图片Map，key为采购sku，value为产品封面图片
        Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(
                skuMappingListMap.values().stream().flatMap(Collection::stream).toList());
        // 构建销售渠道Listing（基础数据）
        SalesChannelListing listing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, item.getSku(),
                source.getStoreId());
        // 沃尔玛的gtin可能为空
        listing.setItem(item.getGtin());
        listing.setMsku(item.getSku());
        listing.setTitle(item.getProductName());
        listing.setStatus(item.getLifecycleStatus());
        // 价格
        Optional<GetAllItemsResponse.Price> priceOptional = Optional.ofNullable(item.getPrice());
        listing.setPrice(priceOptional.map(GetAllItemsResponse.Price::getAmount).orElse(null));
        listing.setCurrency(priceOptional.map(GetAllItemsResponse.Price::getCurrency).orElse(null));
        // 链接
        Optional.ofNullable(item.getWpid()).ifPresent(w -> listing.setLink("https://www.walmart.com/ip/" + w));
        return listing;
    }

}
