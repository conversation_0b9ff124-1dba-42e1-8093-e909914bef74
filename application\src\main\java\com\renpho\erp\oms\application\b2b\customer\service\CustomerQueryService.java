package com.renpho.erp.oms.application.b2b.customer.service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.renpho.erp.oms.application.b2b.customer.convert.CustomerConvert;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerDetailVO;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerPageVO;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerSelectVO;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerVO;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.query.CustomerPageQuery;
import com.renpho.erp.oms.domain.b2b.customer.query.CustomerSelectQuery;
import com.renpho.erp.oms.domain.b2b.customer.repository.CustomerRepository;
import com.renpho.erp.oms.infrastructure.common.cache.CommonCache;
import com.renpho.erp.oms.infrastructure.common.constant.RedisConstant;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;
import com.renpho.erp.oms.infrastructure.feign.pds.CountryClient;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.*;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.*;
import com.renpho.erp.pds.client.vo.PdsCountryRegionVo;
import com.renpho.karma.dto.Paging;

import lombok.RequiredArgsConstructor;

/**
 * @desc: 客户查询服务
 * @time: 2025-05-06 10:32:46
 * @author: Alina
 */
@Service
@RequiredArgsConstructor
public class CustomerQueryService {

	private final CustomerRepository customerRepository;
	private final CustomerConvert customerConvert = CustomerConvert.INSTANCE;
	private final CustomerInfoService customerInfoService;
	private final CountryClient countryClient;
	private final CommonCache commonCache;
	private final CustomerService customerService;
	private final CompanyClient companyClient;
	private final CustomerContactInfoService customerContactInfoService;
	private final CustomerInvoiceInfoService customerInvoiceInfoService;
	private final CustomerShippingInfoService customerShippingInfoService;
	private final CustomerAttachmentInfoService customerAttachmentInfoService;
	private final CustomerSalesCompanyTermsInfoService customerSalesCompanyTermsInfoService;
	private final CustomerInsuranceCreditInfoService customerInsuranceCreditInfoService;

	/**
	 * 分页查询客户
	 *
	 * @param query 查询条件
	 * @return 分页结果
	 */
	public Paging<CustomerPageVO> page(CustomerPageQuery query) {
		// 仓储层查询客户聚合根
		IPage<CustomerAggRoot> page = customerRepository.page(query);
		// 查询国家信息
		List<String> countryCodeList = page.getRecords().stream().map(CustomerAggRoot::getCountry).toList();
		Map<String, PdsCountryRegionVo> countryMap = commonCache
			.batchGet(RedisConstant.REMOTE_COUNTRY_CACHE_PREFIX, countryCodeList, PdsCountryRegionVo::getCode, PdsCountryRegionVo.class,
					countryClient::getCountryList)
			.stream()
			.collect(Collectors.toMap(PdsCountryRegionVo::getCode, Function.identity(), (existing, replacement) -> existing));
		// 转换为VO对象返回
		List<CustomerPageVO> pageVOList = page.getRecords()
			.stream()
			.map(aggRoot -> customerConvert.toCustomerPageVO(aggRoot, countryMap))
			.toList();
		return Paging.of(pageVOList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	/**
	 * 获取客户下拉列表
	 *
	 * @return 客户下拉列表
	 */
	public List<CustomerSelectVO> getCustomerSelectList(CustomerSelectQuery query) {
		List<CustomerInfoPO> customerInfoPos = customerInfoService.getCustomerSelectList(query);
		return customerInfoPos.stream().map(customerConvert::toCustomerSelectVO).toList();
	}

	/**
	 * 查询客户详情
	 * @param companyName 公司名称
	 * @return CustomerVO
	 */
	public CustomerVO getDetailByName(String companyName) {
		CustomerInfoPO customer = customerInfoService.existsByCustomerCompanyName(companyName);
		if (Objects.isNull(customer)) {
			return null;
		}
		return customerService.getCustomerDetail(customer.getId());
	}

	/**
	 * 查询客户详情
	 * @param customerId 客户id
	 * @return CustomerDetailVO
	 */
	public CustomerDetailVO getDetail(Long customerId) {
		// 查询客户基本信息
		CustomerInfoPO customerInfo = customerInfoService.getById(customerId);
		if (Objects.isNull(customerInfo)) {
			return null;
		}
		// 查询客户联系人信息
		List<CustomerContactInfoPO> contactInfos = customerContactInfoService.findByCustomerId(customerId);
		// 查询客户发票信息
		List<CustomerInvoiceInfoPO> invoiceInfos = customerInvoiceInfoService.findByCustomerId(customerId);
		// 查询客户收货信息
		List<CustomerShippingInfoPO> shippingInfos = customerShippingInfoService.findByCustomerId(customerId);
		// 查询客户销售公司和协议信息
		CustomerSalesCompanyTermsInfoPO salesCompanyTermsInfo = customerSalesCompanyTermsInfoService.findByCustomerId(customerId);

		// 转换为VO
		CustomerDetailVO vo = customerConvert.toCustomerVO(customerInfo);
		// 客户联系人信息（Primary Purchase Logistics Finance）
		vo.setContacts(customerConvert.toContactVOList(contactInfos));
		// 客户发票信息
		vo.setInvoiceInfos(customerConvert.toInvoiceVO(invoiceInfos));
		// 客户收货信息列表
		vo.setShippingInfos(customerConvert.toShippingVOList(shippingInfos));
		// 客户销售公司和协议信息
		vo.setSalesCompanyTermsInfo(customerConvert.toSalesCompanyTermsVO(salesCompanyTermsInfo));
		return vo;
	}
}
