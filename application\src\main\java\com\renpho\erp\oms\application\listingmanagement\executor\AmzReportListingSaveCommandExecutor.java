package com.renpho.erp.oms.application.listingmanagement.executor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.listingmanagement.dto.AmzReportParseMerchantListingAllDataDto;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourcePushMqStatusEnum;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourceResolveStatusEnum;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import jakarta.annotation.Resource;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 亚马逊报告-Listing数据保存执行器
 *
 * <AUTHOR>
 * @Date 2024/12/26 11:38
 **/
@Component
public class AmzReportListingSaveCommandExecutor {

    @Resource
    private SalesChannelListingSourceRepository salesChannelListingSourceRepository;

    @Resource
    private StoreClient storeClient;

    @Resource
    private ApplicationEventPublisher eventPublisher;

    /**
     * 执行方法
     *
     * <AUTHOR>
     * @Date 11:40 2024/12/26
     * @Param [dtoList]
     **/
    public void execute(List<AmzReportParseMerchantListingAllDataDto> dtoList) {
        if (CollectionUtil.isNotEmpty(dtoList)) {
            // 查询亚马逊SC店铺所有授权信息
            List<StoreAuthorizationDTO> amzStoreAuthList = storeClient.getStoreAuthorizationsByChannelCode(ChannelCode.AMZ_CHANNEL_CODE,
                    StoreTypeEnum.SC.getValue());
            // 转为map结构
            Map<Integer, StoreAuthorizationDTO> amzStoreAuthMap = amzStoreAuthList.stream()
                    .collect(Collectors.toMap(StoreAuthorizationDTO::getStoreId, store -> store));
            List<SalesChannelListingSource> listingSourceList = new ArrayList<>();
            for (AmzReportParseMerchantListingAllDataDto dto : dtoList) {
                SalesChannelListingSource source = new SalesChannelListingSource();
                StoreAuthorizationDTO storeAuthorizationDTO = amzStoreAuthMap.get(dto.getStoreId());
                if (storeAuthorizationDTO != null) {
                    source.setSalesChannelId(storeAuthorizationDTO.getSalesChannelVo().getId());
                    source.setSalesChannelCode(storeAuthorizationDTO.getSalesChannelVo().getChannelCode());
                    source.setSalesChannelName(storeAuthorizationDTO.getSalesChannelVo().getChannelName());
                    source.setStoreId(storeAuthorizationDTO.getStoreAuthorization().getId());
                    source.setStoreName(storeAuthorizationDTO.getStoreAuthorization().getStoreName());
                    source.setSiteCode(storeAuthorizationDTO.getStoreAuthorization().getSiteCode());
                }
                source.setListingId(dto.getListingId());
                source.setContent(JSONObject.toJSONString(dto));
                source.setResolveStatus(ListingSourceResolveStatusEnum.WAIT_RESOLVE.getStatus());
                source.setPushImsStatus(ListingSourcePushMqStatusEnum.NO_NEED_PUSH.getStatus());
                source.setCreateTime(LocalDateTime.now());
                listingSourceList.add(source);
            }
            salesChannelListingSourceRepository.saveList(listingSourceList);
            // 注册进程内任务（原始Listing解析）领域事件
            listingSourceList.forEach(listingSource -> listingSource.registerParseTaskEvent(eventPublisher));
        }

    }
}
