//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrder;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import java.math.BigDecimal;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.DateUtils;
import org.apache.shenyu.common.utils.JsonUtils;

public class SpfOrderAppConvertor {

	public static SpfOrder toDomain(SpfOrderDTO spfOrderDTO) {
		if (Objects.nonNull(spfOrderDTO)) {
			SpfOrder spfOrder = new SpfOrder();
			spfOrder.setShopifyUrl(spfOrderDTO.getShopUrl());
			spfOrder.setStoreId(spfOrderDTO.getStoreId());
			spfOrder.setShopifyOrderId(spfOrderDTO.getId());
			spfOrder.setAdminGraphqlApiId(spfOrderDTO.getAdmin_graphql_api_id());
			spfOrder.setAppId(spfOrderDTO.getApp_id());
			spfOrder.setBrowserIp(spfOrderDTO.getBrowser_ip());
			spfOrder.setBuyerAcceptsMarketing(spfOrderDTO.isBuyer_accepts_marketing());
			spfOrder.setCancelReason(spfOrderDTO.getCancel_reason());
			spfOrder.setCancelledAt(StringUtils.isNotEmpty(spfOrderDTO.getCancelled_at())
					? DateUtil.parseISOStr(spfOrderDTO.getCancelled_at()) : null);
			spfOrder.setCartToken(spfOrderDTO.getCart_token());
			spfOrder.setCheckoutId(spfOrderDTO.getCheckout_id());
			spfOrder.setClientDetails(
					Objects.nonNull(spfOrderDTO.getClient_details()) ? JsonUtils.toJson(spfOrderDTO.getClient_details()) : null);
			spfOrder.setClosedAt(
					StringUtils.isNotEmpty(spfOrderDTO.getClosed_at()) ? DateUtil.parseISOStr(spfOrderDTO.getClosed_at()) : null);
			spfOrder.setCompany(spfOrderDTO.getCompany());
			spfOrder.setConfirmationNumber(spfOrderDTO.getConfirmation_number());
			spfOrder.setConfirmed(spfOrderDTO.isConfirmed());
			spfOrder.setContactEmail(spfOrderDTO.getContact_email());
			spfOrder.setCreatedAt(
					StringUtils.isNotEmpty(spfOrderDTO.getCreated_at()) ? DateUtil.parseISOStr(spfOrderDTO.getCreated_at()) : null);
			spfOrder.setCurrency(spfOrderDTO.getCurrency());
			spfOrder.setCurrentSubtotalPrice(StringUtils.isNotEmpty(spfOrderDTO.getCurrent_subtotal_price())
					? new BigDecimal(spfOrderDTO.getCurrent_subtotal_price()) : null);
			spfOrder.setCurrentSubtotalPriceSet(Objects.nonNull(spfOrderDTO.getCurrent_subtotal_price_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_subtotal_price_set()) : null);
			spfOrder.setCurrentTotalAdditionalFeesSet(Objects.nonNull(spfOrderDTO.getCurrent_total_additional_fees_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_total_additional_fees_set()) : null);
			spfOrder.setCurrentTotalDiscounts(StringUtils.isNotEmpty(spfOrderDTO.getCurrent_total_discounts())
					? new BigDecimal(spfOrderDTO.getCurrent_total_discounts()) : null);
			spfOrder.setCurrentTotalDiscountsSet(Objects.nonNull(spfOrderDTO.getCurrent_total_discounts_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_total_discounts_set()) : null);
			spfOrder.setCurrentTotalDutiesSet(Objects.nonNull(spfOrderDTO.getCurrent_total_duties_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_total_duties_set()) : null);
			spfOrder.setCurrentTotalPrice(StringUtils.isNotEmpty(spfOrderDTO.getCurrent_total_price())
					? new BigDecimal(spfOrderDTO.getCurrent_total_price()) : null);
			spfOrder.setCurrentTotalPriceSet(Objects.nonNull(spfOrderDTO.getCurrent_total_price_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_total_price_set()) : null);
			spfOrder.setCurrentTotalTax(
					StringUtils.isNotEmpty(spfOrderDTO.getCurrent_total_tax()) ? new BigDecimal(spfOrderDTO.getCurrent_total_tax()) : null);
			spfOrder.setCurrentTotalTaxSet(Objects.nonNull(spfOrderDTO.getCurrent_total_tax_set())
					? JsonUtils.toJson(spfOrderDTO.getCurrent_total_tax_set()) : null);
			spfOrder.setCustomerLocale(spfOrderDTO.getCustomer_locale());
			spfOrder.setDeviceId(spfOrderDTO.getDevice_id());
			spfOrder.setDiscountCodes(
					CollectionUtil.isNotEmpty(spfOrderDTO.getDiscount_codes()) ? JsonUtils.toJson(spfOrderDTO.getDiscount_codes()) : null);
			spfOrder.setDutiesIncluded(spfOrderDTO.getDuties_included());
			spfOrder.setEmail(spfOrderDTO.getEmail());
			spfOrder.setEstimatedTaxes(spfOrderDTO.isEstimated_taxes());
			spfOrder.setFinancialStatus(spfOrderDTO.getFinancial_status());
			spfOrder.setFulfillmentStatus(spfOrderDTO.getFulfillment_status());
			spfOrder.setLandingSite(spfOrderDTO.getLanding_site());
			spfOrder.setLandingSiteRef(spfOrderDTO.getLanding_site_ref());
			spfOrder.setLocationId(spfOrderDTO.getLocation_id());
			spfOrder.setMerchantBusinessEntityId(spfOrderDTO.getMerchant_business_entity_id());
			spfOrder.setMerchantOfRecordAppId(spfOrderDTO.getMerchant_of_record_app_id());
			spfOrder.setName(spfOrderDTO.getName());
			spfOrder.setNote(spfOrderDTO.getNote());
			spfOrder.setNoteAttributes(CollectionUtil.isNotEmpty(spfOrderDTO.getNote_attributes())
					? JsonUtils.toJson(spfOrderDTO.getNote_attributes()) : null);
			spfOrder.setNum(spfOrderDTO.getNumber());
			spfOrder.setOrderNumber(spfOrderDTO.getOrder_number());
			spfOrder.setOrderStatusUrl(spfOrderDTO.getOrder_status_url());
			spfOrder.setOriginalTotalAdditionalFeesSet(Objects.nonNull(spfOrderDTO.getOriginal_total_additional_fees_set())
					? JsonUtils.toJson(spfOrderDTO.getOriginal_total_additional_fees_set()) : null);
			spfOrder.setOriginalTotalDutiesSet(Objects.nonNull(spfOrderDTO.getOriginal_total_duties_set())
					? JsonUtils.toJson(spfOrderDTO.getOriginal_total_duties_set()) : null);
			spfOrder.setPaymentGatewayNames(CollectionUtil.isNotEmpty(spfOrderDTO.getPayment_gateway_names())
					? JsonUtils.toJson(spfOrderDTO.getPayment_gateway_names()) : null);
			spfOrder.setPhone(spfOrderDTO.getPhone());
			spfOrder.setPoNumber(spfOrderDTO.getPo_number());
			spfOrder.setPresentmentCurrency(spfOrder.getPresentmentCurrency());
			spfOrder.setProcessedAt(DateUtil.parseISOStr(spfOrderDTO.getProcessed_at()));
			spfOrder.setReference(spfOrderDTO.getReference());
			spfOrder.setReferringSite(spfOrderDTO.getReferring_site());
			spfOrder.setSourceIdentifier(spfOrderDTO.getSource_identifier());
			spfOrder.setSourceName(spfOrderDTO.getSource_name());
			spfOrder.setSourceUrl(spfOrderDTO.getSource_url());
			spfOrder.setSubtotalPrice(
					StringUtils.isNotEmpty(spfOrderDTO.getSubtotal_price()) ? new BigDecimal(spfOrderDTO.getSubtotal_price()) : null);
			spfOrder.setTags(spfOrderDTO.getTags());
			spfOrder.setTaxExempt(spfOrderDTO.isTax_exempt());
			spfOrder
				.setTaxLines(CollectionUtil.isNotEmpty(spfOrderDTO.getTax_lines()) ? JsonUtils.toJson(spfOrderDTO.getTax_lines()) : null);
			spfOrder.setTaxesIncluded(spfOrderDTO.isTaxes_included());
			spfOrder.setTest(spfOrderDTO.isTest());
			spfOrder.setToken(spfOrderDTO.getToken());
			spfOrder.setTotalCashRoundingPaymentAdjustmentSet(Objects.nonNull(spfOrderDTO.getTotal_cash_rounding_payment_adjustment_set())
					? JsonUtils.toJson(spfOrderDTO.getTotal_cash_rounding_payment_adjustment_set()) : null);
			spfOrder.setTotalCashRoundingRefundAdjustmentSet(Objects.nonNull(spfOrderDTO.getTotal_cash_rounding_refund_adjustment_set())
					? JsonUtils.toJson(spfOrderDTO.getTotal_cash_rounding_refund_adjustment_set()) : null);
			spfOrder.setTotalDiscounts(
					StringUtils.isNotEmpty(spfOrderDTO.getTotal_discounts()) ? new BigDecimal(spfOrderDTO.getTotal_discounts()) : null);
			spfOrder.setTotalDiscountsSet(
					Objects.nonNull(spfOrderDTO.getTotal_discounts_set()) ? JsonUtils.toJson(spfOrderDTO.getTotal_discounts_set()) : null);
			spfOrder.setTotalLineItemsPrice(StringUtils.isNotEmpty(spfOrderDTO.getTotal_line_items_price())
					? new BigDecimal(spfOrderDTO.getTotal_line_items_price()) : null);
			spfOrder.setTotalLineItemsPriceSet(Objects.nonNull(spfOrderDTO.getTotal_line_items_price_set())
					? JsonUtils.toJson(spfOrderDTO.getTotal_line_items_price_set()) : null);
			spfOrder.setTotalOutstanding(
					StringUtils.isNotEmpty(spfOrderDTO.getTotal_outstanding()) ? new BigDecimal(spfOrderDTO.getTotal_outstanding()) : null);
			spfOrder
				.setTotalPrice(StringUtils.isNotEmpty(spfOrderDTO.getTotal_price()) ? new BigDecimal(spfOrderDTO.getTotal_price()) : null);
			spfOrder.setTotalPriceSet(
					Objects.nonNull(spfOrderDTO.getTotal_price_set()) ? JsonUtils.toJson(spfOrderDTO.getTotal_price_set()) : null);
			spfOrder.setTotalTax(StringUtils.isNotEmpty(spfOrderDTO.getTotal_tax()) ? new BigDecimal(spfOrderDTO.getTotal_tax()) : null);
			spfOrder
				.setTotalTaxSet(Objects.nonNull(spfOrderDTO.getTotal_tax_set()) ? JsonUtils.toJson(spfOrderDTO.getTotal_tax_set()) : null);
			spfOrder.setTotalTipReceived(StringUtils.isNotEmpty(spfOrderDTO.getTotal_tip_received())
					? new BigDecimal(spfOrderDTO.getTotal_tip_received()) : null);
			spfOrder.setTotalWeight(spfOrderDTO.getTotal_weight());
			spfOrder.setUpdatedAt(DateUtil.parseISOStr(spfOrderDTO.getUpdated_at()));
			spfOrder.setUserId(spfOrderDTO.getUser_id());
			spfOrder.setBillingAddress(
					Objects.nonNull(spfOrderDTO.getBilling_address()) ? JsonUtils.toJson(spfOrderDTO.getBilling_address()) : null);
			spfOrder.setCustomer(Objects.nonNull(spfOrderDTO.getCustomer()) ? JsonUtils.toJson(spfOrderDTO.getCustomer()) : null);
			spfOrder.setDiscountApplications(CollectionUtil.isNotEmpty(spfOrderDTO.getDiscount_applications())
					? JsonUtils.toJson(spfOrderDTO.getDiscount_applications()) : null);
			spfOrder.setFulfillments(
					CollectionUtil.isNotEmpty(spfOrderDTO.getFulfillments()) ? JsonUtils.toJson(spfOrderDTO.getFulfillments()) : null);
			spfOrder.setPaymentTerms(
					CollectionUtil.isNotEmpty(spfOrderDTO.getPayment_terms()) ? JsonUtils.toJson(spfOrderDTO.getPayment_terms()) : null);
			spfOrder.setRefunds(CollectionUtil.isNotEmpty(spfOrderDTO.getRefunds()) ? JsonUtils.toJson(spfOrderDTO.getRefunds()) : null);
			if (Objects.nonNull(spfOrderDTO.getShipping_address())) {
				SpfOrderDTO.Address shippingAddress = spfOrderDTO.getShipping_address();
				spfOrder.setCity(shippingAddress.getCity());
				spfOrder.setZip(shippingAddress.getZip());
				spfOrder.setCountry(shippingAddress.getCountry());
				spfOrder.setCountryCode(shippingAddress.getCountry_code());
				spfOrder.setProvince(shippingAddress.getProvince());
				spfOrder.setProvinceCode(shippingAddress.getProvince_code());
			}
			spfOrder.setLastMpdsSyncOrderTime(StrUtil.isNotEmpty(spfOrderDTO.getSyncTime())? DateUtils.parseLocalDateTime(spfOrderDTO.getSyncTime()):null);

			return spfOrder;
		}
		else {
			return null;
		}
	}
}
