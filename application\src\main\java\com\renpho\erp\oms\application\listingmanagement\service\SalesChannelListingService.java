package com.renpho.erp.oms.application.listingmanagement.service;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.oms.application.listingmanagement.convert.SalesChannelListingAppConvertor;
import com.renpho.erp.oms.application.listingmanagement.vo.SalesChannelListingExportExcel;
import com.renpho.erp.oms.application.listingmanagement.vo.SalesChannelListingVO;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.query.SkuListingQuery;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingRepository;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.convert.SalesChannelListingConvertor;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.persistence.listing.mapper.SalesChannelListingMapper;
import com.renpho.erp.oms.infrastructure.persistence.listing.po.SalesChannelListingPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.i18n.I18nMessageKit;
import com.renpho.karma.json.JSONKit;

import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * listing 服务类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class SalesChannelListingService {

	@Resource
	private SalesChannelListingRepository skuListingRepository;
	@Resource
	private I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;
	@Resource
	private FileClient fileClient;
	@Resource
	private SalesChannelListingMapper salesChannelListingMapper;

	@Resource
	private StringRedisTemplate stringRedisTemplate;
	@Autowired
	private SalesChannelListingConvertor salesChannelListingConvertor;

	public Paging<SalesChannelListingVO> page(SkuListingQuery query) {
		IPage<SalesChannelListing> page = skuListingRepository.page(query);
		List<SalesChannelListingVO> voList = SalesChannelListingAppConvertor.INSTANCE.toVoList(page.getRecords());
		// pds的图片id（产品封面图片）集合
		Set<String> imageIdSet = voList.stream().map(SalesChannelListingVO::getImageId).collect(Collectors.toSet());
		// 调用FTP获取文件信息
		Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);
		voList.forEach(vo -> {
			// 文件信息
			FileDetailResponse file = fileMap.get(vo.getImageId());
			// 填充文件url
			Optional.ofNullable(file).ifPresent(f -> vo.setImageUrl(f.getUrl()));
		});

		return Paging.of(voList, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	public void exportExcel(SkuListingQuery query, HttpServletResponse response) {

		try (ServletOutputStream out = response.getOutputStream();
			 ExcelWriter excelWriter = EasyExcelFactory.write(out, SalesChannelListingExportExcel.class).registerWriteHandler(i18nHeaderCellWriteHandler).build()) {
			HttpUtil.setExportResponseHeader(response, "刊登列表.xlsx");
			// 这里注意 如果同一个sheet只要创建一次
			WriteSheet writeSheet = EasyExcelFactory.writerSheet("sheet").build();
			// count一下总数（带数据权限）
			long total = salesChannelListingMapper.selectPermissionCount(this.buildQueryWrapper(query));
			// 分批的次数
			int pageNum = (int) Math.ceil((double) total / (double) 500);
			// 上一批最后一条的id（最小id），作为下一批查询的条件，首次查询此值为空
			Long lastId = null;
			for (int i = 1; i <= pageNum; i++) {
				// 查询条件
				LambdaQueryWrapper<SalesChannelListingPO> qw = this.buildQueryWrapper(query);
				// id降序查询
				qw.orderByDesc(SalesChannelListingPO::getId);
				// id小于上一批的最小id（导出数据的同时，数据刚好新增，能避免page分页出现的上一页最后一条数据作为下一页第一条数据（造成重复数据）的问题）
				qw.lt(Objects.nonNull(lastId), SalesChannelListingPO::getId, lastId);
				// 每批的条数
				qw.last("limit 500");
				// 分批查询（没必要page查询，因为每次会多count一次，浪费数据库资源），带数据权限
				List<SalesChannelListingPO> poList = salesChannelListingMapper.selectPermissionList(qw);
				if (CollectionUtils.isEmpty(poList)) {
					break;
				}
				// 上一批最后一条的id（最小id）
				lastId = poList.stream()
					.map(SalesChannelListingPO::getId)
					.min(Long::compareTo)
					.orElseThrow(() -> new IllegalArgumentException("上一批最后一条的id（最小id）不存在"));
				// 转换为excelData List
				List<SalesChannelListingExportExcel> excelData = this.convertExportExcel(poList);
				excelWriter.write(excelData, writeSheet);
			}
		}
		catch (IOException e) {
			log.error("listing导出失败：", e);
			throw new BusinessException(I18nMessageKit.getMessage("FILE_EXPORT_EXCEPTION"));
		}
	}

	/**
	 * 转换为excelData List
	 *
	 * @param poList po列表
	 * @return List
	 */
	private @NotNull List<SalesChannelListingExportExcel> convertExportExcel(List<SalesChannelListingPO> poList) {
		List<SalesChannelListingExportExcel> excelData = SalesChannelListingAppConvertor.INSTANCE.toExcel(poList);
		// 时间-多时区转换
		excelData.forEach(d -> {
			Optional.ofNullable(d.getPublishedTime()).map(LocalDateTimeTransKits::tansByUserZoneId).ifPresent(d::setPublishedTime);
			Optional.ofNullable(d.getItemUpdatedTime()).map(LocalDateTimeTransKits::tansByUserZoneId).ifPresent(d::setItemUpdatedTime);
			Optional.ofNullable(d.getUpdateTime()).map(LocalDateTimeTransKits::tansByUserZoneId).ifPresent(d::setUpdateTime);
		});
		return excelData;
	}

	/**
	 * 构建查询条件
	 *
	 * @param query 查询参数
	 * @return LambdaQueryWrapper
	 */
	private LambdaQueryWrapper<SalesChannelListingPO> buildQueryWrapper(SkuListingQuery query) {
		LambdaQueryWrapper<SalesChannelListingPO> queryWrapper = Wrappers.lambdaQuery();

		// 是否查询展示：0 否、1 是
		queryWrapper.eq(SalesChannelListingPO::getIsQueryShow, 1);

		queryWrapper.eq(Objects.nonNull(query.getStoreId()), SalesChannelListingPO::getStoreId, query.getStoreId());
		queryWrapper.eq(Objects.nonNull(query.getChannelId()), SalesChannelListingPO::getSalesChannelId, query.getChannelId());

		queryWrapper.like(StringUtil.isNotEmpty(query.getItem()), SalesChannelListingPO::getItem, query.getItem());
		queryWrapper.like(StringUtil.isNotEmpty(query.getMsku()), SalesChannelListingPO::getMsku, query.getMsku());
		queryWrapper.like(StringUtil.isNotEmpty(query.getPsku()), SalesChannelListingPO::getPsku, query.getPsku());

		// 生成case when sql条件,不同平台listing状态值表示active不一样
		if (query.getStatus() != null) {
			Map<String, String> activeMap = new HashMap<>();
			activeMap.put(SaleChannelType.AMAZON.getValue(), "Active");
			activeMap.put(SaleChannelType.WALMART.getValue(), "ACTIVE");
			activeMap.put(SaleChannelType.MERCADO.getValue(), "active");
			activeMap.put(SaleChannelType.SHOPIFY.getValue(), "ACTIVE");
			activeMap.put(SaleChannelType.TIKTOK.getValue(), "ACTIVATE");
			activeMap.put(SaleChannelType.TEMU.getValue(), "1");
			activeMap.put(SaleChannelType.EBAY.getValue(), "Active");

			String caseWhen = activeMap.entrySet()
					.stream()
					.map(entry -> "when sales_channel_code = '%s' then status = '%s'".formatted(entry.getKey(), entry.getValue()))
					.collect(Collectors.joining(" ", "(case ", "else false end) = {0}"));
			queryWrapper.apply(caseWhen, query.getStatus() == 1);
		}

		return queryWrapper;
	}

	@LogRecord(module = "Listing", type = "Update", bsId = "#root.bsUniqueCode", oldData = "#db", newData = "#root")
	public void saveNewListingFromOld(SalesChannelListing nex, SalesChannelListing db) {

		// 关键字段发生变动，追加新记录
		nex.setId(null);
		nex.setIsQueryShow(1);
		db.setIsQueryShow(0);

		skuListingRepository.updateById(db);
		skuListingRepository.insert(nex);
	}

	@LogRecord(module = "Listing", type = "Insert", bsId = "#root.bsUniqueCode", newData = "#root")
	public void saveNewListing(SalesChannelListing nex) {

		// 关键字段发生变动，追加新记录
		nex.setId(null);
		nex.setIsQueryShow(1);

		skuListingRepository.insert(nex);
	}

	public SalesChannelListing loadFromCache(Integer storeId, String msku, Long minute) {
		String key = "amz:listing:" + storeId + ":" + msku;
		ValueOperations<String, String> valueOperations = stringRedisTemplate.opsForValue();
		String json = valueOperations.get(key);
		SalesChannelListing salesChannelListing = null;
		if (StringUtil.isNotEmpty(json)) {
			salesChannelListing = JSONKit.parseObject(json, SalesChannelListing.class);
		}
		// 缓存里没有的话去数据库获取
		if (Objects.isNull(salesChannelListing)) {
			SalesChannelListingPO salesChannelListingPO = salesChannelListingMapper.selectOne(Wrappers.<SalesChannelListingPO> lambdaQuery()
				.eq(SalesChannelListingPO::getStoreId, storeId)
				.eq(SalesChannelListingPO::getMsku, msku)
				.last("limit 1"));
			salesChannelListing = salesChannelListingConvertor.toDomain(salesChannelListingPO);
			// 数据库里没有的话弄个空值防止缓存一直被击穿
			if (Objects.isNull(salesChannelListingPO)) {
				salesChannelListing = new SalesChannelListing();
				salesChannelListing.setId(-1L);
				valueOperations.set(key, JSONKit.toJSONString(salesChannelListing), Duration.ofMinutes(5));
				return null;
			}
			valueOperations.set(key, JSONKit.toJSONString(salesChannelListing), Duration.ofMinutes(minute));
			return salesChannelListing;
		}

		if (salesChannelListing.getId() == -1L) {
			return null;
		}
		return salesChannelListing;
	}
}
