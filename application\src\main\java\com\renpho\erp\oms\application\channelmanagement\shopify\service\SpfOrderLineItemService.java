package com.renpho.erp.oms.application.channelmanagement.shopify.service;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderLineItemVO;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderLineItemLogConvertor;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderLineItem;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderLineItemRepository;
import com.renpho.karma.dto.R;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.renpho.erp.oms.infrastructure.common.constant.SpfProperty.EXTEND_IS_EXTENDWARRANTY;

@Service
@AllArgsConstructor
@Slf4j
public class SpfOrderLineItemService {
	private final SpfOrderLineItemRepository spfOrderLineItemRepository;
	private final SpfOrderLineItemLogConvertor spfOrderLineItemLogConvertor;
	private final ObjectMapper objectMapper;

	public R<List<SpfOrderLineItemVO>> listByOrderId(Long orderId) {
		List<SpfOrderLineItem> spfOrderLineItemList = spfOrderLineItemRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(spfOrderLineItemList)) {
			return R.success(spfOrderLineItemList.stream().map(spfOrderLineItem -> {
				SpfOrderLineItemVO vo = spfOrderLineItemLogConvertor.toVO(spfOrderLineItem);
				// 提取json字段
				try {
					if (StringUtils.isNotEmpty(spfOrderLineItem.getProperties())) {

						List<SpfOrderDTO.Property> propertyList = objectMapper.readValue(spfOrderLineItem.getProperties(),
								new TypeReference<>() {
								});
						vo.setExtWarranty(propertyList.stream().anyMatch(property -> EXTEND_IS_EXTENDWARRANTY.equals(property.getName())));
					}
					// 币种、单价
					if (StringUtils.isNotEmpty(spfOrderLineItem.getPriceSet())) {
						SpfOrderDTO.MoneySet moneySet = JsonUtils.jsonToObject(spfOrderLineItem.getPriceSet(), SpfOrderDTO.MoneySet.class);
						Optional.ofNullable(moneySet.getPresentment_money()).ifPresent(money -> vo.setCurrency(money.getCurrency_code()));
						Optional.ofNullable(moneySet.getPresentment_money())
							.ifPresent(money -> vo
								.setPrice(StringUtils.isNotEmpty(money.getAmount()) ? new BigDecimal(money.getAmount()) : BigDecimal.ZERO));
					}
					// 未税价
					if (StringUtils.isNotEmpty(spfOrderLineItem.getPreTaxPriceSet())) {
						SpfOrderDTO.MoneySet moneySet = JsonUtils.jsonToObject(spfOrderLineItem.getPreTaxPriceSet(),
								SpfOrderDTO.MoneySet.class);
						Optional.ofNullable(moneySet.getPresentment_money())
							.ifPresent(money -> vo.setPreTaxPrice(
									StringUtils.isNotEmpty(money.getAmount()) ? new BigDecimal(money.getAmount()) : BigDecimal.ZERO));
					}

					// 折扣
					if (StringUtils.isNotEmpty(spfOrderLineItem.getDiscountAllocations())) {
						List<SpfOrderDTO.DiscountAllocation> discountAllocationList = JSONKit.parseObject(
								spfOrderLineItem.getDiscountAllocations(),
								new TypeReference<List<SpfOrderDTO.DiscountAllocation>>() {
								});

						if (discountAllocationList != null && !discountAllocationList.isEmpty()) {
							discountAllocationList.forEach(discountAllocation -> {
								BigDecimal currentDiscount = Optional.ofNullable(vo.getDiscount()).orElse(BigDecimal.ZERO);
								Optional.ofNullable(discountAllocation.getAmount_set())
										.map(SpfOrderDTO.MoneySet::getPresentment_money)
										.map(SpfOrderDTO.Money::getAmount)
										.filter(StringUtils::isNotEmpty)
										.map(BigDecimal::new)
										.ifPresent(amount -> vo.setDiscount(currentDiscount.add(amount)));
							});
						}
					}

					// 税
					// 解析 taxLines JSON 字符串
					List<SpfOrderDTO.Tax> taxList = objectMapper.readValue(spfOrderLineItem.getTaxLines(),
							new TypeReference<List<SpfOrderDTO.Tax>>() {
							});

					// 计算总税金额
					BigDecimal totalTax = taxList.stream().map(tax -> {
						if (tax.getPrice_set() != null && tax.getPrice_set().getPresentment_money() != null) {
							String amount = tax.getPrice_set().getShop_money().getAmount();
							return StringUtils.isNotEmpty(amount) ? new BigDecimal(amount) : BigDecimal.ZERO;
						}
						return BigDecimal.ZERO;
					}).reduce(BigDecimal.ZERO, BigDecimal::add);

					// 设置到 VO 对象
					vo.setTax(totalTax);

				}
				catch (JsonProcessingException e) {
					log.error("Shopify商品json提取转换失败：原因为{}", e);
				}

				return vo;
			}).collect(Collectors.toList()));
		}
		return R.success();
	}

}
