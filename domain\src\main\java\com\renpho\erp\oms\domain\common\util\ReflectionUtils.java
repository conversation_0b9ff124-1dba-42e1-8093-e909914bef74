package com.renpho.erp.oms.domain.common.util;

import java.util.Arrays;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.experimental.UtilityClass;

/**
 * 反射工具类
 * <AUTHOR>
 */
@UtilityClass
public class ReflectionUtils {

	/**
	 * 判断对象的所有字段是否都为空
	 * @param obj 待检查的对象
	 * @param excludeFieldNames 要排除的字段名
	 * @return true-所有字段都为空 false-存在非空字段
	 */
	public boolean isAllFieldsEmpty(Object obj, String... excludeFieldNames) {
	 if (ObjectUtil.isNull(obj)) {
	 	return true;
	 }

	 // 将对象转换为Map
	 Map<String, Object> beanMap = BeanUtil.beanToMap(obj);

	 // 排除指定字段
	 if (ObjectUtil.isNotNull(excludeFieldNames)) {
	 	Arrays.stream(excludeFieldNames).forEach(beanMap::remove);
	 }

	 // 判断所有值是否都为空
	 return beanMap.values().stream().allMatch(ReflectionUtils::isEmpty);
	}

	/**
	 * 判断是否为空（包含空白字符串）
	 * @param obj 对象
	 * @return boolean
	 */
	public boolean isEmpty(Object obj) {
		if (obj instanceof String str) {
			return StringUtils.isBlank(str);
		}
		return ObjectUtil.isEmpty(obj);
	}
}