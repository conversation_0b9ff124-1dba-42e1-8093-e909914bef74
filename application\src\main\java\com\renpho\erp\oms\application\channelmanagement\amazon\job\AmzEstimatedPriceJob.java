package com.renpho.erp.oms.application.channelmanagement.amazon.job;

import com.renpho.erp.oms.application.channelmanagement.amazon.config.AmzEstimatedPriceConfigProperties;
import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderService;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/1/3
 */
@Slf4j
@Component
@AllArgsConstructor
public class AmzEstimatedPriceJob {

    private final AmzOrderService amzOrderService;
    private final StoreClient storeClient;
    private final AmzEstimatedPriceConfigProperties amzEstimatedPriceConfigProperties;

    @XxlJob("amzEstimatedPrice")
    public void amzEstimatedPrice() {
        XxlJobHelper.log("amzEstimatedPrice");
        if (!amzEstimatedPriceConfigProperties.isEnabled()) {
            return;
        }

        List<StoreAuthorizationDTO> storeAuthorizationDTOList = storeClient
                .getStoreAuthorizationsByChannelCode(SaleChannelType.AMAZON.getValue(), StoreTypeEnum.SC.getValue());
        for (StoreAuthorizationDTO storeAuthorizationDTO : storeAuthorizationDTOList) {
            amzOrderService.estimatedPrice(storeAuthorizationDTO.getStoreId());
        }
    }
}
