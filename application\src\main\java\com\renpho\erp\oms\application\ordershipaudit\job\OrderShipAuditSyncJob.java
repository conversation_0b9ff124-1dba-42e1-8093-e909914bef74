package com.renpho.erp.oms.application.ordershipaudit.job;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.ShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.OrderShipAuditMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.OrderShipAuditPO;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 同步销售单发货审核上相关的物流信息
 *
 * <AUTHOR>
 * @since 2025/2/19
 */
@Slf4j
// @Component
public class OrderShipAuditSyncJob {
	@Autowired
	private OrderShipAuditMapper orderShipAuditMapper;
	@Autowired
	private OrderShipAuditRepository orderShipAuditRepository;
	@Autowired
	private SaleOrderRepository saleOrderRepository;
	@Autowired
	private TransactionTemplate transactionTemplate;

	// FIXME，与 MulFulfillmentQueryJob 任务，存在重复的同步 销售订单的履约信息
	// @XxlJob("orderShipAuditSyncJob")
	public void handle() {
		String jobParam = XxlJobHelper.getJobParam();
		Param param = JSONKit.parseObject(jobParam, Param.class);

		List<FulfillmentServiceType> fulfillmentServiceTypeList = param.getFulfillmentServiceTypes()
			.stream()
			.map(FulfillmentServiceType::enumOf)
			.filter(Objects::nonNull)
			.toList();
		for (FulfillmentServiceType fulfillmentServiceType : fulfillmentServiceTypeList) {
			ChannelOrderShipService channelOrderShipService = ChannelOrderShipService.Factory.getInstance(fulfillmentServiceType);
			if (channelOrderShipService == null) {
				XxlJobHelper.log("没有相关ChannelOrderShipService实现{}", fulfillmentServiceType.getName());
				log.info("没有相关ChannelOrderShipService实现{}", fulfillmentServiceType.getName());
				continue;
			}

			XxlJobHelper.log("开始同步{}类型的发货审核", fulfillmentServiceType.getName());
			log.info("开始同步{}类型的发货审核", fulfillmentServiceType.getName());
			LocalDateTime startTime = LocalDateTime.now().minusMinutes(5);

			Long maxId = 0L;
			Integer pageSize = ObjectUtil.defaultIfNull(param.getPageSize(), 100);
			List<OrderShipAuditPO> orderShipAuditPOList;
			do {

				orderShipAuditPOList = orderShipAuditMapper.selectList(Wrappers.<OrderShipAuditPO> lambdaQuery()
					.select(OrderShipAuditPO::getId, OrderShipAuditPO::getChannelStoreId)
					.eq(OrderShipAuditPO::getFulfillmentServiceType, fulfillmentServiceType.getValue())
					.eq(param.getOrderId() != null, OrderShipAuditPO::getOrderId, param.getOrderId())
					.in(OrderShipAuditPO::getStatus, ShipmentStatus.CREATE.getValue(), ShipmentStatus.PARTIAL_SHIPPED.getValue())
					.gt(OrderShipAuditPO::getId, maxId)
					// 过几分钟再请求吧,有些平台是异步的
					.lt(OrderShipAuditPO::getCreateTime, startTime)
					.orderByAsc(OrderShipAuditPO::getId)
					.last("limit " + pageSize));

				for (OrderShipAuditPO orderShipAuditPO : orderShipAuditPOList) {
					XxlJobHelper.log("开始处理id是{}的发货审核", orderShipAuditPO.getId());
					log.info("开始处理id是{}的发货审核", orderShipAuditPO.getId());

					OrderShipAuditAggRoot orderShipAuditAggRoot = orderShipAuditRepository.getById(orderShipAuditPO.getId());

					try {
						channelOrderShipService.syncShipmentInfo(orderShipAuditAggRoot);
					}
					catch (Exception e) {
						XxlJobHelper.log("同步多渠道发货履约信息失败, 多渠道发货id是{}", orderShipAuditPO.getId());
						XxlJobHelper.log(e);
						log.info("同步多渠道发货履约信息失败, 多渠道发货id是{}", orderShipAuditPO.getId(), e);
						continue;
					}

					// 还没有平台履约信息,不用同步到销售单
					if (ShipmentStatus.CREATE.equals(orderShipAuditAggRoot.getShipmentStatus())) {
						orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
						continue;
					}
					// wfs应该是异步创建订单,调创建接口返回成功,去平台同步的时候发现是创建失败,销售单变更成发货异常
					// 京东仓,用户可以在他们的管理后台取消销售出库单,取消后就不会返回订单数据了,销售单变更成发货异常
					// 易仓,用户可以在他们的管理后台取消销售出库单,取消后就返回发货异常,销售单变更成发货异常
					if (Set.of(ShipmentStatus.CREATE_FAIL, ShipmentStatus.CANCEL, ShipmentStatus.SHIP_EXCEPTION)
						.contains(orderShipAuditAggRoot.getShipmentStatus())) {
						SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findAggRootById(orderShipAuditAggRoot.getOrderId());
						saleOrderAggRoot.multiCreateFail(orderShipAuditAggRoot.getFailMsg());

						transactionTemplate.executeWithoutResult(status -> {
							orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
							saleOrderRepository.updateInterceptOrder(saleOrderAggRoot);
						});
						return;
					}

					orderShipAuditRepository.updateStatusAndFulfillment(orderShipAuditAggRoot);
				}

			}
			while (Objects.equals(CollUtil.size(orderShipAuditPOList), pageSize));

			XxlJobHelper.log("完成同步{}类型的发货审核", fulfillmentServiceType.getName());
			log.info("完成同步{}类型的发货审核", fulfillmentServiceType.getName());

		}

	}

	@Data
	public static class Param {
		/**
		 * 发货服务类型
		 */
		private List<Integer> fulfillmentServiceTypes;
		/**
		 * 订单id
		 */
		private Long orderId;
		/**
		 * 分页大小
		 */
		private Integer pageSize;

	}
}
