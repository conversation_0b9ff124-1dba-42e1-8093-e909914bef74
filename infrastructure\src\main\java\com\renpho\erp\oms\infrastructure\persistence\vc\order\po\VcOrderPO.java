package com.renpho.erp.oms.infrastructure.persistence.vc.order.po;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableName;
import com.renpho.karma.cloud.mybatisplus.po.AlterationPO;

import lombok.Getter;
import lombok.Setter;

/**
 * VC-订单表
 * <AUTHOR>
 */
@Setter
@Getter
@TableName(value = "oms_vc_order")
public class VcOrderPO extends AlterationPO<Long, Integer, LocalDateTime, Integer> {

	/**
	 * 订单号
	 */
	private String orderNo;

	/**
	 * vc采购单号
	 */
	private String vcPurchaseNo;

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 站点编码
	 */
	private String siteCode;

	/**
	 * 订单状态 1-待接单 2-接单异常 3-待上传箱唛 4-备货中 5-待开票 6-已完成 7-已取消
	 */
	private Integer orderStatus;

	/**
	 * 接单子状态 1-未接单 2-接单确认中 3-已接单 4-接单异常
	 */
	private Integer acceptedStatus;

	/**
	 * 接单方式，是否ERP接单 1-是 0-否
	 */
	private Integer acceptedMethod;

	/**
	 * 解析SKU状态 1-解析成功 2-SKU映射异常
	 */
	private Integer parseStatus;

	/**
	 * 业务类型 1-DI（直接进口） 2-DO（海外仓备货模式）
	 */
	private Integer businessType;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 发票类型
	 */
	private String invoiceType;

	/**
	 * 交货窗口-起始时间
	 */
	private LocalDateTime shippingWindowStart;

	/**
	 * 交货窗口-结束时间
	 */
	private LocalDateTime shippingWindowEnd;

	/**
	 * 下单时间
	 */
	private LocalDateTime orderedTime;

	/**
	 * 接单时间
	 */
	private LocalDateTime acceptedTime;

	/**
	 * 发货时间
	 */
	private LocalDateTime shippedTime;

	/**
	 * 开票时间
	 */
	private LocalDateTime invoicedTime;

	/**
	 * 取消时间
	 */
	private LocalDateTime cancelledTime;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 商品收入，[数量*单价]之和，如果已接单则为接单数量，否则为下单数量
	 */
	private BigDecimal productPrice;

	/**
	 * 税率，0.2代表20%
	 */
	private BigDecimal taxRate;

	/**
	 * 税额，税额明细行的汇总
	 */
	private BigDecimal tax;

	/**
	 * 发货地
	 */
	private String shipFrom;

	/**
	 * 收货地
	 */
	private String shipTo;

	/**
	 * 账单地址
	 */
	private String billTo;

	/**
	 * 买家地址
	 */
	private String buying;

	/**
	 * 异常原因
	 */
	private String exceptionReason;

	/**
	 * 备注
	 */
	private String remark;

}
