package com.renpho.erp.oms.application.ordershipaudit.chain.handler;

import java.util.List;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.Carrier;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.Warehouse;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderItem;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单自动发货审核创建-处理器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OrderAutoCreateShipAuditHandler implements OrderAutoShipAuditHandler {

	private final OrderShipAuditCreateService orderShipAuditCreateService;
	private final SaleOrderRepository saleOrderRepository;

	/**
	 * 处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建
	 * @return int
	 */
	@Override
	public int getOrder() {
		return 4;
	}

	@Override
	public String handle(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 构建发货审核DTO
		OrderShipAuditDTO shipAuditDTO = this.buildShipAuditDTO(orderAutoShipAudit);
		// 创建发货审核
		OrderShipAuditAggRoot shipAuditAggRoot;
		try {
			shipAuditAggRoot = orderShipAuditCreateService.create(shipAuditDTO, false);
		}
		catch (Exception e) {
			log.error(String.format("订单id:[%s]自动创建发货审核失败：", shipAuditDTO.getOrderId()), e);
			return "自动创建发货审核失败：" + e.getMessage();
		}
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// 更新自动分仓分物流（发货审核）状态为成功
		orderAggRoot.autoAllocateFulfillmentSuccess();
		saleOrderRepository.updateAutoAllocateFulfillmentSuccess(orderAggRoot);
		// 注册发货审核事件
		shipAuditAggRoot.registerEvent();
		return null;
	}

	/**
	 * 构建发货审核DTO
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return OrderShipAuditDTO
	 */
	private @NotNull OrderShipAuditDTO buildShipAuditDTO(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// 履约服务类型
		FulfillmentServiceType fulfillmentServiceType = orderAutoShipAudit.getFulfillmentServiceType();
		// 仓库
		Warehouse warehouse = orderAutoShipAudit.getWarehouse();
		// 承运商
		Carrier carrier = orderAutoShipAudit.getCarrier();
		// 发货审核DTO
		OrderShipAuditDTO shipAuditDTO = new OrderShipAuditDTO();
		// 订单id
		shipAuditDTO.setOrderId(orderAggRoot.getOrderId());
		// 履约服务类型
		shipAuditDTO.setFulfillmentServiceType(fulfillmentServiceType.getValue());
		// 发货仓库id
		shipAuditDTO.setWarehouseId(warehouse.getWarehouseId());
		// 发货仓库code
		shipAuditDTO.setWarehouseCode(warehouse.getWarehouseCode());
		// 三方仓库编码
		shipAuditDTO.setThirdPartWarehouseCode(warehouse.getThirdPartWarehouseCode());
		// 承运商编码
		shipAuditDTO.setCarrierCode(carrier.getCarrierCode());
		// 承运商服务编码
		shipAuditDTO.setCarrierServiceCode(carrier.getCarrierServiceCode());
		// 包裹类型编码
		shipAuditDTO.setPackageTypeCode(carrier.getPackageTypeCode());
		// 京东仓订单类型
		shipAuditDTO.setJdOrderType(carrier.getJdOrderType());
		// 订单的预计包裹尺寸，单位cm3
		String size = orderAggRoot.getSize();
		// 包裹长度cm
		shipAuditDTO.setLength(SizeUtil.parseSize(size, 0));
		// 包裹宽度cm
		shipAuditDTO.setWidth(SizeUtil.parseSize(size, 1));
		// 包裹高度cm
		shipAuditDTO.setHeight(SizeUtil.parseSize(size, 2));
		// 包裹重量kg
		shipAuditDTO.setWeight(orderAggRoot.getWeight());
		// 物流公司id(目前只有temu在用)
		shipAuditDTO.setShipCompanyId(carrier.getShipCompanyId());
		// 物流公司服务id(目前只有temu在用)
		shipAuditDTO.setShipChannelId(carrier.getShipChannelId());
		// 报价id
		shipAuditDTO.setShippingQuoteId(carrier.getShippingQuoteId());
		// 利率id
		shipAuditDTO.setRateId(carrier.getEbayRateId());
		// 商品行列表（非延保商品）
		shipAuditDTO.setItems(this.buildItems(orderAggRoot.getNotExtendWarrantyItems()));
		return shipAuditDTO;
	}

	/**
	 * 构建商品行列表（非延保商品）
	 * @param notExtendWarrantyItems 订单行列表（非延保商品）
	 * @return List
	 */
	private List<OrderShipAuditDTO.Item> buildItems(List<SaleOrderItem> notExtendWarrantyItems) {
		return notExtendWarrantyItems.stream().map(i -> OrderShipAuditDTO.Item.builder().itemId(i.getPrimaryKeyId()).build()).toList();
	}
}
