package com.renpho.erp.oms.domain.vc.order.service;

import org.jmolecules.ddd.annotation.Service;

import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.converter.VcOrderCreateConverter;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单转换创建-领域服务
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderCreateDomainService {

	/**
	 * 转换创建VC订单
	 * @param amzVcOrder 亚马逊VC订单
	 *
	 * @return VcOrderAggRoot
	 */
	public VcOrderAggRoot convertAndCreate(AmzVcOrderAggRoot amzVcOrder, VcOrderCreateConverter.ValueObject valueObject) {
		// 转换生成VC订单
		VcOrderAggRoot vcOrder = VcOrderCreateConverter.convertOrder(amzVcOrder, valueObject);
		// 生成id和订单号
		vcOrder.generateIdAndOrderNo();
		// 计算订单状态
		vcOrder.calculateStatusOfCreate(amzVcOrder.getPurchaseOrderState());
		// 计算金额
		vcOrder.calculateAmount();
		// 解析sku
		vcOrder.parseSku(valueObject.getAsinToProductMap());
		return vcOrder;
	}

}
