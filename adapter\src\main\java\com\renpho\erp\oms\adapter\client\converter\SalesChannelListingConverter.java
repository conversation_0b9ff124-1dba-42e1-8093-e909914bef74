package com.renpho.erp.oms.adapter.client.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.client.listing.vo.SalesChannelListingVO;
import com.renpho.erp.oms.infrastructure.persistence.listing.po.SalesChannelListingPO;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SalesChannelListingConverter {

	List<SalesChannelListingVO> toVO(List<SalesChannelListingPO> listings);
}
