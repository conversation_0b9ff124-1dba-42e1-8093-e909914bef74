package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

import java.util.List;
import java.util.Set;

/**
 * @desc: 查询发货方式报价策略接口
 * @time: 2025-04-22 17:56:05
 * @author: Alina
 */

public interface ShippingServiceStrategy {
	/**
	 * 获取履约服务类型.
	 * @return FulfillmentServiceType
	 */
	Set<FulfillmentServiceType> getFulfillmentServiceType();

	/**
	 * 根据所选仓库查询发货方式报价表
	 * @param query 参数
	 * @return List<ShippingServicesVO>
	 */
	List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query);
}
