package com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.renpho.erp.data.permission.aop.annotation.DataPermission;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.query.MulSkuMappingPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.vo.MulSkuMappingPageVO;
import com.renpho.erp.oms.infrastructure.expression.expression.MulSkuMappingExpression;
import com.renpho.erp.oms.infrastructure.persistence.mulSkuMapping.po.MulSkuMappingPO;

/**
 * <p>
 * 多渠道SKU映射表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-08
 */
public interface MulSkuMappingMapper extends BaseMapper<MulSkuMappingPO> {

	@DataPermission(condition = MulSkuMappingExpression.class)
	IPage<MulSkuMappingPageVO> page(Page<MulSkuMappingPageVO> page,
			@Param("mulSkuMappingPageQuery") MulSkuMappingPageQuery mulSkuMappingPageQuery);

	List<MulSkuMappingPO> queryByOrderPskuList(@Param("orderStoreId") Integer orderStoreId, @Param("orderPskus") List<String> orderPskus);
}
