package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLineStatusLog;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineStatusVO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLineStatus;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface WmtOrderLineStatusLogConvertor {

	WmtOrderLineStatusLog toLog(WmtOrderLineStatus wmtOrderLineStatus);

	WmtOrderLineStatusVO toVO(WmtOrderLineStatus param);
}
