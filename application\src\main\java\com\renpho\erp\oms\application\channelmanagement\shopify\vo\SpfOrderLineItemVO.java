package com.renpho.erp.oms.application.channelmanagement.shopify.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpfOrderLineItemVO {

	/**
	 * Shopify商品id
	 */
	private Long itemId;

	/**
	 * product_id
	 */
	private Long productId;

	/**
	 * sku
	 */
	private String sku;

	/**
	 * 是否为礼物 0:否 1:是
	 */
	private Boolean giftCard;

	/**
	 * 数量
	 */
	private Integer quantity;

	private Boolean extWarranty;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 未税价格
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal preTaxPrice;

	/**
	 * 折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal discount;

	/**
	 * 单价
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal price;

	/**
	 * 税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal tax;
}
