package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.extra.spring.SpringUtil;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLineAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLineLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLineStatusLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLineLog;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLog;
import com.renpho.erp.oms.application.channelmanagement.walmart.optlog.WmtOrderAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.walmart.optlog.WmtOrderBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.walmart.optlog.WmtOrderUpdateSnapSource;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderDTO;

import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrder;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLine;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLineStatus;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderLineRepository;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderLineStatusRepository;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderRepository;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class WmtOrderService {
	private final WmtOrderRepository wmtOrderRepository;
	private final WmtOrderLineRepository wmtOrderLineRepository;
	private final WmtOrderLineStatusRepository wmtOrderLineStatusRepository;
	private final WmtOrderLogConvertor wmtOrderLogConvertor;
	private final WmtOrderLineLogConvertor wmtOrderLineLogConvertor;
	private final WmtOrderLineStatusLogConvertor wmtOrderLineStatusLogConvertor;

	@Lock4j(name = "oms:walmart:sync", keys = { "#wmtOrderDTO.purchaseOrderId", "#wmtOrderDTO.shopId" })
	public void createOrUpdate(WmtOrderDTO wmtOrderDTO) {
		// 解析成平台单
		WmtOrder wmtOrder = WmtOrderAppConvertor.toDomain(wmtOrderDTO);
		List<WmtOrderLine> wmtOrderLineList = WmtOrderLineAppConvertor.toDomainList(wmtOrderDTO.getOrderLines());
		// 查询有没有
		WmtOrder exitsOrder = wmtOrderRepository.getByUniqueIndex(wmtOrderDTO.getPurchaseOrderId(), wmtOrderDTO.getShopId());
		// 更新插入
		if (Objects.nonNull(exitsOrder)) {
			wmtOrder.setId(exitsOrder.getId());
			SpringUtil.getBean(WmtOrderService.class).doUpdate(wmtOrder, wmtOrderLineList);
		}
		else {
			SpringUtil.getBean(WmtOrderService.class).doCreate(wmtOrder, wmtOrderLineList);
		}
	}

	@Transactional
	@OpLog(snaptSource = WmtOrderAddSnapSource.class, title = "新增沃尔玛订单", businessType = BusinessType.INSERT,
			businessModule = WmtOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doCreate(WmtOrder wmtOrder, List<WmtOrderLine> itemList) {
		// 插入订单
		Long orderId = wmtOrderRepository.insert(wmtOrder);
		// 插入商品
		List<Long> orderLineIds = wmtOrderLineRepository.batchInsert(itemList, orderId);

		List<WmtOrderLineStatus> wmtOrderLineStatusList = Lists.newArrayList();
		for (int i = 0; i < orderLineIds.size(); i++) {
			Long orderLineId = orderLineIds.get(i);
			wmtOrderLineStatusList.addAll(itemList.get(i).getWmtOrderLineStatusList().stream().map(wmOrderLineStatus -> {
				wmOrderLineStatus.setOmsWmtOrderId(orderId);
				wmOrderLineStatus.setOmsWmtOrderLineId(orderLineId);
				return wmOrderLineStatus;
			}).collect(Collectors.toList()));

		}
		// 插入商品行状态
		wmtOrderLineStatusRepository.batchInsert(wmtOrderLineStatusList);
		// 返回主键增加日志
		return R.success(orderId);
	}

	@Transactional
	@OpLog(snaptSource = WmtOrderUpdateSnapSource.class, title = "更新沃尔玛订单", businessType = BusinessType.UPDATE,
			businessModule = WmtOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doUpdate(WmtOrder wmtOrder, List<WmtOrderLine> itemList) {
		// 先删除原有商品和商品状态
		wmtOrderLineRepository.deleteByOrderId(wmtOrder.getId());
		wmtOrderLineStatusRepository.deleteByOrderId(wmtOrder.getId());
		// 更新订单
		Long orderId = wmtOrderRepository.update(wmtOrder);
		// 插入商品
		List<Long> orderLineIds = wmtOrderLineRepository.batchInsert(itemList, orderId);

		List<WmtOrderLineStatus> wmtOrderLineStatusList = Lists.newArrayList();
		for (int i = 0; i < orderLineIds.size(); i++) {
			Long orderLineId = orderLineIds.get(i);
			wmtOrderLineStatusList.addAll(itemList.get(i).getWmtOrderLineStatusList().stream().map(wmOrderLineStatus -> {
				wmOrderLineStatus.setOmsWmtOrderId(orderId);
				wmOrderLineStatus.setOmsWmtOrderLineId(orderLineId);
				return wmOrderLineStatus;
			}).collect(Collectors.toList()));

		}
		// 插入商品行状态
		wmtOrderLineStatusRepository.batchInsert(wmtOrderLineStatusList);
		// 返回主键增加日志
		return R.success(orderId);
	}

	public WmtOrderLog getLogById(Long orderId) {
		// 查询订单
		WmtOrder wmtOrder = wmtOrderRepository.getById(orderId);
		if (Objects.isNull(wmtOrder)) {
			return null;
		}
		WmtOrderLog wmtOrderLog = wmtOrderLogConvertor.toLog(wmtOrder);
		// 查询商品
		List<WmtOrderLine> wmtOrderLineList = wmtOrderLineRepository.listByOrderId(orderId);
		// 查询商品状态行
		Map<Long, List<WmtOrderLineStatus>> wmOrderLineStatusMap = wmtOrderLineStatusRepository.mapByOrderLineId(orderId);
		wmtOrderLog.setWmtOrderLineLogList(wmtOrderLineList.stream().map(wmOrderLine -> {
			WmtOrderLineLog wmtOrderLineLog = wmtOrderLineLogConvertor.toLog(wmOrderLine);
			if (wmOrderLineStatusMap.containsKey(wmOrderLine.getId())) {
				wmtOrderLineLog
					.setWmtOrderLineStatusLogList(wmOrderLineStatusMap.get(wmOrderLine.getId()).stream().map(wmOrderLineStatus -> {
						return wmtOrderLineStatusLogConvertor.toLog(wmOrderLineStatus);
					}).collect(Collectors.toList()));
			}
			return wmtOrderLineLog;
		}).collect(Collectors.toList()));
		return wmtOrderLog;
	}

}
