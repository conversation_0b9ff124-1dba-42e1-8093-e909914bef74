package com.renpho.erp.oms.application.ordershipaudit.converter;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentStatus;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.config.NacosOrderConfig;

import lombok.RequiredArgsConstructor;

/**
 * @desc:
 * @time: 2025-04-09 16:02:40
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class LogisticsShipmentConvertor {
    private final NacosOrderConfig nacosOrderConfig;

    /**
     * 创建物流下单聚合根
     *
     * @param shipAuditAggRoot 发货审核聚合根
     * @param saleOrderAggRoot 销售订单聚合根
     * @return LogisticsShipmentAggRoot
     */
    public LogisticsShipmentAggRoot createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot) {
        return LogisticsShipmentAggRoot.builder()
                .auditId(shipAuditAggRoot.getId())
                .orderId(saleOrderAggRoot.getId().getId())
                .storeId(saleOrderAggRoot.getStore().getStoreId())
                .fulfillmentServiceType(shipAuditAggRoot.getFulfillmentServiceType().getValue())
                .logisticsOrderNo(saleOrderAggRoot.getFulfillment().getLogisticsOrderNo())
                .waybillFileCurrentFailCount(0)
                .status(LogisticsShipmentStatus.PENDING.getValue())
                .currentFailCount(0)
                .maxFailCount(nacosOrderConfig.getMaxFetchLogisticsResultRetries())
                .build();
    }
}
