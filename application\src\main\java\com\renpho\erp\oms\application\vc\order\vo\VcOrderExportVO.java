package com.renpho.erp.oms.application.vc.order.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import com.renpho.erp.oms.infrastructure.common.excel.TimeZoneLocalDateTimeStringConverter;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * VC订单导出VO
 * <AUTHOR>
 */
@Data
@Schema(description = "VC订单导出")
public class VcOrderExportVO implements VO {

    @ExcelIgnore
    private Long id;

    @ExcelHeadName(zhCnName = "订单号", enName = "SO #")
    private String orderNo;

    @ExcelHeadName(zhCnName = "VCPO", enName = "VCPO")
    private String vcPurchaseNo;

    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String storeName;

    @ExcelHeadName(zhCnName = "订单状态", enName = "Order Status")
    private String orderStatusName;

    @ExcelHeadName(zhCnName = "下单时间", enName = "Ordered Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime orderedTime;

    @ExcelHeadName(zhCnName = "接单时间", enName = "Accepted Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime acceptedTime;

    @ExcelHeadName(zhCnName = "发货时间", enName = "Shipped Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime shippedTime;

    @ExcelHeadName(zhCnName = "开票时间", enName = "Invoiced Time")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime invoicedTime;

    @ExcelHeadName(zhCnName = "发货地", enName = "Ship From")
    private String shipFrom;

    @ExcelHeadName(zhCnName = "收货地", enName = "Ship To")
    private String shipTo;

    @ExcelHeadName(zhCnName = "发货窗口开始", enName = "Shipping Windows Start")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime shippingWindowStart;

    @ExcelHeadName(zhCnName = "发货窗口结束", enName = "Shipping Windows End")
    @ExcelProperty(converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime shippingWindowEnd;

    @ExcelHeadName(zhCnName = "异常信息", enName = "Except. Reason")
    private String exceptionReason;

    @ExcelHeadName(zhCnName = "备注", enName = "Remark")
    private String remark;

    @ExcelHeadName(zhCnName = "PSKU", enName = "PSKU")
    private String psku;

    @ExcelHeadName(zhCnName = "ASIN", enName = "ASIN")
    private String asin;

    @ExcelHeadName(zhCnName = "条码", enName = "Barcode")
    private String barcode;

    @ExcelHeadName(zhCnName = "下单数量", enName = "Quantity Ordered")
    private Integer orderedQuantity;

    @ExcelHeadName(zhCnName = "接单数量", enName = "Quantity Accepted")
    private Integer acceptedQuantity;

    @ExcelHeadName(zhCnName = "发货数量", enName = "Quantity Shipped")
    private Integer shippedQuantity;

    @ExcelHeadName(zhCnName = "总收数量", enName = "Quantity Ordered")
    private Integer receivedQuantity;

    @ExcelHeadName(zhCnName = "单价", enName = "Unit Price")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal unitPrice;

    @ExcelHeadName(zhCnName = "税额", enName = "Tax")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal tax;

    @ExcelHeadName(zhCnName = "小计", enName = "Sub Total")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal subTotal;

    @ExcelHeadName(zhCnName = "合计", enName = "Total Amount")
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalAmount;

    @ExcelIgnore
    private String currency;

    @ExcelIgnore
    @Trans(type = TransType.DICTIONARY, key = "VcOrderStatus", ref = "orderStatusName")
    private Integer orderStatus;
}
