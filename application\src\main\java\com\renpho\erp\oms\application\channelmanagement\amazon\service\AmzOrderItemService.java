package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.amazon.convert.AmzAppOrderItemConvertor;
import com.renpho.erp.oms.application.channelmanagement.amazon.vo.AmzOrderItemVO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderItem;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderItemRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class AmzOrderItemService {
	private final AmzOrderItemRepository amzOrderItemRepository;
	private final AmzAppOrderItemConvertor amzAppOrderItemConvertor;

	public R<List<AmzOrderItemVO>> listByOrderId(Long orderId) {
		List<AmzOrderItem> amzOrderItemList = amzOrderItemRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(amzOrderItemList)) {
			return R.success(amzOrderItemList.stream()
				.map(amazonOrderItem -> amzAppOrderItemConvertor.toVO(amazonOrderItem))
				.collect(Collectors.toList()));
		}
		return R.success();

	}
}
