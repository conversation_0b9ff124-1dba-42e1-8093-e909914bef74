package com.renpho.erp.oms.application.channelmanagement.tiktok.dto;

import lombok.Data;

import java.util.List;

@Data
public class TtOrderDTO {

	/**
	 * 同步时间
	 */
	private String syncTime;
	private Integer storeId;
	private String shopId;
	private String id;
	private String buyer_message;
	private String cancellation_initiator;
	private String shipping_provider_id;
	private Integer create_time;
	private String shipping_provider;
	private List<Package> next_page_token;
	private Payment payment;
	private Address recipient_address;
	private String status;
	private String fulfillment_type;
	private String delivery_type;
	private Integer paid_time;
	private Integer rts_sla_time;
	private Integer tts_sla_time;
	private String cancel_reason;
	private Integer update_time;
	private String payment_method_name;
	private Integer rts_time;
	private String tracking_number;
	private String split_or_combine_tag;
	private Boolean has_updated_recipient_address;
	private Integer cancel_order_sla_time;
	private String warehouse_id;
	private Integer request_cancel_time;
	private String shipping_type;
	private String user_id;
	private String seller_note;
	private Integer delivery_sla_time;
	private Boolean is_cod;
	private String delivery_option_id;
	private Integer cancel_time;
	private String need_upload_invoice;
	private String delivery_option_name;
	private String cpf;
	private List<Item> line_items;
	private String buyer_email;
	private Integer delivery_due_time;
	private Boolean is_sample_order;
	private Integer shipping_due_time;
	private Integer collection_due_time;
	private Integer delivery_option_required_delivery_time;
	private Boolean is_on_hold_order;
	private Integer delivery_time;
	private Boolean is_replacement_order;
	private Integer collection_time;
	private String replaced_order_id;
	private Boolean is_buyer_request_cancel;
	private Integer pick_up_cut_off_time;
	private Integer fast_dispatch_sla_time;
	private String commerce_platform;
	private String order_type;
	private Integer release_date;
	private Duration handling_duration;

	@Data
	public static class Package {
		private String id;
	}

	@Data
	public static class Payment {
		private String currency;
		private String sub_total;
		private String shipping_fee;
		private String seller_discount;
		private String platform_discount;
		private String total_amount;
		private String original_total_product_price;
		private String original_shipping_fee;
		private String shipping_fee_seller_discount;
		private String shipping_fee_platform_discount;
		private String tax;
		private String small_order_fee;
		private String shipping_fee_tax;
		private String product_tax;
		private String buyer_service_fee;
		private String handling_fee;
		private String shipping_insurance_fee;
		private String item_insurance_fee;
		private String retail_delivery_fee;
	}

	@Data
	public static class Address {
		private List<DistrictInfo> district_info;
		private String postal_code;
		private String region_code;
	}

	@Data
	public static class DistrictInfo {
		private String address_level_name;
		private String address_name;
		private String address_level;
	}

	@Data
	public static class Item {
		private String id;
		private String sku_id;
		private List<Sku> combined_listing_skus;
		private String display_status;
		private String product_name;
		private String seller_sku;
		private String sku_image;
		private String sku_name;
		private String product_id;
		private String sale_price;
		private String platform_discount;
		private String seller_discount;
		private String sku_type;
		private String cancel_reason;
		private String original_price;
		private Integer rts_time;
		private String package_status;
		private String currency;
		private String shipping_provider_name;
		private String cancel_user;
		private String shipping_provider_id;
		private Boolean is_gift;
		private List<Tax> item_tax;
		private String tracking_number;
		private String package_id;
		private String retail_delivery_fee;
		private String buyer_service_fee;
		private String small_order_fee;
		private String handling_duration_days;

	}

	@Data
	public static class Duration {
		private String days;
		private String type;
	}

	@Data
	public static class Sku {
		private String sku_id;
		private Integer sku_count;
		private String product_id;
		private String seller_sku;
	}

	@Data
	public static class Tax {
		private String tax_type;
		private String tax_amount;
		private String tax_rate;
	}

}
