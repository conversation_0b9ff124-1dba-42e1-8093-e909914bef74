package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor;

import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingExportExcel;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingImportExcel;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.vo.MulSkuMappingPageVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MulSkuMappingExcelConvertor {

	SkuMapping toDomain(MulSkuMappingImportExcel param);

	@Mapping(target = "updater", expression = "java(param.getUpdater()  +  '('  + param.getUpdaterNo() + ')'  )")
	@Mapping(target = "mulChannelType",
			expression = "java(com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType.enumOf(param.getMulChannelType()).getName())")
	@Mapping(target = "status", expression = "java(param.getStatus() == null ? null : (param.getStatus() == 1 ? \"启用\" : \"禁用\"))")
	@Mapping(target = "mulChannelStore", expression = "java(param.getMulChannelStoreName())")
	MulSkuMappingExportExcel toExcel(MulSkuMappingPageVO param);
}
