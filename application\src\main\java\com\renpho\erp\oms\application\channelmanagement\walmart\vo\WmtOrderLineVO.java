package com.renpho.erp.oms.application.channelmanagement.walmart.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class WmtOrderLineVO {
	/**
	 * 商品行号
	 */
	private String lineNumber;

	/**
	 * 卖方SKU
	 */
	private String itemSku;

	/**
	 * 商品数量
	 */
	private String orderLineQuantityAmount;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 商品收入
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal priceAmount;

	/**
	 * 商品收入税
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal priceTaxAmount;

	/**
	 * 运费收入
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingAmount;

	/**
	 * 运费收入税
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingTaxAmount;
}
