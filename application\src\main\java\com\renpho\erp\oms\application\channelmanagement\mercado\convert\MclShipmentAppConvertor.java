package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import java.util.Objects;

import cn.hutool.core.util.StrUtil;
import org.apache.shenyu.common.utils.DateUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentDTO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;

import cn.hutool.core.collection.CollectionUtil;

public class MclShipmentAppConvertor {
	public static MclShipment toDomain(MclShipmentDTO mclShipmentDTO) {
		if (Objects.nonNull(mclShipmentDTO)) {
			MclShipment mclShipment = new MclShipment();
			mclShipment.setMclUserId(mclShipmentDTO.getMclUserId());
			mclShipment.setStoreId(mclShipmentDTO.getStoreId());
			mclShipment.setMclShipmentId(mclShipmentDTO.getId());
			mclShipment.setOrderId(mclShipmentDTO.getOrder_id());
			mclShipment.setStatus(mclShipmentDTO.getStatus());
			mclShipment.setSubstatus(mclShipmentDTO.getSubstatus());
			mclShipment.setDeclaredValue(mclShipmentDTO.getDeclared_value());
			mclShipment.setCurrencyId(mclShipmentDTO.getCurrency_id());
			mclShipment.setDateCreated(
					Objects.nonNull(mclShipmentDTO.getDate_created()) ? DateUtil.parseISOStr(mclShipmentDTO.getDate_created()) : null);
			mclShipment.setLastUpdated(
					Objects.nonNull(mclShipmentDTO.getLast_updated()) ? DateUtil.parseISOStr(mclShipmentDTO.getLast_updated()) : null);
			mclShipment.setTrackingNumber(mclShipmentDTO.getTracking_number());
			mclShipment.setTrackingMethod(mclShipmentDTO.getTracking_method());
			mclShipment.setOrigin(Objects.nonNull(mclShipmentDTO.getOrigin()) ? JsonUtils.toJson(mclShipmentDTO.getOrigin()) : null);
			// 发货信息
			if (Objects.nonNull(mclShipmentDTO.getDestination())) {
				MclShipmentDTO.Destination destination = mclShipmentDTO.getDestination();
				if (Objects.nonNull(destination.getShipping_address())) {
					MclShipmentDTO.ShippingAddress shippingAddress = destination.getShipping_address();
					mclShipment.setZipCode(shippingAddress.getZip_code());
					if (Objects.nonNull(shippingAddress.getCity())) {
						mclShipment.setCityId(shippingAddress.getCity().getId());
						mclShipment.setCityName(shippingAddress.getCity().getName());
					}
					if (Objects.nonNull(shippingAddress.getState())) {
						mclShipment.setStateId(shippingAddress.getState().getId());
						mclShipment.setStateName(shippingAddress.getState().getName());
					}
					if (Objects.nonNull(shippingAddress.getCountry())) {
						mclShipment.setCountryId(shippingAddress.getCountry().getId());
						mclShipment.setCountryName(shippingAddress.getCountry().getName());
					}
				}
			}
			// 尺寸信息
			if (Objects.nonNull(mclShipmentDTO.getDimensions())) {
				MclShipmentDTO.Dimensions dimensions = mclShipmentDTO.getDimensions();
				mclShipment.setHeight(dimensions.getHeight());
				mclShipment.setLength(dimensions.getLength());
				mclShipment.setWidth(dimensions.getWidth());
				mclShipment.setWeight(dimensions.getWeight());
			}
			mclShipment.setExternalReference(mclShipmentDTO.getExternal_reference());
			mclShipment
				.setLeadTime(Objects.nonNull(mclShipmentDTO.getLead_time()) ? JsonUtils.toJson(mclShipmentDTO.getLead_time()) : null);
			mclShipment.setSiteId(Objects.nonNull(mclShipmentDTO.getSource()) ? mclShipmentDTO.getSource().getSite_id() : null);
			// 物流信息
			if (Objects.nonNull(mclShipmentDTO.getLogistic())) {
				MclShipmentDTO.Logistic logistic = mclShipmentDTO.getLogistic();
				mclShipment.setLogisticMode(logistic.getMode());
				mclShipment.setLogisticType(logistic.getType());
				mclShipment.setLogisticDirection(logistic.getDirection());
			}
			mclShipment.setTags(CollectionUtil.isNotEmpty(mclShipmentDTO.getTags()) ? JsonUtils.toJson(mclShipmentDTO.getTags()) : null);
			mclShipment.setSenderId(mclShipmentDTO.getSender_id());
			// shipment的状态历史集
			mclShipment.setShipmentStatusHistory(mclShipmentDTO.getShipmentStatusHistory());
			mclShipment.setLastMpdsSyncShipmentTime(
					StrUtil.isNotEmpty(mclShipmentDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(mclShipmentDTO.getSyncTime()) : null);
			return mclShipment;

		}
		return null;
	}
}
