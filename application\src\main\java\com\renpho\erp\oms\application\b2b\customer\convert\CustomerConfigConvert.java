package com.renpho.erp.oms.application.b2b.customer.convert;

import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.mdm.client.vo.LanguageNameVo;
import com.renpho.erp.oms.application.b2b.customer.vo.IncotermsVO;
import com.renpho.erp.oms.application.b2b.customer.vo.PaymentTermsVO;
import com.renpho.erp.oms.application.b2b.customer.vo.SalesCompanyVO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.IncotermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.PaymentTermsConfigPO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Collections;
import java.util.Optional;

/**
 * @desc: 客户配置转换
 * @time: 2025-04-30 12:11:18
 * @author: Alina
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface CustomerConfigConvert {

	@Mapping(target = "companyName", expression = "java(getCompanyName(companyVo))")
	SalesCompanyVO toSalesCompanyVO(CompanyVo companyVo);

	default String getCompanyName(CompanyVo companyVo) {
		if (companyVo == null) {
			return null;
		}
		String language = Optional.of(LocaleContextHolder.getLocale()).map(locale -> locale.toLanguageTag()).orElse("zh-CN");

		return Optional.ofNullable(companyVo.getCompanyName())
			.orElse(Collections.emptyList())
			.stream()
			.filter(ln -> language.equals(ln.getLanguage()))
			.findFirst()
			.map(LanguageNameVo::getName)
			.orElse("");
	}

	IncotermsVO toIncotermsVO(IncotermsConfigPO incotermsConfigPo);

	PaymentTermsVO toPaymentTermsVO(PaymentTermsConfigPO paymentTermsConfigPo);
}
