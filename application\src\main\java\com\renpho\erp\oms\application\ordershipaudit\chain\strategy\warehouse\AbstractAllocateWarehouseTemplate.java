package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.InitializingBean;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.application.ordershipaudit.service.OrderInventoryQueryService;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.AutoShipAuditConfig;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.Warehouse;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;

import cn.hutool.extra.spring.SpringUtil;

/**
 * 抽象-分仓模板
 *
 * <AUTHOR>
 */
public abstract class AbstractAllocateWarehouseTemplate implements AllocateWarehouse, InitializingBean {

	private static final String SHIP_AUDIT_RULE_FAIL_MSG = "当前订单没有配置发货审核规则，或者不满足配置规则";

	private final WarehouseClient warehouseClient;
	private final OrderInventoryQueryService orderInventoryQueryService;

	protected AbstractAllocateWarehouseTemplate(WarehouseClient warehouseClient, OrderInventoryQueryService orderInventoryQueryService) {
		this.warehouseClient = warehouseClient;
		this.orderInventoryQueryService = orderInventoryQueryService;
	}

	/**
	 * 分仓
	 * @param orderAutoShipAuditDTO 订单-自动发货审核DTO
	 * @param autoShipAuditConfigs 自动发货审核配置
	 * @return String 分仓失败原因
	 */
	@Override
	public String allocateWarehouse(OrderAutoShipAuditDTO orderAutoShipAuditDTO, List<AutoShipAuditConfig> autoShipAuditConfigs) {
		// 检查当前订单是否没有配置发货审核规则，或者不满足配置规则
		if (!this.checkAutoShipAuditConfig(orderAutoShipAuditDTO, autoShipAuditConfigs)) {
			return SHIP_AUDIT_RULE_FAIL_MSG;
		}
		// 获取有序优先级的仓库编码列表
		List<String> warehouseCodes = this.getOrderedPriorityWarehouseCodes(orderAutoShipAuditDTO.getSkuCombinationType(),
				autoShipAuditConfigs);
		// 仓库不存在
		if (CollectionUtils.isEmpty(warehouseCodes)) {
			return SHIP_AUDIT_RULE_FAIL_MSG;
		}
		// 仓库Map,key为仓库编码，value为Warehouse
		Map<String, Warehouse> warehouseMap = warehouseClient.getByCodes(warehouseCodes);
		String failMsg = null;
		// 按仓库优先级，依次匹配
		for (String warehouseCode : warehouseCodes) {
			// 仓库信息
			Warehouse warehouse = warehouseMap.get(warehouseCode);
			// 检查仓库及库存是否充足
			failMsg = this.checkWarehouseAndInventory(warehouse, orderAutoShipAuditDTO.getSaleOrderAggRoot());
			if (StringUtils.isBlank(failMsg)) {
				// 分仓成功
				orderAutoShipAuditDTO.setWarehouse(warehouse);
				XxlJobHelper.log("订单号：{}，分仓成功，仓库：{}", orderAutoShipAuditDTO.getOrderNo(), JSONKit.toJSONString(warehouse));
				return null;
			}
		}
		return failMsg;
	}

	/**
	 * 检查仓库及库存是否充足
	 * @param warehouse 仓库
	 * @param orderAggRoot 订单聚合根
	 * @return 失败原因
	 */
	private String checkWarehouseAndInventory(Warehouse warehouse, SaleOrderAggRoot orderAggRoot) {
		if (Objects.isNull(warehouse)) {
			return "发货审核配置的仓库不存在";
		}
		// 仓库是否禁用
		if (warehouse.isDisable()) {
			return "发货审核配置的仓库状态为禁用";
		}
		// 仓库类型是否支持
		if (!warehouse.isThirdOrSelfWarehouse()) {
			return "发货审核配置的仓库类型不支持";
		}
		// 查询ims，检查库存是否充足
		boolean isSufficient = orderInventoryQueryService.queryInventory(orderAggRoot, warehouse.getWarehouseId());
		if (!isSufficient) {
			return "发货审核配置的仓库里没有可用库存";
		}
		return null;
	}

	/**
	 * 当前订单是否没有配置发货审核规则，或者不满足配置规则
	 *
	 * @param orderAutoShipAuditDTO 订单-自动发货审核DTO
	 * @param autoShipAuditConfigs 自动发货审核配置规则
	 * @return boolean
	 */
	private boolean checkAutoShipAuditConfig(OrderAutoShipAuditDTO orderAutoShipAuditDTO, List<AutoShipAuditConfig> autoShipAuditConfigs) {
		// 没有配置发货审核规则
		if (CollectionUtils.isEmpty(autoShipAuditConfigs)) {
			return false;
		}
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAuditDTO.getSaleOrderAggRoot();
		// 订单psku集（非延保商品）
		Set<String> notExtendWarrantyPskus = orderAggRoot.getNotExtendWarrantyPskus();
		// psku都配置了规则
		return notExtendWarrantyPskus.size() == autoShipAuditConfigs.size();
	}

	/**
	 * 获取有序优先级的仓库编码列表
	 *
	 * @param skuCombinationType sku组合类型
	 * @param autoShipAuditConfigs 自动发货审核配置
	 * @return List
	 */
	protected abstract List<String> getOrderedPriorityWarehouseCodes(SkuCombinationType skuCombinationType,
			List<AutoShipAuditConfig> autoShipAuditConfigs);

	@Override
	public void afterPropertiesSet() throws Exception {
		// sku组合类型集
		Set<SkuCombinationType> skuCombinationTypes = this.getSkuCombinationTypes();
		// 使用代理对象，不用this，避免事务失效等问题
		skuCombinationTypes
			.forEach(skuCombinationType -> AllocateWarehouseFactory.register(skuCombinationType, SpringUtil.getBean(this.getClass())));
	}
}
