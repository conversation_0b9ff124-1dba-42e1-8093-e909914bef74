package com.renpho.erp.oms.application.channelmanagement.monitor;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

/**
 * 渠道订单监控定时任务
 * <AUTHOR>
 */
@Component
public class ChannelOrderMonitorJob {

	/**
	 * 监控渠道订单
	 */
	@XxlJob("monitorChannelOrder")
	public void monitorChannelOrder() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		ChannelOrderMonitor.MonitorCmd cmd = JSONKit.parseObject(jobParam, ChannelOrderMonitor.MonitorCmd.class);
		// 参数校验
		cmd.validate();
		// 渠道订单监控
		ChannelOrderMonitorFactory.get(cmd.getChannelCode()).monitor(cmd);
	}

}
