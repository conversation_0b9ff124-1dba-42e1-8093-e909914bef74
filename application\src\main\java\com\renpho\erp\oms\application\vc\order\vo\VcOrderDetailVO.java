package com.renpho.erp.oms.application.vc.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 12:15
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class VcOrderDetailVO{

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单基本信息
     */
    private OrderBaseInfo orderBaseInfo;

    /**
     * 订单DI信息
     */
    private DIInfo diInfo;

    /**
     * 金额信息
     */
    private AmountInfo amountInfo;

    /**
     * 备注
     */
    private List<RemarkInfo> remarkList;

    /**
     * 商品信息
     */
    private List<ProductInfo> productList;

    /**
     * 订单基本信息
     */
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class OrderBaseInfo implements VO{

        /**
         * 订单ID
         */
        private Long id;

        /**
         * 订单状态 1-待接单 2-接单异常 3-待上传箱唛 4-备货中 5-待开票 6-已完成 7-已取消
         */
        @Trans(type = TransType.DICTIONARY, key = "VcOrderStatus", ref = "orderStatusName")
        private Integer orderStatus;

        /**
         * 订单状态名称
         */
        private String orderStatusName;
        /**
         * 接单子状态 1-未接单 2-接单确认中 3-已接单 4-接单异常
         */
        @Trans(type = TransType.DICTIONARY, key = "VcOrderAcceptedStatus", ref = "acceptedStatusName")
        private Integer acceptedStatus;

        /**
         * 接单子状态名称
         */
        private String acceptedStatusName;


        /**
         * 订单编号
         */
        private String orderNo;

        /**
         * 采购单号
         */
        private String vcPurchaseNo;

        /**
         * 业务类型 1-DI（直接进口） 2-DO（海外仓备货模式）
         */
        @Trans(type = TransType.DICTIONARY, key = "VcOrderBusinessType", ref = "businessTypeName")
        private Integer businessType;

        /**
         * 业务类型名称
         */
        private String businessTypeName;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 修改时间
         */
        private LocalDateTime updateTime;

        /**
         * 下单时间
         */
        private LocalDateTime orderedTime;

        /**
         * 接单时间
         */
        private LocalDateTime acceptedTime;

        /**
         * 发货时间
         */
        private LocalDateTime shippedTime;

        /**
         * 开票时间
         */
        private LocalDateTime invoicedTime;

        /**
         * 取消时间
         */
        private LocalDateTime cancelledTime;

        /**
         * 订单类型
         */
        private String orderType;

        /**
         * 发票类型
         */
        private String invoiceType;

        /**
         * 发货地
         */
        private String shipFrom;

        /**
         * 收货地
         */
        private String shipTo;

        /**
         * 交货窗口-起始时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime shippingWindowStart;

        /**
         * 交货窗口-结束时间
         */
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDateTime shippingWindowEnd;



        /**
         * 交货窗口
         */
        private String shipWindowStr;
    }

    /**
     * 订单DI信息
     */
    @Data
    @Builder
    public static class DIInfo{
        /**
         * 付款方式
         */
        private String paymentMethod;

        /**
         * 贸易条款
         */
        private String incoterms;

        /**
         * 集装箱规格
         */
        private String containerType;

        /**
         * 发货描述
         */
        private String shippingInstructions;
    }

    /**
     * 金额信息
     */
    @Data
    @Builder
    public static class AmountInfo {

        /**
         * 商品收入
         */
        private BigDecimal productPrice;

        /**
         * 币种
         */
        private String currency;

        /**
         * 税率,20代表20%
         */
        private BigDecimal taxRate;

        /**
         * 税额
         */
        private BigDecimal tax;
    }

    /**
     * 备注信息
     */
    @Data
    @Builder
    public static class RemarkInfo{

        /**
         * 订单备注
         */
        private String remark;

        /**
         * 创建时间
         */
        private LocalDateTime createTime;

        /**
         * 创建人
         */
        private Integer createBy;

        /**
         * 创建人姓名
         */
        private String creator;

        /**
         * 创建人工号
         */
        private String creatorNo;
    }

    /**
     * 商品信息
     */
    @Data
    @Builder
    public static class ProductInfo{

        /**
         * 采购SKU
         */
        private String psku;

        /**
         * 采购ASIN
         */
        private String asin;

        /**
         * 商品条码
         */
        private String barcode;


        /**
         * 商品图片ID
         */
        private String imageId;

        /**
         * 商品图片URL
         */
        private String imageUrl;

        /**
         * 下单数量
         */
        private Integer orderedQuantity;

        /**
         * 接受数量
         */
        private Integer acceptedQuantity;

        /**
         * 发货数量
         */
        private Integer shippedQuantity;

        /**
         * 接收数量
         */
        private Integer receivedQuantity;

        /**
         * 币种
         */
        private String currency;

        /**
         * 单价
         */
        private BigDecimal unitPrice;

        /**
         * 税额
         */
        private BigDecimal tax;

        /**
         * 小计
         */
        private BigDecimal subTotal;
    }
}
