package com.renpho.erp.oms.application.listingmanagement.strategy.parser;

import com.baomidou.lock.LockInfo;
import com.baomidou.lock.LockTemplate;
import com.baomidou.lock.executor.RedissonLockExecutor;
import com.renpho.erp.oms.application.listingmanagement.executor.SalesChannelListingSaveExecutor;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMapping;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.util.MD5Util;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.karma.json.JSONKit;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽象Listing解析器模板
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractListingParserTemplate<T> implements ListingParser {

	@Resource
	protected SkuMappingRepository skuMappingRepository;
	@Resource
	private ProductClient productClient;
	@Resource
	private SalesChannelListingSaveExecutor salesChannelListingSaveExecutor;
	@Resource
	private SalesChannelListingSourceRepository channelListingSourceRepository;
	@Resource
	private LockTemplate lockTemplate;

	@Override
	public final void parseListing(SalesChannelListingSource source) {
		// 锁等待时间设置为0，无需等待，Redisson的leaseTime为-1时，才会有看门狗续期任务
		final LockInfo lockInfo = lockTemplate.lock("oms:listing:parse:lock:" + source.getId(), -1L, 0L, RedissonLockExecutor.class);
		// 获取锁失败
		if (Objects.isNull(lockInfo)) {
			return;
		}
		try {
			// 解析Listing
			this.parse(source);
		} finally {
			// 释放锁
			lockTemplate.releaseLock(lockInfo);
		}
	}

	/**
	 * 解析Listing
	 * @param source 原始Listing
	 */
	private void parse(SalesChannelListingSource source) {
		log.info("销售渠道编码：{}，原始listing主键id：{}，开始解析：{}", source.getSalesChannelCode(), source.getId(), source.getContent());
		// 1.反序列化
		T t = JSONKit.parseObject(source.getContent(), this.getClazz());
		// 2.解析原始Listing，输出Listing数据集
		List<SalesChannelListing> salesChannelListingList = this.parseSourceListing(source, t);
		// 3.填补唯一 bs id
		this.fillUniqueBsId(salesChannelListingList);
		// 4.遍历Listing数据集，对比关键字段是否变动，保存修改处理
		this.saveSalesChannelListing(source, salesChannelListingList);
	}

	/**
	 * 填补唯一 bs id
	 *
	 * @param list Listing数据集
	 */
	protected void fillUniqueBsId(List<SalesChannelListing> list) {
		if (CollectionUtils.isEmpty(list)) {
			return;
		}
		StringBuilder sb = new StringBuilder();
		for (SalesChannelListing salesChannelListing : list) {
			String input;
			// temu,ebay的唯一值跟其他渠道不一样
            if (Set.of(SaleChannelType.TEMU.getValue(), SaleChannelType.EBAY.getValue()).contains(salesChannelListing.getSalesChannelCode())) {
				input = sb.append(salesChannelListing.getSalesChannelId())
						.append(Constants.STRING_JOINER)
						.append(salesChannelListing.getStoreId())
						.append(Constants.STRING_JOINER)
						.append(salesChannelListing.getItem())
						.toString();
            } else {
				input = sb.append(salesChannelListing.getSalesChannelId())
						.append(Constants.STRING_JOINER)
						.append(salesChannelListing.getStoreId())
						.append(Constants.STRING_JOINER)
						.append(salesChannelListing.getItem())
						.append(Constants.STRING_JOINER)
						.append(salesChannelListing.getMsku())
						.toString();
			}
			// 如果考虑 md5 的冲突概率 可以改其他 sha-256
			salesChannelListing.setBsUniqueCode(MD5Util.getMd5(input));
			sb.setLength(0);
		}
	}

	/**
	 * 解析原始Listing，输出Listing数据集
	 *
	 * @param t 原始Listing
	 * @return Listing数据集
	 */
	protected abstract List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, T t);

	/**
	 * 遍历Listing数据集，对比关键字段是否变动，保存修改处理
	 *
	 * @param source 原始Listing
	 * @param salesChannelListingList Listing数据集
	 */
	protected void saveSalesChannelListing(SalesChannelListingSource source, List<SalesChannelListing> salesChannelListingList) {
		if (CollectionUtils.isEmpty(salesChannelListingList)) {
			// 无Listing数据集，标记解析状态为作废
			channelListingSourceRepository.updateResolveStatusToCancel(Set.of(source.getId()));
			return;
		}
		// 遍历，对比关键字段是否变动，保存修改处理，一条Listing一个事务
		salesChannelListingList.forEach(listing -> salesChannelListingSaveExecutor.saveSalesChannelListing(listing));
	}

	/**
	 * 获取泛型的class对象
	 *
	 * @return Class
	 */
	@SuppressWarnings({ "unchecked" })
	private Class<T> getClazz() {
		Type superclass = this.getClass().getGenericSuperclass();
		if (superclass instanceof ParameterizedType parameterizedtype) {
			Type type = parameterizedtype.getActualTypeArguments()[0];
			return (Class<T>) type;
		}
		throw new IllegalArgumentException("Unsupported Type: " + superclass);
	}

	/**
	 * 构建销售渠道Listing
	 * @param source 原始Listing
	 * @return SalesChannelListing
	 */
	protected @NotNull SalesChannelListing buildSalesChannelListing(SalesChannelListingSource source,
			Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap, Map<String, String> purchaseSkuImageMap, String uniqueValue, Integer storeId) {
		SalesChannelListing listing = new SalesChannelListing();
		// 原始listing主键ID
		listing.setListingSourceId(source.getId());
		// 销售渠道ID
		listing.setSalesChannelId(source.getSalesChannelId());
		// 销售渠道编码
		listing.setSalesChannelCode(this.getSalesChannelCode());
		// 销售渠道名称
		listing.setSalesChannelName(source.getSalesChannelName());
		// 店铺ID
		listing.setStoreId(source.getStoreId());
		// 店铺名称
		listing.setStoreName(source.getStoreName());
		// sku映射列表
		List<SkuMappingFulfillmentVO> skuMappingList = skuMappingListMap.get(storeId + "-" + uniqueValue);
		if (CollectionUtils.isNotEmpty(skuMappingList)) {
			// 采购sku列表
			List<String> purchaseSkuList = skuMappingList.stream()
				.map(SkuMappingFulfillmentVO::getPurchaseSku)
				.filter(StringUtils::isNotBlank)
				.distinct()
				.toList();
			if (CollectionUtils.isNotEmpty(purchaseSkuList)) {
				// 采购sku，如果有多个，逗号隔开
				listing.setPsku(String.join(",", purchaseSkuList));
				// pds的图片id（产品封面图片），如果采购sku有多个，只展示第一个采购sku的图片
				listing.setImageId(purchaseSkuImageMap.get(purchaseSkuList.get(0)));
			}
		}
		return listing;
	}

	/**
	 * 获取sku映射Map
	 * @param storeId 店铺id
	 * @param sellerSkuSet 销售sku集合
	 * @return Map key为 店铺id+销售SKU，value为List
	 */
	protected @NotNull Map<String, List<SkuMappingFulfillmentVO>> getSkuMappingListMap(Integer storeId, Set<String> sellerSkuSet) {
		// sku映射查询条件
		SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
		// 店铺id
		skuMappingQuery.setStoreIdList(List.of(storeId));
		// 销售SKU列表
		skuMappingQuery.setSellerSkuList(sellerSkuSet.stream().filter(StringUtils::isNotBlank).toList());
		// 查询 sku映射列表
		List<SkuMappingFulfillmentVO> skuMappingList = skuMappingRepository.list(skuMappingQuery);
		// sku映射Map，key为 店铺id+销售SKU，value为List<SkuMapping>
		return skuMappingList.stream().collect(Collectors.groupingBy(s -> s.getStoreId() + "-" + s.getSellerSku()));
	}

	/**
	 * 获取采购sku图片Map
	 * @param skuMappings sku映射集合
	 * @return Map key为采购sku，value为产品封面图片
	 */
	protected @NotNull Map<String, String> getPurchaseSkuImageMap(Collection<SkuMappingFulfillmentVO> skuMappings) {
		if (CollectionUtils.isEmpty(skuMappings)) {
			return new HashMap<>();
		}
		// 采购SKU列表
		List<String> purchaseSkuList = skuMappings.stream().map(SkuMappingFulfillmentVO::getPurchaseSku).distinct().toList();
		// 查询pds的采购SKU信息列表
		List<PdsProductManagerBasicViewVo> purchaseSku = productClient.getPurchaseSku(purchaseSkuList);
		// 采购sku图片Map，key为采购sku，value为产品封面图片
		return purchaseSku.stream()
				.filter(Objects::nonNull)
				.filter(p -> ObjectUtils.allNotNull(p.getPurchaseSku(), p.getProductCoverImageId()))
				.collect(Collectors.toMap(PdsProductManagerBasicViewVo::getPurchaseSku, PdsProductManagerBasicViewVo::getProductCoverImageId, (x1, x2) -> x1));
	}

}
