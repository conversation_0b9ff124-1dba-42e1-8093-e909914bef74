package com.renpho.erp.oms.application.channelmanagement.mercado.optlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.service.MclShipmentService;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 *
 * @since 2024/9/24
 */
@Component
@AllArgsConstructor
public class MclShipmentAddSnapSource implements SnapshotDatatSource {

	private final MclShipmentService mclShipmentService;

	@Override
	public JSONObject getOldData(Object[] args) {
		return new JSONObject();
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		MclShipmentLog mclShipmentLog = mclShipmentService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(mclShipmentLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
