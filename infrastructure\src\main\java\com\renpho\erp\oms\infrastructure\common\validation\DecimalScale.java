package com.renpho.erp.oms.infrastructure.common.validation;

import java.lang.annotation.*;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

/**
 * <AUTHOR>
 * @description 小数位校验
 * @date 2025/7/1 11:21
 */
@Documented
@Constraint(validatedBy = DecimalScaleValidator.class)
@Target({ElementType.TYPE, ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
public @interface DecimalScale {

    String message() default "小数位数最多为 {scale} 位";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};

    int scale(); // 要保留的小数位数
}