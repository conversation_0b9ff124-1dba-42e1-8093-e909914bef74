package com.renpho.erp.oms.adapter.web.controller.amazon;

import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderItemService;
import com.renpho.erp.oms.application.channelmanagement.amazon.vo.AmzOrderItemVO;

import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/amazon/**")
@RequestMapping("/amazon/item")
@AllArgsConstructor
public class AmzItemWebController {

	private final AmzOrderItemService amzOrderItemService;

	/**
	 * 亚马逊订单商品列表
	 * @param orderId
	 * @return R<List<AmazonOrderItemVO>>
	 */
	@GetMapping("/list")
	public R<List<AmzOrderItemVO>> listByOrderId(@RequestParam Long orderId) {
		return amzOrderItemService.listByOrderId(orderId);
	}
}
