package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

/**
 * 客户销售公司和协议信息视图对象
 */
@Data
public class CustomerSalesCompanyTermsVO {
    /**
     * ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 销售公司id
     */
    private Integer salesCompanyId;
    
    /**
     * 销售公司
     */
    private String salesCompany;

    /**
     * 销售公司国家
     */
    private String salesCompanyCountry;
    
    /**
     * 贸易条款代码
     */
    private String incotermsCode;

    /**
     * 贸易条款名称
     */
    private String incotermsName;
    
    /**
     * 付款条款代码
     */
    private String paymentTermsCode;

    /**
     * 付款条款名称
     */
    private String paymentTermsName;
} 