package com.renpho.erp.oms.application.vc.order.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Maps;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.vendororders.GetPurchaseOrdersRequest;
import com.renpho.erp.apiproxy.amazon.model.vendororders.GetPurchaseOrdersStatusRequest;
import com.renpho.erp.mdm.client.store.command.StoreAuthorizationQuery;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.vc.order.command.PullVcSourceOrderCmd;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.AmazonApiCodeEnums;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnums;
import com.renpho.erp.oms.infrastructure.common.mdcThreadPool.MdcThreadPoolTaskExecutor;
import com.renpho.erp.oms.infrastructure.common.util.*;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.AmzVcOrderSourceJsonPO;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.service.AmzVcOrderSourceJsonPOService;
import com.renpho.karma.dto.R;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import io.swagger.client.model.vendoRetailProcurement.orders.*;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.DateUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/7 11:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PullVcSourceOrderService {

    private final StoreClient storeClient;

    private final AmazonClient amazonClient;

    private static final Long SLEEP_TIME_MILLISECONDS = 300L;

    private final AmzVcOrderSourceJsonPOService amzVcOrderSourceJsonPOService;

    @Resource(name = "pullVcOrderExecutor")
    private MdcThreadPoolTaskExecutor pullVcOrderExecutor;


    /**
     * 拉取vc源单
     */
    public void pullVcSourceOrder(PullVcSourceOrderCmd pullVcSourceOrderCmd) {
        //获取VC店铺授权
        List<StoreAuthorizationVo> shardingStoreAuthorizations = getVcAuthorStore(pullVcSourceOrderCmd);
        //获取拉取时间
        Pair<LocalDateTime, LocalDateTime> pullTime = getPullTime(pullVcSourceOrderCmd);
        String storeNames = shardingStoreAuthorizations.stream().map(StoreAuthorizationVo::getStoreName).collect(Collectors.joining(","));
        XxlJobHelper.log("拉取VC源单店铺：{},time:{}", storeNames, JSONKit.toJSONString(pullTime));
        //多线程拉取
        List<CompletableFuture<Void>> completableFutureList = shardingStoreAuthorizations.stream()
                .map(storeAuthorizationVo -> handleCompletableFuture(storeAuthorizationVo, pullTime.getKey(), pullTime.getValue()))
                .collect(Collectors.toList());
        // 等待所有异步任务完成
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 处理异步任务
     *
     * @param storeAuthorizationVo
     * @param changedAfter
     * @param changedBefore
     * @return
     */

    private CompletableFuture<Void> handleCompletableFuture(StoreAuthorizationVo storeAuthorizationVo, LocalDateTime changedAfter, LocalDateTime changedBefore) {
        return CompletableFuture
                .runAsync(() -> {
                    log.info("开始拉取vc源单，店铺名称：{},changedAfter:{},changedBefore:{}", storeAuthorizationVo.getStoreName(), changedAfter, changedBefore);
                    //执行拉取
                    pullSingleStoreOrder(storeAuthorizationVo, changedAfter, changedBefore);
                    log.info("结束拉取vc源单，店铺名称：{}", storeAuthorizationVo.getStoreName());
                }, pullVcOrderExecutor)
                .handle((result, ex) -> {
                    if (ex != null) {
                        log.error("拉取vc源单失败，店铺名称：{}", storeAuthorizationVo.getStoreName(), ex);
                    }
                    return null;
                });
    }


    /**
     * 拉取单个店铺的vc源单
     *
     * @param storeAuthorizationVo
     * @param changedAfter
     * @param changedBefore
     */
    private void pullSingleStoreOrder(StoreAuthorizationVo storeAuthorizationVo, LocalDateTime changedAfter, LocalDateTime changedBefore) {
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setSellerId(storeAuthorizationVo.getAuthorization().getAmzSellerId());
        //1.拉取订单
        List<Order> amazonVcOrders = pullVcOrder(shopAccount, changedAfter, changedBefore);
        if (CollUtil.isEmpty(amazonVcOrders)) {
            //无订单
            return;
        }
        //2.查询DB订单
        Map<String, AmzVcOrderSourceJsonPO> dbOrderNumberMap = getExistingOrdersFromDb(amazonVcOrders, storeAuthorizationVo.getId());
        List<AmzVcOrderSourceJsonPO> saveVcOrderList = Lists.newArrayList();
        amazonVcOrders.forEach(order -> {
            //3.拉取订单状态
            OrderStatus orderStatus = pullVcOrderStatus(shopAccount, order.getPurchaseOrderNumber());
            if (Objects.isNull(orderStatus)) {
                log.error("未拉取到订单状态,订单号:{}", order.getPurchaseOrderNumber());
                return;
            }
            //4.比较是否有数据更新
            Boolean isDataUpdate = compareOrderData(order, orderStatus, dbOrderNumberMap);
            if (isDataUpdate) {
                //有数据更新
                saveVcOrderList.add(buildAmzVcOrderSourceJsonPO(storeAuthorizationVo, order, orderStatus));
            }
        });
        //5.保存拉取的数据
        amzVcOrderSourceJsonPOService.savePullOrder(saveVcOrderList, storeAuthorizationVo.getId());
    }

    /**
     * 获取VC店铺授权
     *
     * @param pullVcSourceOrderCmd
     * @return
     */
    private List<StoreAuthorizationVo> getVcAuthorStore(PullVcSourceOrderCmd pullVcSourceOrderCmd) {
        return storeClient.getShardingStoreAuthorizations(buildStoreAuthorizationQuery(pullVcSourceOrderCmd))
                .stream()
                .filter(x -> Objects.nonNull(x.getAuthorization()) && StrUtil.isNotEmpty(x.getAuthorization().getAmzSellerId()))
                .collect(Collectors.toList());
    }

    /**
     * 构建店铺授权查询参数
     *
     * @param pullVcSourceOrderCmd
     * @return
     */
    private StoreAuthorizationQuery buildStoreAuthorizationQuery(PullVcSourceOrderCmd pullVcSourceOrderCmd) {
        StoreAuthorizationQuery storeAuthorizationQuery = new StoreAuthorizationQuery();
        if (CollUtil.isNotEmpty(pullVcSourceOrderCmd.getStoreIds())) {
            storeAuthorizationQuery.setStoreIds(pullVcSourceOrderCmd.getStoreIds());
        }
        storeAuthorizationQuery.setChannelCode(ChannelCode.AMZ_CHANNEL_CODE);
        storeAuthorizationQuery.setStoreType(StoreTypeEnums.VC.getValue());
        storeAuthorizationQuery.setShardIndex(XxlJobHelper.getShardIndex());
        storeAuthorizationQuery.setShardTotal(XxlJobHelper.getShardTotal());
//        storeAuthorizationQuery.setShardIndex(0);
//        storeAuthorizationQuery.setShardTotal(1);
        return storeAuthorizationQuery;
    }

    /**
     * 查找db中已存在的订单
     *
     * @param amazonVcOrders
     * @param storeId
     * @return
     */
    private Map<String, AmzVcOrderSourceJsonPO> getExistingOrdersFromDb(List<Order> amazonVcOrders, Integer storeId) {
        Map<String, AmzVcOrderSourceJsonPO> amzVcOrderSourceJsonPOMap = Maps.newHashMap();
        CollUtil.split(amazonVcOrders,1000).forEach(tempOrderList->{
            List<String> purchaseOrderNumberList = tempOrderList.stream().map(Order::getPurchaseOrderNumber).collect(Collectors.toList());
            Map<String, AmzVcOrderSourceJsonPO> tempMap = amzVcOrderSourceJsonPOService.list(Wrappers.<AmzVcOrderSourceJsonPO>lambdaQuery()
                            .in(AmzVcOrderSourceJsonPO::getPurchaseOrderNumber, purchaseOrderNumberList)
                            .eq(AmzVcOrderSourceJsonPO::getStoreId, storeId)
                            .orderByDesc(AmzVcOrderSourceJsonPO::getCreateTime))
                    .stream()
                    .collect(Collectors.toMap(AmzVcOrderSourceJsonPO::getPurchaseOrderNumber, Function.identity(), (v1, v2) -> v1));
            amzVcOrderSourceJsonPOMap.putAll(tempMap);
        });
        return amzVcOrderSourceJsonPOMap;
    }

    /**
     * 比较订单数据是否有变化
     *
     * @param order
     * @param orderStatus
     * @param dbOrderNumberMap
     * @return
     */
    private Boolean compareOrderData(Order order, OrderStatus orderStatus, Map<String, AmzVcOrderSourceJsonPO> dbOrderNumberMap) {
        String purchaseOrderNumber = order.getPurchaseOrderNumber();
        AmzVcOrderSourceJsonPO dbOrder = dbOrderNumberMap.get(purchaseOrderNumber);
        if (dbOrder == null) {
            //库里不存在，新增
            return true;
        } else {
            //库里存在，比较数据是否相等
            String dbMd5 = MD5Util.getMd5(dbOrder.getOrderJson() + StrUtil.DASHED + dbOrder.getOrderStatusJson());
            String pullMd5 = MD5Util.getMd5(GsonUtil.toJson(order) + StrUtil.DASHED + GsonUtil.toJson(orderStatus));
            if (!StrUtil.equals(dbMd5, pullMd5)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 构建AmzVcOrderSourceJsonPO
     *
     * @param storeAuthorizationVo
     * @param order
     * @param orderStatus
     * @return
     */
    private AmzVcOrderSourceJsonPO buildAmzVcOrderSourceJsonPO(StoreAuthorizationVo storeAuthorizationVo, Order order, OrderStatus orderStatus) {
        AmzVcOrderSourceJsonPO amzVcOrderSourceJsonPO = new AmzVcOrderSourceJsonPO();
        amzVcOrderSourceJsonPO.setStoreId(storeAuthorizationVo.getId());
        amzVcOrderSourceJsonPO.setPurchaseOrderNumber(order.getPurchaseOrderNumber());
        amzVcOrderSourceJsonPO.setSellerId(storeAuthorizationVo.getAuthorization().getAmzSellerId());
        amzVcOrderSourceJsonPO.setPurchaseOrderState(order.getPurchaseOrderState().getValue());
        amzVcOrderSourceJsonPO.setOrderJson(GsonUtil.toJson(order));
        amzVcOrderSourceJsonPO.setOrderStatusJson(GsonUtil.toJson(orderStatus));
        return amzVcOrderSourceJsonPO;
    }

    /**
     * 拉取订单状态
     *
     * @param shopAccount
     * @param purchaseOrderNumber
     * @return
     */
    private OrderStatus pullVcOrderStatus(ShopAccount shopAccount, String purchaseOrderNumber) {
        GetPurchaseOrdersStatusRequest purchaseOrdersStatusRequest = new GetPurchaseOrdersStatusRequest();
        purchaseOrdersStatusRequest.setPurchaseOrderNumber(purchaseOrderNumber);
        List<OrderStatus> ordersStatus = Lists.newArrayList();
        //最多重试10次
        for (int retryCount = 0; retryCount < 10; retryCount++) {
            R<GetPurchaseOrdersStatusResponse> purchaseOrdersStatusResp = amazonClient.getPurchaseOrdersStatus(shopAccount, purchaseOrdersStatusRequest);
            log.info("查询亚马逊VC订单状态，返回信息为{}", GsonUtil.toJson(purchaseOrdersStatusResp));
            // 限流
            if (AmazonApiCodeEnums.isRequestLimit(purchaseOrdersStatusResp.getCode())) {
                // 暂停后继续拉取
                log.info("请求VC订单状态速率限制，休眠:{}ms后继续请求", SLEEP_TIME_MILLISECONDS);
                sleep(SLEEP_TIME_MILLISECONDS, TimeUnit.MILLISECONDS);
                continue;
            }
            // 失败停止拉取
            if (!purchaseOrdersStatusResp.isSuccess()) {
                log.error("查询亚马逊VC订单状态失败，返回信息为{}", purchaseOrdersStatusResp.getMessage());
                break;
            }
            // 已无数据停止拉取
            if (Objects.isNull(purchaseOrdersStatusResp.getData()) || Objects.isNull(purchaseOrdersStatusResp.getData().getPayload())) {
                break;
            }
            ordersStatus = purchaseOrdersStatusResp.getData().getPayload().getOrdersStatus();
            break;
        }
        return CollUtil.isNotEmpty(ordersStatus) ? ordersStatus.get(0) : null;
    }

    /**
     * 拉取VC订单
     *
     * @param shopAccount
     * @param changedAfter
     * @param changedBefore
     * @return
     */
    private List<io.swagger.client.model.vendoRetailProcurement.orders.Order> pullVcOrder(ShopAccount shopAccount, LocalDateTime changedAfter, LocalDateTime changedBefore) {
        String nextToken = null;
        List<io.swagger.client.model.vendoRetailProcurement.orders.Order> orders = Lists.newArrayList();
        GetPurchaseOrdersRequest purchaseOrdersRequest = new GetPurchaseOrdersRequest();
        purchaseOrdersRequest.setChangedAfter(DateUtil.convertToUTC(changedAfter));
        purchaseOrdersRequest.setChangedBefore(DateUtil.convertToMsUTC(changedBefore));
        do {
            purchaseOrdersRequest.setNextToken(nextToken);
            R<GetPurchaseOrdersResponse> ordersResp = amazonClient.getPurchaseOrders(shopAccount, purchaseOrdersRequest);
            log.info("查询亚马逊VC订单，返回信息为{}", GsonUtil.toJson(ordersResp));
            // 限流
            if (AmazonApiCodeEnums.isRequestLimit(ordersResp.getCode())) {
                log.info("请求VC订单速率限制，休眠:{}ms后继续请求", SLEEP_TIME_MILLISECONDS);
                sleep(SLEEP_TIME_MILLISECONDS, TimeUnit.MILLISECONDS);
                // 暂停后继续拉取
                continue;
            }
            // 失败停止拉取
            if (!ordersResp.isSuccess()) {
                log.error("查询亚马逊VC订单失败，返回信息为{}", ordersResp.getMessage());
                break;
            }
            // 已无数据停止拉取
            if (Objects.isNull(ordersResp.getData()) || Objects.isNull(ordersResp.getData().getPayload()) || CollUtil.isEmpty(ordersResp.getData().getPayload().getOrders())) {
                break;
            }
            OrderList payload = ordersResp.getData().getPayload();
            // 添加订单到list
            orders.addAll(payload.getOrders());
            if (payload.getPagination() == null || StrUtil.isEmpty(payload.getPagination().getNextToken())) {
                //没有下一页
                break;
            }
            nextToken = payload.getPagination().getNextToken();
        } while (StrUtil.isNotEmpty(nextToken));
        return orders;
    }

    /**
     * 获取拉取时间
     *
     * @param pullVcSourceOrderCmd
     * @return
     */
    private Pair<LocalDateTime, LocalDateTime> getPullTime(PullVcSourceOrderCmd pullVcSourceOrderCmd) {
        //开始时间
        LocalDateTime changedAfter;
        //结束时间
        LocalDateTime changedBefore;
        if (StrUtil.isNotEmpty(pullVcSourceOrderCmd.getChangedAfter()) && StrUtil.isNotEmpty(pullVcSourceOrderCmd.getChangedBefore())) {
            //指定开始时间，结束时间
            changedAfter = DateUtils.parseLocalDateTime(pullVcSourceOrderCmd.getChangedAfter());
            changedBefore = DateUtils.parseLocalDateTime(pullVcSourceOrderCmd.getChangedBefore());
        } else {
            //结束时间
            changedBefore = LocalDateTime.now();
            //开始时间
            changedAfter = changedBefore.minusDays(pullVcSourceOrderCmd.getLastDay() == null ? 7 : pullVcSourceOrderCmd.getLastDay());
        }
        return Pair.of(changedAfter, changedBefore);
    }
}

