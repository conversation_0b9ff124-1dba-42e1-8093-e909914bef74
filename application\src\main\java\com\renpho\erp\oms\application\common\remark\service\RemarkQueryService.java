package com.renpho.erp.oms.application.common.remark.service;

import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.oms.application.common.remark.fetcher.RemarkFetcher;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @desc: 备注查询服务
 * @time: 2025-06-20 14:39:06
 * @author: <PERSON><PERSON>
 */
@RequiredArgsConstructor
@Component
public class RemarkQueryService {

    @TransMethodResult
    public <T, VO> List<VO> getRemarkHistory(Long bizId, RemarkFetcher<T, VO> fetcher) {
        List<T> remarks = fetcher.fetchRemarks(bizId);
        return remarks.stream().map(fetcher::toVO).collect(Collectors.toList());
    }
}
