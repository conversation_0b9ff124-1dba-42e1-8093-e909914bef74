package com.renpho.erp.oms.adapter.web.controller.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.application.salemanagement.dto.OfflineFulfillmentImportExcel;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
public class OfflineFulfillmentExcelListener extends AnalysisEventListener<OfflineFulfillmentImportExcel> {
    private final List<OfflineFulfillmentImportExcel> offlineFulfillmentImportExcelList = new ArrayList<>();
    /**
     * 中文表头
     */
    public static final Map<Integer, String> importCNHeadMap = new HashMap<>();

    /**
     * 英文表头
     *
     * @param data
     * @param context
     */
    public static final Map<Integer, String> importUSHeadMap = new HashMap<>();

    static {
        importCNHeadMap.put(0, "销售单号");
        importCNHeadMap.put(1, "发货时间");
        importCNHeadMap.put(2, "签收时间");
        importCNHeadMap.put(3, "仓库单号");
        importCNHeadMap.put(4, "承运商");
        importCNHeadMap.put(5, "跟踪号");

        importUSHeadMap.put(0, "Sales Order #");
        importUSHeadMap.put(1, "Shipped Time");
        importUSHeadMap.put(2, "Delivered Time");
        importUSHeadMap.put(3, "Warehouse Order #");
        importUSHeadMap.put(4, "Carrier");
        importUSHeadMap.put(5, "Tracking #");
    }


    @Override
    public void invoke(OfflineFulfillmentImportExcel data, AnalysisContext context) {
        offlineFulfillmentImportExcelList.add(data);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (headMap.equals(importCNHeadMap)) {
            // 国际化转换
            ExcelUtil.buildUpdateHeadAgain(context, importCNHeadMap, OfflineFulfillmentImportExcel.class);
        } else if (headMap.equals(importUSHeadMap)) {
            ExcelUtil.buildUpdateHeadAgain(context, importUSHeadMap, OfflineFulfillmentImportExcel.class);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("HEAD_NOT_MATCH"));
        }

    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception{
        if (exception instanceof ExcelDataConvertException) {
            throw new BusinessException(I18nMessageKit.getMessage("DATA_FORMAT_ERROR"));
        }
        throw exception;
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("手动发货导入excel解析数据为", JsonUtils.toJson(offlineFulfillmentImportExcelList));

    }
}
