package com.renpho.erp.oms.application.vc;

import java.util.Set;
import java.util.TimeZone;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.renpho.erp.oms.application.vc.order.service.VcOrderConvertService;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class VcOrderConvertServiceTest {

	@Resource
	private VcOrderConvertService vcOrderConvertService;

	@BeforeEach
	void setUp() {
		TimeZone.setDefault(TimeZone.getTimeZone("UTC+0"));
	}

	@Test
	void testConvert1() {
		vcOrderConvertService.convert(163L);
	}

	@Test
	void testConvert2() {
		VcOrderConvertService.VcOrderConvertCmd cmd = new VcOrderConvertService.VcOrderConvertCmd();
		cmd.setVcPurchaseNos(Set.of("6AZT9ZFV"));
		vcOrderConvertService.convert(cmd);
	}
}
