package com.renpho.erp.oms.application.returnorder.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.trans.RemoteTransType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 退货订单分页查询VO
 * @time: 2025-03-17 18:32:25
 * @author: Alina
 */
@Data
public class ReturnOrderPageVO implements VO {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 渠道编码
	 */
	private String channelCode;

	/**
	 * 销售渠道名称
	 */
	private String channelName;

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 订单号，OMS的自定义退货单号
	 */
	private String returnOrderNo;

	/**
	 * 店铺单号（渠道单号），各平台的唯一单号，在平台维度为（店铺+店铺单号）唯一，在OMS不唯一（因为拆单）
	 */
	private String channelOrderNo;

	/**
	 * 订单行（第一个订单行）
	 */
	private Item item;

	/**
	 * 退货订单状态 1-待收货 2-已收货 3-已取消
	 */
	@Trans(type = TransType.DICTIONARY, key = "return_order_status", ref = "returnOrderStatusName")
	private Integer returnOrderStatus;

	/**
	 * 退货订单状态名称
	 */
	private String returnOrderStatusName;
	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 收货时间
	 */
	private LocalDateTime receiveTime;

	/**
	 * 退货仓库id
	 */
	@Trans(type = RemoteTransType.REMOTE, targetClassName = "WarehouseVo.class", ref = "warehouseName", fields = { "name" },
			uniqueField = "id")
	private Integer warehouseId;

	/**
	 * 退货仓库code
	 */
	private String warehouseCode;

	/**
	 * 仓库名称
	 */
	private String warehouseName;

	/**
	 * 承运商
	 */
	private String carrierName;

	/**
	 * 跟踪号
	 */
	private String trackingNo;

	/**
	 * 备注字段
	 */
	private String remark;

	@Data
	public static class Item {
		/**
		 * returnOrderItemID
		 */
		private Long id;

		/**
		 * item编码，平台给的标识当前商品的ID
		 */
		private String item;

		/**
		 * 销售SKU，平台给的销售SKU字段
		 */
		private String msku;

		/**
		 * 退货数量
		 */
		private Integer returnQuantity;

		/**
		 * 采购SKU，根据映射表关联出的本地PSKU（ERP里面的SKU）
		 */
		private String psku;

		/**
		 * FNSKU，根据映射表关联出的商品条码（库存识别码）
		 */
		private String fnSku;

		/**
		 * 图片ID，来自PDS，用于换取临时访问地址
		 */
		private String imageId;

		/**
		 * 图片url，临时访问地址
		 */
		private String imageUrl;

		/**
		 * 当前OMS订单下订单行的数量
		 */
		private Integer itemCount;
	}

}
