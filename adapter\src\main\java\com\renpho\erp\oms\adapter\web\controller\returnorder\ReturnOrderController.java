package com.renpho.erp.oms.adapter.web.controller.returnorder;

import com.renpho.erp.oms.application.common.remark.fetcher.ReturnOrderRemarkFetcher;
import com.renpho.erp.oms.application.common.remark.service.RemarkQueryService;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.application.returnorder.command.*;
import com.renpho.erp.oms.application.returnorder.service.ReturnOrderOperateService;
import com.renpho.erp.oms.application.returnorder.service.ReturnOrderQueryService;
import com.renpho.erp.oms.application.returnorder.vo.OrderInfoVO;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderItemVO;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderPageVO;
import com.renpho.erp.oms.domain.returnorder.query.OrderStoreQuery;
import com.renpho.erp.oms.domain.returnorder.query.ReturnOrderIdQuery;
import com.renpho.erp.oms.domain.returnorder.query.ReturnOrderPageQuery;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @desc: 退货订单
 * @time: 2025-03-17 18:03:22
 * @author: Alina
 */

@Slf4j
@RestController
@ShenyuSpringCloudClient("/return/order/**")
@RequestMapping("/return/order")
@AllArgsConstructor
public class ReturnOrderController {

	private final ReturnOrderQueryService returnOrderQueryService;
	private final ReturnOrderOperateService returnOrderOperateService;
	private final RemarkQueryService remarkQueryService;
	private final ReturnOrderRemarkFetcher returnOrderRemarkFetcher;

	/**
	 * 退货订单列表分页查询.
	 *
	 * @param query 参数
	 * @return R
	 */
	@PostMapping("/page")
	@PreAuthorize(PermissionConstant.ReturnOrder.PAGE)
	public R<Paging<ReturnOrderPageVO>> page(@RequestBody @Valid ReturnOrderPageQuery query) {
		return R.success(returnOrderQueryService.page(query));
	}

	/**
	 * 退货订单明细列表查询.
	 *
	 * @param query 参数
	 * @return R
	 */
	@PostMapping("/item/list")
	@PreAuthorize(PermissionConstant.ReturnOrder.PAGE)
	public R<List<ReturnOrderItemVO>> listItem(@RequestBody @Valid ReturnOrderIdQuery query) {
		return R.success(returnOrderQueryService.listItem(query));
	}

	/**
	 * 根据店铺Id，店铺单号获取所有销售订单以及明细
	 *
	 * @param query 参数
	 * @return R
	 */
	@PostMapping("/getOrderInfo")
	@PreAuthorize(PermissionConstant.ReturnOrder.ADD)
	public R<OrderInfoVO> getOrderInfo(@RequestBody @Valid OrderStoreQuery query) {
		return R.success(returnOrderQueryService.getOrderInfo(query));
	}

	/**
	 * 新建RAM退货订单
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@PostMapping("/addReturnOrder")
	@PreAuthorize(PermissionConstant.ReturnOrder.ADD)
	public R<Long> addReturnOrder(@RequestBody @Valid ReturnOrderAddCmd cmd) {
		return returnOrderOperateService.addReturnOrder(cmd);
	}

	/**
	 * 新建RAM补发订单
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@PostMapping("/addResendOrder")
	@PreAuthorize(PermissionConstant.ReturnOrder.ADD)
	public R<Long> addResendOrder(@RequestBody @Valid ResendOrderAddCmd cmd) {
		return returnOrderOperateService.addResendOrder(cmd);
	}

	/**
	 * 手动收货
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@PostMapping("/manualReceive")
	@PreAuthorize(PermissionConstant.ReturnOrder.MANUAL_RECEIVE)
	public R<Long> manualReceive(@RequestBody @Valid ManualReceiveCmd cmd) {
		returnOrderOperateService.manualReceive(cmd);
		return R.success();
	}

	/**
	 * 退货订单取消
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@PostMapping("/cancel")
	@PreAuthorize(PermissionConstant.ReturnOrder.CANCEL)
	public R<Long> cancel(@RequestBody @Valid CancelReturnOrderCmd cmd) {
		returnOrderOperateService.cancel(cmd);
		return R.success();
	}

	/**
	 * 退货订单备注
	 *
	 * @param cmd 参数
	 * @return R
	 */
	@PostMapping("/remark")
	@PreAuthorize(PermissionConstant.ReturnOrder.REMARK)
	public R<Long> remark(@RequestBody @Valid RemarkReturnOrderCmd cmd) {
		returnOrderOperateService.remark(cmd);
		return R.success();
	}

	/**
	 * 退货订单历史备注
	 *
	 * @param returnOrderId 退货订单id
	 * @return R
	 */
	@GetMapping("/orderRemarkHistory/{returnOrderId}")
	@PreAuthorize(PermissionConstant.ReturnOrder.REMARK)
	public R<List<OrderRemarkHistoryVO>> orderRemarkHistory(@PathVariable("returnOrderId") Long returnOrderId) {
		return R.success(remarkQueryService.getRemarkHistory(returnOrderId,returnOrderRemarkFetcher));
	}

	/**
	 * 导出
	 *
	 * @param query 查询参数
	 * @param response 响应
	 */
	@Operation(summary = "导出", description = "导出")
	@PreAuthorize(PermissionConstant.ReturnOrder.EXPORT)
	@PostMapping(value = "/export")
	public void export(@RequestBody @Valid ReturnOrderPageQuery query, HttpServletResponse response) {
		returnOrderQueryService.export(query, response);
	}
}
