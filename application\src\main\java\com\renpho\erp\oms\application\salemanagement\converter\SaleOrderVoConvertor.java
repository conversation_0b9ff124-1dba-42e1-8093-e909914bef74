package com.renpho.erp.oms.application.salemanagement.converter;

import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.oms.application.salemanagement.dto.SaleOrderExportExcel;
import com.renpho.erp.oms.application.salemanagement.vo.*;
import com.renpho.erp.oms.domain.salemanagement.*;
import com.renpho.erp.oms.infrastructure.common.util.SizeUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Optional;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface SaleOrderVoConvertor {

    @Mapping(target = "amount", defaultValue = "0.00")
    SaleOrderPageVO toPageVO(SaleOrder saleOrder);

    @Mapping(target = "id", expression = "java(saleOrderItem.getId().getId())")
    @Mapping(target = "psku", expression = "java(mapPsku(saleOrderItem))")
    @Mapping(target = "fnSku", expression = "java(mapFnSku(saleOrderItem))")
    @Mapping(target = "imageId", expression = "java(mapImageId(saleOrderItem))")
    SaleOrderPageVO.Item toItemVO(SaleOrderItem saleOrderItem);

    @Mapping(target = "warehouseId", source = "saleOrderFulfillment.warehouseId")
    @Mapping(target = "warehouseCode", source = "saleOrderFulfillment.warehouseCode")
    @Mapping(target = "warehouseName", source = "warehouseName")
    @Mapping(target = "length", expression = "java(parseSize(saleOrderFulfillment.getSize(), 0))")
    @Mapping(target = "width", expression = "java(parseSize(saleOrderFulfillment.getSize(), 1))")
    @Mapping(target = "height", expression = "java(parseSize(saleOrderFulfillment.getSize(), 2))")
    SaleOrderPageVO.Fulfillment toFulfillmentVO(SaleOrderFulfillment saleOrderFulfillment,
                                                SaleOrderItemFulfillment saleOrderItemFulfillment, String warehouseName);

    @Mapping(target = "orderStatusName",
            expression = "java(com.renpho.erp.oms.domain.salemanagement.OrderStatus.enumOf(saleOrder.getOrderStatus()).getName())")
    @Mapping(target = "itemAmount", defaultValue = "0.00")
    @Mapping(target = "itemTax", defaultValue = "0.00")
    @Mapping(target = "shippingAmount", defaultValue = "0.00")
    @Mapping(target = "shippingTax", defaultValue = "0.00")
    @Mapping(target = "incomeAmount", defaultValue = "0.00")
    @Mapping(target = "taxAmount", defaultValue = "0.00")
    @Mapping(target = "amount", defaultValue = "0.00")
    @Mapping(target = "rmaMark", expression = "java(mapRmaMark(saleOrder))")
    SaleOrderDetailVO toSaleOrderDetailVO(SaleOrder saleOrder);

    @Mapping(target = "id", expression = "java(saleOrderItem.getId().getId())")
    @Mapping(target = "status", expression = "java(saleOrderItem.getStatus().getValue())")
    @Mapping(target = "realPriceMark", expression = "java(saleOrderItem.getRealPriceMark().getValue())")
    @Mapping(target = "psku", expression = "java(mapPsku(saleOrderItem))")
    @Mapping(target = "fnSku", expression = "java(mapFnSku(saleOrderItem))")
    @Mapping(target = "imageId", expression = "java(mapImageId(saleOrderItem))")
    @Mapping(target = "size", expression = "java(mapSize(saleOrderItem))")
    @Mapping(target = "weight", expression = "java(mapWeight(saleOrderItem))")
    @Mapping(target = "productPrice", defaultValue = "0.00")
    @Mapping(target = "productTax", defaultValue = "0.00")
    @Mapping(target = "productDiscount", defaultValue = "0.00")
    @Mapping(target = "productDiscountTax", defaultValue = "0.00")
    @Mapping(target = "freightAmount", defaultValue = "0.00")
    @Mapping(target = "freightTax", defaultValue = "0.00")
    @Mapping(target = "freightDiscount", defaultValue = "0.00")
    @Mapping(target = "freightDiscountTax", defaultValue = "0.00")
    @Mapping(target = "totalAmount", defaultValue = "0.00")
    @Mapping(target = "extWarranty", expression = "java(mapExtWarranty(saleOrderItem))")
    SaleOrderItemVO toSaleOrderItemVO(SaleOrderItem saleOrderItem);

    @Mapping(target = "orderId", source = "saleOrderFulfillment.orderId")
    @Mapping(target = "fulfillmentType", expression = "java(saleOrderFulfillment.getFulfillmentType().getValue())")
    @Mapping(target = "fulfillmentServiceType",
            expression = "java(saleOrderFulfillment.getFulfillmentServiceType() != null ? saleOrderFulfillment.getFulfillmentServiceType().getValue() : null)")
    @Mapping(target = "warehouseId", source = "saleOrderFulfillment.warehouseId")
    @Mapping(target = "warehouseCode", source = "saleOrderFulfillment.warehouseCode")
    @Mapping(target = "warehouseName", source = "warehouseName")
    @Mapping(target = "logisticsOrder", source = "saleOrderFulfillment.logisticsOrderNo")
    @Mapping(target = "uploadTrackStatus", expression = "java(saleOrderFulfillment.getUploadTrackStatus() != null ? saleOrderFulfillment.getUploadTrackStatus().getValue() : null)")
    SaleOrderFulfillmentVO toSaleOrderFulfillmentVO(SaleOrderFulfillment saleOrderFulfillment,
                                                    SaleOrderItemFulfillment saleOrderItemFulfillment, String warehouseName);


    SaleOrderBaseVO toSaleOrderBaseVO(SaleOrder saleOrder);

    SaleOrderAddressVO toSaleOrderAddressVO(SaleOrderAddress saleOrderAddress);

    @Mapping(target = "id", expression = "java(saleOrderItem.getId().getId())")
    @Mapping(target = "psku", expression = "java(mapPsku(saleOrderItem))")
    @Mapping(target = "fnSku", expression = "java(mapFnSku(saleOrderItem))")
    @Mapping(target = "skuSource", expression = "java(mapSkuSource(saleOrderItem))")
    SaleOrderBaseVO.SaleOrderItemDetailVO toSaleOrderItemDetailVO(SaleOrderItem saleOrderItem);


    @Mapping(target = "sampleMark", expression = "java(mapSampleMark(saleOrderExcel))")
    @Mapping(target = "fulfillmentServiceType", expression = "java(mapFulfillmentServiceType(saleOrderExcel))")
    @Mapping(target = "size", expression = "java(mapSize(saleOrderExcel))")
    @Mapping(target = "weight", expression = "java(mapWeight(saleOrderExcel))")
    @Mapping(target = "status", expression = "java(mapStatus(saleOrderExcel))")
    @Mapping(target = "orderedTime", expression = "java(mapTime(saleOrderExcel.getOrderedTime()))")
    @Mapping(target = "paidTime", expression = "java(mapTime(saleOrderExcel.getPaidTime()))")
    @Mapping(target = "shippedTime", expression = "java(mapTime(saleOrderExcel.getShippedTime()))")
    @Mapping(target = "deliveredTime", expression = "java(mapTime(saleOrderExcel.getDeliveredTime()))")
    @Mapping(target = "lastShipTime", expression = "java(mapTime(saleOrderExcel.getLastShipTime()))")
    SaleOrderExportExcel toSaleOrderExportExcel(SaleOrderExcel saleOrderExcel);

    default String mapPsku(SaleOrderItem saleOrderItem) {
        return Optional.ofNullable(saleOrderItem.getProduct()).map(Product::getPsku).orElse(null);
    }

    default String mapFnSku(SaleOrderItem saleOrderItem) {
        return Optional.ofNullable(saleOrderItem.getProduct()).map(Product::getFnsku).orElse(null);
    }

    default String mapImageId(SaleOrderItem saleOrderItem) {
        return Optional.ofNullable(saleOrderItem.getProduct()).map(Product::getImageId).orElse(null);
    }

    default Integer mapSkuSource(SaleOrderItem saleOrderItem) {
        return Optional.ofNullable(saleOrderItem.getSkuSource()).map(SkuSource::getValue).orElse(null);
    }

    default String mapSampleMark(SaleOrderExcel saleOrderExcel) {
        return Optional.ofNullable(saleOrderExcel.getSampleMark()).map(sampleMark -> OrderSampleMark.enumOf(sampleMark).getName()).orElse(null);
    }

    default Integer mapRmaMark(SaleOrder saleOrder) {
        if (!Objects.isNull(saleOrder.getOrderSource()) && OrderSource.RMA.getValue().equals(saleOrder.getOrderSource())) {
            return 1;
        }
        return 0;
    }

    default String mapFulfillmentServiceType(SaleOrderExcel saleOrderExcel) {
        return Optional.ofNullable(saleOrderExcel.getFulfillmentServiceType()).map(fulfillmentServiceType -> FulfillmentServiceType.enumOf(fulfillmentServiceType).getName()).orElse(null);
    }

    default LocalDateTime mapTime(LocalDateTime localDateTime) {
        return localDateTime;
    }

    default String mapSize(SaleOrderExcel saleOrderExcel) {
        if (ObjectUtils.anyNull(saleOrderExcel.getLength(), saleOrderExcel.getWidth(), saleOrderExcel.getHeight(), saleOrderExcel.getQuantityShipment())) {
            return null;
        }
        // 尺寸（长 * 宽 * 高 cm³，其中高 = 内部psku的应发数量 * 商品高度）
        return saleOrderExcel.getLength() + " * " + saleOrderExcel.getWidth() + " * "
                + new BigDecimal(saleOrderExcel.getQuantityShipment()).multiply(saleOrderExcel.getHeight());
    }

    /**
     * 商品重量（毛重 * 内部psku的应发数量，含包材，单位kg）
     *
     * @return BigDecimal
     */
    default BigDecimal mapWeight(SaleOrderExcel saleOrderExcel) {

        if (ObjectUtils.anyNull(saleOrderExcel.getWeight(), saleOrderExcel.getQuantityShipment())) {
            return null;
        }
        // 商品重量（毛重 * 内部psku的应发数量，含包材，单位kg）
        return new BigDecimal(saleOrderExcel.getQuantityShipment()).multiply(saleOrderExcel.getWeight());
    }

    default String mapStatus(SaleOrderExcel saleOrderExcel) {

        if (Objects.isNull(saleOrderExcel.getStatus())) {
            return null;
        }
        return OrderItemStatus.enumOf(saleOrderExcel.getStatus()).getName();
    }

    /**
     * 商品重量（毛重 * 内部psku的应发数量，含包材，单位kg）
     *
     * @return BigDecimal
     */
    default BigDecimal mapWeight(SaleOrderItem saleOrderItem) {
        Product product = saleOrderItem.getProduct();
        if (product == null) {
            return null;
        }
        if (ObjectUtils.anyNull(product.getWeight(), saleOrderItem.getQuantityShipment())) {
            return null;
        }
        // 商品重量（毛重 * 内部psku的应发数量，含包材，单位kg）
        return new BigDecimal(saleOrderItem.getQuantityShipment()).multiply(product.getWeight());
    }

    /**
     * 尺寸（长 * 宽 * 高 cm³，其中高 = 内部psku的应发数量 * 商品高度）
     *
     * @return String
     */
    default String mapSize(SaleOrderItem saleOrderItem) {
        Product product = saleOrderItem.getProduct();
        if (product == null) {
            return "";
        }
        if (ObjectUtils.anyNull(product.getLength(), product.getWidth(), product.getHeight(), saleOrderItem.getQuantityShipment())) {
            return "";
        }
        // 尺寸（长 * 宽 * 高 cm³，其中高 = 内部psku的应发数量 * 商品高度）
        return product.getLength() + " * " + product.getWidth() + " * "
                + new BigDecimal(saleOrderItem.getQuantityShipment()).multiply(product.getHeight());
    }

    default BigDecimal parseSize(String size, int index) {
        return SizeUtil.parseSize(size, index);
    }

    default Boolean mapExtWarranty(SaleOrderItem saleOrderItem) {
        return Optional.ofNullable(saleOrderItem.getExtendWarranty()).map(orderItemExtendWarranty -> {
            return orderItemExtendWarranty == OrderItemExtendWarranty.EXTEND_WARRANTY;
        }).orElse(Boolean.FALSE);
    }
}
