package com.renpho.erp.oms.application.channelmanagement.tiktok.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderLogConvertor;

import com.renpho.erp.oms.application.channelmanagement.tiktok.vo.TtOrderVO;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrder;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.repository.TtOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;

import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.tiktok.mapper.TtOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.tiktok.po.TtOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service(ChannelCode.TT_CHANNEL_CODE)
@Slf4j
public class TtOrderPlatformService extends AbstractDefaultPlatfomService {
	private final TtOrderRepository ttOrderRepository;
	private final PlatformOrderConvertor platformOrderConvertor;
	private final TtOrderLogConvertor ttOrderLogConvertor;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private final TtOrderMapper ttOrderMapper;

	@Override
	protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<TtOrderPage> page = ttOrderRepository.page(platformPageQuery);
		List<TtOrderPage> ttOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(ttOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}
		List<PlatformOrderVO> records = ttOrderPageList.stream().map(ttOrderPage -> {
			return platformOrderConvertor.titTokOrderToVO(ttOrderPage);
		}).collect(Collectors.toList());

		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		TtOrder ttOrder = ttOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(ttOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		TtOrderVO ttOrderVO = ttOrderLogConvertor.toVO(ttOrder);
		// 查询店铺
		ttOrderVO.setStoreName(getStoreName(ttOrder.getStoreId()));
		return R.success(ttOrderVO);
	}

	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> platformOrderDtoList = ttOrderMapper.selectList(Wrappers.<TtOrderPO>lambdaQuery()
						.select(TtOrderPO::getTiktokOrderId, TtOrderPO::getLastMpdsSyncOrderTime, TtOrderPO::getStoreId)
						.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, TtOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
						.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, TtOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
				.stream()
				.map(x -> monitorPlatformOrderConvertor.titTokOrderToDto(x))
				.collect(Collectors.toList());
		return platformOrderDtoList;
	}
}
