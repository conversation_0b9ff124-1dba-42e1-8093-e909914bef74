package com.renpho.erp.oms.application.ordershipaudit.channel.offline;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.renpho.erp.oms.infrastructure.common.excel.ExcelHeadName;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Data
public class OfflineExcelDto {
    @NotBlank(message = "{ORDER_SHIP_AUDIT_STORE_NAME}")
    @ExcelHeadName(zhCnName = "店铺", enName = "Shop")
    private String shop;
    @NotBlank(message = "{ORDER_SHIP_AUDIT_ORDER_NO}")
    @ExcelHeadName(zhCnName = "销售单号", enName = "SO#")
    private String orderNo = "";
    @NotBlank(message = "{ORDER_SHIP_AUDIT_WAREHOUSE_CODE}")
    @ExcelHeadName(zhCnName = "发货仓库编码", enName = "Shipping Warehouse Code")
    private String imsWarehouseCode;

    @ExcelHeadName(zhCnName = "错误信息", enName = "error info")
    private String errorInfo;

    @ExcelIgnore
    private List<String> errorInfoList = new ArrayList<>();

    public void addErrorInfo(String errorInfo) {
        this.errorInfoList.add(errorInfo);
    }

    public void buildErrorInfo() {
        errorInfo = String.join("\n", errorInfoList);
    }
}
