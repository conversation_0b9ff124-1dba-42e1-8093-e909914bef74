package com.renpho.erp.oms.application.ordershipaudit.chain.handler;

import org.springframework.core.Ordered;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;

/**
 * 订单自动发货审核-处理器
 * <AUTHOR>
 */
public interface OrderAutoShipAuditHandler extends Ordered {

	/**
	 * 处理
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return String 失败原因
	 */
	String handle(OrderAutoShipAuditDTO orderAutoShipAudit);
}
