package com.renpho.erp.oms.application.salemanagement.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.renpho.erp.oms.infrastructure.common.excel.TimeZoneLocalDateTimeStringConverter;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class OfflineFulfillmentImportExcel {

    /**
     * 销售单号
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.SALE_ORDER")
    private String orderNo;
    /**
     * 发货时间
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.SHIPPED_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime shipDate;
    /**
     * 送达时间
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.DELIVERED_TIME", converter = TimeZoneLocalDateTimeStringConverter.class)
    private LocalDateTime deliveryDate;

    /**
     * 出库单号 仅手动发货
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.WAREHOUSE_ORDER")
    private String outBoundNo;
    /**
     * 承运商
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.CARRIER")
    private String carrierName;
    /**
     * 跟踪号
     */
    @ExcelProperty(value = "OFFLINE_FULFILLMENT_IMPORT_EXCEL.TRACKING")
    private String trackingNo;

    @ExcelProperty(value = "SKU_MAPPING_EXCEL.ERRORMESSAGE")
    private String errMessage;
}
