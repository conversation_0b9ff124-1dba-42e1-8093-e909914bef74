package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.springframework.util.Assert;

import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;

/**
 * 分仓-工厂
 * <AUTHOR>
 */
public class AllocateWarehouseFactory {

	private AllocateWarehouseFactory() {
	}

	private static final Map<Integer, AllocateWarehouse> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 获取
	 * @param skuCombinationType sku组合类型
	 * @return ChannelConvertSaleOrderStrategy
	 */
	public static AllocateWarehouse get(SkuCombinationType skuCombinationType) {
		Assert.notNull(skuCombinationType, "获取分仓策略，sku组合类型不能为空");
		if (STRATEGY_MAP.containsKey(skuCombinationType.getValue())) {
			return STRATEGY_MAP.get(skuCombinationType.getValue());
		}
		throw new IllegalArgumentException(MessageFormat.format("获取分仓策略，找不到 {0} ", skuCombinationType.getName()));
	}

	/**
	 * 注册.
	 * @param skuCombinationType sku组合类型
	 */
	public static void register(SkuCombinationType skuCombinationType, AllocateWarehouse orderInterceptStrategy) {
		Assert.notNull(skuCombinationType, "sku组合类型不能为空");
		STRATEGY_MAP.put(skuCombinationType.getValue(), orderInterceptStrategy);
	}
}
