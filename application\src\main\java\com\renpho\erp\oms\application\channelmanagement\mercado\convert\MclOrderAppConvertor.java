package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import cn.hutool.core.collection.CollectionUtil;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderVO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentDTO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrder;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.DateUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.math.BigDecimal;
import java.util.Objects;

public class MclOrderAppConvertor {
	public static MclOrder toDomain(MclOrderDTO mclOrderDTO) {
		if (Objects.nonNull(mclOrderDTO)) {
			MclOrder mclOrder = new MclOrder();
			mclOrder.setMclUserId(mclOrderDTO.getMclUserId());
			mclOrder.setStoreId(mclOrderDTO.getStoreId());
			mclOrder.setMercadoOrderId(mclOrderDTO.getId());
			mclOrder.setDateCreated(DateUtil.parseISOStr(mclOrderDTO.getDate_created()));
			mclOrder.setDateClosed(DateUtil.parseISOStr(mclOrderDTO.getDate_closed()));
			mclOrder.setLastUpdated(DateUtil.parseISOStr(mclOrderDTO.getLast_updated()));
			mclOrder.setManufacturingEndingDate(DateUtil.parseISOStr(mclOrderDTO.getManufacturing_ending_date()));
			mclOrder.setFeedBack(Objects.nonNull(mclOrderDTO.getFeedback()) ? JsonUtils.toJson(mclOrderDTO.getFeedback()) : null);
			mclOrder.setMediations(
					CollectionUtil.isNotEmpty(mclOrderDTO.getMediations()) ? JsonUtils.toJson(mclOrderDTO.getMediations()) : null);
			mclOrder.setComments(mclOrderDTO.getComments());
			mclOrder.setPackId(mclOrderDTO.getPack_id());
			mclOrder.setPickupId(mclOrderDTO.getPickup_id());
			mclOrder
				.setOrderRequest(Objects.nonNull(mclOrderDTO.getOrder_request()) ? JsonUtils.toJson(mclOrderDTO.getOrder_request()) : null);
			mclOrder.setFulfilled(mclOrderDTO.getFulfilled());
			mclOrder.setPaidAmount(mclOrderDTO.getPaid_amount());
			mclOrder.setCoupon(Objects.nonNull(mclOrderDTO.getCoupon()) ? JsonUtils.toJson(mclOrderDTO.getCoupon()) : null);
			mclOrder.setExpirationDate(DateUtil.parseISOStr(mclOrderDTO.getExpiration_date()));
			mclOrder.setCurrencyId(mclOrderDTO.getCurrency_id());
			mclOrder.setShippingId(Objects.nonNull(mclOrderDTO.getShipping()) ? mclOrderDTO.getShipping().getId() : null);
			mclOrder.setStatus(mclOrderDTO.getStatus());
			mclOrder.setStatusDetailCode(Objects.nonNull(mclOrderDTO.getStatus_detail()) ? mclOrderDTO.getStatus_detail().getCode() : null);
			mclOrder.setStatusDetailDescription(
					Objects.nonNull(mclOrderDTO.getStatus_detail()) ? mclOrderDTO.getStatus_detail().getDescription() : null);
			mclOrder.setBuyer(Objects.nonNull(mclOrderDTO.getBuyer()) ? JsonUtils.toJson(mclOrderDTO.getBuyer()) : null);
			mclOrder.setSellerId(Objects.nonNull(mclOrderDTO.getSeller()) ? mclOrderDTO.getSeller().getId() : null);
			// 税费信息
			if (Objects.nonNull(mclOrderDTO.getTaxes())) {
				mclOrder.setTaxCurrencyId(mclOrderDTO.getTaxes().getCurrency_id());
				if (StringUtils.isNotEmpty(mclOrderDTO.getTaxes().getAmount())) {
					mclOrder.setTaxAmount(new BigDecimal(mclOrderDTO.getTaxes().getAmount()));
				}
			}
			mclOrder.setContext(Objects.nonNull(mclOrderDTO.getContext()) ? JsonUtils.toJson(mclOrderDTO.getContext()) : null);
			mclOrder.setLastMpdsSyncOrderTime(
					StrUtil.isNotEmpty(mclOrderDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(mclOrderDTO.getSyncTime()) : null);
			return mclOrder;

		}
		return null;
	}

	public static void fillShipment(MclOrderVO mclOrderVO, MclShipment mclShipment) {
		mclOrderVO.setTrackingNumber(mclShipment.getTrackingNumber());
		mclOrderVO.setTrackingMethod(mclShipment.getTrackingMethod());
		mclOrderVO.setShipMentStatus(mclShipment.getStatus());
		mclOrderVO.setLogisticMode(mclShipment.getLogisticMode());
		mclOrderVO.setLogisticType(mclShipment.getLogisticType());
		if (StringUtils.isNotEmpty(mclShipment.getLeadTime())) {
			MclShipmentDTO.LeadTime leadTime = JsonUtils.jsonToObject(mclShipment.getLeadTime(), MclShipmentDTO.LeadTime.class);
			mclOrderVO.setCost(leadTime.getCost());
			mclOrderVO.setList_cost(leadTime.getList_cost());
			mclOrderVO.setCost_type(leadTime.getCost_type());
		}
		mclOrderVO.setHeight(mclShipment.getHeight());
		mclOrderVO.setLength(mclShipment.getLength());
		mclOrderVO.setWidth(mclShipment.getWidth());
		mclOrderVO.setWeight(mclShipment.getWeight());
		mclOrderVO.setStateName(mclShipment.getStateName());
		mclOrderVO.setCountryName(mclShipment.getCountryName());
		mclOrderVO.setCityName(mclShipment.getCityName());
		mclOrderVO.setZipCode(mclShipment.getZipCode());
	}
}
