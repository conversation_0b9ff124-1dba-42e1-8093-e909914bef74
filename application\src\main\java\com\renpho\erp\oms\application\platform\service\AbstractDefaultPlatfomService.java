package com.renpho.erp.oms.application.platform.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;

import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import jakarta.annotation.Resource;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

public abstract class AbstractDefaultPlatfomService implements PlatformService {
	@Resource
	private StoreClient storeClient;

	@Override
	public R<Paging<PlatformOrderVO>> page(PlatformPageQuery platformPageQuery) {
		// 订单号处理
		if (StringUtils.isNotEmpty(platformPageQuery.getOrderNos())) {
			platformPageQuery.setOrderNoArray(platformPageQuery.getOrderNos().split("\\s+"));

		}
		Paging<PlatformOrderVO> platformOrderVOPaging = this.doPage(platformPageQuery);
		List<PlatformOrderVO> records = platformOrderVOPaging.getRecords();

		// 填充店铺 、销售渠道
		Map<Integer, StoreVo> storeInfoMap = storeClient
			.getByStoreIds(records.stream().map(PlatformOrderVO::getStoreId).distinct().collect(Collectors.toList()))
			.stream()
			.collect(Collectors.toMap(StoreVo::getId, Function.identity()));

		records.stream().map(platformOrderVO -> {
			if (storeInfoMap.containsKey(platformOrderVO.getStoreId())) {
				StoreVo storeInfo = storeInfoMap.get(platformOrderVO.getStoreId());
				platformOrderVO.setStoreName(storeInfo.getStoreName());
				platformOrderVO.setSalesChannel(storeInfo.getSalesChannel());
				platformOrderVO.setChannelCode(storeInfo.getSalesChannelCode());
			}
			return platformOrderVO;
		}).collect(Collectors.toList());

		return R.success(platformOrderVOPaging);
	}

	public String getStoreName(Integer storeId) {
		List<StoreVo> storeVoList = storeClient.getByStoreIds(Arrays.asList(storeId));
		if (CollectionUtil.isNotEmpty(storeVoList)) {
			return storeVoList.get(0).getStoreName();
		}
		return null;
	}

	protected abstract Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery);
}
