package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @desc: 物流下单-工厂
 * @time: 2025-04-03 14:27:40
 * @author: Alina
 */

public class LogisticsFactory {
	private static final Map<Integer, LogisticsStrategy> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 获取
	 * @param fulfillmentServiceType 履约服务类型
	 * @return LogisticsStrategy
	 */
	public static LogisticsStrategy get(FulfillmentServiceType fulfillmentServiceType) {
		Assert.notNull(fulfillmentServiceType, "获取物流下单策略，履约服务类型不能为空");
		if (STRATEGY_MAP.containsKey(fulfillmentServiceType.getValue())) {
			return STRATEGY_MAP.get(fulfillmentServiceType.getValue());
		}
		throw new RuntimeException(MessageFormat.format("获取物流下单策略，找不到 {0} 转换器", fulfillmentServiceType.getName()));
	}

	/**
	 * 注册.
	 * @param fulfillmentServiceType 履约服务类型
	 */
	public static void register(FulfillmentServiceType fulfillmentServiceType, LogisticsStrategy logisticsStrategy) {
		Assert.notNull(fulfillmentServiceType, "");
		STRATEGY_MAP.put(fulfillmentServiceType.getValue(), logisticsStrategy);
	}
}
