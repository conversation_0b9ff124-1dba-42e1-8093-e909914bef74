package com.renpho.erp.oms.application.returnorder.vo;

import lombok.Data;

/**
 * @desc: 根据店铺Id，店铺单号获取销售订单详情 出参
 * @time: 2025-03-20 13:58:23
 * @author: <PERSON><PERSON>
 */
@Data
public class OrderItemInfoVO {

	/**
	 * 销售订单行表Id
	 */
	private Long orderItemId;

	/**
	 * 销售订单主键
	 */
	private Long orderId;

	/**
	 * 订单号，OMS的自定义单号
	 */
	private String orderNo;

	/**
	 * 采购SKU
	 */
	private String psku;

	/**
	 * 条形码
	 */
	private String fnSku;

	/**
	 * 应发数量（内部psku的应发货数量）
	 */
	private Integer quantityShipment;

}
