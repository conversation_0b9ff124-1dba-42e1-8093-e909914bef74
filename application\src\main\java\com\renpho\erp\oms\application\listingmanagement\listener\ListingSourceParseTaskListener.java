package com.renpho.erp.oms.application.listingmanagement.listener;

import com.renpho.erp.oms.application.listingmanagement.strategy.parser.ListingParser;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.ListingParserRegister;
import com.renpho.erp.oms.domain.listingmanagement.event.ListingSourceParseTaskEvent;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 原始Listing解析任务-监听器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ListingSourceParseTaskListener {

	private final SalesChannelListingSourceRepository salesChannelListingSourceRepository;
	private final ListingParserRegister listingParserRegister;

	@Async
	@EventListener
	public void parseTask(ListingSourceParseTaskEvent event) {
		if (Objects.isNull(event) || Objects.isNull(event.listingSourceId())) {
			return;
		}
		Long listingSourceId = event.listingSourceId();
		SalesChannelListingSource listingSource = salesChannelListingSourceRepository.findById(listingSourceId);
		if (Objects.isNull(listingSource)) {
			return;
		}
		String salesChannelCode = listingSource.getSalesChannelCode();
		// 获取解析器
		ListingParser parser = listingParserRegister.getParser(salesChannelCode);
		if (Objects.isNull(parser)) {
			log.warn("销售渠道编码：{}，对应的解析器不存在", salesChannelCode);
			return;
		}
		try {
			// 解析Listing
			parser.parseListing(listingSource);
		}
		catch (Exception e) {
			log.error("销售渠道编码：{}，原始listing主键id:{}，解析失败：", salesChannelCode, listingSourceId, e);
		}
	}

}
