package com.renpho.erp.oms.domain.vc.order.model;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单商品值对象 基于Amazon SP-API getPurchaseOrder接口的items字段
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderItem {

	/**
	 * 商品行号 采购订单上商品的编号，第一个商品为1，第二个为2，依此类推
	 */
	private String itemSequenceNumber;

	/**
	 * 亚马逊商品标识符（ASIN）
	 */
	private String amazonProductIdentifier;

	/**
	 * 供应商商品标识符
	 */
	private String vendorProductIdentifier;

	/**
	 * 订购数量详情
	 */
	private AmzVcOrderedQuantity orderedQuantity;

	/**
	 * 是否允许缺货
	 */
	private Boolean isBackOrderAllowed;

	/**
	 * 净成本
	 */
	private AmzVcOrderItemMoney netCost;

	/**
	 * 标价
	 */
	private AmzVcOrderItemMoney listPrice;

}
