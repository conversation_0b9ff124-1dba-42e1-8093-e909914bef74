package com.renpho.erp.oms.application.vc;

import java.util.TimeZone;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.renpho.erp.oms.domain.vc.order.VcOrderModifyDomainService;
import com.renpho.erp.oms.domain.vc.order.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcOrderAggRoot;
import com.renpho.erp.oms.infrastructure.manager.AmzVcOrderSourceJsonManager;

import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
class VcOrderModifyDomainServiceTest {

	@Resource
	private VcOrderModifyDomainService vcOrderModifyDomainService;
	@Resource
	private VcOrderRepository vcOrderRepository;
	@Resource
	private AmzVcOrderSourceJsonManager amzVcOrderSourceJsonManager;

	@BeforeEach
	void setUp() {
		TimeZone.setDefault(TimeZone.getTimeZone("UTC+0"));
	}

	@Test
	void convertAndModify() {
		// 查询DB-VC订单
		VcOrderAggRoot oldVcOrder = vcOrderRepository.findById(1943147712195559426L);
		// 查询亚马逊vc源订单
		AmzVcOrder amzVcOrder = amzVcOrderSourceJsonManager.findById(160L);
		// 转换修改VC订单（生成新聚合根）
		VcOrderAggRoot newVcOrder = vcOrderModifyDomainService.convertAndModify(amzVcOrder, oldVcOrder);
		System.err.println(newVcOrder.toString());
	}
}
