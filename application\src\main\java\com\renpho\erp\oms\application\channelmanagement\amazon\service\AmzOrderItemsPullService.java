package com.renpho.erp.oms.application.channelmanagement.amazon.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;

import com.renpho.erp.mdm.client.store.command.StoreAuthorizationQuery;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.common.dto.PullOrerParam;
import com.renpho.erp.oms.domain.channelmanagement.amazon.command.query.AmzOrderSourceJsonQuery;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderSourceJson;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSourceJsonRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.AmazonApiCodeEnums;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnums;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.renpho.erp.oms.infrastructure.common.util.MDCUtil;
import com.renpho.erp.oms.infrastructure.config.AmzPullConfig;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.karma.dto.R;
import io.swagger.client.model.orders.GetOrderItemsResponse;
import io.swagger.client.model.orders.OrderItem;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.thread.ThreadUtil.sleep;

@Service
@Slf4j
@RequiredArgsConstructor
public class AmzOrderItemsPullService {

	private static final Long SLEEP_TIME_MILLISECONDS = 30000L;

	private final AmzPullConfig amzPullConfig;

	private final AmzOrderSourceJsonRepository amzOrderSourceJsonRepository;
	private final StoreClient storeClient;
	private final AmazonClient amazonClient;
	private final AmzItemCreateJsonService amzItemCreateJsonService;
	private final ExecutorService pullItemExecutor;

	/**
	 * 获取订单商品
	 */
	public void pullItems(PullOrerParam pullOrerParam) {
		// 获取店铺及授权信息
		StoreAuthorizationQuery storeAuthorizationQuery = new StoreAuthorizationQuery();
		storeAuthorizationQuery.setChannelCode(ChannelCode.AMZ_CHANNEL_CODE);
		storeAuthorizationQuery.setStoreType(StoreTypeEnums.SC.getValue());
		// 任务自动执行时填充分片参数
		Optional.ofNullable(JobShardContext.getShardInfo()).ifPresent(jobShardInfo -> {
			storeAuthorizationQuery.setShardIndex(jobShardInfo.getShardIndex());
			storeAuthorizationQuery.setShardTotal(jobShardInfo.getShardTotal());
		});
		List<StoreAuthorizationVo> storeAuthorizationList = storeClient.getShardingStoreAuthorizations(storeAuthorizationQuery);
		if (CollectionUtil.isNotEmpty(storeAuthorizationList)) {
			log.info("Starting asynchronous pull item process...");
			try {
				List<CompletableFuture<Void>> futures = storeAuthorizationList.stream()
					.map(storeAuthorization -> handleCompletableFuture(storeAuthorization, pullOrerParam))
					.collect(Collectors.toList());
				// 等待所有异步任务完成
				CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
			}
			catch (Exception e) {
				log.error("异步任务执行异常：", e);
			}
			log.info("All asynchronous pull item tasks completed.");
		}

	}

	private CompletableFuture<Void> handleCompletableFuture(StoreAuthorizationVo storeAuthorization, PullOrerParam pullOrerParam) {
		return CompletableFuture
			.runAsync(MDCUtil.wrapWithMDC(() -> processSingleStore(pullOrerParam, storeAuthorization.getAuthorization().getAmzSellerId())),
					pullItemExecutor)
			.handle((result, ex) -> {
				if (ex != null) {
					log.error("处理 卖家 拉取商品异步任务时出错：{}", storeAuthorization.getAuthorization().getAmzSellerId(), ex);
				}
				return null;
			})
			.thenAccept(o -> {
			});
	}

	private void processSingleStore(PullOrerParam pullOrerParam, String sellerId) {
		// 通过店铺id分页查询未同步的
		AmzOrderSourceJsonQuery amzOrderSourceJsonQuery = new AmzOrderSourceJsonQuery();
		// 设置订单查询参数
		amzOrderSourceJsonQuery.setOrderStatus(amzPullConfig.getOrderStatus());
		amzOrderSourceJsonQuery.setLimit(amzPullConfig.getLimit());

		// 手动输入
		if (Objects.nonNull(pullOrerParam)) {
			amzOrderSourceJsonQuery.setOrderIds(pullOrerParam.getOrderIds());
			amzOrderSourceJsonQuery.setSellerIds(pullOrerParam.getSellerIds());
		}
		else {
			amzOrderSourceJsonQuery.setSellerIds(Arrays.asList(sellerId));
		}

		// 执行分页查询
		List<AmzOrderSourceJson> amzOrderSourceFilterPIIJsonList = amzOrderSourceJsonRepository
			.list(amzOrderSourceJsonQuery);
		amzOrderSourceFilterPIIJsonList.forEach(amazonOrderSourceFilterPIIJson -> {
			List<OrderItem> orderItemList = doPull(sellerId, amazonOrderSourceFilterPIIJson.getPlatOrderId());
			// 入库操作
			if (CollectionUtil.isNotEmpty(orderItemList)) {
				amzItemCreateJsonService.insert(orderItemList, amazonOrderSourceFilterPIIJson.getPlatOrderId(),
						amazonOrderSourceFilterPIIJson.getSellerId(), amazonOrderSourceFilterPIIJson.getStoreId());
			}
		});
	}

	private List<OrderItem> doPull(String sellerId, String orderId) {
		List<OrderItem> orderItemList = Lists.newArrayList();
		boolean successFlag = true;
		String nextToken = null; // 构建参数查询
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSellerId(sellerId);
		do {
			R<GetOrderItemsResponse> itemsResponse = amazonClient.getItems(shopAccount, orderId, nextToken);
			// 限流
			if (AmazonApiCodeEnums.REQUEST_LIMITING.getCode().equals(itemsResponse.getCode())) {
				sleep(SLEEP_TIME_MILLISECONDS, TimeUnit.MILLISECONDS);
				// 暂停后继续拉取
				continue;
			}
			// 失败停止拉取
			if (!itemsResponse.isSuccess()) {
				log.error("查询亚马逊订单商品失败，失败原因为{}", itemsResponse.getMessage());
				successFlag = false;
				// todo 异常通知
				break;
			}
			log.info("查询亚马逊订单商品详情返回结果为{}", JsonUtils.toJson(itemsResponse));
			orderItemList.addAll(itemsResponse.getData().getPayload().getOrderItems());
		}
		while (StringUtils.isNotEmpty(nextToken));
		if (successFlag) {
			return orderItemList;
		}
		return null;
	}
}
