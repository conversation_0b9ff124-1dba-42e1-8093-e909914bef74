package com.renpho.erp.oms.domain.vc.order.model;

import java.time.LocalDateTime;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * VC交货窗口值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class VcShippingWindow {

	/**
	 * 交货窗口-起始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 交货窗口-结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 检查交货窗口是否有效
	 * @return boolean
	 */
	public boolean isValid() {
		return startTime != null && endTime != null && startTime.isBefore(endTime);
	}

	/**
	 * 检查指定时间是否在交货窗口内
	 * @param time 指定时间
	 * @return boolean
	 */
	public boolean contains(LocalDateTime time) {
		if (!isValid() || time == null) {
			return false;
		}
		return !time.isBefore(startTime) && !time.isAfter(endTime);
	}

}
