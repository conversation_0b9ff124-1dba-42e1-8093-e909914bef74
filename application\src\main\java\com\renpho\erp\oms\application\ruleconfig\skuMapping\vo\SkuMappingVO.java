package com.renpho.erp.oms.application.ruleconfig.skuMapping.vo;

import java.math.BigDecimal;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import lombok.Data;

@Data
public class SkuMappingVO {
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺
	 */
	private String store;

	/**
	 * 店铺id
	 */
	private Integer storeId;

	/**
	 * 渠道
	 */
	private String channel;

	/**
	 * 渠道id
	 */
	private Integer channelId;

	/**
	 * 运营人员
	 */
	private String operator;

	/**
	 * 运营人员工号
	 */
	private String operatorNo;

	/**
	 * 运营人员id
	 */

	private Integer operatorId;

	/**
	 * 状态
	 */
	private Integer status;

	private String asin;

	/**
	 * 销售SKU
	 */
	private String msku;

	/**
	 * 是否统计FBA库存
	 */
	private Integer fbaInventoryCounted;

	/**
	 * 发货方式
	 */
	private Integer fulfillmentType;

	/**
	 * 映射行
	 */
	private List<SkuMappingLineVO> skuMappingLineVOList;

	@Data
	public static class SkuMappingLineVO {
		/**
		 * 采购SKU
		 */
		private String psku;

		/**
		 * 条形码
		 */
		private String fnsku;

		/**
		 * 条形码商品数量
		 */
		private Integer fnskuQuantity;

		/**
		 * 零售单价
		 */
		private BigDecimal retailPrice;
	}

}
