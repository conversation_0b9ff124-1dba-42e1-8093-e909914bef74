package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

/**
 * 客户收货信息视图对象
 */
@Data
public class CustomerShippingVO {
    /**
     * ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 收件人
     */
    private String attnTo;
    
    /**
     * 收货公司
     */
    private String shippingCompany;
    
    /**
     * 地址
     */
    private String address;
    
    /**
     * 邮编
     */
    private String zipCode;
    
    /**
     * 城市
     */
    private String city;
    
    /**
     * 国家
     */
    private String country;
    
    /**
     * 联系电话
     */
    private String contactNumber;
} 