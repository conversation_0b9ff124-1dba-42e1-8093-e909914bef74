package com.renpho.erp.oms.adapter.client.converter;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

import com.renpho.erp.oms.client.amz.vo.AmzSellerMtpInfoVO;
import com.renpho.erp.oms.infrastructure.persistence.amazon.po.AmzSellerMtpInfoPO;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface AmzSellerMtpInfoConverter {

	List<AmzSellerMtpInfoVO> toVO(List<AmzSellerMtpInfoPO> po);

}
