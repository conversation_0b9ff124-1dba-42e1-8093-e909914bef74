package com.renpho.erp.oms.application.channelmanagement.monitor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

import cn.hutool.core.lang.Assert;
import lombok.Getter;
import lombok.Setter;

/**
 * 渠道订单监控器
 * <AUTHOR>
 */
public interface ChannelOrderMonitor {

	/**
	 * 获取销售渠道编码
	 *
	 * @return ChannelCode的值
	 */
	String getChannelCode();

	/**
	 * 渠道订单监控
	 * @param monitorCmd 指令
	 */
	void monitor(MonitorCmd monitorCmd);

	@Getter
	@Setter
	class MonitorCmd {

		/**
		 * 渠道编码
		 */
		private String channelCode;

		/**
		 * 监控列表参数
		 */
		private List<Monitor> monitors;

		public void validate() {
			Assert.notNull(channelCode);
			Assert.notEmpty(monitors);
			monitors.forEach(monitor -> Assert.notEmpty(monitor.getStoreNames()));
		}

		@Getter
		@Setter
		public static class Monitor {

			/**
			 * 店铺名称集合
			 */
			private Set<String> storeNames;

			/**
			 * 是否按照卖家维度统计，默认否
			 */
			private boolean seller = false;

			/**
			 * 昨天之前的天数，默认30天
			 */
			private Integer day = 30;

			/**
			 * 日平均单量(昨天之前的n天)与昨日单量的差异比例阈值，默认10%，0.1
			 */
			private BigDecimal ratio = new BigDecimal("0.1");
		}
	}
}
