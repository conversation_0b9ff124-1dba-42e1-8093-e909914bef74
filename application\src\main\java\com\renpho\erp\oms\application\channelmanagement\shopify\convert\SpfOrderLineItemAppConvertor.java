//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderLineItem;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;

public class SpfOrderLineItemAppConvertor {

	public static List<SpfOrderLineItem> toDomainList(List<SpfOrderDTO.LineItem> lineItems) {
		List<SpfOrderLineItem> spfOrderLineItemList = Lists.newArrayList();
		lineItems.forEach((lineItem) -> {
			spfOrderLineItemList.add(toDomain(lineItem));
		});
		return spfOrderLineItemList;
	}

	public static SpfOrderLineItem toDomain(SpfOrderDTO.LineItem item) {
		if (Objects.nonNull(item)) {
			SpfOrderLineItem spfOrderLineItem = new SpfOrderLineItem();
			spfOrderLineItem.setItemId(item.getId());
			spfOrderLineItem.setAdminGraphqlApiId(item.getAdmin_graphql_api_id());
			spfOrderLineItem.setAttributedStaffs(
					CollectionUtil.isNotEmpty(item.getAttributed_staffs()) ? JsonUtils.toJson(item.getAttributed_staffs()) : null);
			spfOrderLineItem.setCurrentQuantity(item.getCurrent_quantity());
			spfOrderLineItem.setFulfillmentService(item.getFulfillment_service());
			spfOrderLineItem.setFulfillmentStatus(item.getFulfillment_status());
			spfOrderLineItem.setGiftCard(item.isGift_card());
			spfOrderLineItem.setGrams(item.getGrams());
			spfOrderLineItem.setName(item.getName());
			spfOrderLineItem
				.setPreTaxPrice(StringUtils.isNotEmpty(item.getPre_tax_price()) ? new BigDecimal(item.getPre_tax_price()) : null);
			spfOrderLineItem
				.setPreTaxPriceSet(Objects.nonNull(item.getPre_tax_price_set()) ? JsonUtils.toJson(item.getPre_tax_price_set()) : null);
			spfOrderLineItem.setPrice(StringUtils.isNotEmpty(item.getPrice()) ? new BigDecimal(item.getPrice()) : null);
			spfOrderLineItem.setPriceSet(Objects.nonNull(item.getPrice_set()) ? JsonUtils.toJson(item.getPrice_set()) : null);
			spfOrderLineItem.setProductExists(item.isProduct_exists());
			spfOrderLineItem.setProductId(item.getProduct_id());
			spfOrderLineItem.setProperties(CollectionUtil.isNotEmpty(item.getProperties()) ? JsonUtils.toJson(item.getProperties()) : null);
			spfOrderLineItem.setQuantity(item.getQuantity());
			spfOrderLineItem.setRequiresShipping(item.isRequires_shipping());
			spfOrderLineItem.setSku(item.getSku());
			spfOrderLineItem.setTaxable(item.isTaxable());
			spfOrderLineItem
				.setTotalDiscount(StringUtils.isNotEmpty(item.getTotal_discount()) ? new BigDecimal(item.getTotal_discount()) : null);
			spfOrderLineItem
				.setTotalDiscountSet(Objects.nonNull(item.getTotal_discount_set()) ? JsonUtils.toJson(item.getTotal_discount_set()) : null);
			spfOrderLineItem.setVariantId(item.getVariant_id());
			spfOrderLineItem.setVariantInventoryManagement(item.getVariant_inventory_management());
			spfOrderLineItem.setVariantTitle(item.getVariant_title());
			spfOrderLineItem.setVendor(item.getVendor());
			spfOrderLineItem.setTaxLines(CollectionUtil.isNotEmpty(item.getTax_lines()) ? JsonUtils.toJson(item.getTax_lines()) : null);
			spfOrderLineItem.setDuties(CollectionUtil.isNotEmpty(item.getDuties()) ? JsonUtils.toJson(item.getDuties()) : null);
			spfOrderLineItem.setDiscountAllocations(
					CollectionUtil.isNotEmpty(item.getDiscount_allocations()) ? JsonUtils.toJson(item.getDiscount_allocations()) : null);
			return spfOrderLineItem;
		}
		else {
			return null;
		}
	}
}
