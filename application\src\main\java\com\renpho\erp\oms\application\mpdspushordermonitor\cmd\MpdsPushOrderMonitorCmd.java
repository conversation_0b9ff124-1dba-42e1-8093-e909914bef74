package com.renpho.erp.oms.application.mpdspushordermonitor.cmd;

import lombok.Data;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @description 监控mds推送订单
 * @date 2025/5/8 14:59
 */
@Data
public class MpdsPushOrderMonitorCmd {

    /**
	 * 指定开始时间,格式yyyy-MM-dd HH:mm:ss
	 */
    private String startTime;

    /**
	 * 指定结束时间,格式yyyy-MM-dd HH:mm:ss
	 */
    private String endTime;

    /**
     * 没指定开始时间，结束时间时，默认比较近lastHour小时
     */
    private Integer lastHour = 1;

    /**
     * 要监控的渠道
     */
    private List<String> monitorChannelTypes = Lists.newArrayList();

    /**
     * 自动重推订单
     */
    private boolean autoRetryPushOrder = false;
}
