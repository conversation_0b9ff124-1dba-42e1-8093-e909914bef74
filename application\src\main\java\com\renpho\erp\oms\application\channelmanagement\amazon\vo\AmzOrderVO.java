package com.renpho.erp.oms.application.channelmanagement.amazon.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AmzOrderVO {
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;
	/**
	 * 亚马逊订单号
	 */
	private String amazonOrderId;

	/**
	 * 卖家订单号
	 */
	private String sellerOrderId;

	/**
	 * 订单是否优先配送 0 否 1 是
	 */
	private Boolean isPremiumOrder;

	/**
	 * 是否Amazon Prime订单 0 否 1 是
	 */
	private Boolean isPrime;

	/**
	 * 是否有管制物品 0 否 1 是
	 */
	private Boolean hasRegulatedItems;

	/**
	 * 是否替换订单 0 否 1 是
	 */
	private Boolean isReplacementOrder;

	/**
	 * 是否由ABEU转售 0 否 1 是
	 */
	private Boolean isSoldByAb;

	/**
	 * 订单被标记为从商店取货而不是交付 0 否 1 是
	 */
	private Boolean isIspu;

	/**
	 * 订单是否已送达标记点 0 否 1 是
	 */
	private Boolean isAccessPointOrder;

	/**
	 * 是否是AmazonBusiness订单 0 否 1 是
	 */
	private Boolean isBusinessOrder;

	/**
	 * 是否GlobalExpress订单 0 否 1 是
	 */
	private Boolean isGlobalExpressEnabled;

	/**
	 * 订单的类型 StandardOrder,LongLeadTimeOrder,Preorder,BackOrder,SourcingOnDemandOrder
	 */
	private String orderType;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 订单是由亚马逊 (AFN) 还是由卖家 (MFN) 完成的 MFN,AFN
	 */
	private String fulfillmentChannel;

	/**
	 * 订单状态
	 * Pending,Unshipped,PartiallyShipped,Shipped,Canceled,Unfulfillable,InvoiceUnconfirmed,PendingAvailability
	 */
	private String orderStatus;

	/**
	 * 销售渠道
	 */
	private String salesChannel;

	/**
	 * 发货区
	 */
	private String shippingStateOrRegion;

	/**
	 * 发货国家代码
	 */
	private String shippingCountryCode;

	/**
	 * 发货城市
	 */
	private String shippingCity;

	/**
	 * 发货邮政编码
	 */
	private String shippingPostalCode;

	/**
	 * 订单的发货服务级别类别 Expedited, FreeEconomy, NextDay, Priority, SameDay, SecondDay,
	 * Scheduled, and Standard
	 */
	private String shipmentServiceLevelCategory;

	/**
	 * 最早发货日期
	 */
	private LocalDateTime earliestShipDate;

	/**
	 * 最晚发货日期
	 */
	private LocalDateTime latestShipDate;

	/**
	 * 创建订单的日期
	 */
	private LocalDateTime purchaseDate;

	/**
	 * 上次更新订单的日期
	 */
	private LocalDateTime lastUpdateDate;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

}
