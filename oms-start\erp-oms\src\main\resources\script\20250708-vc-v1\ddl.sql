-- 亚马逊vc源订单表
CREATE TABLE `oms_amz_vc_order_source_json` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `store_id` int NOT NULL COMMENT '店铺id',
  `purchase_order_number` varchar(64) NOT NULL COMMENT '订单号',
  `seller_id` varchar(64) DEFAULT NULL COMMENT '卖家id',
  `purchase_order_state` varchar(32) NOT NULL COMMENT '订单状态',
  `order_json` text  NOT NULL COMMENT '订单json',
  `order_status_json` text  DEFAULT NULL COMMENT '订单状态json',
  `parse_order_status` int NOT NULL DEFAULT '0' COMMENT '解析订单状态:0:待解析订单,1:已解析,2解析失败,3无需处理',
  `fail_count` int NOT NULL DEFAULT '0' COMMENT '失败次数',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_purchase_order_number_store_id` (`purchase_order_number`,`store_id`),
  KEY `idx_store_id` (`store_id`) USING BTREE,
  KEY `idx_create_time` (`create_time`) USING BTREE,
  KEY `idx_fail_count` (`fail_count`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='亚马逊vc源订单表';


CREATE TABLE `oms_vc_order` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `vc_purchase_no` varchar(32) DEFAULT NULL COMMENT 'vc采购单号',
  `store_id` int unsigned NOT NULL COMMENT '店铺ID',
  `store_name` varchar(32) NOT NULL COMMENT '店铺名称',
  `site_code` varchar(16) DEFAULT NULL COMMENT '站点编码',
  `order_status` int NOT NULL COMMENT '订单状态 1-待接单 2-接单异常 3-待上传箱唛 4-备货中 5-待开票 6-已完成 7-已取消',
  `accepted_status` int DEFAULT NULL COMMENT '接单子状态 1-未接单 2-接单确认中 3-已接单 4-接单异常',
  `accepted_method` int DEFAULT NULL COMMENT '接单方式，是否ERP接单 1-是 0-否',
  `parse_status` int DEFAULT NULL COMMENT '解析SKU状态 1-解析成功 2-SKU映射异常',
  `business_type` int NOT NULL COMMENT '业务类型 1-DI（直接进口） 2-DO（海外仓备货模式）',
  `order_type` varchar(32) DEFAULT NULL COMMENT '订单类型',
  `invoice_type` varchar(32) DEFAULT NULL COMMENT '发票类型',
  `shipping_window_start` datetime NOT NULL COMMENT '交货窗口-起始时间',
  `shipping_window_end` datetime NOT NULL COMMENT '交货窗口-结束时间',
  `ordered_time` datetime DEFAULT NULL COMMENT '下单时间',
  `accepted_time` datetime DEFAULT NULL COMMENT '接单时间',
  `shipped_time` datetime DEFAULT NULL COMMENT '发货时间',
  `invoiced_time` datetime DEFAULT NULL COMMENT '开票时间',
  `cancelled_time` datetime DEFAULT NULL COMMENT '取消时间',
  `currency` varchar(8) NOT NULL COMMENT '币种',
  `product_price` decimal(14,2) NOT NULL COMMENT '商品收入，[数量*单价]之和，如果已接单则为接单数量，否则为下单数量',
  `tax_rate` decimal(7,4) NOT NULL COMMENT '税率，0.2代表20%',
  `tax` decimal(14,2) NOT NULL COMMENT '税额，税额明细行的汇总',
  `ship_from` varchar(32) NOT NULL COMMENT '发货地',
  `ship_to` varchar(32) NOT NULL COMMENT '收货地',
  `bill_to` varchar(32) NOT NULL COMMENT '账单地址',
  `buying` varchar(32) NOT NULL COMMENT '买家地址',
  `exception_reason` varchar(256) DEFAULT NULL COMMENT '异常原因',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_no` (`order_no`),
  KEY `idx_vc_purchase_no` (`vc_purchase_no`)
) COMMENT='VC-订单表';

CREATE TABLE `oms_vc_order_di` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` bigint unsigned NOT NULL COMMENT 'vc订单id',
  `payment_method` varchar(64) NOT NULL COMMENT '付款条款',
  `incoterms` varchar(64) NOT NULL COMMENT '贸易条款',
  `container_type` varchar(64) NOT NULL COMMENT '集装箱规格',
  `shipping_instructions` varchar(64) DEFAULT NULL COMMENT '发货描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='VC-订单DI表';

CREATE TABLE `oms_vc_order_item` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` bigint unsigned NOT NULL COMMENT 'vc订单id',
  `channel_order_line_no` varchar(32) DEFAULT NULL COMMENT '渠道订单行号',
  `msku` varchar(64) NOT NULL COMMENT '销售SKU',
  `asin` varchar(64) NOT NULL COMMENT '采购SKU',
  `psku` varchar(64) DEFAULT NULL COMMENT '采购SKU',
  `barcode` varchar(64) DEFAULT NULL COMMENT '条码',
  `number_of_units_per_box` int DEFAULT NULL COMMENT '装箱数量（每箱的psku数量）',
  `image_id` varchar(64) DEFAULT NULL COMMENT '图片id',
  `ordered_quantity` int NOT NULL COMMENT '下单数量',
  `accepted_quantity` int NOT NULL COMMENT '接受数量',
  `rejected_quantity` int NOT NULL COMMENT '拒绝数量',
  `rejection_reason` int DEFAULT NULL COMMENT '拒绝原因 1-缺货 2-sku错误 3-退市',
  `shipped_quantity` int DEFAULT NULL COMMENT '发货数量',
  `received_quantity` int DEFAULT NULL COMMENT '接收数量',
  `currency` varchar(8) NOT NULL COMMENT '币种',
  `unit_price` decimal(14,2) NOT NULL COMMENT '单价',
  `tax` decimal(14,2) NOT NULL COMMENT '税额',
  `sub_total` decimal(14,2) NOT NULL COMMENT '小计，单价*数量+税额，如果已接单则为接单数量，否则为下单数量',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='VC-订单商品表';

CREATE TABLE `oms_vc_order_item_label` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` bigint unsigned NOT NULL COMMENT 'vc订单id',
  `order_item_id` bigint unsigned NOT NULL COMMENT 'vc订单行id',
  `asin_label_url` varchar(256) NOT NULL COMMENT '物流标-文件url',
  `asin_label_name` varchar(256) NOT NULL COMMENT '物流标-文件名',
  `carton_label_url` varchar(256) NOT NULL COMMENT '外箱标-文件url',
  `carton_label_name` varchar(256) NOT NULL COMMENT '外箱标-文件名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='VC-订单商品箱唛表';

CREATE TABLE `oms_vc_order_invoice` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` bigint unsigned NOT NULL COMMENT 'vc订单id',
  `invoice_no` varchar(32) NOT NULL COMMENT '发票号',
  `invoice_date` date NOT NULL COMMENT '开票日期',
  `tax_rate` decimal(7,4) NOT NULL COMMENT '税率，0.2代表20%',
  `tax` decimal(14,2) NOT NULL COMMENT '发票金额',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='VC-订单发票表';

CREATE TABLE `oms_vc_order_remark` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `order_id` bigint unsigned NOT NULL COMMENT 'vc订单id',
  `remark` varchar(256) NOT NULL COMMENT '备注信息',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `create_by` int NOT NULL COMMENT '创建人ID',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `update_by` int NOT NULL COMMENT '更新人ID',
  `is_deleted` int NOT NULL DEFAULT '0' COMMENT '是否删除，0 否、1 是，默认0',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) COMMENT='VC-订单备注表';