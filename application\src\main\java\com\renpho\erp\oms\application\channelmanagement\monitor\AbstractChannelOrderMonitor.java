package com.renpho.erp.oms.application.channelmanagement.monitor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ThreadLocalRandom;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

import cn.hutool.extra.spring.SpringUtil;
import lombok.Getter;
import lombok.Setter;

/**
 * 抽象渠道订单监控器
 *
 * <AUTHOR>
 */
public abstract class AbstractChannelOrderMonitor implements ChannelOrderMonitor, InitializingBean {

	private final StoreClient storeClient;
	private final RedisTemplate<String, String> redisTemplate;
	private final DingTalkClient dingTalkClient;

	/**
	 * 获取当前环境profile
	 */
	@Value("${spring.profiles.active}")
	private String env;

	protected AbstractChannelOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate,
			DingTalkClient dingTalkClient) {
		this.storeClient = storeClient;
		this.redisTemplate = redisTemplate;
		this.dingTalkClient = dingTalkClient;
	}

	@Override
	public void monitor(MonitorCmd cmd) {
		// 根据渠道编码，获取渠道编码
		List<StoreAuthorizationVo> storeAuthorizationList = storeClient.getStoreAuthorizations(this.getChannelCode());
		// 监控列表参数
		List<MonitorCmd.Monitor> monitors = cmd.getMonitors();
		monitors.forEach(monitor -> {
			// 构建店铺授权信息
			List<StoreAuthorization> storeAuthorizations = this.buildStoreAuthorization(storeAuthorizationList, monitor);
			// 今天的0点
			LocalDateTime todayZero = LocalDate.now().atStartOfDay();
			// 昨天之前的n天
			Integer day = monitor.getDay();
			// 昨天的0点
			LocalDateTime yesterdayZero = LocalDate.now().minusDays(1).atStartOfDay();
			// 昨天之前的n天的0点时间
			LocalDateTime nDaysAgo = yesterdayZero.minusDays(day);
			// 按相应维度统计
			storeAuthorizations.forEach(storeAuthorization -> {
				// 获取昨天之前的n天的日均单量
				long averagePerDayOrderCount = this.getAveragePerDayOrderCount(storeAuthorization, nDaysAgo, yesterdayZero, day);
				if (BigDecimal.ZERO.compareTo(BigDecimal.valueOf(averagePerDayOrderCount)) == 0) {
					return;
				}
				// 昨天的单量
				Long yesterdayOrderCount = this.countOrder(storeAuthorization, yesterdayZero, todayZero);
				// 差异比例：(昨天的单量−昨天之前的n天的日均单量)/昨天之前的n天的日均单量
				BigDecimal radio = BigDecimal.valueOf(yesterdayOrderCount)
					.subtract(BigDecimal.valueOf(averagePerDayOrderCount))
					.divide(BigDecimal.valueOf(averagePerDayOrderCount), 2, RoundingMode.HALF_UP);
				// 差异比例小于0 并且 差异比例的绝对值 大于 阈值，钉钉告警
				if (radio.compareTo(BigDecimal.ZERO) < 0 && radio.abs().compareTo(monitor.getRatio()) > 0) {
					// 环境-平台-店铺
					String s1 = "环境：" + env + " 平台：" + cmd.getChannelCode() + " 店铺：" + storeAuthorization.getStoreNames();
					// 时间范围的单量
					String s2 = " 时间范围" + DateUtil.convertToUTC(nDaysAgo) + "至" + DateUtil.convertToUTC(yesterdayZero) + " 日平均订单量："
							+ averagePerDayOrderCount;
					// 昨天单量
					String s3 = " 昨日" + yesterdayZero.toLocalDate() + "订单量：" + yesterdayOrderCount;
					// 差异比例
					String s4 = " 较日平均单量：" + (yesterdayOrderCount - averagePerDayOrderCount) + " 超过阈值"
							+ monitor.getRatio().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP) + "%";
					// 钉钉告警
					dingTalkClient.sendAlarmMessage(s1 + s2 + s3 + s4);
				}
			});
		});
	}

	/**
	 * 昨天之前的n天的日均单量
	 * @param storeAuthorization 店铺授权信息
	 * @param nDaysAgo 昨天之前的n天的0点时间
	 * @param yesterdayZero 昨天的0点
	 * @return BigDecimal
	 */
	private long getAveragePerDayOrderCount(StoreAuthorization storeAuthorization, LocalDateTime nDaysAgo, LocalDateTime yesterdayZero,
			Integer day) {
		// 昨天之前的n天的日均单量的缓存key
		String cacheKey = this.getAveragePerDayOrderCountCacheKey(storeAuthorization, nDaysAgo.toLocalDate().toString(),
				yesterdayZero.toLocalDate().minusDays(1).toString());
		String averagePerDayOrderCountStr = redisTemplate.opsForValue().get(cacheKey);
		if (Objects.isNull(averagePerDayOrderCountStr)) {
			// 定时任务入口，不会有缓存击穿情况，不用加互斥锁
			// 昨天之前的n天的单量
			Long nDaysAgoOrderCount = this.countOrder(storeAuthorization, nDaysAgo, yesterdayZero);
			// 昨天之前的n天的日均单量
			long averagePerDayOrderCount = BigDecimal.valueOf(nDaysAgoOrderCount)
				.divide(BigDecimal.valueOf(day), 0, RoundingMode.HALF_UP)
				.longValue();
			// 缓存redis，设置过期时间（打散过期时间：明天 0 点到 1 点之间的随机时间）
			redisTemplate.opsForValue().set(cacheKey, String.valueOf(averagePerDayOrderCount), this.getCacheExpireDuration());
			return averagePerDayOrderCount;
		}
		else {
			return Long.parseLong(averagePerDayOrderCountStr);
		}
	}

	/**
	 * 获取明天的 0 点 - 1 点（随机时间）到当前时间的 Duration
	 * @return Duration
	 */
	private Duration getCacheExpireDuration() {
		// 明天的 0 点和 1 点
		LocalDateTime tomorrowZero = LocalDate.now().plusDays(1).atStartOfDay();
		LocalDateTime tomorrowOne = tomorrowZero.plusHours(1);

		// 随机生成明天 0 点到 1 点之间的时间
		long startEpoch = tomorrowZero.toEpochSecond(ZoneOffset.UTC);
		long endEpoch = tomorrowOne.toEpochSecond(ZoneOffset.UTC);
		long randomEpoch = ThreadLocalRandom.current().nextLong(startEpoch, endEpoch);

		LocalDateTime randomTime = LocalDateTime.ofEpochSecond(randomEpoch, 0, ZoneOffset.UTC);

		// 计算 Duration：随机时间 到 当前时间
		return Duration.between(LocalDateTime.now(), randomTime);
	}

	/**
	 * 构建店铺授权信息
	 *
	 * @param storeAuthorizationList 店铺授权信息列表
	 * @param monitor 监控参数
	 * @return List
	 */
	protected abstract List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor);

	/**
	 * 查询单量
	 *
	 * @param start 开始时间（包含）
	 * @param end 结束时间（不包含）
	 * @return 单量
	 */
	protected abstract Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end);

	/**
	 * 昨天之前的n天的日均单量的缓存key
	 * @param storeAuthorization 店铺授权信息
	 * @param nDaysAgo 开始日期
	 * @param yesterdayZero 结束日期
	 * @return String
	 */
	protected String getAveragePerDayOrderCountCacheKey(StoreAuthorization storeAuthorization, String nDaysAgo, String yesterdayZero) {
		return "oms:channel:order:averagePerDayOrderCount:" + this.getChannelCode() + ":" + this.getCacheKey(storeAuthorization) + ":"
				+ nDaysAgo + " " + yesterdayZero;
	}

	/**
	 * 获取缓存key的店铺标识符
	 * @param storeAuthorization 店铺授权信息
	 * @return String
	 */
	protected abstract String getCacheKey(StoreAuthorization storeAuthorization);

	@Override
	public void afterPropertiesSet() {
		// 使用代理对象，不用this，避免事务失效等问题
		String channelCode = this.getChannelCode();
		ChannelOrderMonitorFactory.register(channelCode, SpringUtil.getBean(this.getClass()));
	}

	@Getter
	@Setter
	public static class StoreAuthorization {

		/**
		 * Amazon，市场ID
		 */
		private String amzMarketplaceId;

		/**
		 * Amazon，卖家ID
		 */
		private String amzSellerId;

		/**
		 * Walmart，店铺ID
		 */
		private String wmtShopId;

		/**
		 * Mercadolibre，全球用户ID
		 */
		private String mclUserId;

		/**
		 * Tiktok，店铺ID
		 */
		private String ttkShopId;

		/**
		 * Shopify，店铺URL
		 */
		private String spfShopUrl;

		/**
		 * temu 店铺id
		 */
		private String temuShopId;
		/**
		 * ebay 卖家ID
		 */
		private String ebaySellerId;

		/**
		 * 店铺名称
		 */
		private Set<String> storeNames;

	}
}
