package com.renpho.erp.oms.application.channelmanagement.amazon.job;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.renpho.erp.oms.domain.salemanagement.*;
import com.renpho.erp.oms.infrastructure.persistence.carriermapping.service.CarrierMappingService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzReportFbaShipment;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzReportFbaShipmentRepository;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 亚马逊fba发货信息同步到销售单履约信息
 *
 * <AUTHOR>
 * @since 2025/1/8
 */
@Slf4j
@Component
@AllArgsConstructor
public class AmzFbaShipmentMarkJob {

	private final StoreClient storeClient;
	private final AmzReportFbaShipmentRepository amzReportFbaShipmentRepository;
	private final SaleOrderRepository saleOrderRepository;

	private final TransactionTemplate transactionTemplate;

	private final CarrierMappingService carrierMappingService;

	@XxlJob("fbaShipmentMark")
	public void fbaShipmentMark() {
		log.info("fbaShipmentMark");

		List<StoreAuthorizationDTO> storeAuthorizationDTOList = storeClient
			.getStoreAuthorizationsByChannelCode(SaleChannelType.AMAZON.getValue(), StoreTypeEnum.SC.getValue());
		for (StoreAuthorizationDTO storeAuthorizationDTO : storeAuthorizationDTOList) {
			Integer storeId = storeAuthorizationDTO.getStoreId();

			Integer maxId = 0;
			int limit = 1000;
			List<AmzReportFbaShipment> amzReportFbaShipmentList;
			do {
				amzReportFbaShipmentList = amzReportFbaShipmentRepository.loadToSync(storeId, maxId, limit);
				Map<String, List<AmzReportFbaShipment>> amzOrderIdFbaShipmentMap = amzReportFbaShipmentList.stream()
					.collect(Collectors.groupingBy(AmzReportFbaShipment::getAmazonOrderId));
				for (Map.Entry<String, List<AmzReportFbaShipment>> amzOrderIdFbaShipmentEntry : amzOrderIdFbaShipmentMap.entrySet()) {
					String amzOrderId = amzOrderIdFbaShipmentEntry.getKey();
					List<AmzReportFbaShipment> oneAmzOrderFbaShipmentList = amzOrderIdFbaShipmentEntry.getValue();

					// 同步一个亚马逊渠道单的发货记录
					try {
						// 一个订单一个事务
						transactionTemplate
							.executeWithoutResult(status -> syncFulfillment(storeId, amzOrderId, oneAmzOrderFbaShipmentList));
					}
					catch (Exception e) {
						log.error("fba标记发货失败,亚马逊订单号是{}", amzOrderId, e);
					}
				}
				maxId = Optional.ofNullable(CollUtil.getLast(amzReportFbaShipmentList)).map(AmzReportFbaShipment::getId).orElse(0);
			}
			while (Objects.equals(amzReportFbaShipmentList.size(), limit));

		}
	}

	private void syncFulfillment(Integer storeId, String amzOrderId, List<AmzReportFbaShipment> amzReportFbaShipmentList) {
		SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findByStoreIdAndChannelOrderNo(storeId, amzOrderId);
		// 没有对应销售单标记失败
		// 解析异常的数据会有缺失数据
		if (saleOrderAggRoot == null || OrderStatus.PARSING_EXCEPTION.equals(saleOrderAggRoot.getOrderStatus())) {
			for (AmzReportFbaShipment amzReportFbaShipment : amzReportFbaShipmentList) {
				amzReportFbaShipmentRepository.updateTransStatus(amzReportFbaShipment.getId(), Boolean.FALSE);
			}
			return;
		}
		// msku 映射
		Map<String, SaleOrderItem> mksu2Item = saleOrderAggRoot.getItems()
			.stream()
			.collect(Collectors.toMap(SaleOrderItem::getMsku, Function.identity()));
		// 支付时间作为审核时间
		LocalDateTime payTime = null;
		for (AmzReportFbaShipment amzReportFbaShipment : amzReportFbaShipmentList) {
			String sku = amzReportFbaShipment.getSku();

			LocalDateTime shipmentDate = null;
			if (StrUtil.isNotBlank(amzReportFbaShipment.getShipmentDate())) {
				shipmentDate = LocalDateTime.ofInstant(ZonedDateTime.parse(amzReportFbaShipment.getEstimatedArrivalDate()).toInstant(),
						ZoneId.systemDefault());
			}
			String carrier = carrierMappingService.getCarrier(SaleChannelType.AMAZON, FulfillmentServiceType.FBA_MULTI_CHANNEL, amzReportFbaShipment.getCarrier());
			carrier = StrUtil.isNotBlank(carrier)? carrier : amzReportFbaShipment.getCarrier();

			saleOrderAggRoot.markShipping(SaleOrderItem.ItemFulfillmentInfo.builder()
				.msku(sku)
				.id(mksu2Item.containsKey(sku) ? mksu2Item.get(sku).getId().getId() : null)
				.quantityShipped(amzReportFbaShipment.getQuantityShipped())
				.carrierName(carrier)
				.originalCarrierName(amzReportFbaShipment.getCarrier())
				.trackingNo(amzReportFbaShipment.getTrackingNumber())
				.shipmentNo(amzReportFbaShipment.getShipmentId())
				.shipmentItemNo(amzReportFbaShipment.getShipmentItemId())
				.shippedTime(shipmentDate)
				.build());

			// 设置送达时间
			if (StrUtil.isNotBlank(amzReportFbaShipment.getEstimatedArrivalDate())) {
				LocalDateTime estimatedArrivalDate = LocalDateTime
					.ofInstant(ZonedDateTime.parse(amzReportFbaShipment.getEstimatedArrivalDate()).toInstant(), ZoneId.systemDefault());
				// 预计送达时间只有小于当前时间才设置
				if (estimatedArrivalDate.isBefore(LocalDateTime.now())) {
					saleOrderAggRoot.updateDeliveryTime(amzReportFbaShipment.getShipmentItemId(), estimatedArrivalDate);
				}
			}

			if (payTime == null && StrUtil.isNotBlank(amzReportFbaShipment.getPaymentsDate())) {
				payTime = LocalDateTime.ofInstant(ZonedDateTime.parse(amzReportFbaShipment.getPaymentsDate()).toInstant(),
						ZoneId.systemDefault());
			}

		}

		// 平台发货审核时间就是付款时间
		if (payTime != null) {
			saleOrderAggRoot.updateReviewedTime(payTime);
			saleOrderAggRoot.updatePaidTime(payTime);
		}
		saleOrderAggRoot.calculateStatus();

		saleOrderRepository.update(saleOrderAggRoot);
		for (AmzReportFbaShipment amzReportFbaShipment : amzReportFbaShipmentList) {
			amzReportFbaShipmentRepository.updateTransStatus(amzReportFbaShipment.getId(), Boolean.TRUE);
		}

	}
}
