package com.renpho.erp.oms.domain.vc.order.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 亚马逊VC采购订单状态
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AmzVcPurchaseOrderState {
	/**
	 * 新订单
	 */
	NEW("New"),
	/**
	 * 已确认
	 */
	ACKNOWLEDGED("Acknowledged"),
	/**
	 * 已关闭
	 */
	CLOSED("Closed");

	private final String value;

	public static AmzVcPurchaseOrderState fromValue(String value) {
		for (AmzVcPurchaseOrderState state : values()) {
			if (state.value.equalsIgnoreCase(value)) {
				return state;
			}
		}
		return null;
	}
}