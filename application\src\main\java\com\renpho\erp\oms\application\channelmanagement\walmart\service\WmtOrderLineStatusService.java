package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderLineStatusVO;
import com.renpho.erp.oms.application.channelmanagement.walmart.convert.WmtOrderLineStatusLogConvertor;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderLineStatus;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderLineStatusRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class WmtOrderLineStatusService {
	private final WmtOrderLineStatusRepository wmtOrderLineStatusRepository;
	private final WmtOrderLineStatusLogConvertor wmtOrderLineStatusLogConvertor;

	public R<List<WmtOrderLineStatusVO>> listByOrderId(Long orderId) {
		List<WmtOrderLineStatus> wmtOrderLineStatusList = wmtOrderLineStatusRepository.listByOrderId(orderId);
		return R.success(wmtOrderLineStatusList.stream().map(wmOrderLineStatus -> {
			return wmtOrderLineStatusLogConvertor.toVO(wmOrderLineStatus);
		}).collect(Collectors.toList()));
	}
}
