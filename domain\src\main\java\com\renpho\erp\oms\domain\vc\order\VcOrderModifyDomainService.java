package com.renpho.erp.oms.domain.vc.order;

import java.util.Map;

import org.jmolecules.ddd.annotation.Service;

import com.renpho.erp.oms.domain.vc.order.converter.VcOrderModifyConverter;
import com.renpho.erp.oms.domain.vc.order.model.AmzVcOrder;
import com.renpho.erp.oms.domain.vc.order.model.VcOrderAggRoot;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * VC订单修改-领域服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderModifyDomainService {

	private final SubmitSupplierChainService sscService;

	/**
	 * 转换修改VC订单（生成新聚合根）
	 *
	 * @param amzVcOrder 亚马逊VC订单
	 * @param oldVcOrder DB的VC订单
	 * @return VcOrderAggRoot
	 */
	public VcOrderAggRoot convertAndModify(AmzVcOrder amzVcOrder, VcOrderAggRoot oldVcOrder) {
		// 生成新的VC聚合根
		VcOrderAggRoot newVcOrder = VcOrderModifyConverter.convertOrder(amzVcOrder, oldVcOrder);
		// 转换订单商品行-新的接受数量Map
		Map<Long, Integer> acceptedQuantityMap = VcOrderModifyConverter.convertAcceptedItems(amzVcOrder, oldVcOrder);
		// 计算订单状态
		newVcOrder.calculateStatusOfModify(amzVcOrder.getPurchaseOrderState(), acceptedQuantityMap);
		// 重新计算金额
		newVcOrder.calculateAmount();
		// 是否自动提交供应链
		if (newVcOrder.isAutoSubmitSupplyChain()) {
			newVcOrder.submitSupplyChain(sscService);
		}
		return newVcOrder;
	}

}
