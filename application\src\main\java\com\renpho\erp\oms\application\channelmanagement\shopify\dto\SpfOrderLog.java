package com.renpho.erp.oms.application.channelmanagement.shopify.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class SpfOrderLog {
	private Long id;

	/**
	 * 店铺id
	 */
	private Integer storeId;

	/**
	 * shopify_url
	 */
	private String shopifyUrl;

	/**
	 * Shopify订单id
	 */
	private Long shopifyOrderId;

	/**
	 * 管理员 graphql api id
	 */
	private String adminGraphqlApiId;

	/**
	 * app_id
	 */
	private Integer appId;

	/**
	 * 浏览ip地址
	 */
	private String browserIp;

	/**
	 * 买家是否接受营销 0:否 1:是
	 */
	private Boolean buyerAcceptsMarketing;

	/**
	 * 取消原因
	 */
	private String cancelReason;

	/**
	 * 取消时间
	 */
	private LocalDateTime cancelledAt;

	/**
	 * 购物车token
	 */
	private String cartToken;

	/**
	 * checkout_id
	 */
	private Long checkoutId;

	/**
	 * checkout_token
	 */
	private String checkoutToken;

	/**
	 * 客户明细json
	 */
	private String clientDetails;

	/**
	 * 关闭时间
	 */
	private LocalDateTime closedAt;

	/**
	 * 公司
	 */
	private String company;

	/**
	 * 确认号码
	 */
	private String confirmationNumber;

	/**
	 * 是否确认 0:否 1:是
	 */
	private Boolean confirmed;

	/**
	 * 联系电子邮件
	 */
	private String contactEmail;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * Shopify订单创建时间
	 */
	private LocalDateTime createdAt;

	/**
	 * 订单费用
	 */
	private BigDecimal currentSubtotalPrice;

	/**
	 * 订单费用set json
	 */
	private String currentSubtotalPriceSet;

	/**
	 * 订单额外费用set json
	 */
	private String currentTotalAdditionalFeesSet;

	/**
	 * 订单折扣金额
	 */
	private BigDecimal currentTotalDiscounts;

	/**
	 * 订单折扣费用set json
	 */
	private String currentTotalDiscountsSet;

	/**
	 * 订单duties set json
	 */
	private String currentTotalDutiesSet;

	/**
	 * 订单总费用
	 */
	private BigDecimal currentTotalPrice;

	/**
	 * 订单总费用set json
	 */
	private String currentTotalPriceSet;

	/**
	 * 订单税费
	 */
	private BigDecimal currentTotalTax;

	/**
	 * 订单税费set json
	 */
	private String currentTotalTaxSet;

	/**
	 * 客户区域
	 */
	private String customerLocale;

	/**
	 * 设备 ID
	 */
	private String deviceId;

	/**
	 * 折扣编码 json
	 */
	private String discountCodes;

	/**
	 * 是否包括职责 0:否 1:是
	 */
	private Boolean dutiesIncluded;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 是否预估税费 0:否 1:是
	 */
	private Boolean estimatedTaxes;

	/**
	 * 支付状态
	 */
	private String financialStatus;

	/**
	 * 履约状态
	 */
	private String fulfillmentStatus;

	/**
	 * 站点
	 */
	private String landingSite;

	/**
	 * 站点引用
	 */
	private String landingSiteRef;

	/**
	 * 定位id
	 */
	private String locationId;

	/**
	 * 商家id
	 */
	private String merchantBusinessEntityId;

	/**
	 * 商家app id
	 */
	private String merchantOfRecordAppId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * 备注
	 */
	private String note;

	/**
	 * 备注属性json
	 */
	private String noteAttributes;

	/**
	 * number
	 */
	private Integer num;

	/**
	 * 订单号码
	 */
	private Integer orderNumber;

	/**
	 * 订单状态url
	 */
	private String orderStatusUrl;

	/**
	 * 原始订单额外费用set json
	 */
	private String originalTotalAdditionalFeesSet;

	/**
	 * 原始订单duties set json
	 */
	private String originalTotalDutiesSet;

	/**
	 * 支付网关名称列表json
	 */
	private String paymentGatewayNames;

	/**
	 * 电话
	 */
	private String phone;

	/**
	 * 电话号码
	 */
	private String poNumber;

	/**
	 * 出示货币
	 */
	private String presentmentCurrency;

	/**
	 * 处理时间
	 */
	private LocalDateTime processedAt;

	/**
	 * 引用
	 */
	private String reference;

	/**
	 * 引用站点
	 */
	private String referringSite;

	/**
	 * 源标识符
	 */
	private String sourceIdentifier;

	/**
	 * 来源名称
	 */
	private String sourceName;

	/**
	 * 来源url
	 */
	private String sourceUrl;

	/**
	 * 小计价格
	 */
	private BigDecimal subtotalPrice;

	/**
	 * 小计价格json
	 */
	private String subtotalPriceSet;

	/**
	 * tags
	 */
	private String tags;

	/**
	 * 是否免税 0:否 1:是
	 */
	private Boolean taxExempt;

	/**
	 * 税费行 json
	 */
	private String taxLines;

	/**
	 * 是否含税 0:否 1:是
	 */
	private Boolean taxesIncluded;

	/**
	 * 是否测试 0:否 1:是
	 */
	private Boolean test;

	/**
	 * token
	 */
	private String token;

	/**
	 * 支付四舍五入集合 json
	 */
	private String totalCashRoundingPaymentAdjustmentSet;

	/**
	 * 退款四舍五入集合 json
	 */
	private String totalCashRoundingRefundAdjustmentSet;

	/**
	 * 折扣金额
	 */
	private BigDecimal totalDiscounts;

	/**
	 * 折扣金额集合 json
	 */
	private String totalDiscountsSet;

	/**
	 * 总商品金额
	 */
	private BigDecimal totalLineItemsPrice;

	/**
	 * 总商品金额集合 json
	 */
	private String totalLineItemsPriceSet;

	/**
	 * 未偿还总额
	 */
	private BigDecimal totalOutstanding;

	/**
	 * 总金额
	 */
	private BigDecimal totalPrice;

	/**
	 * 总金额集合 json
	 */
	private String totalPriceSet;

	/**
	 * 运费金额集合 json
	 */
	private String totalShippingPriceSet;

	/**
	 * 税费
	 */
	private BigDecimal totalTax;

	/**
	 * 税费金额集合 json
	 */
	private String totalTaxSet;

	/**
	 * 收到的小费总额
	 */
	private BigDecimal totalTipReceived;

	/**
	 * 重量
	 */
	private Integer totalWeight;

	/**
	 * 更新时间
	 */
	private LocalDateTime updatedAt;

	/**
	 * userId
	 */
	private String userId;

	/**
	 * 账单地址 json
	 */
	private String billingAddress;

	/**
	 * 用户信息 json
	 */
	private String customer;

	/**
	 * 用户信息 json
	 */
	private String discountApplications;

	/**
	 * 履约数组 json
	 */
	private String fulfillments;

	/**
	 * 付款条件json
	 */
	private String paymentTerms;

	/**
	 * 退款数组 json
	 */
	private String refunds;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 邮编
	 */
	private String zip;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 国家编码
	 */
	private String countryCode;

	/**
	 * 省份
	 */
	private String province;

	/**
	 * 省份编码
	 */
	private String provinceCode;
	private List<SpfOrderLineItemLog> spfOrderLineItemLogList;
	private List<SpfOrderShippingLineLog> spfOrderShippingLineLogList;
}