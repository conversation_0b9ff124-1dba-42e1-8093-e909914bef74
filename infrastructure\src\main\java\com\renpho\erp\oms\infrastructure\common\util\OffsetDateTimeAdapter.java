package com.renpho.erp.oms.infrastructure.common.util;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import org.threeten.bp.OffsetDateTime;
import org.threeten.bp.format.DateTimeFormatter;

import java.io.IOException;
/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 10:53
 */
public class OffsetDateTimeAdapter extends TypeAdapter<OffsetDateTime> {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME;

    @Override
    public void write(JsonWriter out, OffsetDateTime value) throws IOException {
        if (value == null) {
            out.nullValue();
        } else {
            out.value(FORMATTER.format(value));
        }
    }

    @Override
    public OffsetDateTime read(JsonReader in) throws IOException {
        String str = in.nextString();
        return OffsetDateTime.parse(str, FORMATTER);
    }
}
