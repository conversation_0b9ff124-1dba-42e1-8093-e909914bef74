package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderItemLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderItemVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderItem;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MclOrderItemLogConvertor {
	MclOrderItemLog toLog(MclOrderItem param);

	MclOrderItemVO toVO(MclOrderItem param);
}
