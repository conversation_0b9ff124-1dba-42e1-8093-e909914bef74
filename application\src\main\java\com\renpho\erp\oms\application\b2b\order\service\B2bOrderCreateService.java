package com.renpho.erp.oms.application.b2b.order.service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderCreateCmd;
import com.renpho.erp.oms.application.b2b.order.convert.B2bOrderCmdConvert;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.PurchaseSkuQueryService;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.repository.CustomerRepository;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderAggRoot;
import com.renpho.erp.oms.domain.b2b.order.repository.B2bOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDetailDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;

import lombok.RequiredArgsConstructor;

/**
 * B2b订单创建-应用服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class B2bOrderCreateService {

	private final CustomerRepository customerRepository;
	private final B2bOrderCmdConvert b2bOrderCmdConvert;
	private final B2bOrderRepository b2bOrderRepository;
	private final PurchaseSkuQueryService purchaseSkuQueryService;
	private final CompanyClient companyClient;

	/**
	 * 创建
	 * @param cmd 指令
	 */
	@Lock4j(name = "b2b:order:create", keys = "#userId", acquireTimeout = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void create(Integer userId, B2bOrderCreateCmd cmd) {
		// 查询客户聚合根信息
		CustomerAggRoot customerAggRoot = customerRepository.findById(cmd.getCustomer().getCustomerId());
		// 查询公司信息
		CompanyVo comany = companyClient.getById(cmd.getCustomer().getSalesCompanyId());
		// 查询psku Map，key为psku，value为PurchaseProductDetailDTO
		Map<String, PurchaseProductDetailDTO> pskuMap = purchaseSkuQueryService.b2bPurchaseSkuList(this.getPskus(cmd));
		// 转换为订单聚合根
		B2bOrderAggRoot orderAggRoot = b2bOrderCmdConvert.toAggRoot(cmd, pskuMap, customerAggRoot, comany);
		// 生成id和订单号
		orderAggRoot.generateIdAndNo();
		// 计算订单金额
		orderAggRoot.calculateAmount();
		// 保存订单
		b2bOrderRepository.save(orderAggRoot);
	}

	/**
	 * 获取psku集
	 * @param cmd 订单编辑命令
	 * @return Set
	 */
	private @NotNull Set<String> getPskus(B2bOrderCreateCmd cmd) {
		return cmd.getItems().stream().map(B2bOrderCreateCmd.Item::getPsku).collect(Collectors.toSet());
	}
}
