package com.renpho.erp.oms.application.listingmanagement.strategy.amz.report;

import cn.hutool.core.text.csv.CsvRow;
import com.renpho.erp.amz.report.common.AmzReportTypeEnum;
import com.renpho.erp.amz.report.po.AmzReportRecordPo;
import com.renpho.erp.amz.report.strategy.parse.AbstractAmzReportParseStrategy;
import com.renpho.erp.oms.application.listingmanagement.dto.AmzReportParseMerchantListingAllDataDto;
import com.renpho.erp.oms.application.listingmanagement.executor.AmzReportListingSaveCommandExecutor;
import com.renpho.erp.oms.application.listingmanagement.executor.AmzReportMerchantListingAllDataParseCommandExecutor;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 亚马逊报告解析策略-商家列表所有数据
 *
 * <AUTHOR>
 * @Date 2024/12/24 14:52
 **/
@Slf4j
@Component
public class AmzReportParseMerchantListingAllDataStrategy extends AbstractAmzReportParseStrategy<AmzReportParseMerchantListingAllDataDto>
        implements InitializingBean {

    @Resource
    private AmzReportMerchantListingAllDataParseCommandExecutor amzReportMerchantListingAllDataParseCommandExecutor;


    @Resource
    private AmzReportListingSaveCommandExecutor amzReportListingSaveCommandExecutor;


    @Override
    public void afterPropertiesSet() throws Exception {
        register(AmzReportTypeEnum.GET_MERCHANT_LISTINGS_ALL_DATA.getCode());
    }


    @Override
    protected List<AmzReportParseMerchantListingAllDataDto> parseData(List<CsvRow> csvRows, AmzReportRecordPo reportRecordPo) {
        return amzReportMerchantListingAllDataParseCommandExecutor.execute(csvRows, reportRecordPo.getStoreId());
    }

    @Override
    protected void saveReportData(List<AmzReportParseMerchantListingAllDataDto> dataList) {
        amzReportListingSaveCommandExecutor.execute(dataList);
    }
}
