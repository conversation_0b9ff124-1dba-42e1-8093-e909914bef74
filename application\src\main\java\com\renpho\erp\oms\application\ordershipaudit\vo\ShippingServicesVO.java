package com.renpho.erp.oms.application.ordershipaudit.vo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import lombok.Data;

/**
 * @desc: 根据所选仓库查询发货方式报价表
 * @time: 2025-04-01 14:47:09
 * @author: Ali<PERSON>
 */

@Data
public class ShippingServicesVO {

	/**
	 * 承运商
	 */
	private String carrier;

	/**
	 * 承运商服务
	 */
	private String carrierService;

	/**
	 * 运输时效
	 */
	private String shippingTime;

	/**
	 * 运输成本
	 */
	private String shippingCost;

	/**
	 * 渠道ID 物流服务对应的渠道唯一标识 TEMU需要 (TEMU发货方式报价的唯一值 shipChannelId+shipCompanyId)
	 */
	private Long shipChannelId;

	/**
	 * 物流公司ID 唯一标识具体的物流公司 TEMU需要
	 */
	private Long shipCompanyId;

	/**
	 * 承运商服务编码表Id(Ecom 易仓发货方式报价的唯一值)
	 */
	private Long fulfillmentServiceId;

	/**
	 * 承运商编码
	 */
	private String carrierCode;

	/**
	 * 包裹类型编码 Ecom需要
	 */
	private String packageTypeCode;

	/**
	 * Ebay 费率Id(Ebay发货方式报价的唯一值)
	 */
	private String ebayRateId;

	/**
	 * Ebay 发货报价Id
	 */
	private String shippingQuoteId;

	/**
	 * 最晚签收时间
	 */
	private LocalDateTime latestDeliveryTime;

	/**
	 * Ecom 查询单个发货报价返回的错误信息
	 */
	private String errorMessage;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 金额
	 */
	private BigDecimal amount;

	/**
	 * 承运商服务编码 易仓
	 */
	private String carrierServiceCode;
}
