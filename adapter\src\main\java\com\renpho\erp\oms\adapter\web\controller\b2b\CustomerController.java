package com.renpho.erp.oms.adapter.web.controller.b2b;

import java.util.List;

import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import com.renpho.erp.bpm.api.annotation.BpmApi;
import com.renpho.erp.bpm.api.dto.BpmStatusParam;
import com.renpho.erp.oms.application.b2b.customer.command.CreateCustomerCommand;
import com.renpho.erp.oms.application.b2b.customer.command.UpdateCustomerCommand;
import com.renpho.erp.oms.application.b2b.customer.service.CustomerCardService;
import com.renpho.erp.oms.application.b2b.customer.service.CustomerConfigService;
import com.renpho.erp.oms.application.b2b.customer.service.CustomerQueryService;
import com.renpho.erp.oms.application.b2b.customer.service.CustomerService;
import com.renpho.erp.oms.application.b2b.customer.vo.*;
import com.renpho.erp.oms.domain.b2b.customer.query.CustomerPageQuery;
import com.renpho.erp.oms.domain.b2b.customer.query.CustomerSelectQuery;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.openai.gpt.vo.BusinessCardVo;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;

import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * 客户控制器
 */
@RestController
@RequestMapping("/b2b/customer")
@ShenyuSpringCloudClient("/b2b/customer/**")
@RequiredArgsConstructor
public class CustomerController {
	private final CustomerService customerService;
	private final CustomerConfigService customerConfigService;
	private final CustomerQueryService customerQueryService;
	private final CustomerCardService customerCardService;

	/**
	 * 创建客户
	 *
	 * @param command 创建命令
	 * @return 客户ID
	 */
	@PostMapping("/create")
	@PreAuthorize(PermissionConstant.Customer.ADD)
	public R<Long> createCustomer(@RequestBody @Valid CreateCustomerCommand command) {
		Long customerId = customerService.createCustomer(command);
		return R.success(customerId);
	}

	/**
	 * 更新客户
	 *
	 * @param command 更新命令
	 * @return 是否成功
	 */
	@PostMapping("/update")
	@PreAuthorize(PermissionConstant.Customer.UPDATE)
	public R<Boolean> updateCustomer(@RequestBody @Valid UpdateCustomerCommand command) {
		Boolean result = customerService.updateCustomer(command);
		return R.success(result);
	}

	/**
	 * 查询客户详情
	 *
	 * @param id 客户ID
	 * @return 客户详情
	 */
	@GetMapping("/get")
	public R<CustomerVO> getCustomer(@RequestParam Long id) {
		CustomerVO customerVO = customerService.getCustomerDetail(id);
		return R.success(customerVO);
	}

	/**
	 * 销售公司下拉列表
	 * @return 销售公司列表
	 */
	@GetMapping("/getSalesCompanyList")
	public R<List<SalesCompanyVO>> getSalesCompanyList(@RequestParam(required = false) Integer status) {
		return R.success(customerConfigService.getSalesCompanyList(status));
	}

	/**
	 * 获取贸易条款下拉列表
	 * @return 贸易条款列表
	 */
	@GetMapping("/getIncotermsList")
	@BpmApi(desc = "获取贸易条款下拉列表", paramClass = { BpmStatusParam.class }, showFields = { "name" }, valueField = "code")
	public R<List<IncotermsVO>> getIncotermsList() {
		return R.success(customerConfigService.getIncotermsList());
	}

	/**
	 * 获取付款条款下拉列表
	 * @return 付款条款列表
	 */
	@GetMapping("/getPaymentTermsList")
	@BpmApi(desc = "获取付款条款下拉列表", paramClass = { BpmStatusParam.class }, showFields = { "name" }, valueField = "code")
	public R<List<PaymentTermsVO>> getPaymentTermsList() {
		return R.success(customerConfigService.getPaymentTermsList());
	}

	/**
	 * 客户信息下拉列表
	 * @return 客户信息下拉列表
	 */
	@PostMapping("/getCustomerSelectList")
	@BpmApi(desc = "查询客户下拉", paramClass = { BpmStatusParam.class }, showFields = { "customerCompanyName" }, valueField = "customerId")
	public R<List<CustomerSelectVO>> getCustomerSelectList(@RequestBody @Valid CustomerSelectQuery query) {
		return R.success(customerQueryService.getCustomerSelectList(query));
	}

	/**
	 * 客户信息列表分页查询
	 * @param query 查询条件
	 * @return 是否成功
	 */
	@PostMapping("/page")
	@PreAuthorize(PermissionConstant.Customer.PAGE)
	public R<Paging<CustomerPageVO>> page(@RequestBody @Valid CustomerPageQuery query) {
		return R.success(customerQueryService.page(query));
	}

	/**
	 * 启用客户
	 *
	 * @param id 客户ID
	 * @return 是否成功
	 */
	@GetMapping("/enable")
	@PreAuthorize(PermissionConstant.Customer.CHANGE_STATUS)
	public R<Boolean> enableCustomer(@RequestParam Long id) {
		Boolean result = customerService.enableCustomer(id);
		return R.success(result);
	}

	/**
	 * 禁用客户
	 *
	 * @param id 客户ID
	 * @return 是否成功
	 */
	@GetMapping("/disable")
	@PreAuthorize(PermissionConstant.Customer.CHANGE_STATUS)
	public R<Boolean> disableCustomer(@RequestParam Long id) {
		Boolean result = customerService.disableCustomer(id);
		return R.success(result);
	}

	/**
	 * 上传客户附件
	 *
	 * @param file 附件文件
	 * @return 上传结果
	 */
	@PostMapping("/uploadAttachment")
	public R<CustomerAttachmentUploadVO> uploadAttachment(@RequestParam("file") MultipartFile file) {
		CustomerAttachmentUploadVO result = customerService.uploadAttachment(file);
		return R.success(result);
	}

	/**
	 * 识别客户名片
	 * @param files 附件集
	 * @return R
	 */
	@PostMapping("/identifyCard")
	public R<BusinessCardVo> identifyCard(@RequestParam("files") MultipartFile[] files) {
		BusinessCardVo cardVo = customerCardService.identify(files);
		return R.success(cardVo);
	}

	/**
	 * 查询客户详情
	 *
	 * @param companyName 客户公司名称
	 * @return 客户详情
	 */
	@GetMapping("/getDetailByName")
	public R<CustomerVO> getDetailByName(@RequestParam String companyName) {
		CustomerVO customerVO = customerQueryService.getDetailByName(companyName);
		return R.success(customerVO);
	}

	/**
	 * 查询客户详情
	 *
	 * @param customerId 客户id
	 * @return 客户详情
	 */
	@GetMapping("/getDetail")
	public R<CustomerDetailVO> getDetail(@RequestParam Long customerId) {
		CustomerDetailVO vo = customerQueryService.getDetail(customerId);
		return R.success(vo);
	}
}