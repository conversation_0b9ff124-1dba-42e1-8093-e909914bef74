package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto;

import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.compress.utils.Sets;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 导入数据的基础数据
 * @date 2025/5/19 17:03
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AutoShipAuditConfigImportBaseDataDTO {

	/**
	 * 店铺list
	 */
	private List<String> storeList = Lists.newArrayList();

	/**
	 * pskuList
	 */
	private List<String> pskuList = Lists.newArrayList();

    /**
     * db存在的psku
     */
    private List<String> dbPskuList = Lists.newArrayList();

	/**
	 * 仓库codeList
	 */
	private List<String> warehouseCodeList = Lists.newArrayList();

    /**
     * 店铺名对于的map
     */
	private Map<String, StoreVo> storeNameMap = new HashMap<>();

    /**
     * 仓库编码对于的mapping
     */
	private Map<String, WarehouseVo> warehouseCodeMap = new HashMap<>();

	/**
	 * 有权限的psku
	 */
	private List<String> hadPermissionPskuList = Lists.newArrayList();

	/**
	 * 有权限的店铺id
	 */
	private Set<Integer> hadPermissionStoreIdSet = Sets.newHashSet();


	/**
	 * db的销售渠道
	 */
	private Set<String> dbSalesChannelNameSet = Sets.newHashSet();
}
