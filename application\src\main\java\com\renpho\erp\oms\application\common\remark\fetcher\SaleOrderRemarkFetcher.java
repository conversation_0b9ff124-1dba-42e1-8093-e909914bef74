package com.renpho.erp.oms.application.common.remark.fetcher;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.common.remark.convert.OrderRemarkHistoryConvert;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderRemarkMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderRemarkPo;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @desc: 销售订单备注获取器
 * @time: 2025-06-20 14:41:58
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class SaleOrderRemarkFetcher implements RemarkFetcher<SaleOrderRemarkPo, OrderRemarkHistoryVO> {

    private final SaleOrderRemarkMapper saleOrderRemarkMapper;

    private final OrderRemarkHistoryConvert convert;

    /**
     * 订单备注获取
     *
     * @param orderId 订单ID
     * @return 订单备注
     */
    @Override
    public List<SaleOrderRemarkPo> fetchRemarks(Long orderId) {
        return saleOrderRemarkMapper.selectList(Wrappers.<SaleOrderRemarkPo>lambdaQuery()
                .eq(SaleOrderRemarkPo::getOrderId, orderId)
                .orderByAsc(SaleOrderRemarkPo::getId));
    }


    /**
     * 订单备注转换
     *
     * @param remark 订单备注
     * @return 订单备注
     */
    @Override
    public OrderRemarkHistoryVO toVO(SaleOrderRemarkPo remark) {
        return convert.toVO(remark);
    }

}
