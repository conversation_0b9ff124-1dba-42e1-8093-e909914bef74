package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.shopify.model.PageInfo;
import com.renpho.erp.apiproxy.shopify.model.ShopAccount;
import com.renpho.erp.apiproxy.shopify.model.products.RetrieveProductListRequest;
import com.renpho.erp.apiproxy.shopify.model.products.RetrieveProductListResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.ShopifyClient;
import com.renpho.karma.json.JSONKit;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * shopify Listing拉取器
 * <AUTHOR> wei
 */
@Slf4j
@Component
@AllArgsConstructor
public class ShopifyListingPuller extends AbstractListingPullerTemplate {

	private final ShopifyClient shopifyClient;

	@Override
	protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
		ShopAccount shopAccount = this.buildShopAccount(storeAuthorization);
		if (shopAccount == null) {
			return null;
		}
		// 构建分页查询条件
		RetrieveProductListRequest retrieveProductListRequest = this.buildRetrieveProductListRequest(listingSearchQuery);
		// 拉取shopify商品信息列表
		RetrieveProductListResponse retrieveProductListResponse = shopifyClient.getShopifyItems(shopAccount, retrieveProductListRequest);
		if (retrieveProductListResponse == null) {
			log.error("shopify listing 拉取失败,商品列表接口没有返回数据, shopAccount是{}, retrieveProductListRequest是{}", JSONKit.toJSONString(shopAccount),
					JSONKit.toJSONString(retrieveProductListRequest));
			return null;
		}
		RetrieveProductListResponse.Products products = retrieveProductListResponse.getProducts();
		List<RetrieveProductListResponse.Product> productList = Optional.ofNullable(products)
			.map(RetrieveProductListResponse.Products::getNodes)
			.orElse(Collections.emptyList());
		if (CollUtil.isEmpty(productList)) {
			log.error("shopify listing返回商品数据错误, shopAccount是{}, retrieveProductListRequest是{}, retrieveProductListResponse是{}",
					JSONKit.toJSONString(shopAccount), JSONKit.toJSONString(retrieveProductListRequest),
					JSONKit.toJSONString(retrieveProductListResponse));
			return null;
		}

		// 处理相应结果
		ListingSearchDTO listingSearchDTO = new ListingSearchDTO();
		List<SalesChannelListingSource> salesChannelListingSourceList = this.buildListingSourceList(storeAuthorization, shopAccount,
				productList);
		listingSearchDTO.setListingSourceList(salesChannelListingSourceList);

		String nextCursor = Optional.ofNullable(products)
			.map(RetrieveProductListResponse.Products::getPageInfo)
			.map(PageInfo::getEndCursor)
			.orElse(null);
		listingSearchDTO.setSearchAfter(nextCursor);
		return listingSearchDTO;
	}

	private ShopAccount buildShopAccount(StoreAuthorizationDTO storeAuthorization) {
		String shopUrl = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
			.map(StoreAuthorizationVo::getAuthorization)
			.map(StoreAuthorizationVo.Authorization::getSpfShopUrl)
			.orElse(null);
		if (StringUtils.isBlank(shopUrl)) {
			log.error("shopify listing 拉取错误,没有shopifyUrl,storeAuthorization是{}", JSONKit.toJSONString(storeAuthorization));
			return null;
		}

		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setShopUrl(shopUrl);
		return shopAccount;
	}

	private RetrieveProductListRequest buildRetrieveProductListRequest(ListingSearchQuery listingSearchQuery) {
		RetrieveProductListRequest retrieveProductListRequest = new RetrieveProductListRequest();
		retrieveProductListRequest.setNextCursor(listingSearchQuery.getSearchAfter());
		return retrieveProductListRequest;
	}

	private List<SalesChannelListingSource> buildListingSourceList(StoreAuthorizationDTO storeAuthorization, ShopAccount shopAccount,
			List<RetrieveProductListResponse.Product> productList) {
		String shopifyUrl = shopAccount.getShopUrl();

		List<SalesChannelListingSource> listingSourcesList = new ArrayList<>(productList.size());
		for (RetrieveProductListResponse.Product product : productList) {
			SalesChannelListingSource salesChannelListingSource = super.buildListingSource(storeAuthorization);
			salesChannelListingSource.setListingId(product.getId());

			salesChannelListingSource.setContent(JSONKit.toJSONString(product));
			salesChannelListingSource.setExtField(JSONKit.toJSONString(Collections.singletonMap("shopifyUrl", shopifyUrl)));

			listingSourcesList.add(salesChannelListingSource);
		}
		return listingSourcesList;

	}

	@Override
	public String getSalesChannelCode() {
		return ChannelCode.SPF_CHANNEL_CODE;
	}
}
