package com.renpho.erp.oms.domain.b2b.order.cmd;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import lombok.Builder;
import lombok.Getter;

/**
 * B2B订单修改命令
 * <AUTHOR>
 */
@Getter
@Builder
public class B2bOrderModifyCmd {

	/**
	 * 订单id
	 */
	private Long orderId;

	/**
	 * 客户参考号
	 */
	private String referNo;

	/**
	 * 订单商品
	 */
	private List<Item> items;

	/**
	 * 订单金额
	 */
	private Amount amount;

	/**
	 * 订单收货地址
	 */
	private Address address;

	/**
	 * 订单发货要求
	 */
	private ShipmentRequirement shipmentRequirement;

	/**
	 * 订单客户信息
	 */
	private Customer customer;

	/**
	 * 客户联系人
	 */
	private List<CustomerContact> customerContacts;

	/**
	 * 订单客户发票
	 */
	private CustomerInvoice customerInvoice;

	@Getter
	@Builder
	public static class Item {

		/**
		 * 订单商品主键id
		 */
		private Long orderItemId;

		/**
		 * 采购SKU
		 */
		private String psku;

		/**
		 * 下单数量
		 */
		private Integer orderedQuantity;

		/**
		 * 单价
		 */
		private BigDecimal unitPrice;

		/**
		 * 税率
		 */
		private BigDecimal taxRate;

		/**
		 * 税金
		 */
		private BigDecimal tax;
	}

	@Getter
	@Builder
	public static class Amount {
		/**
		 * 币种
		 */
		private String currency;

		/**
		 * 运费收入
		 */
		private BigDecimal shippingFee;

		/**
		 * 费用明细
		 */
		private List<FreeDetail> feesDetails;
	}

	@Getter
	@Builder
	public static class FreeDetail {
		/**
		 * 费用类型，1-推广费 2-广告费
		 */
		private Integer feeType;

		/**
		 * 费用金额
		 */
		private BigDecimal feeAmount;
	}

	@Getter
	@Builder
	public static class Address {
		/**
		 * 收货人
		 */
		private String attnTo;

		/**
		 * 收货方公司
		 */
		private String shippingCompany;

		/**
		 * 收货地址
		 */
		private String address;

		/**
		 * 邮编
		 */
		private String zipCode;

		/**
		 * 城市
		 */
		private String city;

		/**
		 * 国家
		 */
		private String country;

		/**
		 * 联系号码
		 */
		private String contactNumber;

		/**
		 * 备注
		 */
		private String comment;
	}

	@Getter
	@Builder
	public static class ShipmentRequirement {
		/**
		 * 货物最晚交期
		 */
		private LocalDate requestDeliveryDate;

		/**
		 * 是否打托，0-否 1-是
		 */
		private Integer palletizationRequired;

		/**
		 * 提单类型，1-Original 2-Seaway 3-Express 4-Telex
		 */
		private Integer blType;

		/**
		 * 外箱标签名称
		 */
		private String masterCartonLabelName;

		/**
		 * 外箱标签链接
		 */
		private String masterCartonLabelUrl;

		/**
		 * 其他要求
		 */
		private String otherRequirements;
	}

	@Getter
	@Builder
	public static class Customer {
		/**
		 * 客户ID
		 */
		private Long customerId;

		/**
		 * 销售公司ID
		 */
		private Integer salesCompanyId;

		/**
		 * 贸易条款编码
		 */
		private String incotermsCode;

		/**
		 * 付款条款编码
		 */
		private String paymentTermsCode;
	}

	@Getter
	@Builder
	public static class CustomerContact {
		/**
		 * 联系人类型，1-主联系人 2-采购 3-物流 4-财务
		 */
		private Integer contactType;

		/**
		 * 客户联系信息id
		 */
		private Long customerContactId;
	}

	@Getter
	@Builder
	public static class CustomerInvoice {
		/**
		 * 收件人
		 */
		private String attnTo;

		/**
		 * 发票公司
		 */
		private String invoiceCompany;

		/**
		 * 地址
		 */
		private String address;

		/**
		 * 邮编
		 */
		private String zipCode;

		/**
		 * 城市
		 */
		private String city;

		/**
		 * 国家
		 */
		private String country;

		/**
		 * 联系号码
		 */
		private String contactNumber;

		/**
		 * 增值税号码
		 */
		private String vatNumber;
	}
}