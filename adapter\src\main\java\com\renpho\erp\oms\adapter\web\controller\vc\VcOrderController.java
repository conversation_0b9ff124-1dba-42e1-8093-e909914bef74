package com.renpho.erp.oms.adapter.web.controller.vc;

import com.renpho.erp.oms.application.vc.order.command.SubmitOrderCmd;
import com.renpho.erp.oms.application.vc.order.command.VcOrderLabelUploadCmd;
import com.renpho.erp.oms.application.vc.order.vo.*;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderRemarkCmd;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderInvoiceCmd;
import com.renpho.erp.oms.application.vc.order.command.VcOrderReparseSkusCmd;
import com.renpho.erp.oms.application.vc.order.service.VcOrderOperateService;
import com.renpho.erp.oms.application.vc.order.service.VcOrderQueryService;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.query.VcOrderPageQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * @desc: VC订单控制器
 * @time: 2025-07-07 10:23:31
 * @author: Alina
 */

@Tag(name = "VC订单管理")
@RestController
@RequestMapping("/vc/order")
@ShenyuSpringCloudClient("/vc/order/**")
@RequiredArgsConstructor
public class VcOrderController {

    private final VcOrderOperateService vcOrderOperateService;
    private final VcOrderQueryService vcOrderQueryService;

    /**
     * VC订单列表分页查询
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @PreAuthorize(PermissionConstant.VcOrder.PAGE)
    @Operation(summary = "VC订单分页查询", description = "VC订单分页查询")
    public R<Paging<VcOrderPageVO>> page(@RequestBody @Valid VcOrderPageQuery query) {
        return R.success(vcOrderQueryService.page(query));
    }

    /**
     * VC订单商品列表查询
     * @param orderId 订单ID
     * @return 商品列表
     */
    @GetMapping("/itemList")
    @Operation(summary = "VC订单商品列表查询", description = "VC订单商品列表查询")
    public R<List<VcOrderItemListVO>> getItemList(@RequestParam Long orderId) {
        List<VcOrderItemListVO> items = vcOrderQueryService.getVcOrderItemList(orderId);
        return R.success(items);
    }

    /**
     * VC订单导出
     * @param query 查询条件
     * @param response HTTP响应
     * @throws IOException IO异常
     */
    @PostMapping("/export")
    @PreAuthorize(PermissionConstant.VcOrder.EXPORT)
    @Operation(summary = "VC订单导出", description = "VC订单导出")
    public void export(@RequestBody @Valid VcOrderPageQuery query, HttpServletResponse response) throws IOException {
        vcOrderQueryService.export(query, response);
    }

    /**
     * 上传VC订单箱唛
     *
     * @param cmd 上传VC订单箱唛入参
     * @return 操作结果
     */
    @PostMapping("/uploadLabel")
    @PreAuthorize(PermissionConstant.VcOrder.UPLOAD_LABEL)
    public R<Void> uploadLabel(@RequestBody @Valid VcOrderLabelUploadCmd cmd) {
        return vcOrderOperateService.uploadLabel(cmd);
    }

    /**
     * 提交供应链
     *
     * @param orderId 订单id
     * @return 操作结果
     */
    @PostMapping("/submitSupplierChain/{orderId}")
    @PreAuthorize(PermissionConstant.VcOrder.SUBMIT_SUPPLIER_CHAIN)
    public R<Void> submitSupplierChain(@PathVariable Long orderId) {
        return vcOrderOperateService.submitSupplierChain(orderId);
    }

    /**
     * VC订单备注
     *
     * @param cmd 备注命令
     * @return 操作结果
     */
    @PostMapping("/remark")
    @PreAuthorize(PermissionConstant.VcOrder.REMARK)
    @Operation(summary = "VC订单备注", description = "给VC订单添加备注")
    public R<Void> remark(@RequestBody @Valid VcOrderRemarkCmd cmd) {
        vcOrderOperateService.remark(cmd);
        return R.success();
    }

    /**
     * VC订单历史备注
     *
     * @param orderId 订单id
     * @return 备注历史列表
     */
    @GetMapping("/remarkHistory/{orderId}")
    @Operation(summary = "VC订单历史备注", description = "获取VC订单的历史备注记录")
    public R<List<VcOrderRemarkHistoryVO>> remarkHistory(@PathVariable Long orderId) {
        return R.success(vcOrderQueryService.getRemarkHistory(orderId));
    }

    /**
     * 获取VC订单的操作信息
     *
     * @param orderId     订单id
     * @param operateType 操作类型 1,接单 2,上传箱唛 3,提交供应链
     */
    @GetMapping("/operatorDetail")
    public R<VcOrderOperatorDetailVo> operatorDetail(@RequestParam Long orderId, @RequestParam Integer operateType) {
        return R.success(vcOrderQueryService.operatorDetail(orderId, operateType));
    }

    /**
     * 接单
     */
    @PostMapping("/submitOrder")
    @PreAuthorize(PermissionConstant.VcOrder.SUBMIT_ORDER)
    public R<Void> acceptOrder(@RequestBody @Valid SubmitOrderCmd submitOrderCmd) {
        vcOrderOperateService.submitAcknowledgement(submitOrderCmd);
        return R.success();
    }

    /**
     * 批量重新解析VC订单SKU
     * @param cmd 批量重新解析命令
     * @return 操作结果
     */
    @PostMapping("/reparseSkus")
    @Operation(summary = "批量重新解析VC订单SKU", description = "批量重新解析VC订单SKU映射")
    @PreAuthorize(PermissionConstant.VcOrder.REPARSE_SKUS)
    public R<Void> reparseSkus(@RequestBody @Valid VcOrderReparseSkusCmd cmd) {
        return vcOrderOperateService.reparseSkus(cmd);
    }

    /**
     * 获取VC订单开票信息
     *
     * @param orderId 订单ID
     * @return 开票信息
     */
    @Operation(summary = "获取VC订单开票信息", description = "根据订单ID获取用于开票界面的基本信息")
    @GetMapping("/{orderId}/invoice-info")
    public R<VcOrderInvoiceInfoVO> getInvoiceInfo(@Parameter(description = "订单ID") @PathVariable Long orderId) {
        return R.success(vcOrderQueryService.getInvoiceInfo(orderId));
    }

    /**
     * VC订单开票
     *
     * @param cmd 开票命令
     * @return 操作结果
     */
    @PostMapping("/invoice")
    @Operation(summary = "VC订单开票", description = "对VC订单进行开票操作")
    @PreAuthorize(PermissionConstant.VcOrder.INVOICE)
    public R<Void> invoiceOrder(@RequestBody @Valid VcOrderInvoiceCmd cmd) {
        vcOrderOperateService.invoiceOrder(cmd);
        return R.success();
    }

    /**
     * 查询不重复的站点编码列表
     *
     * @return 站点编码列表
     */
    @GetMapping("/distinctSiteCodes")
    @Operation(summary = "查询不重复的站点编码列表", description = "获取系统中所有不重复的站点编码，用于下拉选择等场景")
    public R<List<String>> getDistinctSiteCodes() {
        List<String> siteCodes = vcOrderQueryService.getDistinctSiteCodes();
        return R.success(siteCodes);
    }

    /**
     * 获取VC订单详情
     *
     * @param orderId 订单ID
     * @return 订单详情
     */
    @GetMapping("/vcOrderDetail/{orderId}")
    public R<VcOrderDetailVO> vcOrderDetailVO(@PathVariable Long orderId) {
        return R.success(vcOrderQueryService.getVcOrderDetail(orderId));
    }
}
