package com.renpho.erp.oms.application.channelmanagement.tiktok.service;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderLineItemAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderLineItemLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.tiktok.convert.TtOrderLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderLog;
import com.renpho.erp.oms.application.channelmanagement.tiktok.optlog.TtOrderAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.tiktok.optlog.TtOrderBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.tiktok.optlog.TtOrderUpdateSnapSource;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrder;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderLineItem;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.repository.TtOrderLineItemRepository;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.repository.TtOrderRepository;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class TtOrderService {
	private final TtOrderRepository ttOrderRepository;
	private final TtOrderLineItemRepository ttOrderLineItemRepository;
	private final TtOrderLogConvertor ttOrderLogConvertor;
	private final TtOrderLineItemLogConvertor ttOrderLineItemLogConvertor;

	@Lock4j(name = "oms:tiktok:sync", keys = { "#ttOrderDTO.id", "#ttOrderDTO.shopId" })
	public void createOrUpdate(TtOrderDTO ttOrderDTO) {
		// 解析成平台单
		TtOrder ttOrder = TtOrderAppConvertor.toDomain(ttOrderDTO);
		List<TtOrderLineItem> ttOrderLineItemList = TtOrderLineItemAppConvertor.toDomainList(ttOrderDTO.getLine_items());
		// 查询有没有
		TtOrder exitsOrder = ttOrderRepository.getByUniqueIndex(ttOrderDTO.getId(), ttOrderDTO.getShopId());
		// 更新插入
		if (Objects.nonNull(exitsOrder)) {
			ttOrder.setId(exitsOrder.getId());
			SpringUtil.getBean(TtOrderService.class).doUpdate(ttOrder, ttOrderLineItemList);
		}
		else {
			SpringUtil.getBean(TtOrderService.class).doCreate(ttOrder, ttOrderLineItemList);
		}
	}

	@Transactional
	@OpLog(snaptSource = TtOrderAddSnapSource.class, title = "新增Tiktok订单", businessType = BusinessType.INSERT,
			businessModule = TtOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doCreate(TtOrder ttOrder, List<TtOrderLineItem> itemList) {
		// 插入订单
		Long orderId = ttOrderRepository.insert(ttOrder);
		// 插入商品
		ttOrderLineItemRepository.batchInsert(itemList, orderId);
		// 返回主键增加日志
		return R.success(orderId);
	}

	@Transactional
	@OpLog(snaptSource = TtOrderUpdateSnapSource.class, title = "更新Tiktok订单", businessType = BusinessType.UPDATE,
			businessModule = TtOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doUpdate(TtOrder ttOrder, List<TtOrderLineItem> itemList) {
		// 先删除原有商品
		ttOrderLineItemRepository.deleteByOrderId(ttOrder.getId());
		// 插入商品
		ttOrderLineItemRepository.batchInsert(itemList, ttOrder.getId());
		// 更新订单
		Long orderId = ttOrderRepository.update(ttOrder);
		// 返回主键增加日志
		return R.success(orderId);
	}

	public TtOrderLog getLogById(Long orderId) {
		// 查询订单
		TtOrder ttOrder = ttOrderRepository.getById(orderId);
		if (Objects.isNull(ttOrder)) {
			return null;
		}
		TtOrderLog ttOrderLog = ttOrderLogConvertor.toLog(ttOrder);
		// 查询商品
		List<TtOrderLineItem> ttOrderLineItemList = ttOrderLineItemRepository.listByOrderId(orderId);
		ttOrderLog.setItemLogList(ttOrderLineItemList.stream().map(ttOrderLineItem -> {
			return ttOrderLineItemLogConvertor.toLog(ttOrderLineItem);
		}).collect(Collectors.toList()));
		return ttOrderLog;
	}

}
