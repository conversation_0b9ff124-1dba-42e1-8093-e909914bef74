package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MclOrderPaymentLog {
	/**
	 * 美客多支付id
	 */
	private Long mclPaymentId;

	/**
	 * 美客多订单id
	 */
	private Long mclOrderId;

	/**
	 * 收集者id
	 */
	private Long collectorId;

	/**
	 * 支付人id
	 */
	private Long payerId;

	/**
	 * card id
	 */
	private Long cardId;

	/**
	 * site id
	 */
	private String siteId;

	/**
	 * 原因
	 */
	private String reason;

	/**
	 * 支付方式id
	 */
	private String paymentMethodId;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 分期付款
	 */
	private Integer installments;

	/**
	 * issuer_id
	 */
	private String issuerId;

	/**
	 * atm转账json
	 */
	private String atmTransferReference;

	/**
	 * 优惠券id
	 */
	private String couponId;

	/**
	 * 激活 uri
	 */
	private String activationUri;

	/**
	 * 操作类型
	 */
	private String operationType;

	/**
	 * 支付类型
	 */
	private String paymentType;

	/**
	 * 可用操作列表json
	 */
	private String availableActions;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 状态明细
	 */
	private String statusDetail;

	/**
	 * 交易金额
	 */
	private BigDecimal transactionAmount;

	/**
	 * 税费
	 */
	private BigDecimal taxesAmount;

	/**
	 * 运费
	 */
	private BigDecimal shippingCost;

	/**
	 * 优惠金额
	 */
	private BigDecimal couponAmount;

	/**
	 * 多付金额
	 */
	private BigDecimal overpaidAmount;

	/**
	 * 总支付金额
	 */
	private BigDecimal totalPaidAmount;

	/**
	 * 分期付款金额
	 */
	private BigDecimal installmentAmount;

	/**
	 * 延期时间
	 */
	private String deferredPeriod;

	/**
	 * 审核日期
	 */
	private LocalDateTime dateApproved;

	/**
	 * 授权码
	 */
	private String authorizationCode;

	/**
	 * 交易id
	 */
	private String transactionOrderId;

	/**
	 * 支付创建时间
	 */
	private LocalDateTime dateCreated;

	/**
	 * 支付更新时间
	 */
	private LocalDateTime dateLastModified;
}
