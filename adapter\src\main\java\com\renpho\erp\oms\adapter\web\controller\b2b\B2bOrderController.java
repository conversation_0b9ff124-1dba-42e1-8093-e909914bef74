package com.renpho.erp.oms.adapter.web.controller.b2b;

import java.io.IOException;
import java.util.List;

import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import com.renpho.erp.oms.application.b2b.order.command.B2bOrderCreateCmd;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderEditCmd;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderPageQuery;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderRemarkCmd;
import com.renpho.erp.oms.application.b2b.order.service.*;
import com.renpho.erp.oms.application.b2b.order.vo.*;
import com.renpho.erp.oms.application.common.remark.fetcher.B2bOrderRemarkFetcher;
import com.renpho.erp.oms.application.common.remark.service.RemarkQueryService;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

/**
 * B2B订单控制器
 */
@RestController
@RequestMapping("/b2b/order")
@ShenyuSpringCloudClient("/b2b/order/**")
@RequiredArgsConstructor
public class B2bOrderController {

	private final B2bOrderQueryService b2bOrderQueryService;
	private final B2bOrderCreateService b2bOrderCreateService;
	private final B2bOrderEditService b2bOrderEditService;
	private final B2bOrderAuditService b2bOrderAuditService;
	private final RemarkQueryService remarkQueryService;
	private final B2bOrderRemarkFetcher b2bOrderRemarkFetcher;
	private final B2bOrderOperateService b2bOrderOperateService;

	/**
	 * B2B订单备注
	 */
	@PostMapping("/remark")
	@PreAuthorize(PermissionConstant.B2bOrder.REMARK)
	public R<Void> remark(@RequestBody @Valid B2bOrderRemarkCmd cmd) {
		b2bOrderOperateService.remark(cmd);
		return R.success();
	}

	/**
	 * B2B订单历史备注
	 */
	@GetMapping("/orderRemarkHistory/{orderId}")
	@PreAuthorize(PermissionConstant.B2bOrder.REMARK)
	public R<List<OrderRemarkHistoryVO>> orderRemarkHistory(@PathVariable("orderId") Long orderId) {
		return R.success(remarkQueryService.getRemarkHistory(orderId, b2bOrderRemarkFetcher));
	}

	/**
	 * B2B订单分页查询
	 *
	 * @param query 查询条件
	 * @return 分页结果
	 */
	@PostMapping("/page")
	@PreAuthorize(PermissionConstant.B2bOrder.PAGE)
	public R<Paging<B2bOrderPageVO>> page(@RequestBody B2bOrderPageQuery query) {
		return R.success(b2bOrderQueryService.page(query));
	}

	/**
	 * B2B订单导出
	 *
	 * @param query 查询条件
	 * @param response HTTP响应
	 * @throws IOException IO异常
	 */
	@PostMapping("/export")
	@PreAuthorize(PermissionConstant.B2bOrder.EXPORT)
	public void export(@RequestBody B2bOrderPageQuery query, HttpServletResponse response) throws IOException {
		b2bOrderQueryService.export(query, response);
	}

	/**
	 * 创建
	 */
	@PostMapping("/create")
	@PreAuthorize(PermissionConstant.B2bOrder.CREATE)
	public R<Void> create(@RequestBody @Valid B2bOrderCreateCmd cmd) {
		b2bOrderCreateService.create(SecurityUtils.getUserId(), cmd);
		return R.success();
	}

	/**
	 * 编辑
	 */
	@PostMapping("/edit")
	@PreAuthorize(PermissionConstant.B2bOrder.EDIT)
	public R<Void> edit(@RequestBody @Valid B2bOrderEditCmd cmd) {
		b2bOrderEditService.edit(cmd);
		return R.success();
	}

	/**
	 * B2B订单详情查询
	 *
	 * @param orderId 订单ID
	 * @return 订单详情
	 */
	@GetMapping("/detail")
	public R<B2bOrderDetailVO> detail(@RequestParam Long orderId) {
		B2bOrderDetailVO result = b2bOrderQueryService.getB2bOrderDetail(orderId);
		if (result == null) {
			return R.fail("订单不存在");
		}
		return R.success(result);
	}

	/**
	 * 订单详情（编辑订单回显时使用）
	 * @param orderId 订单id
	 * @return R
	 */
	@GetMapping("/detailForUpdate")
	public R<B2bOrderDetailForUpdateVo> detailForUpdate(@RequestParam Long orderId) {
		B2bOrderDetailForUpdateVo b2bOrderDetailForUpdate = b2bOrderQueryService.getB2bOrderDetailForUpdate(orderId);
		if (b2bOrderDetailForUpdate == null) {
			return R.fail("订单不存在");
		}
		return R.success(b2bOrderDetailForUpdate);
	}

	/**
	 * B2B订单商品列表查询
	 *
	 * @param orderId 订单ID
	 * @return 商品列表
	 */
	@GetMapping("/itemList")
	public R<List<B2bOrderItemVO>> getItemList(@RequestParam Long orderId) {
		List<B2bOrderItemVO> items = b2bOrderQueryService.getB2bOrderItemList(orderId);
		return R.success(items);
	}

	/**
	 * B2B订单客户联系人查询
	 *
	 * @param orderId 订单ID
	 * @return 客户联系人列表
	 */
	@GetMapping("/customerContacts")
	public R<List<B2bOrderCustomerContactVO>> getCustomerContacts(@RequestParam Long orderId) {
		List<B2bOrderCustomerContactVO> contacts = b2bOrderQueryService.getB2bOrderCustomerContacts(orderId);
		return R.success(contacts);
	}

	/**
	 * B2B订单收款单查询
	 *
	 * @param orderId 订单ID
	 * @return 收款单列表
	 */
	@GetMapping("/receiptOrders/{orderId}")
	public R<List<B2bReceiptOrderVO>> getReceiptOrders(@PathVariable Long orderId) {
		List<B2bReceiptOrderVO> receiptOrders = b2bOrderQueryService.getB2bReceiptOrders(orderId);
		return R.success(receiptOrders);
	}

	/**
	 * 提交订单审核
	 *
	 * @param orderId 订单ID
	 * @return 操作结果
	 */
	@PostMapping("/submitAudit/{orderId}")
	@PreAuthorize(PermissionConstant.B2bOrder.AUDIT)
	public R<Void> submitAudit(@PathVariable Long orderId) {
		b2bOrderAuditService.submitAudit(orderId);
		return R.success();
	}

	/**
	 * B2B 取消订单
	 */
	@PostMapping("/cancel/{orderId}")
	@PreAuthorize(PermissionConstant.B2bOrder.CANCEL)
	public R<Void> cancelOrder(@PathVariable("orderId") Long orderId) {
		b2bOrderOperateService.cancelOrder(orderId);
		return R.success();
	}

	/**
	 * 提交订单发货审核
	 *
	 * @param orderId 订单ID
	 * @return 操作结果
	 */
	@PostMapping("/submitShipmentAudit/{orderId}")
	@PreAuthorize(PermissionConstant.B2bOrder.SHIPMENT_AUDIT)
	public R<Void> submitShipmentAudit(@PathVariable Long orderId) {
		b2bOrderAuditService.submitShipmentAudit(orderId);
		return R.success();
	}
}
