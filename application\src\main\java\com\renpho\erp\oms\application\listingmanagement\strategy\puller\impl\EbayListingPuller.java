package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.apiproxy.ebay.model.ShopAccount;
import com.renpho.erp.apiproxy.ebay.model.listing.GetSellerListRequest;
import com.renpho.erp.apiproxy.ebay.model.listing.GetSellerListResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.EbayClient;
import com.renpho.karma.json.JSONKit;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class EbayListingPuller  extends AbstractListingPullerTemplate {
    @Autowired
    private EbayClient ebayClient;

    private static final int DAY = 100;

    @Override
    protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
        // 时间范围范围当前时间,不继续拉取
        LocalDateTime now = LocalDateTime.now();
        if (now.isBefore(listingSearchQuery.getStartTime()) || now.isBefore(listingSearchQuery.getEndTime())) {
            return null;
        }

        // 获取店铺信息
        String ebaySellerId = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
                .map(StoreAuthorizationVo::getAuthorization)
                .map(StoreAuthorizationVo.Authorization::getEbaySellerId)
                .filter(StrUtil::isNotBlank)
                .orElseThrow(() -> new BusinessException("ebay sellerId为空,storeId是%s".formatted(storeAuthorization.getStoreId())));
        String siteCode = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
                .map(StoreAuthorizationVo::getSiteCode)
                .filter(StrUtil::isNotBlank)
                .orElseThrow(() -> new BusinessException("ebay站点code为空,storeId是%s".formatted(storeAuthorization.getStoreId())));
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setSellerId(ebaySellerId);
        shopAccount.setSiteCode(siteCode);
        // 组装参数
        GetSellerListRequest getSellerListRequest = new GetSellerListRequest();
        getSellerListRequest.setErrorLanguage("en_US");
        getSellerListRequest.setWarningLevel("High");
        getSellerListRequest.setGranularityLevel("Coarse");
        getSellerListRequest.setIncludeWatchCount(true);

        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DatePattern.UTC_MS_PATTERN);
        getSellerListRequest.setStartTimeFrom(listingSearchQuery.getStartTime().format(dateTimeFormatter));
        getSellerListRequest.setStartTimeTo(listingSearchQuery.getEndTime().format(dateTimeFormatter));

        GetSellerListRequest.Pagination pagination = new GetSellerListRequest.Pagination();
        pagination.setPageNumber(listingSearchQuery.getPageNo());
        pagination.setEntriesPerPage(listingSearchQuery.getPageSize());
        getSellerListRequest.setPagination(pagination);

        // 请求获取listing数据
        GetSellerListResponse sellerList = ebayClient.getSellerList(shopAccount, getSellerListRequest);
        ListingSearchDTO listingSearchDTO = new ListingSearchDTO();
        // 处理listing数据
        handleListingData(listingSearchDTO, storeAuthorization, sellerList);
        // 处理下一页分页参数
        handleNextPageParam(listingSearchDTO, listingSearchQuery, sellerList);

        return listingSearchDTO;
    }

    private void handleListingData(ListingSearchDTO listingSearchDTO, StoreAuthorizationDTO storeAuthorization, GetSellerListResponse sellerList) {
        List<GetSellerListResponse.Item> itemList = Optional.ofNullable(sellerList.getItemArray())
                .map(GetSellerListResponse.ItemArray::getItems)
                .orElse(Collections.emptyList());
        List<SalesChannelListingSource> salesChannelListingSourceList = new ArrayList<>();
        for (GetSellerListResponse.Item item : itemList) {
            SalesChannelListingSource salesChannelListingSource = super.buildListingSource(storeAuthorization);
            salesChannelListingSource.setListingId(item.getItemID());
            salesChannelListingSource.setContent(JSONKit.toJSONString(item));
            salesChannelListingSourceList.add(salesChannelListingSource);
        }

        listingSearchDTO.setListingSourceList(salesChannelListingSourceList);
    }

    private void handleNextPageParam(ListingSearchDTO listingSearchDTO, ListingSearchQuery listingSearchQuery, GetSellerListResponse sellerList) {
        // 处理翻页
        if (Boolean.TRUE.equals(sellerList.getHasMoreItems())) {
            listingSearchDTO.setPageNo(listingSearchQuery.getPageNo() + 1);
        } else {
            // 这个时间范围没有listing了,下个时间范围
            listingSearchDTO.setPageNo(1);
            listingSearchDTO.setStartTime(listingSearchQuery.getEndTime());

            // 产品需求,listing拉取终止时间为当前时间减3分钟
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = listingSearchQuery.getEndTime().plusDays(DAY);
            if (endTime.isAfter(now)) {
                endTime = now.minusMinutes(3);
            }
            listingSearchDTO.setEndTime(endTime);
        }
    }

    @NotNull
    @Override
    protected ListingSearchQuery buildSearchAfterQuery() {
        ListingSearchQuery listingSearchQuery = new ListingSearchQuery();
        listingSearchQuery.setPageNo(1);
        // ebay最大两百,别太极端100条每页
        listingSearchQuery.setPageSize(100);

        // ebay支持时间范围查询,产品要求从20240101开始
        LocalDateTime startTime = LocalDateTime.of(2024, 1, 1, 0, 0, 0);
        listingSearchQuery.setStartTime(startTime);
        // ebay支持最大时间范围120天
        LocalDateTime endTime = startTime.plusDays(DAY);
        listingSearchQuery.setEndTime(endTime);
        return listingSearchQuery;
    }

    @Override
    protected void fillNextSearchQuery(ListingSearchQuery listingSearchQuery, ListingSearchDTO listingSearchDTO) {
        listingSearchQuery.setPageNo(listingSearchDTO.getPageNo());
        listingSearchQuery.setStartTime(listingSearchDTO.getStartTime());
        listingSearchQuery.setEndTime(listingSearchDTO.getEndTime());
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.EBY_CHANNEL_CODE;
    }
}
