package com.renpho.erp.oms.application.channelmanagement.tiktok.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class TtOrderLog {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 店铺id
	 */
	private String shopId;

	/**
	 * Tiktok shop order id
	 */
	private String tiktokOrderId;

	/**
	 * 买家说明
	 */
	private String buyerMessage;

	/**
	 * 取消请求发起者 SELLER/ BUYER/ SYSTEM
	 */
	private String cancellationInitiator;

	/**
	 * 发货商ID
	 */
	private String shippingProviderId;

	/**
	 * Tiktok订单创建时间
	 */
	private LocalDateTime tiktokOrderCreateTime;

	/**
	 * 发货商名字
	 */
	private String shippingProvider;

	/**
	 * 此订单中包含的包裹清单id列表
	 */
	private String packages;

	/**
	 * 支付货币
	 */
	private String paymentCurrency;

	/**
	 * 买家支付订单中所有 SKU 的总金额
	 */
	private BigDecimal paymentSubTotal;

	/**
	 * 运费
	 */
	private BigDecimal paymentShippingFee;

	/**
	 * 卖家折扣
	 */
	private BigDecimal paymentSellerDiscount;

	/**
	 * 平台折扣
	 */
	private BigDecimal paymentPlatformDiscount;

	/**
	 * 产品原价总额
	 */
	private BigDecimal paymentOriginalTotalProductPrice;

	/**
	 * 原价运费
	 */
	private BigDecimal paymentOriginalShippingFee;

	/**
	 * 卖家提供运费折扣
	 */
	private BigDecimal paymentShippingFeeSellerDiscount;

	/**
	 * 平台提供运费折扣
	 */
	private BigDecimal paymentShippingFeePlatformDiscount;

	/**
	 * 税费
	 */
	private BigDecimal paymentTax;

	/**
	 * 小额订单费
	 */
	private BigDecimal paymentSmallOrderFee;

	/**
	 * 运费税费
	 */
	private BigDecimal paymentShippingFeeTax;

	/**
	 * 商品税费
	 */
	private BigDecimal paymentProductTax;

	/**
	 * 零售送货费
	 */
	private BigDecimal paymentRetailDeliveryFee;

	/**
	 * 服务费
	 */
	private BigDecimal paymentBuyerServiceFee;

	/**
	 * 手续费
	 */
	private BigDecimal paymentHandlingFee;

	/**
	 * 运费险
	 */
	private BigDecimal paymentShippingInsuranceFee;

	/**
	 * 商品险
	 */
	private BigDecimal paymentItemInsuranceFee;

	/**
	 * 邮政编码
	 */
	private String postalCode;

	/**
	 * 区域代码
	 */
	private String regionCode;

	/**
	 * 区信息数组json
	 */
	private String districtInfo;

	/**
	 * 订单状态
	 */
	private String status;

	/**
	 * 履约类型
	 */
	private String fulfillmentType;

	/**
	 * 发货类型
	 */
	private String deliveryType;

	/**
	 * 支付时间
	 */
	private LocalDateTime paidTime;

	/**
	 * 平台规定的最晚发货时间
	 */
	private LocalDateTime rtsSlaTime;

	/**
	 * 平台规定的最晚收款时间
	 */
	private LocalDateTime ttsSlaTime;

	/**
	 * 取消原因
	 */
	private String cancelReason;

	/**
	 * Tiktok订单更新时间
	 */
	private LocalDateTime tiktokOrderUpdateTime;

	/**
	 * 支付方式
	 */
	private String paymentMethodName;

	/**
	 * 卖家发货订单的时间
	 */
	private LocalDateTime rtsTime;

	/**
	 * 追踪号码
	 */
	private String trackingNumber;

	/**
	 * 订单合并拆分标识 COMBINED、SPLIT
	 */
	private String splitOrCombineTag;

	/**
	 * 是否已更新收件人地址 0:否 1:是
	 */
	private Boolean hasUpdatedRecipientAddress;

	/**
	 * 卖家仓库id
	 */
	private String warehouseId;

	/**
	 * 买家请求取消时间
	 */
	private LocalDateTime requestCancelTime;

	/**
	 * 发货方式
	 */
	private String shippingType;

	/**
	 * 买家id
	 */
	private String userId;

	/**
	 * 卖家备注
	 */
	private String sellerNote;

	/**
	 * 平台规定的最晚发货时间
	 */
	private LocalDateTime deliverySlaTime;

	/**
	 * 是否货到付款
	 */
	private Boolean isCod;

	/**
	 * 订单配送选项 ID
	 */
	private String deliveryOptionId;

	/**
	 * 取消时间
	 */
	private LocalDateTime cancelTime;

	/**
	 * 是否需要上传发票 UNKNOWN、NEED_INVOICE、NO_NEED
	 */
	private String needUploadInvoice;

	/**
	 * 配送选项名称
	 */
	private String deliveryOptionName;

	/**
	 * 发票号码
	 */
	private String cpf;

	/**
	 * 买家邮箱
	 */
	private String buyerEmail;

	/**
	 * 交货截止时间（如果订单在此时间之前尚未将其状态更新为“已发货”，则订单将被 TikTok Shop 取消）
	 */
	private LocalDateTime deliveryDueTime;

	/**
	 * 是否是样品订单 0:否 1:是
	 */
	private Boolean isSampleOrder;

	/**
	 * 发货截止时间（如果订单在此时间之前尚未将其状态更新为“AWAITING_COLLECTION”，则订单将被 TikTok Shop 取消）
	 */
	private LocalDateTime shippingDueTime;

	/**
	 * 揽收截止时间（如果订单在此时间之前尚未将其状态更新为“IN_TRANSIT”，则订单将被 TikTok Shop 取消）
	 */
	private LocalDateTime collectionDueTime;

	/**
	 * 订单是否经历或将会经历 ON_HOLD 状态 0:否 1:是
	 */
	private Boolean isOnHoldOrder;

	/**
	 * 发货时间
	 */
	private LocalDateTime deliveryTime;

	/**
	 * 是否是替换订单 0:否 1:是
	 */
	private Boolean isReplacementOrder;

	/**
	 * 揽收时间
	 */
	private LocalDateTime collectionTime;

	/**
	 * 正在替换的订单的订单 ID
	 */
	private String replacedOrderId;

	/**
	 * 当买家有待处理的取消请求时为 True 0:否 1:是
	 */
	private Boolean isBuyerRequestCancel;

	/**
	 * 取货截止时间
	 */
	private LocalDateTime pickUpCutOffTime;

	/**
	 * 最新领取时间
	 */
	private LocalDateTime fastDispatchSlaTime;

	/**
	 * 下订单的平台
	 */
	private String commercePlatform;

	/**
	 * 订单类型
	 */
	private String orderType;

	/**
	 * 发货时间
	 */
	private LocalDateTime releaseDate;

	/**
	 * 订单处理时间json
	 */
	private String handlingDuration;

	/**
	 * 平台指定的订单自动取消时间
	 */
	private LocalDateTime cancelOrderSlaTime;

	private List<TtOrderLineItemLog> itemLogList;
}
