package com.renpho.erp.oms.application.channelmanagement.shopify.service;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.platform.convert.MonitorPlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.convert.PlatformOrderConvertor;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.service.AbstractDefaultPlatfomService;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderVO;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrder;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderRepository;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.persistence.shopify.mapper.SpfOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.shopify.po.SpfOrderPO;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@AllArgsConstructor
@Service(ChannelCode.SPF_CHANNEL_CODE)
@Slf4j
public class SpfOrderPlatformService extends AbstractDefaultPlatfomService {
	private final SpfOrderRepository spfOrderRepository;
	private final PlatformOrderConvertor platformOrderConvertor;
	private final SpfOrderLogConvertor spfOrderLogConvertor;

	private final MonitorPlatformOrderConvertor monitorPlatformOrderConvertor;

	private final SpfOrderMapper spfOrderMapper;

	@Override
	protected Paging<PlatformOrderVO> doPage(PlatformPageQuery platformPageQuery) {
		IPage<SpfOrderPage> page = spfOrderRepository.page(platformPageQuery);
		List<SpfOrderPage> spfOrderPageList = page.getRecords();
		if (CollectionUtil.isEmpty(spfOrderPageList)) {
			return Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
		}
		List<PlatformOrderVO> records = spfOrderPageList.stream().map(spfOrderPage -> {
			return platformOrderConvertor.shopifyOrderToVO(spfOrderPage);
		}).collect(Collectors.toList());

		return Paging.of(records, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent());
	}

	@Override
	public R get(PlatformQuery platformQuery) {
		SpfOrder spfOrder = spfOrderRepository.getByIdOrChannelOrderId(platformQuery);
		if (Objects.isNull(spfOrder)) {
			throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
		}
		SpfOrderVO spfOrderVO = spfOrderLogConvertor.toVO(spfOrder);
		// 查询店铺
		spfOrderVO.setStoreName(getStoreName(spfOrder.getStoreId()));
		return R.success(spfOrderVO);
	}

	@Override
	public List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery) {
		List<MonitorPlatformOrderDto> platformOrderDtos = spfOrderMapper.selectList(Wrappers.<SpfOrderPO>lambdaQuery()
						.select(SpfOrderPO::getShopifyOrderId, SpfOrderPO::getLastMpdsSyncOrderTime, SpfOrderPO::getStoreId)
						.ge(monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime() != null, SpfOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getStartLastMpdsSyncOrderTime())
						.le(monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime() != null, SpfOrderPO::getLastMpdsSyncOrderTime, monitorPlatformOrderQuery.getEndLastMpdsSyncOrderTime()))
				.stream()
				.map(x -> monitorPlatformOrderConvertor.shopifyOrderToDto(x))
				.collect(Collectors.toList());
		return platformOrderDtos;
	}
}
