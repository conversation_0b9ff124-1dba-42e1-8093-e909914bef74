package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户保险授信信息视图对象
 */
@Data
public class CustomerInsuranceCreditVO {
    /**
     * ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 保险公司
     */
    private String insuranceCompany;
    
    /**
     * 保险金额
     */
    private BigDecimal insuredAmount;
    
    /**
     * 保险币种
     */
    private String insuredCurrency;
    
    /**
     * 有效期开始时间
     */
    private LocalDateTime validTimeStart;
    
    /**
     * 有效期结束时间
     */
    private LocalDateTime validTimeEnd;
} 