package com.renpho.erp.oms.domain.vc.order.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RejectReason {

    OUT_OF_STOCK(1, "缺货"),
    INCORRECT_SKU(2, "sku错误"),
    DELIST(3, "退市"),
    ;
    private final Integer value;
    private final String description;


    public static RejectReason enumOf(Integer value) {
        for (RejectReason rejectReason : values()) {
            if (rejectReason.getValue().equals(value)) {
                return rejectReason;
            }
        }
        return null;
    }
}
