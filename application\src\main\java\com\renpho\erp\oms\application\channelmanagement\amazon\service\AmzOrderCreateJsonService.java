package com.renpho.erp.oms.application.channelmanagement.amazon.service;


import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderSourceJson;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderSyncLog;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSourceJsonRepository;
import com.renpho.erp.oms.domain.channelmanagement.amazon.repository.AmzOrderSyncLogRepository;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.karma.json.JSONKit;
import io.swagger.client.model.orders.Order;
import lombok.AllArgsConstructor;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.renpho.erp.oms.infrastructure.common.enums.SyncTypeEnum.SYNC_AMAZON_ORDER;

@Service
@AllArgsConstructor
public class AmzOrderCreateJsonService {
	private final AmzOrderSourceJsonRepository amzOrderSourceJsonRepository;
	private final AmzOrderSyncLogRepository amzOrderSyncLogRepository;
	private final StoreClient storeClient;

	// 入库操作
	@Transactional
	public void insertOrUpdate(List<Order> amazonOrders, String sellerId) {
		Map<String, Integer> marketPlaceId2StoreId = storeClient.getStoreIdByAmzSellerIdAndMarketPlaceId(sellerId,
				amazonOrders.stream().map(Order::getMarketplaceId).collect(Collectors.toList()));
		// 原始json更新或入库
		List<AmzOrderSourceJson> amzOrderSourceJsonList = amazonOrders.stream().map(order -> {
			AmzOrderSourceJson amzOrderSourceJson = new AmzOrderSourceJson();
			amzOrderSourceJson.setOrderStatus(order.getOrderStatus().toString());
			amzOrderSourceJson.setStoreId(marketPlaceId2StoreId.get(order.getMarketplaceId()));
			amzOrderSourceJson.setSellerId(sellerId);
			amzOrderSourceJson.setPlatOrderId(order.getAmazonOrderId());
			amzOrderSourceJson.setOrderJson(JSONKit.toJSONString(order));
			return amzOrderSourceJson;
		}).collect(Collectors.toList());
		amzOrderSourceJsonRepository.batchInsertOrUpdate(amzOrderSourceJsonList);

		// 记录更新拉取日志
		List<AmzOrderSyncLog> amzOrderSyncLogList = amazonOrders.stream().map(order -> {
			AmzOrderSyncLog amzOrderSyncLog = new AmzOrderSyncLog();
			amzOrderSyncLog.setDetailLog(JsonUtils.toJson(order));
			amzOrderSyncLog.setPlatOrderId(order.getAmazonOrderId());
			amzOrderSyncLog.setStoreId(marketPlaceId2StoreId.get(order.getMarketplaceId()));
			amzOrderSyncLog.setSyncType(SYNC_AMAZON_ORDER.getValue());
			amzOrderSyncLog.setSellerId(sellerId);
			return amzOrderSyncLog;
		}).collect(Collectors.toList());
		amzOrderSyncLogRepository.batchInsert(amzOrderSyncLogList);

	}

}
