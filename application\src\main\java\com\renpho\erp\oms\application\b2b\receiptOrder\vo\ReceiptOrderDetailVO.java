package com.renpho.erp.oms.application.b2b.receiptOrder.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description 确认收款单详情的vo
 * @date 2025/6/23 11:00
 */
@Data
public class ReceiptOrderDetailVO {

    /**
     * id
     */
    private Long id;

    /**
     * orderId
     */
    private Long orderId;

    /**
     * B2B订单号
     */
    private String orderNo;

    /**
     * 币种
     */
    private String currency;


    /**
     * 金额合计
     */
    private BigDecimal totalAmount;


    /**
     * 订单已收款金额
     */
    private BigDecimal orderReceiptAmount;

    /**
     * 已收款百分比例(20代表20%)
     */
    private BigDecimal orderReceiptRate;

    /**
     * 银行账号
     */
    private Long fmsFundAccountCurrencyId;

    /**
     * 银行账号名称
     */
    private String fmsFundAccountCurrencyName;

    /**
     * 付款条款编码
     */
    private String paymentTermsCode;

    /**
     * 付款条款名称
     */
    private String paymentTermsName;


    /**
     * 银行参考号
     */
    private String bankReferNo;


    /**
     * 客户付款金额
     */
    private BigDecimal customerPaidAmount;

    /**
     * 客户付款时间
     */
    private LocalDateTime customerPaidTime;


    /**
     * 客戶付款信息文件url
     */
    private String customerPaidInfoFileUrl;

    /**
     * 客戶付款信息文件名
     */
    private String customerPaidInfoFileName;

}
