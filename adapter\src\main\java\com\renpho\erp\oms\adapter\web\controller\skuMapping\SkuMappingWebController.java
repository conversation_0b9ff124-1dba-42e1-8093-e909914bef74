package com.renpho.erp.oms.adapter.web.controller.skuMapping;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.renpho.erp.bpm.api.annotation.BpmApi;
import com.renpho.erp.bpm.api.dto.BpmStatusParam;
import com.renpho.erp.oms.adapter.web.controller.listener.SkuMappingExcelListener;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingUpdateCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.SkuMappingService;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.SkuMappingVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingFnListQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.enums.StatusEnum;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingLineVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingPageVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.common.component.DownLoadComponent;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/skuMapping/**")
@RequestMapping("/skuMapping")
@AllArgsConstructor
@Tag(name = "销售SKU映射接口", description = "销售SKU映射接口")
public class SkuMappingWebController {
    private final SkuMappingService skuMappingService;
    private final DownLoadComponent downLoadComponent;

    /**
     * 新增销售SKU映射
     *
     * @param skuMappingAddCmd
     * @return R
     */
    @Operation(summary = "新增销售SKU映射", description = "新增销售SKU映射")
    @PostMapping("/add")
    @PreAuthorize(PermissionConstant.SkuMapping.ADD)
    public R<Long> add(@RequestBody @Valid SkuMappingAddCmd skuMappingAddCmd) {
        return skuMappingService.addSkuMapping(skuMappingAddCmd);
    }

    /**
     * 根据id查详情
     *
     * @param id
     * @return SkuMappingVO
     */
    @Operation(summary = "根据id查详情", description = "根据id查详情")
    @PreAuthorize(PermissionConstant.SkuMapping.PAGE)
    @GetMapping("/getById")
    public R<SkuMappingVO> getById(@RequestParam Long id) {
        return skuMappingService.getById(id);
    }

    /**
     * 编辑
     *
     * @param skuMappingUpdateCmd
     * @return R
     */
    @Operation(summary = "编辑", description = "编辑")
    @PostMapping("/update")
    @PreAuthorize(PermissionConstant.SkuMapping.EDIT)
    public R<Integer> update(@RequestBody @Valid SkuMappingUpdateCmd skuMappingUpdateCmd) {
        return skuMappingService.update(skuMappingUpdateCmd);
    }

    /**
     * 禁用销售SKU映射
     *
     * @param id
     * @return R
     */
    @Operation(summary = "禁用销售SKU映射", description = "禁用销售SKU映射")
    @GetMapping("/disable")
    @PreAuthorize(PermissionConstant.SkuMapping.CHANGESTATUS)
    public R disable(@RequestParam Long id) {
        return skuMappingService.disable(id, StatusEnum.DISABLE, StatusEnum.ENABLE);
    }

    /**
     * 启用销售SKU映射
     *
     * @param id
     * @return R
     */
    @Operation(summary = "启用销售SKU映射", description = "启用销售SKU映射")
    @GetMapping("/enable")
    @PreAuthorize(PermissionConstant.SkuMapping.CHANGESTATUS)
    public R enable(@RequestParam Long id) {
        return skuMappingService.enable(id, StatusEnum.ENABLE, StatusEnum.DISABLE);
    }

    /**
     * 分页查询
     *
     * @param skuMappingPageQuery
     * @return Paging<SkuMappingVO>
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PreAuthorize(PermissionConstant.SkuMapping.PAGE)
    @PostMapping("/page")
    public R<Paging<SkuMappingPageVO>> page(@RequestBody SkuMappingPageQuery skuMappingPageQuery) {
        return skuMappingService.page(skuMappingPageQuery);
    }

    /**
     * 列表查询
     *
     * @param skuMappingQuery
     * @return List<SkuMappingVO>
     */
    @Operation(summary = "列表查询", description = "列表查询")
    @PostMapping("/list")
    R<List<SkuMappingFulfillmentVO>> list(@RequestBody SkuMappingQuery skuMappingQuery) {
        skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
        return skuMappingService.list(skuMappingQuery);
    }

    /**
     * 列表查询
     *
     * @param skuMappingQuery
     * @return List<SkuMappingVO>
     */
    @Operation(summary = "全部列表查询", description = "列表查询")
    @PostMapping("/listAll")
    R<List<SkuMappingFulfillmentVO>> listAll(@RequestBody SkuMappingQuery skuMappingQuery) {
        return skuMappingService.list(skuMappingQuery);
    }

    /**
     * 查询全部FNSKU（去重）
     *
     * @return com.renpho.karma.dto.R<java.util.List <
            * com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO>>
     * <AUTHOR>
     * @Date 10:05 2025/3/20
     * @Param [skuMappingQuery]
     **/
    @Operation(summary = "全部FNSKU查询", description = "全部FNSKU查询")
    @BpmApi(desc = "查询FNSKU", paramClass = {BpmStatusParam.class}, showFields = {"fnSku"}, valueField = "fnSku")
    @PostMapping("/listALLFnSku")
    R<List<SkuMappingFulfillmentVO>> listALLFnSku(@RequestBody SkuMappingQuery skuMappingQuery) {
        R<List<SkuMappingFulfillmentVO>> r = skuMappingService.list(skuMappingQuery);
        if (r.isSuccess() && CollectionUtil.isNotEmpty(r.getData())) {
            List<SkuMappingFulfillmentVO> list = r.getData();
            // 基于fnsku属性去重：// 解决冲突，保留第一个元素。可以根据需要调整合并策略。
            Map<String, SkuMappingFulfillmentVO> uniqueMap = list.stream()
                    .collect(Collectors.toMap(SkuMappingFulfillmentVO::getFnSku, Function.identity(), (existing, replacement) -> existing));
            // 将Map的值转换回List
            List<SkuMappingFulfillmentVO> uniqueList = new ArrayList<>(uniqueMap.values());
            return R.success(uniqueList);
        }
        return r;
    }

    /**
     * 列表查询
     *
     * @param skuMappingQuery
     * @return List<SkuMappingVO>
     */
    @Operation(summary = "列表内部查询", description = "列表内部查询")
    @Inner
    @PostMapping("/inner/list")
    R<List<SkuMappingFulfillmentVO>> innerList(@RequestBody SkuMappingQuery skuMappingQuery) {
        return skuMappingService.list(skuMappingQuery);
    }

    @Operation(summary = "列表内部查询", description = "列表内部查询")
    @Inner
    @PostMapping("/inner/fnList")
    R<List<SkuMappingLineVO>> fnList(@RequestBody SkuMappingFnListQuery skuMappingQuery) {
        return R.success(skuMappingService.fnList(skuMappingQuery));
    }

    /**
     * fnsku列表查询
     *
     * @return List<String>
     */
    @Operation(summary = "fnsku列表查询", description = "fnsku列表查询")
    @Inner
    @GetMapping("/listFnsku")
    R<List<String>> listFnsku() {
        return skuMappingService.listFnsku();
    }

    /**
     * 导入
     *
     * @param skuMappingPageQuery
     * @param response
     */
    @Operation(summary = "导出", description = "导出")
    @PreAuthorize(PermissionConstant.SkuMapping.EXPORT)
    @PostMapping(value = "/exportExcel")
    public void exportExcel(@RequestBody SkuMappingPageQuery skuMappingPageQuery, HttpServletResponse response) {
        skuMappingService.exportExcel(skuMappingPageQuery, response);
    }

    /**
     * 导入
     *
     * @param file
     * @return R
     */
    @Operation(summary = "导入", description = "导入")
    @PreAuthorize(PermissionConstant.SkuMapping.IMPORT)
    @PostMapping(value = "/importExcel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            SkuMappingExcelListener skuMappingExcelListener = new SkuMappingExcelListener();
            EasyExcel.read(inputStream, SkuMappingImportExcel.class, skuMappingExcelListener).sheet().doRead();
            List<SkuMappingImportExcel> skuMappingImportExcels = skuMappingExcelListener.getSkuMappingImportExcels();
            if (CollectionUtil.isEmpty(skuMappingImportExcels)) {
                throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
            }
            return skuMappingService.importExcel(skuMappingImportExcels);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 下载销售SKU映射导入模板
     */
    @Operation(summary = "导入模板下载", description = "导入模板下载")
    @PostMapping("/downLoadTemplate")
    public void getTemplateFile(HttpServletResponse response) {
        downLoadComponent.templateDownLoad("SKU_TEMPLATE_FILE_NAME", response);
    }


}
