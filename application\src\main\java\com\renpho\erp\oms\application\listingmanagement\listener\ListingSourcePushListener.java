package com.renpho.erp.oms.application.listingmanagement.listener;

import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.ListingPusher;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.ListingPusherRegister;
import com.renpho.erp.oms.domain.listingmanagement.event.ListingSourcePushEvent;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 原始Listing推送-监听器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ListingSourcePushListener {

	private final SalesChannelListingSourceRepository salesChannelListingSourceRepository;
	private final ListingPusherRegister listingPusherRegister;

	@Async
	@EventListener
	public void pushMq(ListingSourcePushEvent event) {
		if (Objects.isNull(event) || Objects.isNull(event.listingSourceId())) {
			return;
		}
		Long listingSourceId = event.listingSourceId();
		SalesChannelListingSource listingSource = salesChannelListingSourceRepository.findById(listingSourceId);
		if (Objects.isNull(listingSource)) {
			return;
		}
		// 获取推送器
		ListingPusher pusher = listingPusherRegister.getPusher(listingSource.getSalesChannelCode());
		if (Objects.isNull(pusher)) {
			return;
		}
		// 推送Listing
		pusher.pushListing(listingSource);
	}

}
