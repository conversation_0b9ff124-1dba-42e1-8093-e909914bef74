package com.renpho.erp.oms.application.channelmanagement.temu.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.renpho.erp.apiproxy.temu.model.orders.GetOrdersDetailData;
import com.renpho.erp.apiproxy.temu.model.orders.OrderLabel;
import com.renpho.erp.apiproxy.temu.model.orders.Product;
import com.renpho.erp.oms.application.channelmanagement.temu.dto.TemOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.temu.vo.TemOrderItemVO;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderItem;
import com.renpho.karma.json.JSONKit;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class TemOrderItemAppConvertor {

    public static List<TemOrderItem> toDomainList(TemOrderPushDTO temOrderPushDTO) {
        List<TemOrderItem> temOrderItemList = Lists.newArrayList();
        if (Objects.nonNull(temOrderPushDTO) && CollectionUtil.isNotEmpty(temOrderPushDTO.getOrderList())) {
            temOrderPushDTO.getOrderList().forEach(order -> {
                TemOrderItem temOrderItem = new TemOrderItem();
                // 基本信息
                temOrderItem.setOrderSn(order.getOrderSn());
                temOrderItem.setCanceledQuantityBeforeShipment(order.getCanceledQuantityBeforeShipment());
                temOrderItem.setQuantity(order.getQuantity());
                temOrderItem.setGoodsId(order.getGoodsId());
                temOrderItem.setPackageSnInfo(CollectionUtil.isNotEmpty(order.getPackageSnInfo()) ? JSONKit.toJSONString(order.getPackageSnInfo()) : null);
                temOrderItem.setOrderLabel(CollectionUtil.isNotEmpty(order.getOrderLabel()) ? JSONKit.toJSONString(order.getOrderLabel()) : null);
                temOrderItem.setOrderStatus(order.getOrderStatus());
                temOrderItem.setInventoryDeductionWarehouseId(order.getInventoryDeductionWarehouseId());
                temOrderItem.setFulfillmentType(order.getFulfillmentType());
                temOrderItem.setIsCancelledDuringPending(order.getIsCancelledDuringPending());
                temOrderItem.setSpec(order.getSpec());
                temOrderItem.setFulfillmentWarning(CollectionUtil.isNotEmpty(order.getFulfillmentWarning()) ? JSONKit.toJSONString(order.getFulfillmentWarning()) : null);
                temOrderItem.setOrderPaymentType(order.getOrderPaymentType());
                temOrderItem.setOriginalOrderQuantity(order.getOriginalOrderQuantity());
                temOrderItem.setThumbUrl(order.getThumbUrl());
                temOrderItem.setGoodsName(order.getGoodsName());
                temOrderItem.setInventoryDeductionWarehouseName(order.getInventoryDeductionWarehouseName());
                temOrderItem.setSkuId(order.getSkuId());
                temOrderItem.setProductList(CollectionUtil.isNotEmpty(order.getProductList()) ? JSONKit.toJSONString(order.getProductList()) : null);


                temOrderItemList.add(temOrderItem);
            });
        }
        return temOrderItemList;
    }

    public static TemOrderItemVO toVO(TemOrderItem temOrderItem) {
        TemOrderItemVO temOrderItemVO = new TemOrderItemVO();
        temOrderItemVO.setOrderSn(temOrderItem.getOrderSn());
        temOrderItemVO.setCanceledQuantityBeforeShipment(temOrderItem.getCanceledQuantityBeforeShipment());
        temOrderItemVO.setQuantity(temOrderItem.getQuantity());
        temOrderItemVO.setThumbUrl(temOrderItem.getThumbUrl());
        temOrderItemVO.setOrderStatus(temOrderItem.getOrderStatus());
        temOrderItemVO.setOriginalOrderQuantity(temOrderItem.getOriginalOrderQuantity());
        temOrderItemVO.setFulfillmentType(temOrderItem.getFulfillmentType());
        if (StringUtils.isNotEmpty(temOrderItem.getProductList())) {
            List<Product> productList = JSONKit.parseObject(temOrderItem.getProductList(), new TypeReference<List<Product>>() {
            });
            if (CollectionUtil.isNotEmpty(productList)) {
                Product product = productList.get(0);
                temOrderItemVO.setProductId(product.getProductId());
                temOrderItemVO.setProductSkuId(product.getProductSkuId());
                temOrderItemVO.setExtCode(product.getExtCode());
            }
        }
        if (StringUtils.isNotEmpty(temOrderItem.getPackageSnInfo())) {
            List<GetOrdersDetailData.PackageSnInfo> packageSnList = JSONKit.parseObject(temOrderItem.getPackageSnInfo(), new TypeReference<>() {
            });
            if (CollectionUtil.isNotEmpty(packageSnList)) {
                temOrderItemVO.setPackageSnList(packageSnList.stream().map(GetOrdersDetailData.PackageSnInfo::getPackageSn).collect(Collectors.toList()));
            }

        }
        if (StringUtils.isNotEmpty(temOrderItem.getOrderLabel())) {
            List<OrderLabel> labelList = JSONKit.parseObject(temOrderItem.getOrderLabel(), new TypeReference<List<OrderLabel>>() {
            });
            temOrderItemVO.setLabelList(labelList.stream().filter(orderLable -> !orderLable.getValue().equals(0)).map(OrderLabel::getName).collect(Collectors.toList()));
        }
        return temOrderItemVO;
    }
}