package com.renpho.erp.oms.adapter.web.controller.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.oms.application.ruleconfig.autoShipAudit.dto.AutoShipAuditConfigImportExcel;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/19 16:38
 */
@Slf4j
@Getter
public class AutoShipAuditConfigListener  extends AnalysisEventListener<AutoShipAuditConfigImportExcel> {

    private final List<AutoShipAuditConfigImportExcel> autoShipAuditConfigImportExcelList = new ArrayList<>();
    /**
     * 中文表头
     */
    public static final Map<Integer, String> importCNHeadMap = new HashMap<>();

    /**
     * 英文表头
     *
     * @param data
     * @param context
     */
    public static final Map<Integer, String> importUSHeadMap = new HashMap<>();

    static {
        importCNHeadMap.put(0,"销售渠道");
        importCNHeadMap.put(1,"店铺");
        importCNHeadMap.put(2,"PSKU");
        importCNHeadMap.put(3,"仓库编码列表(多个以英文逗号隔开)");
        importCNHeadMap.put(4,"SKU组合类型:单品单件(是/否)");
        importCNHeadMap.put(5,"SKU组合类型:单品多件(是/否)");
        importCNHeadMap.put(6,"SKU组合类型:多品多件(是/否)");
        importCNHeadMap.put(7,"自动发货审核开关(开启/关闭)");

        importUSHeadMap.put(0,"Sales Channel");
        importUSHeadMap.put(1,"Shop");
        importUSHeadMap.put(2,"PSKU");
        importUSHeadMap.put(3,"Warehouse Code List(Split with comma)");
        importUSHeadMap.put(4,"SKU Comb Type:One SKU One PC(Y/N)");
        importUSHeadMap.put(5,"SKU Comb Type:One SKU Multi PCs(Y/N)");
        importUSHeadMap.put(6,"SKU Comb Type:Multi SKUs(Y/N)");
        importUSHeadMap.put(7,"Auto Shipment Review Switch(On/Off)");
    }


    @Override
    public void invoke(AutoShipAuditConfigImportExcel autoShipAuditConfigImportExcel, AnalysisContext analysisContext) {
        autoShipAuditConfigImportExcelList.add(autoShipAuditConfigImportExcel);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (headMap.equals(importCNHeadMap)) {
            // 国际化转换
            ExcelUtil.buildUpdateHeadAgain(context, importCNHeadMap, AutoShipAuditConfigImportExcel.class);
        } else if (headMap.equals(importUSHeadMap)) {
            ExcelUtil.buildUpdateHeadAgain(context, importUSHeadMap, AutoShipAuditConfigImportExcel.class);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("HEAD_NOT_MATCH"));
        }

    }



    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {

    }
}
