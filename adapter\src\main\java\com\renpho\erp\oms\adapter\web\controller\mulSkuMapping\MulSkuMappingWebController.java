package com.renpho.erp.oms.adapter.web.controller.mulSkuMapping;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.renpho.erp.oms.adapter.web.controller.listener.MulSkuMappingExcelListener;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.MulSkuMappingUpdateCmd;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.command.query.MulSkuMappingQuery;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingImportExcel;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.service.MulSkuMappingService;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.vo.MulSkuMappingPriorityVO;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.vo.MulSkuMappingVO;

import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.query.MulSkuMappingPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.vo.MulSkuMappingPageVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.enums.StatusEnum;
import com.renpho.erp.oms.infrastructure.common.component.DownLoadComponent;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URLEncoder;
import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/mulSkuMapping/**")
@RequestMapping("/mulSkuMapping")
@AllArgsConstructor
@Tag(name = "多渠道SKU映射接口", description = "多渠道SKU映射接口")
public class MulSkuMappingWebController {
    private final MulSkuMappingService mulSkuMappingService;
    private final DownLoadComponent downLoadComponent;

    /**
     * 新增多渠道SKU映射
     *
     * @param mulSkuMappingAddCmd
     * @return R
     */
    @Operation(summary = "新增多渠道SKU映射", description = "新增多渠道SKU映射")
    @PostMapping("/add")
    @PreAuthorize(PermissionConstant.MulSkuMapping.ADD)
    public R<Long> add(@RequestBody @Valid MulSkuMappingAddCmd mulSkuMappingAddCmd) {
        return mulSkuMappingService.addMulSkuMapping(mulSkuMappingAddCmd);
    }

    /**
     * 根据id查详情
     *
     * @param id
     * @return SkuMappingVO
     */
    @Operation(summary = "根据id查详情", description = "根据id查详情")
    @PreAuthorize(PermissionConstant.SkuMapping.PAGE)
    @GetMapping("/getById")
    public R<MulSkuMappingVO> getById(@RequestParam Long id) {
        return mulSkuMappingService.getById(id);
    }

    /**
     * 编辑
     *
     * @param mulSkuMappingUpdateCmd
     * @return R
     */
    @Operation(summary = "编辑", description = "编辑")
    @PostMapping("/update")
    @PreAuthorize(PermissionConstant.MulSkuMapping.EDIT)
    public R update(@RequestBody @Valid MulSkuMappingUpdateCmd mulSkuMappingUpdateCmd) {
        return mulSkuMappingService.updateMulSkuMapping(mulSkuMappingUpdateCmd);
    }

    /**
     * 禁用销售SKU映射
     *
     * @param id
     * @return R
     */
    @Operation(summary = "禁用多渠道SKU映射", description = "禁用多渠道SKU映射")
    @GetMapping("/disable")
    @PreAuthorize(PermissionConstant.MulSkuMapping.CHANGESTATUS)
    public R disable(@RequestParam Long id) {
        return mulSkuMappingService.changeStatus(id, StatusEnum.DISABLE, StatusEnum.ENABLE);
    }

    /**
     * 启用销售SKU映射
     *
     * @param id
     * @return R
     */
    @Operation(summary = "启用多渠道SKU映射", description = "启用多渠道SKU映射")
    @GetMapping("/enable")
    @PreAuthorize(PermissionConstant.MulSkuMapping.CHANGESTATUS)
    public R enable(@RequestParam Long id) {
        return mulSkuMappingService.changeStatus(id, StatusEnum.ENABLE, StatusEnum.DISABLE);
    }

    /**
     * 分页查询
     *
     * @param mulSkuMappingPageQuery
     * @return Paging<MulSkuMappingPageVO>
     */
    @Operation(summary = "分页查询", description = "分页查询")
    @PreAuthorize(PermissionConstant.MulSkuMapping.PAGE)
    @PostMapping("/page")
    public R<Paging<MulSkuMappingPageVO>> page(@RequestBody MulSkuMappingPageQuery mulSkuMappingPageQuery) {
        return mulSkuMappingService.page(mulSkuMappingPageQuery);
    }

    /**
     * 匹配列表查询
     *
     * @param mulSkuMappingQuery
     * @return List<MulSkuMappingPriorityVO>
     */
    @Operation(summary = "匹配列表查询", description = "匹配列表查询")
    @PostMapping("/macthList")
    R<List<MulSkuMappingPriorityVO>> macthList(@RequestBody @Valid MulSkuMappingQuery mulSkuMappingQuery) {
        return mulSkuMappingService.macthList(mulSkuMappingQuery);
    }


    /**
     * 导出
     *
     * @param mulSkuMappingPageQuery
     * @param response
     */
    @Operation(summary = "导出", description = "导出")
    @PreAuthorize(PermissionConstant.MulSkuMapping.EXPORT)
    @PostMapping(value = "/exportExcel")
    public void exportExcel(@RequestBody MulSkuMappingPageQuery mulSkuMappingPageQuery, HttpServletResponse response) {
        mulSkuMappingService.exportExcel(mulSkuMappingPageQuery, response);
    }

    /**
     * 导入
     *
     * @param file
     * @return R
     */
    @Operation(summary = "导入", description = "导入")
    @PreAuthorize(PermissionConstant.MulSkuMapping.IMPORT)
    @PostMapping(value = "/importExcel")
    public R importExcel(@RequestParam("file") MultipartFile file) {
        try (InputStream inputStream = file.getInputStream()) {
            MulSkuMappingExcelListener mulSkuMappingExcelListener = new MulSkuMappingExcelListener();
            EasyExcel.read(inputStream, MulSkuMappingImportExcel.class, mulSkuMappingExcelListener).sheet().doRead();
            List<MulSkuMappingImportExcel> mulSkuMappingImportExcelList = mulSkuMappingExcelListener.getMulSkuMappingImportExcelList();
            if (CollectionUtil.isEmpty(mulSkuMappingImportExcelList)) {
                throw new BusinessException(I18nMessageKit.getMessage("DATA_EMPTY"));
            }
            return mulSkuMappingService.importExcel(mulSkuMappingImportExcelList);
        } catch (Exception e) {
            log.error("导入失败", e);
            return R.fail(e.getMessage());
        }
    }

    /**
     * 下载多渠道SKU映射导入模板
     */
    @Operation(summary = "导入模板下载", description = "导入模板下载")
    @PostMapping("/downLoadTemplate")
    public void getTemplateFile(HttpServletResponse response) {
        downLoadComponent.templateDownLoad("MUL_SKU_TEMPLATE_FILE_NAME", response);
    }

}
