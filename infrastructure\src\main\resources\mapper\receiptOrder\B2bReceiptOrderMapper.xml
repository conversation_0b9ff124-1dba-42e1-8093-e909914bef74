<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.oms.infrastructure.persistence.receiptOrder.mapper.B2bReceiptOrderMapper">

    <select id="getB2bOrderBaseInfoDto"
            resultType="com.renpho.erp.oms.domain.receiptOrder.model.B2bOrderBaseInfoDto">
        select a.id,
        a.order_no,
        a.order_status,
        a.customer_id as customerId,
        b.customer_company_name,
        b.sales_company_id,
        b.incoterms_code,
        b.payment_terms_code,
        b.country,
        c.currency,
        c.total_amount,
        c.receipt_amount,
        c.receipt_rate from oms_b2b_order a
        left join oms_b2b_order_customer b on a.id = b.order_id and b.is_deleted ='0'
        left join oms_b2b_order_amount c on a.id=c.order_id and c.is_deleted ='0'
        where a.order_no = #{orderNo} and a.is_deleted='0'  limit 1
    </select>

    <select id="page"
            resultType="com.renpho.erp.oms.infrastructure.persistence.receiptOrder.dto.B2BReceiptOrderPageDto">
        select receipt_order.id,
               receipt_order.bank_refer_no,
               receipt_order.receipt_order_no,
               receipt_order.b2b_order_no,
               receipt_order.b2b_order_id,
               b2b_order.customer_id,
               receipt_order.customer_paid_amount,
               receipt_order.receipt_amount,
               receipt_order.status,
               receipt_order.create_time,
               receipt_order.customer_paid_time,
               receipt_order.receipt_time,
               receipt_order.fms_fund_account_currency_cnname fmsFundAccountCurrencyCnname,
               receipt_order.fms_fund_account_currency_enname fmsFundAccountCurrencyEnname
        from oms_b2b_receipt_order receipt_order
                 right join oms_b2b_order b2b_order
                            on receipt_order.b2b_order_id = b2b_order.id
        <where>
            b2b_order.is_deleted = 0
              and receipt_order.is_deleted = 0
              and receipt_order.id is not null
            <!--客户id-->
            <if test="queryDto.customerId != null">
                and b2b_order.customer_id = #{queryDto.customerId}
            </if>
            <!--创建时间-->
            <if test="queryDto.createTimeStart != null">
                and receipt_order.create_time &gt;= #{queryDto.createTimeStart}
            </if>
            <if test="queryDto.createTimeEnd != null">
                and receipt_order.create_time &lt;= #{queryDto.createTimeEnd}
            </if>
            <!--付款时间-->
            <if test="queryDto.payTimeStart != null">
                and receipt_order.customer_paid_time &gt;= #{queryDto.payTimeStart}
            </if>
            <if test="queryDto.payTimeEnd != null">
                and receipt_order.customer_paid_time &lt;= #{queryDto.payTimeEnd}
            </if>
            <!--收款时间-->
            <if test="queryDto.receiveTimeStart != null">
                and receipt_order.receipt_time &gt;= #{queryDto.receiveTimeStart}
            </if>
            <if test="queryDto.receiveTimeEnd != null">
                and receipt_order.receipt_time &lt;= #{queryDto.receiveTimeEnd}
            </if>
            <!--收款单号-->
            <if test="queryDto.receiptOrderNos != null and queryDto.receiptOrderNos.size() != 0">
                and receipt_order.receipt_order_no in
                <foreach collection="queryDto.receiptOrderNos" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <!--参考单号-->
            <if test="queryDto.bankReferNos != null and queryDto.bankReferNos.size() != 0">
                and receipt_order.bank_refer_no in
                <foreach collection="queryDto.bankReferNos" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <!--b2b订单编号-->
            <if test="queryDto.b2bOrderNos != null  and queryDto.b2bOrderNos.size() != 0">
                and receipt_order.b2b_order_no in
                <foreach collection="queryDto.b2bOrderNos" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            <!--银行账号id-->
            <if test="queryDto.bankAccountId != null">
                and receipt_order.fms_fund_account_currency_id = #{queryDto.bankAccountId}
            </if>
            <!--状态-->
            <if test="queryDto.status != null">
                and receipt_order.status = #{queryDto.status}
            </if>
        </where>
        order by receipt_order.update_time desc
    </select>
</mapper>