package com.renpho.erp.oms.application.channelmanagement.ebay.convert;

import cn.hutool.core.util.StrUtil;
import com.renpho.erp.apiproxy.ebay.model.orders.GetOrdersDetailResponse;
import com.renpho.erp.apiproxy.ebay.model.orders.GetOrdersListResponse;
import com.renpho.erp.oms.application.channelmanagement.ebay.dto.EbyOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.ebay.vo.EbyOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrder;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderLineItem;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.common.util.JsonUtil;
import com.renpho.karma.json.JSONKit;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shenyu.common.utils.DateUtils;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * eBay订单转换器
 */
@Component
public class EbyOrderAppConvertor {

	/**
	 * 将DTO转换为领域模型
	 * @param ebyOrderPushDTO eBay订单推送DTO
	 * @return eBay订单聚合根
	 */
	public static EbyOrderAggRoot toDomain(EbyOrderPushDTO ebyOrderPushDTO) {
		if (ebyOrderPushDTO == null) {
			return null;
		}

		EbyOrderAggRoot ebyOrderAggRoot = new EbyOrderAggRoot();
		// 设置基本信息
		ebyOrderAggRoot.setStoreId(ebyOrderPushDTO.getStoreId());
		ebyOrderAggRoot.setEbaySellerId(ebyOrderPushDTO.getEbaySellerId());
		ebyOrderAggRoot.setEbyOrderId(ebyOrderPushDTO.getOrderId());
		ebyOrderAggRoot.setBuyerCheckoutNotes(ebyOrderPushDTO.getBuyerCheckoutNotes());
		ebyOrderAggRoot.setCreationDate(DateUtil.parse(ebyOrderPushDTO.getCreationDate()));
		ebyOrderAggRoot.setEbayCollectAndRemitTax(ebyOrderPushDTO.getEbayCollectAndRemitTax());

		// 处理JSON格式数据
		if(ebyOrderPushDTO.getCancelStatus() != null){
			ebyOrderAggRoot.setCancelStatus(JSONKit.toJSONString(ebyOrderPushDTO.getCancelStatus()));
		}

		if (ebyOrderPushDTO.getFulfillmentHrefs() != null) {
			ebyOrderAggRoot.setFulfillmentHrefs(JSONKit.toJSONString(ebyOrderPushDTO.getFulfillmentHrefs()));
		}

		if (ebyOrderPushDTO.getFulfillmentStartInstructions() != null) {
			ebyOrderAggRoot.setFulfillmentStartInstructions(JSONKit.toJSONString(ebyOrderPushDTO.getFulfillmentStartInstructions()));
		}

		ebyOrderAggRoot.setLastModifiedDate(DateUtil.parse(ebyOrderPushDTO.getLastModifiedDate()));
		ebyOrderAggRoot.setLegacyOrderId(ebyOrderPushDTO.getLegacyOrderId());
		ebyOrderAggRoot.setOrderFulfillmentStatus(ebyOrderPushDTO.getOrderFulfillmentStatus());
		ebyOrderAggRoot.setOrderPaymentStatus(ebyOrderPushDTO.getOrderPaymentStatus());

		if (ebyOrderPushDTO.getPaymentSummary() != null) {
			ebyOrderAggRoot.setPaymentSummary(JSONKit.toJSONString(ebyOrderPushDTO.getPaymentSummary()));
		}

		if (ebyOrderPushDTO.getPricingSummary() != null) {
			ebyOrderAggRoot.setPricingSummary(JSONKit.toJSONString(ebyOrderPushDTO.getPricingSummary()));
		}

		if (ebyOrderPushDTO.getProgram() != null) {
			ebyOrderAggRoot.setProgram(JSONKit.toJSONString(ebyOrderPushDTO.getProgram()));
		}

		ebyOrderAggRoot.setSalesRecordReference(ebyOrderPushDTO.getSalesRecordReference());
		ebyOrderAggRoot.setSellerId(ebyOrderPushDTO.getSellerId());

		if (ebyOrderPushDTO.getTotalFeeBasisAmount() != null) {
			ebyOrderAggRoot.setTotalFeeBasisAmount(JSONKit.toJSONString(ebyOrderPushDTO.getTotalFeeBasisAmount()));
		}

		if (ebyOrderPushDTO.getTotalMarketplaceFee() != null) {
			ebyOrderAggRoot.setTotalMarketplaceFee(JSONKit.toJSONString(ebyOrderPushDTO.getTotalMarketplaceFee()));
		}

		// 设置状态为待转换
		ebyOrderAggRoot.setTransOmsStatus(TransOmsStatus.NOT_TRANS);
		ebyOrderAggRoot.setIsDeleted(false);
		ebyOrderAggRoot.setLastMpdsSyncOrderTime(
				StrUtil.isNotEmpty(ebyOrderPushDTO.getSyncTime()) ? DateUtils.parseLocalDateTime(ebyOrderPushDTO.getSyncTime()) : null);

		return ebyOrderAggRoot;
	}

	/**
	 * ebay订单转换
	 * @param ebyOrder ebyOrder
	 * @return EbyOrderVO
	 */
	public static EbyOrderVO toVO(EbyOrder ebyOrder) {
		EbyOrderVO ebyOrderVO = new EbyOrderVO();
		ebyOrderVO.setId(ebyOrder.getId())
				.setStoreId(ebyOrder.getStoreId())
				.setEbyOrderId(ebyOrder.getEbyOrderId())
				.setCancelStatus(getCancelStatus(ebyOrder.getCancelStatus()))
				.setOrderPaymentStatus(ebyOrder.getOrderPaymentStatus())
				.setOrderFulfillmentStatus(ebyOrder.getOrderFulfillmentStatus())
				.setCreationDate(ebyOrder.getCreationDate())
				.setLastModifiedDate(ebyOrder.getLastModifiedDate())
				.setEbayCollectAndRemitTax(ebyOrder.getEbayCollectAndRemitTax())
				.setTotalMarketplaceFee(getTotalMarketplaceFee(ebyOrder.getTotalMarketplaceFee()))
				.setCurrency(getCurrency(ebyOrder.getTotalMarketplaceFee()))
				.setBuyerCheckoutNotes(ebyOrder.getBuyerCheckoutNotes());
		// 设置配送信息
		setFulfillmentInfo(ebyOrder, ebyOrderVO);
		// 从明细中设置价格信息
		setPriceInfo(ebyOrder, ebyOrderVO);
		return ebyOrderVO;
	}

	/**
	 * 从明细中设置价格信息
	 * @param ebyOrder 订单聚合根
	 * @param ebyOrderVO 返回订单信息
	 */
	private static void setPriceInfo(EbyOrder ebyOrder, EbyOrderVO ebyOrderVO) {
		BigDecimal itemPrice = BigDecimal.ZERO;
		BigDecimal discount = BigDecimal.ZERO;
		BigDecimal shippingPrice = BigDecimal.ZERO;
		BigDecimal collectAndRemitTax = BigDecimal.ZERO;
		BigDecimal refund = BigDecimal.ZERO;
		if (CollectionUtils.isNotEmpty(ebyOrder.getEbyOrderLineItemList())) {
			for (EbyOrderLineItem ebyOrderLineItem : ebyOrder.getEbyOrderLineItemList()) {
				itemPrice = itemPrice.add(ebyOrderLineItem.getLineItemCostValue());
				GetOrdersListResponse.DeliveryCost deliveryCost = JsonUtil.parseJson(ebyOrderLineItem.getDeliveryCost(),
						GetOrdersListResponse.DeliveryCost.class);
				if (deliveryCost != null && deliveryCost.getShippingCost() != null && deliveryCost.getShippingCost().getValue() != null) {
					shippingPrice = shippingPrice.add(new BigDecimal(deliveryCost.getShippingCost().getValue()));
				}
				// 从appliedPromotions 获取并设置discount
				List<GetOrdersListResponse.AppliedPromotion> appliedPromotions = JsonUtil.parseArray(
						ebyOrderLineItem.getAppliedPromotions(), new TypeReference<List<GetOrdersListResponse.AppliedPromotion>>() {
						});
				if (CollectionUtils.isNotEmpty(appliedPromotions)) {
					for (GetOrdersListResponse.AppliedPromotion appliedPromotion : appliedPromotions) {
						discount = discount.add(new BigDecimal(appliedPromotion.getDiscountAmount().getValue()));
					}
				}
				// ebayCollectAndRemitTaxes 获取并设置设置collectAndRemitTax
				List<GetOrdersListResponse.EbayCollectAndRemitTaxes> ebayCollectAndRemitTaxes = JsonUtil.parseArray(
						ebyOrderLineItem.getEbayCollectAndRemitTaxes(),
						new TypeReference<List<GetOrdersListResponse.EbayCollectAndRemitTaxes>>() {
						});
				if (CollectionUtils.isNotEmpty(ebayCollectAndRemitTaxes)) {
					for (GetOrdersListResponse.EbayCollectAndRemitTaxes ebayCollectAndRemitTaxe : ebayCollectAndRemitTaxes) {
						collectAndRemitTax = collectAndRemitTax.add(new BigDecimal(ebayCollectAndRemitTaxe.getAmount().getValue()));
					}
				}
				// 退款金额（从 JSON 中解析）
				List<GetOrdersListResponse.Refund> refunds = JsonUtil.parseArray(ebyOrderLineItem.getRefunds(),
						new TypeReference<List<GetOrdersListResponse.Refund>>() {
						});
				if (CollectionUtils.isNotEmpty(refunds)) {
					for (GetOrdersListResponse.Refund refund1 : refunds) {
						refund = refund.add(new BigDecimal(refund1.getAmount().getValue()));
					}
				}
			}
			ebyOrderVO.setItemPrice(itemPrice);
			ebyOrderVO.setDiscount(discount);
			ebyOrderVO.setShippingPrice(shippingPrice);
			ebyOrderVO.setCollectAndRemitTax(collectAndRemitTax);
			ebyOrderVO.setRefundAmount(refund);
		}
	}

	/**
	 * 设置配送信息
	 * @param ebyOrder 值对象
	 * @param ebyOrderVO 返回出参
	 */
	private static void setFulfillmentInfo(EbyOrder ebyOrder, EbyOrderVO ebyOrderVO) {
		List<GetOrdersListResponse.FulfillmentStartInstructions> fulfillmentStartInstructions = JsonUtil.parseArray(
				ebyOrder.getFulfillmentStartInstructions(), new TypeReference<List<GetOrdersListResponse.FulfillmentStartInstructions>>() {
				});
		GetOrdersListResponse.FulfillmentStartInstructions fulfillmentStartInstruction = fulfillmentStartInstructions != null
				&& !fulfillmentStartInstructions.isEmpty() ? fulfillmentStartInstructions.get(0) : null;
		if (fulfillmentStartInstruction != null && fulfillmentStartInstruction.getShippingStep() != null
				&& fulfillmentStartInstruction.getShippingStep().getShipTo() != null
				&& fulfillmentStartInstruction.getShippingStep().getShipTo().getContactAddress() != null) {
			GetOrdersListResponse.Address address = fulfillmentStartInstruction.getShippingStep().getShipTo().getContactAddress();
			ebyOrderVO.setCity(address.getCity())
					.setState(address.getStateOrProvince())
					.setCountry(address.getCountryCode())
					.setFulfillmentInstructionsType(fulfillmentStartInstruction.getFulfillmentInstructionsType())
					.setMinEstimatedDeliveryDate(DateUtil.parse(fulfillmentStartInstruction.getMinEstimatedDeliveryDate()))
					.setMaxEstimatedDeliveryDate(DateUtil.parse(fulfillmentStartInstruction.getMaxEstimatedDeliveryDate()))
					.setShippingCarrierCode(fulfillmentStartInstruction.getShippingStep().getShippingCarrierCode())
					.setShippingServiceCode(fulfillmentStartInstruction.getShippingStep().getShippingServiceCode());
		}
	}

	/**
	 * 获取总平台费用
	 * @param totalMarketplaceFee json 结构
	 * @return 解析后的 总平台费用
	 */
	private static String getTotalMarketplaceFee(String totalMarketplaceFee) {
		GetOrdersListResponse.Amount amount = JsonUtil.parseJson(totalMarketplaceFee, GetOrdersListResponse.Amount.class);
		if (amount != null) {
			return amount.getValue();
		}
		return null;
	}

	/**
	 * 获取币种
	 * @param totalMarketplaceFee json 结构
	 * @return 解析后的币种
	 */
	private static String getCurrency(String totalMarketplaceFee) {
		GetOrdersListResponse.Amount amount = JsonUtil.parseJson(totalMarketplaceFee, GetOrdersListResponse.Amount.class);
		if (amount != null) {
			return amount.getCurrency();
		}
		return null;
	}

	/**
	 * 获取取消状态
	 * @param cancelStatus 取消状态
	 * @return 取消状态
	 */
	private static String getCancelStatus(String cancelStatus) {
		GetOrdersListResponse.CancelStatus cancelStatus1 = JsonUtil.parseJson(cancelStatus, GetOrdersListResponse.CancelStatus.class);
		if (cancelStatus1 != null && !cancelStatus1.getCancelState().equals("NONE_REQUESTED")) {
			return cancelStatus1.getCancelState();
		}
		return null;
	}
}