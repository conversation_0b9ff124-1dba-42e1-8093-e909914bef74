package com.renpho.erp.oms.infrastructure.common.mq;

import com.renpho.erp.oms.infrastructure.common.util.MqTraceUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.messaging.Message;

import java.util.Map;
import java.util.function.Consumer;

/**
 * MQ消息处理器装饰器
 * 用于包装Spring Cloud Stream的Consumer，确保TraceId在整个消息处理过程中保持一致
 * 
 * 使用方式：
 * @Bean
 * public Consumer<Message<String>> auditB2bOrder() {
 *     return MqMessageHandlerDecorator.wrap(msg -> {
 *         // 业务逻辑处理
 *     });
 * }
 */
@Slf4j
public class MqMessageHandlerDecorator {

    /**
     * 包装消息处理器，确保TraceId传递
     * 
     * @param handler 原始消息处理器
     * @param <T> 消息类型
     * @return 包装后的消息处理器
     */
    public static <T> Consumer<Message<T>> wrap(Consumer<Message<T>> handler) {
        return message -> {
            // 获取或生成TraceId
            String traceId = MqTraceUtil.getTraceId();
            boolean shouldSetTraceId = traceId == null || traceId.trim().isEmpty();
            
            if (shouldSetTraceId) {
                traceId = MqTraceUtil.setTraceId();
                log.debug("为MQ消息处理设置TraceId: {}", traceId);
            } else {
                log.debug("使用已存在的TraceId: {}", traceId);
            }
            
            // 保存当前MDC上下文
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            
            try {
                // 执行业务逻辑
                handler.accept(message);
            } catch (Exception e) {
                // 确保异常时也有TraceId
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                log.error("MQ消息处理异常，TraceId: {}", traceId, e);
                throw e;
            } finally {
                // 只有当前装饰器设置的TraceId才清除
                if (shouldSetTraceId) {
                    MqTraceUtil.clearTraceId();
                    log.debug("清除MQ消息处理TraceId: {}", traceId);
                }
            }
        };
    }

    /**
     * 包装普通的Consumer（非Message类型）
     * 
     * @param handler 原始处理器
     * @param <T> 消息类型
     * @return 包装后的处理器
     */
    public static <T> Consumer<T> wrapSimple(Consumer<T> handler) {
        return payload -> {
            // 获取或生成TraceId
            String traceId = MqTraceUtil.getTraceId();
            boolean shouldSetTraceId = traceId == null || traceId.trim().isEmpty();
            
            if (shouldSetTraceId) {
                traceId = MqTraceUtil.setTraceId();
                log.debug("为MQ消息处理设置TraceId: {}", traceId);
            } else {
                log.debug("使用已存在的TraceId: {}", traceId);
            }
            
            // 保存当前MDC上下文
            Map<String, String> contextMap = MDC.getCopyOfContextMap();
            
            try {
                // 执行业务逻辑
                handler.accept(payload);
            } catch (Exception e) {
                // 确保异常时也有TraceId
                if (contextMap != null) {
                    MDC.setContextMap(contextMap);
                }
                log.error("MQ消息处理异常，TraceId: {}", traceId, e);
                throw e;
            } finally {
                // 只有当前装饰器设置的TraceId才清除
                if (shouldSetTraceId) {
                    MqTraceUtil.clearTraceId();
                    log.debug("清除MQ消息处理TraceId: {}", traceId);
                }
            }
        };
    }

    /**
     * 创建带有MDC传递功能的Runnable
     * 用于在MQ消息处理中执行异步任务
     * 
     * @param task 原始任务
     * @return 包装后的任务
     */
    public static Runnable wrapWithMDC(Runnable task) {
        Map<String, String> contextMap = MDC.getCopyOfContextMap();
        return () -> {
            if (contextMap != null) {
                MDC.setContextMap(contextMap);
            }
            try {
                task.run();
            } finally {
                MDC.clear();
            }
        };
    }
}
