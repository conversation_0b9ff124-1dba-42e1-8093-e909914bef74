package com.renpho.erp.oms.application.ruleconfig.autoShipAudit.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description 新增或者保存自动发货审核配置
 * @date 2025/5/16 15:22
 */
@Data
public class SaveAutoShipAuditConfigCmd {

	/**
	 * 渠道id
	 */
	@NotNull(message = "CHANNLE_EMPTY")
	private Integer channelId;

	/**
	 * 渠道code
	 */
	@NotNull(message = "CHANNLE_EMPTY")
	private String channelCode;

	/**
	 * 店铺id
	 */
	@NotNull(message = "STORE_EMPTY")
	private Integer storeId;

	/**
	 * 审核开关 true:开启,false:关闭
	 */
	@NotNull(message = "AUDIT_SWITCH_EMPTY")
	private Boolean auditSwitch;

	/**
	 * pksu list
	 */
	@NotNull(message = "PSKU_EMPTY")
	@Size(min = 1, message = "PSKU_EMPTY")
	private List<String> pskuList;

	/**
	 * 仓库选择顺序
	 */
	@NotNull(message = "WAREHOUSE_SELECTOR_PRIORITY_EMPTY")
	@Size(min = 1, message = "WAREHOUSE_SELECTOR_PRIORITY_EMPTY")
	@Valid
	private List<WarehouseSelectorPriorityObj> warehouseSelectorPriorityList;

	/**
	 * 是否单品单件,true:是，false：否
	 */
	private Boolean oneSkuOnePc;

	/**
	 * 是否单品多件,true:是，false：否
	 */
	private Boolean oneSkuMultiPcs;

	/**
	 * 是否多品多件,true:是，false：否
	 */
	private Boolean multiSkus;

	@Data
	public static class WarehouseSelectorPriorityObj {
		/**
		 * 仓库id
		 */
		@NotNull(message = "WAREHOUSE_ID_EMPTY")
		private Integer warehouseId;


		/**
		 * 仓库编码
		 */
		@NotNull(message = "WAREHOUSE_CODE_EMPTY")
		private String warehouseCode;

		/**
		 * 优先级
		 */
		@NotNull(message = "PRIORITY_EMPTY")
		private Integer priority;
	}
}
