package com.renpho.erp.oms.application.listingmanagement.executor;

import com.renpho.erp.apiproxy.mercado.model.ShopAccount;
import com.renpho.erp.apiproxy.mercado.model.items.GetItemsMappingResponse;
import com.renpho.erp.apiproxy.mercado.model.items.GetUserItemsRequest;
import com.renpho.erp.apiproxy.mercado.model.items.GetUserItemsResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.convert.MercadoItemsMappingConvertor;
import com.renpho.erp.oms.domain.listingmanagement.model.MercadoGlobalItem;
import com.renpho.erp.oms.domain.listingmanagement.repository.MercadoGlobalItemRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.MercadoGlobalItemStatus;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.MercadoClient;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 美客多全球/市场Item映射拉取-执行器
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class MercadoGlobalItemMappingPullExecutor {

	private final StoreClient storeClient;
	private final MercadoClient mercadoClient;
	private final MercadoGlobalItemRepository mercadoGlobalItemRepository;

	/**
	 * 拉取美客多全球/市场Item映射
	 */
	public void pullMercadoGlobalItemMapping() {
		// 1.从MDM获取渠道店铺授权信列表
		List<StoreAuthorizationVo.Authorization> authorizationList = this.getAuthorizationList();
		// 2.以店铺授权为维度，拉取
		authorizationList.forEach(authorization -> {
			// 2.1 全球用户id
			String globalUserId = authorization.getMclUserId();
			// 2.2 构建店铺账号
			ShopAccount shopAccount = this.buildShopAccount(globalUserId);
			int offset = 0;
			int limit = 100;
			int total = 0;
			// 2.3 构建分页请求
			GetUserItemsRequest userItemsRequest = this.buildUserItemsRequest(limit);
			// 2.4.分页拉取
			while (offset < total || total == 0) {
				// 2.4.1 全球itemId集合
				Set<String> itemIdSet = new HashSet<>();
				// 2.4.2 拉取全球用户对应的item
				total = this.pullGlobalUserItemId(userItemsRequest, offset, shopAccount, itemIdSet);
				// 2.4.3 返回的总数为0，结束循环
				if (total == 0) {
					break;
				}
				// 2.4.4 Map，key为全球itemId,value为状态
				Map<String, Integer> itemIdStatusMap = new HashMap<>();
				// 2.4.5 拉取全球item和市场item的映射
				List<MercadoGlobalItem> mercadoGlobalItemList = this.pullItemsMapping(itemIdSet, shopAccount, itemIdStatusMap);
				// 2.4.6 修改全球item状态
				mercadoGlobalItemRepository.updateItemStatus(itemIdStatusMap, Integer.valueOf(globalUserId),
						Integer.valueOf(authorization.getMclMarketUserId()));
				// 2.4.7 保存全球/市场Item映射
				mercadoGlobalItemRepository.saveMappingList(mercadoGlobalItemList, Integer.valueOf(authorization.getMclMarketUserId()));
				// 2.4.8 更新 offset 以便下一页请求
				offset += limit;
			}
		});

	}

	/**
	 * 从MDM获取渠道店铺授权信列表
	 * @return List
	 */
	private @NotNull List<StoreAuthorizationVo.Authorization> getAuthorizationList() {
		// 1.从MDM获取渠道店铺授权信息
		List<StoreAuthorizationDTO> storeAuthorizationList = storeClient.getStoreAuthorizationsByChannelCode(ChannelCode.MCL_CHANNEL_CODE,
				StoreTypeEnum.SC.getValue());
		// 2.全球用户ID集合（从店铺获取）
		return storeAuthorizationList.stream()
			.map(StoreAuthorizationDTO::getStoreAuthorization)
			.filter(Objects::nonNull)
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			.toList();
	}

	/**
	 * 构建已关闭 及 全球Items 与 本地Items 的映射列表
	 * @param itemIdSet 全球itemId集合
	 * @param shopAccount 店铺账号
	 * @param itemIdStatusMap Map，key为全球itemId,value为状态
	 */
	private List<MercadoGlobalItem> pullItemsMapping(Set<String> itemIdSet, ShopAccount shopAccount, Map<String, Integer> itemIdStatusMap) {
		// 全球Items 与 本地Items 的映射列表
		List<MercadoGlobalItem> mercadoGlobalItemList = new ArrayList<>();
		itemIdSet.forEach(itemId -> {
			R<GetItemsMappingResponse> itemsMappingR = mercadoClient.getItemsMapping(shopAccount, itemId);
			if (!itemsMappingR.isSuccess()) {
				if ("Listing is not active".equals(itemsMappingR.getMessage())) {
					// 全球Item已下架（not active）
					itemIdStatusMap.put(itemId, MercadoGlobalItemStatus.NOT_ACTIVE.getStatus());
				}
				else if (String.format("item %s is not a cbt item", itemId).equals(itemsMappingR.getMessage())) {
					// 全球Item不存在（not found）
					itemIdStatusMap.put(itemId, MercadoGlobalItemStatus.NOT_FOUND.getStatus());
				}
				else {
					log.error("查询美客多全球Items 与 本地Items 的映射，全球itemId：{}，失败原因：{}", itemId, itemsMappingR.getMessage());
					itemIdStatusMap.put(itemId, MercadoGlobalItemStatus.UNKNOWN_EXCEPTION.getStatus());
				}
				return;
			}
			Optional.ofNullable(itemsMappingR.getData()).ifPresent(i -> {
				MercadoGlobalItem mercadoGlobalItem = MercadoItemsMappingConvertor.toMercadoGlobalItem(i);
				mercadoGlobalItemList.add(mercadoGlobalItem);
			});
		});
		return mercadoGlobalItemList;
	}

	/**
	 * 拉取全球用户item
	 * @param userItemsRequest 分页请求
	 * @param offset 偏移量
	 * @param shopAccount 店铺账号
	 * @param itemIdSet 全球item集合
	 * @return 总数
	 */
	private int pullGlobalUserItemId(GetUserItemsRequest userItemsRequest, int offset, ShopAccount shopAccount, Set<String> itemIdSet) {
		int total;
		// 重新设置偏移量
		userItemsRequest.setOffset(offset);
		// 拉取用户全球Items
		R<GetUserItemsResponse> userItemsR = mercadoClient.getUserItems(shopAccount, userItemsRequest);
		if (!userItemsR.isSuccess()) {
			log.error("查询美客多用户全球Items，失败原因：{}", userItemsR.getMessage());
		}
		// 如果全球itemId存在，加入集合中
		Optional.ofNullable(userItemsR.getData()).map(GetUserItemsResponse::getResults).ifPresent(itemIdSet::addAll);
		// 返回的总数
		total = Optional.ofNullable(userItemsR.getData())
			.map(GetUserItemsResponse::getPaging)
			.map(GetUserItemsResponse.PagingInfo::getTotal)
			.orElse(0);
		return total;
	}

	/**
	 * 构建全球用户item分页请求
	 * @param limit 每页条数
	 * @return GetUserItemsRequest
	 */
	private @NotNull GetUserItemsRequest buildUserItemsRequest(int limit) {
		GetUserItemsRequest userItemsRequest = new GetUserItemsRequest();
		userItemsRequest.setLimit(limit);
		return userItemsRequest;
	}

	/**
	 * 构建店铺账号
	 * @param userId 全球用户id
	 * @return ShopAccount
	 */
	private @NotNull ShopAccount buildShopAccount(String userId) {
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setUserId(userId);
		return shopAccount;
	}

}
