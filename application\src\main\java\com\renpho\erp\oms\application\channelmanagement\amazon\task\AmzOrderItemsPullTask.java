package com.renpho.erp.oms.application.channelmanagement.amazon.task;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzOrderItemsPullService;
import com.renpho.erp.oms.application.channelmanagement.common.dto.PullOrerParam;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.stereotype.Component;

/**
 * 亚马逊拉取订单商品定时任务
 */
@Component
@AllArgsConstructor
public class AmzOrderItemsPullTask {
	private final AmzOrderItemsPullService amzOrderItemsPullService;

	@XxlJob("AmzOrderItemsPullTask")
	public void amzOrderItemsPullTask() throws Exception {
		XxlJobHelper.log("亚马逊拉取订单商品开始");
		// 获取参数
		String param = XxlJobHelper.getJobParam();
		if (StringUtils.isNotEmpty(param)) {
			XxlJobHelper.log("亚马逊主动拉取订单参数: {}", param);
			// 构建参数
			PullOrerParam pullOrerParam = JsonUtils.jsonToObject(param, PullOrerParam.class);

			if (CollectionUtil.isEmpty(pullOrerParam.getSellerIds()) && CollectionUtil.isEmpty(pullOrerParam.getOrderIds())) {
				XxlJobHelper.log("手动拉取输入订单id或卖家信息为空");
				return;
			}
			amzOrderItemsPullService.pullItems(pullOrerParam);

		}
		else {
			// 获取分片参数
			Integer shardIndex = XxlJobHelper.getShardIndex(); // 当前分片
			Integer shardTotal = XxlJobHelper.getShardTotal(); // 总分片数
			// 将分片参数存入ThreadLocal
			JobShardContext.JobShardInfo shardInfo = new JobShardContext.JobShardInfo();
			shardInfo.setShardIndex(shardIndex);
			shardInfo.setShardTotal(shardTotal);
			JobShardContext.setShardInfo(shardInfo);
			try {
				amzOrderItemsPullService.pullItems(null);

			}
			finally {
				// 清除ThreadLocal中的分片信息
				JobShardContext.clear();
			}
		}
	}

}
