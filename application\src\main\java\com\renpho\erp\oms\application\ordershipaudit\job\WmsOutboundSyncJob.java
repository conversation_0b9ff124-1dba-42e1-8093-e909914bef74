package com.renpho.erp.oms.application.ordershipaudit.job;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.ordershipaudit.service.WmsOutboundService;
import com.renpho.erp.oms.domain.ordershipaudit.CreateWmsOutboundStatus;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.OrderShipAuditMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.OrderShipAuditPO;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class WmsOutboundSyncJob {
	@Autowired
	private OrderShipAuditMapper orderShipAuditMapper;
	@Autowired
	private OrderShipAuditRepository orderShipAuditRepository;
	@Autowired
	private SaleOrderRepository saleOrderRepository;
	@Autowired
	private WmsOutboundService wmsOutboundCreateService;

	@XxlJob("wmsOutboundSyncJob")
	public void handle() {
		log.info("wmsOutboundSyncJob");
		String jobParam = StrUtil.blankToDefault(XxlJobHelper.getJobParam(), "{}");
		Param param = JSONKit.parseObject(jobParam, Param.class);
		Integer pageSize = Optional.ofNullable(param.getPageSize()).orElse(1000);

		List<OrderShipAuditPO> orderShipAuditList;
		Long lastId = 0L;
		do {
			Wrapper<OrderShipAuditPO> wrapper = Wrappers.<OrderShipAuditPO> lambdaQuery()
				.select(OrderShipAuditPO::getId)
				// 创建wms出库单成功
				.eq(OrderShipAuditPO::getCreateWmsOutboundStatus, CreateWmsOutboundStatus.CREATE_SUCCESS.getValue())
				// 发货服务类型为自建仓
				.in(OrderShipAuditPO::getFulfillmentServiceType, FulfillmentServiceType.getSelfWarehouseFulfillmentType())
				// 特定情况只处理这几个发货审核
				.in(CollUtil.isNotEmpty(param.getAuditNoList()), OrderShipAuditPO::getAuditNo, param.getAuditNoList())
				.gt(OrderShipAuditPO::getId, lastId)
				.orderByAsc(OrderShipAuditPO::getId)
				.last("limit " + pageSize);

			orderShipAuditList = orderShipAuditMapper.selectList(wrapper);

			for (OrderShipAuditPO orderShipAuditPO : orderShipAuditList) {
				XxlJobHelper.log("开始处理id是{}的发货审核", orderShipAuditPO.getId());
				log.info("开始处理id是{}的发货审核", orderShipAuditPO.getId());
				try {
					syncWmsOutbound(orderShipAuditPO.getId());
				}
				catch (Exception e) {
					XxlJobHelper.log("同步wms出库单失败, 发货审核id是{}", orderShipAuditPO.getId());
					XxlJobHelper.log(e);
					log.info("同步wms出库单失败, 发货审核id是{}", orderShipAuditPO.getId(), e);
				}
			}

			lastId = Optional.ofNullable(CollUtil.getLast(orderShipAuditList)).map(OrderShipAuditPO::getId).orElse(0L);
		}
		while (Objects.equals(CollUtil.size(orderShipAuditList), pageSize));
	}

	private void syncWmsOutbound(Long orderShipAuditId) {
		OrderShipAuditAggRoot orderShipAuditAggRoot = orderShipAuditRepository.getById(orderShipAuditId);
		SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findAggRootById(orderShipAuditAggRoot.getOrderId());
		try {
			wmsOutboundCreateService.syncWmsOutbound(orderShipAuditAggRoot, saleOrderAggRoot);
		}
		catch (Exception e) {
			XxlJobHelper.log("同步wms出库单异常,发货审核id是{},订单id是{}", orderShipAuditAggRoot.getId(), saleOrderAggRoot.getId().getId());
			XxlJobHelper.log(e);
			log.error("同步wms出库单异常,发货审核id是{},订单id是{}", orderShipAuditAggRoot.getId(), saleOrderAggRoot.getId().getId(), e);
		}

	}

	@Data
	public static class Param {
		private Integer pageSize;
		private List<String> auditNoList;
	}
}
