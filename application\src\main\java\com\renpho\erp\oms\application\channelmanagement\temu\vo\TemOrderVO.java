package com.renpho.erp.oms.application.channelmanagement.temu.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class TemOrderVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    /**
     * 订单号
     */
    private String parentOrderSn;

    /**
     * 订单状态
     */
    private Integer parentOrderStatus;

    /**
     * 城市
     */
    private String regionName1;

    /**
     * 州
     */
    private String regionName2;

    /**
     * 国家
     */
    private String regionName3;

    /**
     * 预计最迟发货时间
     */
    private LocalDateTime expectShipLatestTime;


    /**
     * 最迟送达时间
     */
    private LocalDateTime latestDeliveryTime;


    /**
     * 下单时间
     */
    private LocalDateTime parentOrderTime;


    /**
     * 卖家发货时间
     */
    private LocalDateTime parentShippingTime;

    /**
     * 平台审核时间
     */
    private LocalDateTime parentOrderPendingFinishTime;

    /**
     * 店铺名称
     */
    private String storeName;


    private Integer soonToBeOverdue;

    private Integer pastDue;

    private Integer pendingBuyerCancellation;

    private Integer pendingBuyerAddressChange;

    private Integer pendingRiskControlAlert;

    private List<String> fulfillmentWarnings;

} 