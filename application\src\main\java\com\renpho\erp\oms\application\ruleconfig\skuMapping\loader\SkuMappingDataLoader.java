package com.renpho.erp.oms.application.ruleconfig.skuMapping.loader;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@AllArgsConstructor
public class SkuMappingDataLoader {

    private final StoreClient storeClient;
    private final UserClient userClient;
    private final ProductClient productClient;

    @Data
    public static class LoadedData {
        private Map<String, StoreVo> storeMap;
        private Map<String, OumUserInfoRes> operatorMap;
        private Set<String> validPurchaseSkus;
    }

    public LoadedData loadRequiredData(List<SkuMappingImportExcel> importExcelList) {
        log.debug("开始加载导入所需的基础数据，总记录数: {}", importExcelList.size());
        
        LoadedData loadedData = new LoadedData();
        
        // 批量加载店铺数据
        loadedData.setStoreMap(loadStoreData(importExcelList));
        
        // 批量加载操作员数据
        loadedData.setOperatorMap(loadOperatorData(importExcelList));
        
        // 批量加载采购SKU数据
        loadedData.setValidPurchaseSkus(loadPurchaseSkuData(importExcelList));
        
        log.debug("基础数据加载完成 - 店铺: {}, 操作员: {}, 采购SKU: {}", 
                 loadedData.getStoreMap().size(), 
                 loadedData.getOperatorMap().size(),
                 loadedData.getValidPurchaseSkus().size());
        
        return loadedData;
    }

    private Map<String, StoreVo> loadStoreData(List<SkuMappingImportExcel> importExcelList) {
        List<String> distinctStores = importExcelList.stream()
                .map(SkuMappingImportExcel::getStore)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(distinctStores)) {
            log.warn("没有找到有效的店铺数据");
            return Collections.emptyMap();
        }

        log.debug("加载店铺数据，店铺数量: {}", distinctStores.size());
        
        try {
            List<StoreVo> storeList = storeClient.getByStoreNames(distinctStores);
            return storeList.stream()
                    .collect(Collectors.toMap(StoreVo::getStoreName, Function.identity()));
        } catch (Exception e) {
            log.error("加载店铺数据失败", e);
            return Collections.emptyMap();
        }
    }

    private Map<String, OumUserInfoRes> loadOperatorData(List<SkuMappingImportExcel> importExcelList) {
        List<String> distinctOperatorNos = importExcelList.stream()
                .map(SkuMappingImportExcel::getOperatorNo)
                .filter(Objects::nonNull)
                .filter(operatorNo -> !operatorNo.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(distinctOperatorNos)) {
            log.debug("没有找到操作员数据");
            return Collections.emptyMap();
        }

        log.debug("加载操作员数据，操作员数量: {}", distinctOperatorNos.size());
        
        try {
            List<OumUserInfoRes> operatorList = userClient.getUserByNo(distinctOperatorNos);
            return operatorList.stream()
                    .collect(Collectors.toMap(OumUserInfoRes::getCode, Function.identity()));
        } catch (Exception e) {
            log.error("加载操作员数据失败", e);
            return Collections.emptyMap();
        }
    }

    private Set<String> loadPurchaseSkuData(List<SkuMappingImportExcel> importExcelList) {
        List<String> distinctPurchaseSkus = importExcelList.stream()
                .map(SkuMappingImportExcel::getPsku)
                .filter(Objects::nonNull)
                .filter(psku -> !psku.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(distinctPurchaseSkus)) {
            log.warn("没有找到有效的采购SKU数据");
            return Collections.emptySet();
        }

        log.debug("加载采购SKU数据，SKU数量: {}", distinctPurchaseSkus.size());
        
        try {
            List<PdsProductManagerBasicViewVo> productList = productClient.getPurchaseSku(distinctPurchaseSkus);
            return productList.stream()
                    .map(PdsProductManagerBasicViewVo::getPurchaseSku)
                    .collect(Collectors.toSet());
        } catch (Exception e) {
            log.error("加载采购SKU数据失败", e);
            return Collections.emptySet();
        }
    }

    public Map<String, StoreVo> getStoreMapByIds(Collection<Integer> storeIds) {
        if (CollectionUtil.isEmpty(storeIds)) {
            return Collections.emptyMap();
        }
        
        try {
            List<StoreVo> storeList = storeClient.getByStoreIds(new ArrayList<>(storeIds));
            return storeList.stream()
                    .collect(Collectors.toMap(store -> store.getId().toString(), Function.identity()));
        } catch (Exception e) {
            log.error("根据ID加载店铺数据失败", e);
            return Collections.emptyMap();
        }
    }

    public Map<String, OumUserInfoRes> getOperatorMapByIds(Collection<Integer> operatorIds) {
        if (CollectionUtil.isEmpty(operatorIds)) {
            return Collections.emptyMap();
        }
        
        try {
            List<OumUserInfoRes> operatorList = userClient.getUserByIds(new ArrayList<>(operatorIds));
            return operatorList.stream()
                    .collect(Collectors.toMap(operator -> operator.getId().toString(), Function.identity()));
        } catch (Exception e) {
            log.error("根据ID加载操作员数据失败", e);
            return Collections.emptyMap();
        }
    }
}