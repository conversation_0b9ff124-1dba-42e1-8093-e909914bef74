package com.renpho.erp.oms.domain.vc.order.model;

import org.jmolecules.ddd.annotation.Entity;
import org.jmolecules.ddd.annotation.Identity;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单DI实体
 * <AUTHOR>
 */
@Getter
@Builder
@Entity
public class VcOrderDi {

	/**
	 * 主键
	 */
	@Identity
	private Long id;

	/**
	 * 付款条款
	 */
	private String paymentMethod;

	/**
	 * 贸易条款
	 */
	private String incoterms;

	/**
	 * 集装箱规格
	 */
	private String containerType;

	/**
	 * 发货描述
	 */
	private String shippingInstructions;

	/**
	 * 创建人ID，0 System/系统
	 */
	private Integer createBy;

}
