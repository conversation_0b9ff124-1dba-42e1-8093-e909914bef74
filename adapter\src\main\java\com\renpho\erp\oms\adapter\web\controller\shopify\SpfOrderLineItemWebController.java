package com.renpho.erp.oms.adapter.web.controller.shopify;

import com.renpho.erp.oms.application.channelmanagement.shopify.service.SpfOrderLineItemService;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderLineItemVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/shopify/**")
@RequestMapping("/shopify/item")
@AllArgsConstructor
public class SpfOrderLineItemWebController {
	private final SpfOrderLineItemService spfOrderLineItemService;

	/**
	 * Shopify订单商品列表
	 * @param orderId
	 * @return R<List<SpfOrderLineItemVO>>
	 */
	@GetMapping("/list")
	public R<List<SpfOrderLineItemVO>> listByOrderId(@RequestParam Long orderId) {
		return spfOrderLineItemService.listByOrderId(orderId);
	}
}
