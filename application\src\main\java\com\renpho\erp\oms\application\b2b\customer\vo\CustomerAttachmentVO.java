package com.renpho.erp.oms.application.b2b.customer.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.VO;
import lombok.Data;

/**
 * 客户附件信息视图对象
 */
@Data
public class CustomerAttachmentVO implements VO {
    /**
     * ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 附件类型
     */
    @Trans(type = TransType.DICTIONARY, key = "attachment_type", ref = "attachmentName")
    private Integer attachmentType;


    private String attachmentName;
    
    /**
     * 文件名
     */
    private String fileName;
    
    /**
     * 文件URL
     */
    private String fileUrl;
    
    /**
     * 文件格式
     */
    private String format;
    
    /**
     * 备注
     */
    private String remark;
} 