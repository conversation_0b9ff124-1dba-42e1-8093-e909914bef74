package com.renpho.erp.oms.domain.vc.order.enums;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VC订单接单方式枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VcOrderAcceptedMethod {

	/**
	 * 否
	 */
	NO(0, "否"),

	/**
	 * 是
	 */
	YES(1, "是");

	private final Integer value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return VcOrderAcceptedMethod
	 */
	public static VcOrderAcceptedMethod getByValue(Integer value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
