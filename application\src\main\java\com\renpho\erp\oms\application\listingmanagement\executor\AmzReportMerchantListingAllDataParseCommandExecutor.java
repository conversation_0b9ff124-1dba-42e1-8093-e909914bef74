package com.renpho.erp.oms.application.listingmanagement.executor;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.csv.CsvRow;
import com.renpho.erp.oms.application.listingmanagement.dto.AmzReportParseMerchantListingAllDataDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 亚马逊商户全部数据报告解析执行器
 *
 * <AUTHOR>
 * @Date 2024/12/27 9:18
 **/
@Slf4j
@Component
public class AmzReportMerchantListingAllDataParseCommandExecutor {

	/**
	 * 执行方法
	 *
	 * @return java.util.List<com.renpho.erp.amz.report.dto.AmzReportParseMerchantListingAllDataDto>
	 * <AUTHOR>
	 * @Date 9:20 2024/12/27
	 * @Param [csvList, storeId]
	 **/
	public List<AmzReportParseMerchantListingAllDataDto> execute(List<CsvRow> csvList, Integer storeId) {
		List<AmzReportParseMerchantListingAllDataDto> list = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(csvList)) {
			for (int i = 0; i < csvList.size(); i++) {
				CsvRow csvRow = csvList.get(i);
				AmzReportParseMerchantListingAllDataDto dto = buildDto(csvRow);
				buildDto2(csvRow, dto);
				buildDto3(csvRow, dto);
				dto.setStoreId(storeId);
				// Listing id 不为空，则存储到Listing 原始表
				if (StringUtils.isNotBlank(dto.getListingId())) {
					list.add(dto);
				}
			}
		}
		return list;
	}

	/**
	 * 构建dto
	 *
	 * @return com.renpho.erp.amz.report.dto.AmzReportParseMerchantListingAllDataDto
	 * <AUTHOR>
	 * @Date 11:28 2024/12/26
	 * @Param [csvRow]
	 **/
	private static AmzReportParseMerchantListingAllDataDto buildDto(CsvRow csvRow) {
		AmzReportParseMerchantListingAllDataDto dto = new AmzReportParseMerchantListingAllDataDto();
		try {
			dto.setItemName(csvRow.getByName("item-name"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setItemDescription(csvRow.getByName("item-description"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setListingId(csvRow.getByName("listing-id"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setSellerSku(csvRow.getByName("seller-sku"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setPrice(csvRow.getByName("price"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setQuantity(csvRow.getByName("quantity"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setOpenDate(csvRow.getByName("open-date"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setImageUrl(csvRow.getByName("image-url"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setItemIsMarketplace(csvRow.getByName("item-is-marketplace"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		return dto;
	}

	/**
	 * 构建dto 第二部分
	 *
	 * @param csvRow
	 * @param dto
	 */
	private static void buildDto2(CsvRow csvRow, AmzReportParseMerchantListingAllDataDto dto) {
		try {
			dto.setProductIdType(csvRow.getByName("product-id-type"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setZShopShippingFee(csvRow.getByName("zshop-shipping-fee"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setItemNote(csvRow.getByName("item-note"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setItemCondition(csvRow.getByName("item-condition"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setZShopCategory1(csvRow.getByName("zshop-category1"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setZShopBrowsePath(csvRow.getByName("zshop-browse-path"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setZShopStoreFrontFeature(csvRow.getByName("zshop-storefront-feature"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setAsin1(csvRow.getByName("asin1"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setAsin2(csvRow.getByName("asin2"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setAsin3(csvRow.getByName("asin3"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}

	/**
	 * 构建dto 第三部分
	 *
	 * @return void
	 * <AUTHOR>
	 * @Date 11:32 2024/12/26
	 * @Param [csvRow, dto]
	 **/
	private static void buildDto3(CsvRow csvRow, AmzReportParseMerchantListingAllDataDto dto) {
		try {
			dto.setWillShipInternationally(csvRow.getByName("will-ship-internationally"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setExpeditedShipping(csvRow.getByName("expedited-shipping"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setZShopBoldface(csvRow.getByName("zshop-boldface"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setProductId(csvRow.getByName("product-id"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setBidForFeaturedPlacement(csvRow.getByName("bid-for-featured-placement"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setAddDelete(csvRow.getByName("add-delete"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setPendingQuantity(csvRow.getByName("pending-quantity"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setFulfillmentChannel(csvRow.getByName("fulfillment-channel"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setStatus(csvRow.getByName("status"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
		try {
			dto.setMerchantShippingGroup(csvRow.getByName("merchant-shipping-group"));
		}
		catch (Exception e) {
			log.error(e.getMessage(), e);
		}
	}
}
