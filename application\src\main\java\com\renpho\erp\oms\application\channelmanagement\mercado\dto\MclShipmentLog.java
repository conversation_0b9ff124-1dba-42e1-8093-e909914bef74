package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class MclShipmentLog {

	private Long id;

	/**
	 * 美客多用户id
	 */
	private String mclUserId;

	/**
	 * 美客多运单id
	 */
	private Long mclShipmentId;

	/**
	 * 美客多订单id
	 */
	private Long orderId;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 子状态
	 */
	private String substatus;

	/**
	 * 申报费用
	 */
	private BigDecimal declaredValue;

	/**
	 * 币种id
	 */
	private String currencyId;

	/**
	 * 运单创建时间
	 */
	private LocalDateTime dateCreated;

	/**
	 * 运单最新更新时间
	 */
	private LocalDateTime lastUpdated;

	/**
	 * 追踪号码
	 */
	private String trackingNumber;

	/**
	 * 追踪方式
	 */
	private String trackingMethod;

	/**
	 * 来源json
	 */
	private String origin;

	/**
	 * zip_code
	 */
	private String zipCode;

	/**
	 * city id
	 */
	private String cityId;

	/**
	 * 城市
	 */
	private String cityName;

	/**
	 * state id
	 */
	private String stateId;

	/**
	 * 区
	 */
	private String stateName;

	/**
	 * country id
	 */
	private String countryId;

	/**
	 * 国家
	 */
	private String countryName;

	/**
	 * 高
	 */
	private Integer height;

	/**
	 * 长
	 */
	private Integer length;

	/**
	 * 宽
	 */
	private Integer width;

	/**
	 * 重量
	 */
	private Integer weight;

	/**
	 * 外部引用
	 */
	private String externalReference;

	/**
	 * 交货时间json
	 */
	private String leadTime;

	/**
	 * site_id
	 */
	private String siteId;

	/**
	 * 物流模式
	 */
	private String logisticMode;

	/**
	 * 物流类型
	 */
	private String logisticType;

	/**
	 * 物流方向
	 */
	private String logisticDirection;

	/**
	 * tags列表json
	 */
	private String tags;

	/**
	 * 发送人id
	 */
	private Long senderId;
}
