package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.util.List;
import java.util.Objects;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订购数量值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcQuantity {

	/**
	 * 订购数量 此值不应为零
	 */
	private Integer amount;

	/**
	 * 计量单位 Cases: 将单个商品包装成箱 Eaches: 单个商品
	 */
	private String unitOfMeasure;

	/**
	 * 箱规格 当使用箱为单位订购时的箱规格
	 */
	private Integer unitSize;

	/**
	 * 订购数量详情
	 */
	private List<AmzVcQuantityDetail> orderedQuantityDetails;

	/**
	 * 获取数量
	 * @return Integer
	 */
	public Integer getQuantity() {
		if (Objects.isNull(amount) || Objects.isNull(unitSize)) {
			return null;
		}
		return amount * unitSize;
	}

}
