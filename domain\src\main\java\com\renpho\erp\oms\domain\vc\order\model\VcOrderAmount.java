package com.renpho.erp.oms.domain.vc.order.model;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单金额值对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class VcOrderAmount {

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 商品收入，[数量*单价]之和，如果已接单则为接单数量，否则为下单数量
	 */
	private BigDecimal productPrice;

	/**
	 * 税率，0.2代表20%
	 */
	private BigDecimal taxRate;

	/**
	 * 税额，税额明细行的汇总
	 */
	private BigDecimal tax;

	/**
	 * 计算总金额
	 * @return BigDecimal
	 */
	public BigDecimal getTotalAmount() {
		if (productPrice == null) {
			return tax != null ? tax : BigDecimal.ZERO;
		}
		if (tax == null) {
			return productPrice;
		}
		return productPrice.add(tax);
	}

	/**
	 * 计算订单金额 TODO 方法修饰改为受保护
	 * @param items 订单商品行
	 */
	public void calculateAmount(List<VcOrderItem> items) {
		// 所有订单商品行的商品总价相加
		this.productPrice = items.stream()
			.map(VcOrderItem::getAmount)
			.filter(Objects::nonNull)
			.map(VcOrderItemAmount::getProductPrice)
			.filter(Objects::nonNull)
			.reduce(BigDecimal.ZERO, BigDecimal::add);
		// 税率、税额（如果没值，默认为0）
		this.taxRate = Optional.ofNullable(taxRate).orElse(BigDecimal.ZERO);
		this.tax = Optional.ofNullable(tax).orElse(BigDecimal.ZERO);
	}
}
