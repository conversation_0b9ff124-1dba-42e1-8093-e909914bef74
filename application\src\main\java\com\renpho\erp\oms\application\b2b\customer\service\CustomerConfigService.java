package com.renpho.erp.oms.application.b2b.customer.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.b2b.customer.convert.CustomerConfigConvert;
import com.renpho.erp.oms.application.b2b.customer.vo.IncotermsVO;
import com.renpho.erp.oms.application.b2b.customer.vo.PaymentTermsVO;
import com.renpho.erp.oms.application.b2b.customer.vo.SalesCompanyVO;
import com.renpho.erp.oms.infrastructure.common.cache.CommonCache;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.IncotermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.PaymentTermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.IncotermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.PaymentTermsConfigService;

import lombok.RequiredArgsConstructor;

/**
 * @desc: 客户配置服务
 * @time: 2025-04-30 11:30:58
 * @author: Alina
 */
@Service
@RequiredArgsConstructor
public class CustomerConfigService {

	private final IncotermsConfigService incotermsConfigService;
	private final PaymentTermsConfigService paymentTermsConfigService;

	private final CompanyClient companyClient;
	private final CustomerConfigConvert customerConfigConvert;
	private final CommonCache commonCache;

	/**
	 * 销售公司列表
	 *
	 * @return 销售公司列表
	 */
	public List<SalesCompanyVO> getSalesCompanyList(Integer status) {
		// 查询MDM 配置有b2b收款账号的公司列表
		List<CompanyVo> companyVos = companyClient.getCompanyListWithB2BAccount(status);
		// 转换VO对象并设置对应的收款账号ID
		return companyVos.stream().map(customerConfigConvert::toSalesCompanyVO).toList();
	}

	/**
	 * 贸易条款列表
	 * @return 贸易条款列表
	 */
	public List<IncotermsVO> getIncotermsList() {
		List<IncotermsConfigPO> incotermsConfigPos = incotermsConfigService.getIncotermsList();
		return incotermsConfigPos.stream().map(customerConfigConvert::toIncotermsVO).toList();
	}

	/**
	 * 支付条款列表
	 * @return 支付条款列表
	 */
	public List<PaymentTermsVO> getPaymentTermsList() {
		List<PaymentTermsConfigPO> paymentTermsConfigPos = paymentTermsConfigService.getPaymentTermsList();
		return paymentTermsConfigPos.stream().map(customerConfigConvert::toPaymentTermsVO).toList();
	}

}
