package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.ordershipaudit.model.Carrier;
import com.renpho.erp.oms.domain.ordershipaudit.model.FulfillmentServiceCode;
import com.renpho.erp.oms.domain.ordershipaudit.repository.FulfillmentServiceCodeRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;

import lombok.RequiredArgsConstructor;

/**
 * 默认-分物流策略
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class DefaultAllocateLogistics extends AbstractAllocateLogisticsTemplate {

	private final FulfillmentServiceCodeRepository fulfillmentServiceCodeRepository;

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.JD_WAREHOUSE);
	}

	@Override
	public String allocateLogistics(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 履约服务类型
		FulfillmentServiceType fulfillmentServiceType = orderAutoShipAudit.getFulfillmentServiceType();
		// 查询履约服务配置
		List<FulfillmentServiceCode> fulfillmentServiceCodes = fulfillmentServiceCodeRepository
			.findByFulfillmentServiceType(fulfillmentServiceType.getValue());
		// 选取默认的承运商服务
		Optional<FulfillmentServiceCode> optional = fulfillmentServiceCodes.stream()
			.filter(FulfillmentServiceCode::isDefaultCarrierService)
			.findFirst();
		if (optional.isEmpty()) {
			return "京东仓的发货审核配置，没有默认承运商服务";
		}
		// 分物流成功
		FulfillmentServiceCode fulfillmentService = optional.get();
		// 承运商
		Carrier carrier = this.buildCarrier(fulfillmentService);
		orderAutoShipAudit.setCarrier(carrier);
		XxlJobHelper.log("订单号：{}，分仓成功，承运商：{}", orderAutoShipAudit.getOrderNo(), JSONKit.toJSONString(carrier));
		return null;
	}

	/**
	 * 构建承运商
	 * @param fulfillmentService 履约服务
	 * @return Carrier
	 */
	private Carrier buildCarrier(FulfillmentServiceCode fulfillmentService) {
		return Carrier.builder()
			.carrierCode(fulfillmentService.getCarrierCode())
			.carrierServiceCode(fulfillmentService.getCarrierServiceCode())
			.packageTypeCode(fulfillmentService.getPackageTypeCode())
			.jdOrderType(fulfillmentService.getJdOrderType())
			.build();
	}

}
