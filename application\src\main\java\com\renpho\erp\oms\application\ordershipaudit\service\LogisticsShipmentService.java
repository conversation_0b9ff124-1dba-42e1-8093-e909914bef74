package com.renpho.erp.oms.application.ordershipaudit.service;

import com.renpho.erp.apiproxy.ecom.model.logistics.RetrieveCarrierTrackingInfoResponse;
import com.renpho.erp.oms.application.ordershipaudit.converter.LogisticsTrackingConvertor;
import com.renpho.erp.oms.application.ordershipaudit.strategy.logistics.LogisticsFactory;
import com.renpho.erp.oms.application.ordershipaudit.strategy.logistics.LogisticsStrategy;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.query.LogisticsShipmentQuery;
import com.renpho.erp.oms.domain.ordershipaudit.repository.LogisticsShipmentRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.common.util.BatchUtils;
import com.renpho.erp.oms.infrastructure.feign.proxy.EcomClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.EcomLogisticsTrackingPO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.EcomLogisticsTrackingService;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.service.SaleOrderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @desc: 发货审核物流下单服务
 * @time: 2025-04-07 16:47:32
 * @author: Alina
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogisticsShipmentService {

	private final LogisticsShipmentRepository logisticsShipmentRepository;
	private final SaleOrderRepository saleOrderRepository;
	private final SaleOrderService saleOrderService;
	private final EcomClient ecomClient;
	private final EcomLogisticsTrackingService ecomLogisticsTrackingService;
	private final LogisticsTrackingConvertor logisticsTrackingConvertor;

	/**
	 * 定时获取物流下单结果 并重试
	 * @param query 查询入参
	 */
	public void logisticsShipmentResultProcessor(LogisticsShipmentQuery query) {
		BatchUtils.batchDeal(
				// 根据 lastId 和 batchSize 获取列表
				(Long lastId, Integer size) -> {
					query.setLastId(lastId);
					query.setPageSize(size);
					return logisticsShipmentRepository.getBatchList(query);
				},
				// 对获取到的这一批列表进行处理
				this::processLogisticsShipment,
				// 列表id获取的方法
				LogisticsShipmentAggRoot::getId,
				// 批处理大小
				1000);
	}

	/**
	 * 处理物流下单信息
	 */
	private void processLogisticsShipment(List<LogisticsShipmentAggRoot> logisticsShipmentAggRoots) {
		logisticsShipmentAggRoots.forEach(logisticsShipmentAggRoot -> {
			LogisticsStrategy logisticsStrategy = LogisticsFactory
				.get(FulfillmentServiceType.enumOf(logisticsShipmentAggRoot.getFulfillmentServiceType()));
			// 获取并处理物流下单结果
			logisticsStrategy.processLogisticsResult(logisticsShipmentAggRoot);
		});
	}

	/**
	 * 定时获取ecom物流跟踪信息
	 * @param query 查询入参
	 */
	public void fetchEcomShipmentStatus(LogisticsShipmentQuery query) {
		BatchUtils.batchDeal(
				// 根据 lastId 和 batchSize 获取列表
				(Long lastId, Integer size) -> {
					query.setLastId(lastId);
					query.setPageSize(size);
					return saleOrderService.queryShippedOrders(query);
				},
				// 对获取到的这一批列表进行处理
				this::processShippedOrders,
				// 列表id获取的方法
				Function.identity(),
				// 批处理大小
				1000);
	}

	/**
	 * 处理已发货的订单信息
	 * @param orderIds 订单ID列表
	 */
	public void processShippedOrders(List<Long> orderIds) {
		orderIds.forEach(orderId -> {
			SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findAggRootById(orderId);
			String bookNumber = saleOrderAggRoot.getFulfillment().getLogisticsOrderNo();
			try {
				// 调用Ecom 获取承运商跟踪信息接口
				RetrieveCarrierTrackingInfoResponse response = ecomClient.retrieveCarrierTrackingInfo(bookNumber);
				List<RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo> carrierTrackingInfos = Optional.ofNullable(response)
					.map(RetrieveCarrierTrackingInfoResponse::getCarrierTrackingInfos)
					.orElse(Collections.emptyList());
				if (CollectionUtils.isNotEmpty(carrierTrackingInfos)) {
					// 保存ecom物流跟踪信息
					List<EcomLogisticsTrackingPO> ecomLogisticsTrackingPos = carrierTrackingInfos.stream()
						.map(info -> logisticsTrackingConvertor.toEcomLogisticsTrackingPo(info, bookNumber))
						.collect(Collectors.toList());
					ecomLogisticsTrackingService.saveBatchIgnoreDuplicate(ecomLogisticsTrackingPos);
					// 更新订单签收状态
					updateSaleOrderStatus(carrierTrackingInfos, saleOrderAggRoot);
				}
			}
			catch (Exception e) {
				log.error("调用Ecom 获取承运商跟踪信息接口发生异常,异常信息 %s".formatted(e.getMessage()), e);
			}
		});
	}

	/**
	 * 更新订单签收状态
	 * @param carrierTrackingInfos 物流跟踪信息列表
	 * @param saleOrderAggRoot 销售订单聚合根
	 */
	private void updateSaleOrderStatus(List<RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo> carrierTrackingInfos,
			SaleOrderAggRoot saleOrderAggRoot) {
		if (containsStatus(carrierTrackingInfos, "Delivered")) {
			// 所有订单行-标记签收
			LocalDateTime deliveredTime = DateUtil
				.convertStrToUTC(getTrackingInfoByStatus(carrierTrackingInfos, "Delivered").getEventTime());
			// 领域层-所有订单行-标记签收
			if (saleOrderAggRoot.markDelivered(deliveredTime)) {
				// 领域层-重新计算订单状态
				saleOrderAggRoot.calculateStatus();
				// 仓储层更新-订单签收
				saleOrderRepository.updateDelivered(saleOrderAggRoot);
			}
		}
		else if (containsExceptionStatus(carrierTrackingInfos, "Exception")) {
			// 标记签收异常
			if (saleOrderAggRoot.markUnDeliverable()) {
				// 领域层-重新计算订单状态
				saleOrderAggRoot.calculateStatus();
				// 仓储层更新-更新订单状态-签收异常
				saleOrderRepository.updateStatus(saleOrderAggRoot);
			}
		}
	}

	/**
	 * 判断是否包含指定状态的数据
	 * @param carrierTrackingInfos 物流跟踪信息列表
	 * @param status 状态
	 * @return 是否包含
	 */
	public boolean containsStatus(List<RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo> carrierTrackingInfos, String status) {
		return carrierTrackingInfos.stream().anyMatch(info -> status.equals(info.getEventStatus()));
	}

	/**
	 * 判断是否包含异常的数据
	 * @param carrierTrackingInfos 物流跟踪信息列表
	 * @param status 状态
	 * @return 是否包含
	 */
	public boolean containsExceptionStatus(List<RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo> carrierTrackingInfos, String status) {
		return carrierTrackingInfos.stream()
			.anyMatch(info -> status.equals(info.getEventStatus()) && !"NO SECURE LOCATION AVVAILABLE".equals(info.getEvent()));
	}

	/**
	 * 获取指定状态的物流跟踪信息
	 * @param carrierTrackingInfos 物流跟踪信息列表
	 * @param status 要匹配的状态
	 * @return 匹配的物流跟踪信息(可能为null)
	 */
	public RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo getTrackingInfoByStatus(
			List<RetrieveCarrierTrackingInfoResponse.CarrierTrackingInfo> carrierTrackingInfos, String status) {
		return carrierTrackingInfos.stream().filter(info -> status.equals(info.getEventStatus())).findFirst().orElse(null);
	}
}
