package com.renpho.erp.oms.domain.vc.order.model;

import java.math.BigDecimal;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC金额值对象 基于Amazon SP-API Money模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcMoney {

	/**
	 * 金额 对应API字段：amount
	 */
	private BigDecimal amount;

	/**
	 * 币种代码 对应API字段：currencyCode ISO 4217货币代码
	 */
	private String currencyCode;

	/**
	 * 检查金额是否为正数
	 * @return boolean
	 */
	public boolean isPositive() {
		return amount != null && amount.compareTo(BigDecimal.ZERO) > 0;
	}

	/**
	 * 检查金额是否为零
	 * @return boolean
	 */
	public boolean isZero() {
		return amount != null && amount.compareTo(BigDecimal.ZERO) == 0;
	}

	/**
	 * 检查金额是否为负数
	 * @return boolean
	 */
	public boolean isNegative() {
		return amount != null && amount.compareTo(BigDecimal.ZERO) < 0;
	}

	/**
	 * 检查是否为美元
	 * @return boolean
	 */
	public boolean isUSD() {
		return "USD".equals(currencyCode);
	}

	/**
	 * 检查是否为欧元
	 * @return boolean
	 */
	public boolean isEUR() {
		return "EUR".equals(currencyCode);
	}

	/**
	 * 获取格式化的金额字符串
	 * @return String
	 */
	public String getFormattedAmount() {
		if (amount == null) {
			return null;
		}

		StringBuilder formatted = new StringBuilder();
		if (currencyCode != null) {
			formatted.append(currencyCode).append(" ");
		}
		formatted.append(amount.toString());

		return formatted.toString();
	}

	/**
	 * 与另一个金额相加
	 * @param other 另一个金额
	 * @return AmzVcMoney 新的金额对象
	 */
	public AmzVcMoney add(AmzVcMoney other) {
		if (other == null || other.amount == null) {
			return this;
		}

		if (this.amount == null) {
			return other;
		}

		// 检查币种是否一致
		if (!java.util.Objects.equals(this.currencyCode, other.currencyCode)) {
			throw new IllegalArgumentException("Cannot add amounts with different currencies");
		}

		return AmzVcMoney.builder().amount(this.amount.add(other.amount)).currencyCode(this.currencyCode).build();
	}

	/**
	 * 与另一个金额相减
	 * @param other 另一个金额
	 * @return AmzVcMoney 新的金额对象
	 */
	public AmzVcMoney subtract(AmzVcMoney other) {
		if (other == null || other.amount == null) {
			return this;
		}

		if (this.amount == null) {
			throw new IllegalArgumentException("Cannot subtract from null amount");
		}

		// 检查币种是否一致
		if (!java.util.Objects.equals(this.currencyCode, other.currencyCode)) {
			throw new IllegalArgumentException("Cannot subtract amounts with different currencies");
		}

		return AmzVcMoney.builder().amount(this.amount.subtract(other.amount)).currencyCode(this.currencyCode).build();
	}

}
