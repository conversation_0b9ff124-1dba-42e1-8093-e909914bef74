package com.renpho.erp.oms.application.channelmanagement.mercado.dto;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;

@Data
public class MclShipmentDTO {

	/**
	 * 同步时间
	 */
	private String syncTime;
	private String mclUserId;
	private Integer storeId;
	private Long id;
	private Long order_id;
	private String status;
	private String substatus;
	private BigDecimal declared_value;
	private String currency_id;
	private String date_created;
	private String last_updated;
	private String tracking_number;
	private String tracking_method;
	private Origin origin;
	private Destination destination;
	private Dimensions dimensions;
	private String external_reference;
	private LeadTime lead_time;
	private Source source;
	private Logistic logistic;
	private List<String> tags;
	private Long sender_id;

	/**
	 * shipment的状态历史集
	 */
	private String shipmentStatusHistory;

	@Data
	public static class Destination {
		private String type;
		private Long receiver_id;
		private String receiver_name;
		private String receiver_phone;
		private String comments;
		private ShippingAddress shipping_address;
	}

	@Data
	public static class Origin {
		private Long sender_id;
		private ShippingAddress shipping_address;
		private String type;
	}

	@Data
	public static class ShippingAddress {
		private Integer address_id;
		private String address_line;
		private String street_name;
		private String street_number;
		private String comment;
		private String zip_code;
		private City city;
		private State state;
		private Country country;
		private Neighborhood neighborhood;
		private Municipality municipality;
		private Agency agency;
		private List<String> types;
		private BigDecimal latitude;
		private BigDecimal longitude;
		private String geolocation_type;
		private String geolocation_last_updated;
		private String geolocation_source;
		private String delivery_preference;
	}

	@Data
	public static class City {
		private String id;
		private String name;
	}

	@Data
	public static class State {
		private String id;
		private String name;
	}

	@Data
	public static class Country {
		private String id;
		private String name;
	}

	@Data
	public static class Neighborhood {
		private String id;
		private String name;
	}

	@Data
	public static class Municipality {
		private String id;
		private String name;
	}

	@Data
	public static class Agency {
		private String agency_id;
		private Long carrier_id;
		private String description;
		private String open_hours;
		private String phone;
		private String type;
	}

	@Data
	public static class Dimensions {
		private Integer height;
		private Integer length;
		private Integer width;
		private Integer weight;
	}

	@Data
	public static class LeadTime {
		private Long option_id;
		private ShippingMethod shipping_method;
		private String currency_id;
		private BigDecimal cost;
		private BigDecimal list_cost;
		private String cost_type;
		private Long service_id;
		private EstimatedDeliveryTime estimated_delivery_time;
	}

	@Data
	public static class ShippingMethod {
		private Long id;
		private String type;
		private String name;
		private String deliver_to;
	}

	@Data
	public static class EstimatedDeliveryTime {
		private String type;
		private String date;
		private String unit;
		private Offset offset;
		private TimeFrame time_frame;
		private String pay_before;
		private Integer shipping;
		private Integer handling;
		private Schedule schedule;
	}

	@Data
	public static class Offset {
		private String date;
		private String shipping;
	}

	@Data
	public static class TimeFrame {
		private String from;
		private String to;
	}

	@Data
	public static class Schedule {
		// Define schedule fields if needed
	}

	@Data
	public static class Source {
		private String site_id;
	}

	@Data
	public static class Logistic {
		private String mode;
		private String type;
		private String direction;
	}
}
