package com.renpho.erp.oms.application.ordershipaudit.job;

import com.renpho.erp.oms.application.wms.WmsTokenService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class WmsTokenRefreshJob {
    @Autowired
    private WmsTokenService wmsTokenService;

    @XxlJob("wmsTokenRefreshJob")
    public void handle() {
        log.info("WmsTokenRefreshJob");
        wmsTokenService.refreshToken();

    }
}
