package com.renpho.erp.oms.application.ordershipaudit;

import java.util.EnumMap;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * 多渠道发货行为
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
public interface ChannelOrderShipService {

	/**
	 * 获取履约服务类型集合
	 * @return Set
	 */
	Set<FulfillmentServiceType> getFulfillmentServiceTypes();

	/**
	 * 创建履约单（仓储物流）
	 *
	 * @param orderShipAuditAggRoot 发货审核领域驱动对象
	 * @param saleOrderAggRoot 订单聚合根
	 */
	void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot);

	/**
	 * 同步平台出库单的发货信息
	 *
	 * @param orderShipAuditAggRoot 发货审核领域驱动对象
	 */
	void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot);

	@PostConstruct
	default void init() {
		Set<FulfillmentServiceType> fulfillmentServiceTypes = this.getFulfillmentServiceTypes();
		fulfillmentServiceTypes.forEach(type -> Factory.MAP.put(type, this));
	}

	@AllArgsConstructor(access = AccessLevel.PRIVATE)
	class Factory {
		private static final Map<FulfillmentServiceType, ChannelOrderShipService> MAP = new EnumMap<>(FulfillmentServiceType.class);

		public static ChannelOrderShipService getInstance(FulfillmentServiceType fulfillmentServiceType) {
			return MAP.get(fulfillmentServiceType);
		}
	}
}
