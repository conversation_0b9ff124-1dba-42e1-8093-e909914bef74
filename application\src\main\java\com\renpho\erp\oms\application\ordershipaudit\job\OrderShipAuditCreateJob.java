package com.renpho.erp.oms.application.ordershipaudit.job;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.ShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.OrderShipAuditMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.OrderShipAuditPO;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/2/19
 */
@Slf4j
@Component
public class OrderShipAuditCreateJob {
	@Autowired
	private OrderShipAuditMapper orderShipAuditMapper;

	@Autowired
	private OrderShipAuditRepository orderShipAuditRepository;
	@Autowired
	private SaleOrderRepository saleOrderRepository;
	@Autowired
	private OrderShipAuditService orderShipAuditService;

	@XxlJob("orderShipAuditCreateJob")
	public void handle() {
		String jobParam = XxlJobHelper.getJobParam();
		OrderShipAuditCreateJob.Param param = JSONKit.parseObject(jobParam, OrderShipAuditCreateJob.Param.class);

		List<FulfillmentServiceType> fulfillmentServiceTypeList = param.getFulfillmentServiceTypes()
			.stream()
			.map(FulfillmentServiceType::enumOf)
			.filter(Objects::nonNull)
			.toList();
		for (FulfillmentServiceType fulfillmentServiceType : fulfillmentServiceTypeList) {
			ChannelOrderShipService channelOrderShipService = ChannelOrderShipService.Factory.getInstance(fulfillmentServiceType);
			if (channelOrderShipService == null) {
				XxlJobHelper.log("没有相关ChannelOrderShipService实现{}", fulfillmentServiceType);
				log.info("没有相关ChannelOrderShipService实现{}", fulfillmentServiceType);
				continue;
			}

			XxlJobHelper.log("开始创建{}类型的发货审核", fulfillmentServiceType.getName());
			log.info("开始创建{}类型的发货审核", fulfillmentServiceType.getName());

			Long maxId = 0L;
			Integer pageSize = ObjectUtil.defaultIfNull(param.getPageSize(), 100);
			List<OrderShipAuditPO> orderShipAuditPOList;
			do {
				// 检索初始化发货审核去调用平台接口创建
				orderShipAuditPOList = orderShipAuditMapper.selectList(Wrappers.<OrderShipAuditPO> lambdaQuery()
					.select(OrderShipAuditPO::getId, OrderShipAuditPO::getOrderId, OrderShipAuditPO::getChannelStoreId)
					.eq(OrderShipAuditPO::getFulfillmentServiceType, fulfillmentServiceType.getValue())
					.eq(param.getOrderId() != null, OrderShipAuditPO::getOrderId, param.getOrderId())
					.in(OrderShipAuditPO::getStatus, ShipmentStatus.INIT.getValue())
					.gt(OrderShipAuditPO::getId, maxId)
					.orderByAsc(OrderShipAuditPO::getId)
					.last("limit " + pageSize));

				for (OrderShipAuditPO orderShipAudit : orderShipAuditPOList) {
					XxlJobHelper.log("开始处理id是{}的发货审核", orderShipAudit.getId());
					log.info("开始处理id是{}的发货审核", orderShipAudit.getId());

					OrderShipAuditAggRoot orderShipAuditAggRoot = orderShipAuditRepository.getById(orderShipAudit.getId());

					SaleOrderAggRoot saleOrderAggRoot = saleOrderRepository.findAggRootById(orderShipAudit.getOrderId());
					if (saleOrderAggRoot == null) {
						orderShipAuditAggRoot.createFail("订单不存在");
						orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
						continue;
					}
					String errorInfo = saleOrderAggRoot.checkAudit();
					if (StrUtil.isNotBlank(errorInfo)) {
						orderShipAuditAggRoot.createFail(errorInfo);
						orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
					}

					try {
						orderShipAuditService.shipAudit(orderShipAuditAggRoot.getAuditNo(), true);
					}
					catch (Exception e) {
						XxlJobHelper.log("处理发货审核失败id是{}", orderShipAudit.getId());
						XxlJobHelper.log(e);
						log.info("处理发货审核失败id是{}", orderShipAudit.getId(), e);
					}
				}

			}
			while (Objects.equals(CollUtil.size(orderShipAuditPOList), pageSize));

			XxlJobHelper.log("完成创建{}类型的发货审核", fulfillmentServiceType.getName());
			log.info("完成创建{}类型的发货审核", fulfillmentServiceType.getName());
		}

	}

	@Data
	public static class Param {
		/**
		 * 发货服务类型
		 */
		private List<Integer> fulfillmentServiceTypes;
		/**
		 * 订单id
		 */
		private Long orderId;
		/**
		 * 分页大小
		 */
		private Integer pageSize;

	}
}
