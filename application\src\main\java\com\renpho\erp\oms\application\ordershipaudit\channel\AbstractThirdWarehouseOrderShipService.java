package com.renpho.erp.oms.application.ordershipaudit.channel;

import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.feign.dto.WarehouseConfigDto;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;

import lombok.extern.slf4j.Slf4j;

/**
 * 抽象三方仓-发货 应用服务
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractThirdWarehouseOrderShipService extends AbstractOrderShipService {

	private final WarehouseClient warehouseClient;

	protected AbstractThirdWarehouseOrderShipService(SaleOrderQueryService saleOrderQueryService, WarehouseClient warehouseClient) {
		super(saleOrderQueryService);
		this.warehouseClient = warehouseClient;
	}

	/**
	 * 创建履约单（仓储物流）
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 */
	@Override
	public void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress) {
		// 查询ims客户编码
		WarehouseConfigDto warehouseConfig = warehouseClient.getWarehouseConfig(orderShipAuditAggRoot.getWarehouseId());
		if (warehouseConfig == null) {
			orderShipAuditAggRoot.createFail(String.format("查询ims客户编码为空，仓库id：%s", orderShipAuditAggRoot.getWarehouseId()));
			return;
		}
		// 创建履约单（仓储物流）
		this.createFulfillmentOrder(orderShipAuditAggRoot, saleOrderAggRoot, receivingAddress, warehouseConfig);
	}

	/**
	 * 创建履约单（仓储物流）
	 *
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 销售订单聚合根
	 * @param receivingAddress 敏感地址信息
	 * @param warehouseConfigDto ims仓库客户编码
	 */
	protected abstract void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, WarehouseConfigDto warehouseConfigDto);

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot) {
		// 查询ims客户编码
		WarehouseConfigDto warehouseConfig = warehouseClient.getWarehouseConfig(orderShipAuditAggRoot.getWarehouseId());
		if (warehouseConfig == null) {
			orderShipAuditAggRoot.syncFail(String.format("查询ims客户编码为空，仓库id：%s", orderShipAuditAggRoot.getWarehouseId()));
			return;
		}
		this.syncShipmentInfo(orderShipAuditAggRoot, warehouseConfig);
	}

	/**
	 * 同步平台店铺信息
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param warehouseConfigDto 三方仓配置信息
	 */
	protected abstract void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, WarehouseConfigDto warehouseConfigDto);
}
