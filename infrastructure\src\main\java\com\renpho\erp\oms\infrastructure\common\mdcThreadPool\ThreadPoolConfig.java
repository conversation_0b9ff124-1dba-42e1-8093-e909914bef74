package com.renpho.erp.oms.infrastructure.common.mdcThreadPool;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.alibaba.ttl.threadpool.TtlExecutors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

/**
 * @desc: 自定义线程池
 * @time: 2025-03-17 10:00:37
 * @author: Alina
 */
@Configuration
public class ThreadPoolConfig {

	@Value("${threadpool.corePoolSize:20}")
	private int corePoolSize;

	@Value("${threadpool.maxPoolSize:50}")
	private int maxPoolSize;

	@Value("${threadpool.queueCapacity:100}")
	private int queueCapacity;

	@Value("${threadpool.keepAliveTime:60}")
	private long keepAliveTime;

	/**
	 *
	 * @return ThreadPoolTaskExecutor 可以带父线程的MDC内容
	 */
	@Bean(name = "mdcThreadPoolTaskExecutor")
	public MdcThreadPoolTaskExecutor mdcThreadPoolTaskExecutor() {
		MdcThreadPoolTaskExecutor executor = new MdcThreadPoolTaskExecutor();
		executor.setCorePoolSize(16);
		executor.setMaxPoolSize(32);
		executor.setQueueCapacity(100);
		executor.setKeepAliveSeconds(60);
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		executor.setThreadNamePrefix("MDC-Executor-");
		executor.initialize();
		return executor;
	}

	@Bean(name = "eventMdcThreadPoolTaskExecutor")
	public MdcThreadPoolTaskExecutor eventMdcThreadPoolTaskExecutor() {
		MdcThreadPoolTaskExecutor executor = new MdcThreadPoolTaskExecutor();
		executor.setCorePoolSize(16);
		executor.setMaxPoolSize(32);
		executor.setQueueCapacity(100);
		executor.setKeepAliveSeconds(60);
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		executor.setThreadNamePrefix("EVENTMDC-Executor-");
		executor.initialize();
		return executor;
	}

	@Bean(name = "pullOrderExecutor")
	public ExecutorService pullOrderExecutor() {
		return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(corePoolSize, // corePoolSize:
				// 核心线程数
				maxPoolSize, // maximumPoolSize: 最大线程数
				keepAliveTime, // keepAliveTime: 线程空闲时最大存活时间
				TimeUnit.SECONDS, // keepAliveTime单位
				new LinkedBlockingQueue<>(queueCapacity), // 工作队列
				new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
		));
	}

	@Bean(name = "pullItemExecutor")
	public ExecutorService pullItemExecutor() {
		return TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(corePoolSize, // corePoolSize:
				// 核心线程数
				maxPoolSize, // maximumPoolSize: 最大线程数
				keepAliveTime, // keepAliveTime: 线程空闲时最大存活时间
				TimeUnit.SECONDS, // keepAliveTime单位
				new LinkedBlockingQueue<>(queueCapacity), // 工作队列
				new ThreadPoolExecutor.CallerRunsPolicy() // 拒绝策略
		));
	}

	@Bean(name = "shippingServiceExecutor")
	public MdcThreadPoolTaskExecutor shippingServiceExecutor() {
		MdcThreadPoolTaskExecutor executor = new MdcThreadPoolTaskExecutor();
		executor.setCorePoolSize(16);
		executor.setMaxPoolSize(32);
		executor.setQueueCapacity(100);
		executor.setKeepAliveSeconds(60);
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		executor.setThreadNamePrefix("ShippingService-Executor-");
		executor.initialize();
		return executor;
	}

	/**
	 * 拉取VC订单的线程池
	 * @return
	 */
	@Bean(name = "pullVcOrderExecutor")
	public MdcThreadPoolTaskExecutor pullVcOrderExecutor() {
		MdcThreadPoolTaskExecutor executor = new MdcThreadPoolTaskExecutor();
		// 核心线程数
		executor.setCorePoolSize(8);
		// 最大线程数
		executor.setMaxPoolSize(16);
		// 队列
		executor.setQueueCapacity(500);
		// 线程空闲时最大存活时间
		executor.setKeepAliveSeconds(60);
		// 拒绝策略
		executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
		//线程名称前缀
		executor.setThreadNamePrefix("pullVcOrder-Executor-");
		executor.initialize();
		return executor;
	}
}
