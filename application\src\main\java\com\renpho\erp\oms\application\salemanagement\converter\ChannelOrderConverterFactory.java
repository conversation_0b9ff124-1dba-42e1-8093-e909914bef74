package com.renpho.erp.oms.application.salemanagement.converter;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.convert.ChannelConvertServiceFactory;

/**
 * 渠道订单转换器工厂类.
 * <AUTHOR>
 * @date 2025/1/5 16:44
 */
@Component
public class ChannelOrderConverterFactory {

	private static final Map<ChannelConvertServiceFactory.StrategyKey, ChannelConvertSaleOrderStrategy> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 获取
	 * @param channelType 入参
	 * @return ChannelConvertSaleOrderStrategy
	 */
	public static ChannelConvertSaleOrderStrategy get(SaleChannelType channelType, FulfillmentType fulfillmentType) {
		Assert.notNull(channelType, "获取渠道订单转换器，渠道类型不能为空");
		Assert.notNull(fulfillmentType, "获取渠道订单转换器，发货类型不能为空");
		ChannelConvertServiceFactory.StrategyKey strategyKey = ChannelConvertServiceFactory.StrategyKey.newOf(channelType, fulfillmentType);
		if (STRATEGY_MAP.containsKey(strategyKey)) {
			return STRATEGY_MAP.get(strategyKey);
		}
		throw new RuntimeException(MessageFormat.format("获取渠道订单转换器，找不到 {0}-{1} 转换器", channelType.getName(), fulfillmentType.getName()));
	}

	/**
	 * 注册.
	 * @param channelType 渠道类型
	 * @param channelConvertSaleOrderStrategy 策略实体
	 */
	public static void register(SaleChannelType channelType, FulfillmentType fulfillmentType,
			ChannelConvertSaleOrderStrategy channelConvertSaleOrderStrategy) {
		Assert.notNull(channelType, "");
		Assert.notNull(fulfillmentType, "");
		Assert.notNull(channelConvertSaleOrderStrategy, "");
		ChannelConvertServiceFactory.StrategyKey strategyKey = ChannelConvertServiceFactory.StrategyKey.newOf(channelType, fulfillmentType);
		STRATEGY_MAP.put(strategyKey, channelConvertSaleOrderStrategy);
	}

}
