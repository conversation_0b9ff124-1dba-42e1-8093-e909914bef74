package com.renpho.erp.oms.adapter.client;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.renpho.erp.oms.adapter.client.converter.AmzSellerMtpInfoConverter;
import com.renpho.erp.oms.application.channelmanagement.amazon.service.AmzSellerMtpInfoQueryService;
import com.renpho.erp.oms.client.amz.RemoteAmzSellerMtpInfoService;
import com.renpho.erp.oms.client.amz.vo.AmzSellerMtpInfoVO;
import com.renpho.erp.security.annotation.Inner;
import com.renpho.karma.dto.R;

import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@RestController
@RequiredArgsConstructor
public class RemoteAmzSellerMtpInfoClient implements RemoteAmzSellerMtpInfoService {

	private final AmzSellerMtpInfoQueryService amzSellerMtpInfoQueryService;
	private final AmzSellerMtpInfoConverter amzSellerMtpInfoConverter;

	/**
	 * 亚马逊卖家参与市场信息列表
	 * @return R
	 */
	@Inner
	@Override
	public R<List<AmzSellerMtpInfoVO>> list() {
		return R.success(amzSellerMtpInfoConverter.toVO(amzSellerMtpInfoQueryService.list()));
	}
}
