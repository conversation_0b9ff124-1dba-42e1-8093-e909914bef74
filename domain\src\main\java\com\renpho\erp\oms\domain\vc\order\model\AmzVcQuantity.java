package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Objects;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC数量对象
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcQuantity {

	/**
	 * 数量
	 */
	private Integer amount;

	/**
	 * 计量单位
	 */
	private String unitOfMeasure;

	/**
	 * 箱规格 当使用箱为单位的箱规格
	 */
	private Integer unitSize;

	/**
	 * 获取数量
	 * @return Integer
	 */
	public Integer getQuantity() {
		if (Objects.isNull(amount) || Objects.isNull(unitSize)) {
			return null;
		}
		return amount * unitSize;
	}
}