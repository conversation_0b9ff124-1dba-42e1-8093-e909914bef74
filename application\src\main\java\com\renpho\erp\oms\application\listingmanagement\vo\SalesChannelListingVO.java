package com.renpho.erp.oms.application.listingmanagement.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * Listing VO
 * <AUTHOR>
 */
@Data
public class SalesChannelListingVO {

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 销售渠道ID
	 */
	private Integer salesChannelId;

	/**
	 * 销售渠道编码
	 */
	private String salesChannelCode;

	/**
	 * 销售渠道名称
	 */
	private String salesChannelName;

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * item编码
	 */
	private String item;

	/**
	 * 销售SKU
	 */
	private String msku;

	/**
	 * 采购SKU
	 */
	private String psku;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * Item link
	 */
	private String link;

	/**
	 * pds的图片id（产品封面图片）
	 */
	private String imageId;

	/**
	 * pds的图片url（产品封面图片）
	 */
	private String imageUrl;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 价格
	 */
	private BigDecimal price;

	/**
	 * Item状态
	 */
	private String status;

	/**
	 * Item发布时间
	 */
	private LocalDateTime publishedTime;

	/**
	 * Item平台更新时间
	 */
	private LocalDateTime itemUpdatedTime;

	/**
	 * 原始listing主键ID
	 */
	private Long listingSourceId;

	/**
	 * 是否查询展示：0 否、1 是
	 */
	private Integer isQueryShow;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 业务唯一key （销售渠道id + “_"+ 店铺id+ “_"+ Item + “_"+ MSKU）拼接后 MD5
	 */
	private String bsUniqueCode;
}
