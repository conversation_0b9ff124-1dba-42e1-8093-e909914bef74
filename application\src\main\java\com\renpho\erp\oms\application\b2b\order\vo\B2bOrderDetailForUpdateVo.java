package com.renpho.erp.oms.application.b2b.order.vo;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;

import lombok.Builder;
import lombok.Data;
import lombok.Getter;

@Data
public class B2bOrderDetailForUpdateVo implements VO {
	private Long id;
	/**
	 * 订单id
	 */
	private Long orderId;

	/**
	 * 客户参考号
	 */
	private String referNo;

	/**
	 * 海外区域经理姓名
	 */
	private String countryManagerName;

	/**
	 * 销售助理姓名
	 */
	private String salesAssistantName;

	/**
	 * 订单商品
	 */
	private List<Item> items;

	/**
	 * 订单金额
	 */
	private Amount amount;

	/**
	 * 订单收货地址
	 */
	private Address address;

	/**
	 * 订单发货要求
	 */
	private ShipmentRequirement shipmentRequirement;

	/**
	 * 订单客户信息
	 */
	private Customer customer;

	/**
	 * 客户联系人
	 */
	private List<CustomerContact> customerContacts;

	/**
	 * 订单客户发票
	 */
	private CustomerInvoice customerInvoice;

	@Data
	public static class Item {

		/**
		 * 订单商品主键id
		 */
		private Long orderItemId;

		/**
		 * 采购SKU
		 */
		private String psku;

		/**
		 * 下单数量
		 */
		private Integer orderedQuantity;

		/**
		 * 单价
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal unitPrice;

		/**
		 * 税率
		 */
		private BigDecimal taxRate;

		/**
		 * 税金
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal tax;
		/**
		 * 条码
		 */
		private String barcode;
		/**
		 * 装箱数量（每箱的psku数量）
		 */
		private Integer numberOfUnitsPerBox;
		/**
		 * 下单的箱数
		 */
		private BigDecimal orderedBoxNumber;
		/**
		 * 小计
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal subTotal;

		/**
		 * 采购SKU名称（根据语言环境返回）
		 */
		private String pskuName;
	}

	@Data
	public static class Amount {
		/**
		 * 币种
		 */
		private String currency;

		/**
		 * 运费收入
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal shippingFee;

		/**
		 * 其他收入
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal otherFees;

		/**
		 * 金额合计
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal totalAmount;

		/**
		 * 费用明细
		 */
		private List<FreeDetail> feesDetails;
	}

	@Data
	public static class FreeDetail {
		/**
		 * 费用类型，1-推广费 2-广告费
		 */
		private Integer feeType;

		/**
		 * 费用金额
		 */
		@JsonSerialize(using = BigDecimalSerializer.class)
		private BigDecimal feeAmount;
	}

	@Data
	public static class Address {
		/**
		 * 收货人
		 */
		private String attnTo;

		/**
		 * 收货方公司
		 */
		private String shippingCompany;

		/**
		 * 收货地址
		 */
		private String address;

		/**
		 * 邮编
		 */
		private String zipCode;

		/**
		 * 城市
		 */
		private String city;

		/**
		 * 国家
		 */
		private String country;

		/**
		 * 联系号码
		 */
		private String contactNumber;

		/**
		 * 备注
		 */
		private String comment;
	}

	@Data
	public static class ShipmentRequirement {
		/**
		 * 货物最晚交期
		 */
		private LocalDate requestDeliveryDate;

		/**
		 * 是否打托，0-否 1-是
		 */
		private Integer palletizationRequired;

		/**
		 * 提单类型，1-Original 2-Seaway 3-Express 4-Telex
		 */
		private Integer blType;

		/**
		 * 外箱标签名称
		 */
		private String masterCartonLabelName;

		/**
		 * 外箱标签链接
		 */
		private String masterCartonLabelUrl;

		/**
		 * 其他要求
		 */
		private String otherRequirements;
	}

	@Data
	public static class Customer {
		/**
		 * 客户ID
		 */
		private Long customerId;

		/**
		 * 销售公司ID
		 */
		private Integer salesCompanyId;

		/**
		 * 贸易条款编码
		 */
		private String incotermsCode;

		/**
		 * 付款条款编码
		 */
		private String paymentTermsCode;
	}

	@Getter
	@Builder
	public static class CustomerContact {
		/**
		 * 联系人类型，1-主联系人 2-采购 3-物流 4-财务
		 */
		private Integer contactType;

		/**
		 * 客户联系信息id
		 */
		private Long customerContactId;

		/**
		 * 岗位
		 */
		private String contactPosition;

		/**
		 * 名字
		 */
		private String contactName;

		/**
		 * 备注
		 */
		private String remark;
	}

	@Data
	public static class CustomerInvoice {
		/**
		 * 收件人
		 */
		private String attnTo;

		/**
		 * 发票公司
		 */
		private String invoiceCompany;

		/**
		 * 地址
		 */
		private String address;

		/**
		 * 邮编
		 */
		private String zipCode;

		/**
		 * 城市
		 */
		private String city;

		/**
		 * 国家
		 */
		private String country;

		/**
		 * 联系号码
		 */
		private String contactNumber;

		/**
		 * 增值税号码
		 */
		private String vatNumber;
	}
}
