package com.renpho.erp.oms.application.channelmanagement.mercado.service;

import java.util.Objects;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclShipmentAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclShipmentLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentDTO;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclShipmentAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclShipmentBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.mercado.optlog.MclShipmentUpdateSnapSource;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclShipment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclShipmentRepository;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;

import cn.hutool.extra.spring.SpringUtil;
import lombok.AllArgsConstructor;

@Service
@AllArgsConstructor
public class MclShipmentService {
	private final MclShipmentRepository mclShipmentRepository;
	private final MclShipmentLogConvertor mclShipmentLogConvertor;

	@Lock4j(name = "oms:mercado:sync", keys = { "#mclShipmentDTO.id", "#mclShipmentDTO.mclUserId", "#mclShipmentDTO.storeId" })
	public void createOrUpdate(MclShipmentDTO mclShipmentDTO) {
		// 解析成平台单
		MclShipment mclShipment = MclShipmentAppConvertor.toDomain(mclShipmentDTO);
		// 查询有没有
		MclShipment exitsShipment = mclShipmentRepository.getByUniqueIndex(mclShipmentDTO.getId(), mclShipmentDTO.getMclUserId());
		// 更新插入
		if (Objects.nonNull(exitsShipment)) {
			mclShipment.setId(exitsShipment.getId());
			SpringUtil.getBean(MclShipmentService.class).doUpdate(mclShipment);
		}
		else {
			SpringUtil.getBean(MclShipmentService.class).doCreate(mclShipment);
		}
	}

	@Transactional
	@OpLog(snaptSource = MclShipmentAddSnapSource.class, title = "新增美客多运单单", businessType = BusinessType.INSERT,
			businessModule = MclShipmentBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doCreate(MclShipment mclShipment) {
		// 插入运单
		Long id = mclShipmentRepository.insert(mclShipment);
		// 渠道单转销售单状态:未转换
		mclShipment.setTransOmsStatus(TransOmsStatus.NOT_TRANS.getValue());
		// 返回主键增加日志
		return R.success(id);
	}

	@Transactional
	@OpLog(snaptSource = MclShipmentUpdateSnapSource.class, title = "更新美客多订单", businessType = BusinessType.UPDATE,
			businessModule = MclShipmentBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doUpdate(MclShipment mclShipment) {
		// 更新订单
		Long id = mclShipmentRepository.update(mclShipment);
		// 渠道单转销售单状态:待转换
		mclShipment.setTransOmsStatus(TransOmsStatus.TO_BE_TRANS.getValue());
		// 返回主键增加日志
		return R.success(id);
	}

	public MclShipmentLog getLogById(Long id) {
		// 查询订单
		MclShipment mclShipment = mclShipmentRepository.getById(id);
		if (Objects.isNull(mclShipment)) {
			return null;
		}
		return mclShipmentLogConvertor.toLog(mclShipment);
	}
}
