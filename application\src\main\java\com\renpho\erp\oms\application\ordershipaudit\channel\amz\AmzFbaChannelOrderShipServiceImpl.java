package com.renpho.erp.oms.application.ordershipaudit.channel.amz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractMultiChannelOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.ChannelItemFulfillmentInfo;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditItem;
import com.renpho.erp.oms.domain.ordershipaudit.amz.AmzDeliveryInstruction;
import com.renpho.erp.oms.domain.ordershipaudit.amz.AmzDeliveryProperties;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.karma.dto.R;
import io.swagger.client.model.fulfillmentOutbound.*;
import io.swagger.client.model.fulfillmentOutbound.Error;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * fba出库单 <a href=
 * "https://g1c55p.axshare.com/#id=x8w140&p=%E5%88%9B%E5%BB%BA%E8%AE%A2%E5%8D%95&g=1">需求文档</a>
 *
 * <AUTHOR>
 * @since 2025/2/13
 */
@Slf4j
@Component
public class AmzFbaChannelOrderShipServiceImpl extends AbstractMultiChannelOrderShipService {

	private final AmazonClient amazonClient;

	protected AmzFbaChannelOrderShipServiceImpl(SaleOrderQueryService saleOrderQueryService, AmazonClient amazonClient,
			StoreClient storeClient) {
		super(saleOrderQueryService, storeClient);
		this.amazonClient = amazonClient;
	}

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.FBA_MULTI_CHANNEL);
	}

	/**
	 * 创建出库单
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	@Override
	public void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, StoreAuthorizationVo storeAuthorizationVo) {
		CreateFulfillmentOrderRequest createFulfillmentOrderRequest;
		try {
			createFulfillmentOrderRequest = transformRequestParam(orderShipAuditAggRoot, storeAuthorizationVo, saleOrderAggRoot,
					receivingAddress);
		}
		catch (Exception e) {
			log.error("组装亚马逊创建fba出库单参数失败, 发货审核id是{}", orderShipAuditAggRoot.getId(), e);
			// 处理填充参数发生错误
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}

		ShopAccount shopAccount = getShopAccount(storeAuthorizationVo);

		R<CreateFulfillmentOrderResponse> fulfillmentOrder;
		try {
			fulfillmentOrder = amazonClient.createFulfillmentOrder(shopAccount, createFulfillmentOrderRequest);
		}
		catch (Exception e) {
			log.error("调用亚马逊创建出库单接口报错", e);
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}
		handleCreateResult(fulfillmentOrder, orderShipAuditAggRoot);
	}

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, StoreAuthorizationVo storeAuthorizationVo) {
		R<GetFulfillmentOrderResponse> fulfillmentOrder;
		try {
			fulfillmentOrder = amazonClient.getFulfillmentOrder(getShopAccount(storeAuthorizationVo),
					orderShipAuditAggRoot.getChannelStoreOrderId());
		}
		catch (Exception e) {
			log.error("调用亚马逊查询出库单接口报错", e);
			orderShipAuditAggRoot.syncFail(e.getMessage());
			return;
		}
		if (!fulfillmentOrder.isSuccess()) {
			orderShipAuditAggRoot.syncFail(fulfillmentOrder.getMessage());
			return;
		}
		GetFulfillmentOrderResponse getFulfillmentOrderResponse = fulfillmentOrder.getData();
		if (CollUtil.isNotEmpty(getFulfillmentOrderResponse.getErrors())) {
			orderShipAuditAggRoot.syncFail(getFulfillmentOrderResponse.getErrors()
				.stream()
				.map(error -> error.getCode() + "___" + error.getMessage())
				.collect(Collectors.joining("\n")));
			return;
		}
		FulfillmentShipmentList fulfillmentShipments = getFulfillmentOrderResponse.getPayload().getFulfillmentShipments();
		for (FulfillmentShipment fulfillmentShipment : fulfillmentShipments) {
			Map<Integer, FulfillmentShipmentPackage> packageMap = fulfillmentShipment.getFulfillmentShipmentPackage()
				.stream()
				.collect(Collectors.toMap(FulfillmentShipmentPackage::getPackageNumber, Function.identity()));

			// 发货日期
			LocalDateTime shippingDate = DateUtil.parseISOStr(fulfillmentShipment.getShippingDate());
			// 处理相同的sku, 相同的packageNumber, 相同的sellerFulfillmentOrderItemId的情况, 合并数量
			Map<String, FulfillmentShipmentItem> itemMap = new HashMap<>();
			for (FulfillmentShipmentItem fulfillmentShipmentItem : fulfillmentShipment.getFulfillmentShipmentItem()) {
				String key = String.join("_", fulfillmentShipmentItem.getSellerSku(), fulfillmentShipmentItem.getSellerFulfillmentOrderItemId(),
                        String.valueOf(fulfillmentShipmentItem.getPackageNumber()));
				Optional.ofNullable(itemMap.put(key, fulfillmentShipmentItem))
						.ifPresent(last -> fulfillmentShipmentItem.setQuantity(fulfillmentShipmentItem.getQuantity() + last.getQuantity()));
			}
			for (FulfillmentShipmentItem fulfillmentShipmentItem : itemMap.values()) {
				Integer quantity = fulfillmentShipmentItem.getQuantity();
				String sellerFulfillmentOrderItemId = fulfillmentShipmentItem.getSellerFulfillmentOrderItemId();
				FulfillmentShipmentPackage fulfillmentShipmentPackage = packageMap.get(fulfillmentShipmentItem.getPackageNumber());
				if (fulfillmentShipmentPackage != null) {
					// 承运商
					String carrierCode = fulfillmentShipmentPackage.getCarrierCode();
					// 物流单号
					String trackingNumber = fulfillmentShipmentPackage.getTrackingNumber();
					// 预计送达时间
					LocalDateTime estimatedArrivalDate = DateUtil.parseISOStr(StrUtil.blankToDefault(
							fulfillmentShipmentPackage.getEstimatedArrivalDate(), fulfillmentShipment.getEstimatedArrivalDate()));
					// 更新物流状态
					orderShipAuditAggRoot.markShipping(sellerFulfillmentOrderItemId, null, quantity,
							ChannelItemFulfillmentInfo.builder()
								.shippingDate(shippingDate)
								.estimatedArrivalDate(estimatedArrivalDate)
								.carrierName(carrierCode)
								.trackingNo(trackingNumber)
								.build());

				}
			}
		}

		// 更新状态
		orderShipAuditAggRoot.calculateStatus();
	}

	private void handleCreateResult(R<CreateFulfillmentOrderResponse> fulfillmentOrder, OrderShipAuditAggRoot orderShipAuditAggRoot) {
		if (!fulfillmentOrder.isSuccess()) {
			orderShipAuditAggRoot.createFail(fulfillmentOrder.getCode() + "___" + fulfillmentOrder.getMessage());
			return;
		}

		CreateFulfillmentOrderResponse fulfillmentOrderData = fulfillmentOrder.getData();
		ErrorList errors = fulfillmentOrderData.getErrors();
		if (CollUtil.isEmpty(errors)) {
			// 亚马逊的平台订单号创建接口的时候可以指定
			orderShipAuditAggRoot.createSuccess(orderShipAuditAggRoot.getAuditNo());
			return;
		}

		List<String> errorInfos = new ArrayList<>();
		for (Error error : errors) {
			errorInfos.add(error.getCode() + "___" + error.getMessage() + "___" + error.getDetails());
		}
		orderShipAuditAggRoot.createFail(String.join("\n", errorInfos));
	}

	private CreateFulfillmentOrderRequest transformRequestParam(OrderShipAuditAggRoot orderShipAuditAggRoot,
			StoreAuthorizationVo storeAuthorizationVo, SaleOrderAggRoot saleOrderAggRoot, SaleOrderAddressVO receivingAddress) {
		CreateFulfillmentOrderRequest createFulfillmentOrderRequest = new CreateFulfillmentOrderRequest();
		// 亚马逊站点id
		createFulfillmentOrderRequest.setMarketplaceId(storeAuthorizationVo.getAuthorization().getAmzMarketplaceId());
		// 亚马逊商户订单id
		createFulfillmentOrderRequest.setSellerFulfillmentOrderId(orderShipAuditAggRoot.getAuditNo());
		// 原订单号
		createFulfillmentOrderRequest.setDisplayableOrderId(saleOrderAggRoot.getChannelOrderNo());
		// 下单时间
		createFulfillmentOrderRequest.setDisplayableOrderDate(DateUtil.convertToMsUTC(saleOrderAggRoot.getOrderedTime()));
		// 装箱备注
		createFulfillmentOrderRequest.setDisplayableOrderComment(orderShipAuditAggRoot.getAmzOutboundProperties().getRemark());
		// 不要部分发货,没有货就取消
		createFulfillmentOrderRequest.fulfillmentPolicy(FulfillmentPolicy.FILLORKILL);
		// 发货速度
		createFulfillmentOrderRequest.shippingSpeedCategory(
				ShippingSpeedCategory.valueOf(orderShipAuditAggRoot.getAmzOutboundProperties().getShippingSpeedCategory().name()));
		// 地址信息
		createFulfillmentOrderRequest.setDestinationAddress(this.buildAddress(receivingAddress));

		// 非生产环境传hold防止真的发货了
		boolean prodFlag = Arrays.stream(ObjectUtil.defaultIfNull(SpringUtil.getActiveProfiles(), ArrayUtils.EMPTY_STRING_ARRAY))
			.anyMatch("prod"::equalsIgnoreCase);
		FulfillmentAction fulfillmentAction = prodFlag ? FulfillmentAction.SHIP : FulfillmentAction.HOLD;
		createFulfillmentOrderRequest.setFulfillmentAction(fulfillmentAction);

		// 通知邮箱
		if (orderShipAuditAggRoot.getEmail() != null) {
			NotificationEmailList notificationEmailList = new NotificationEmailList();
			notificationEmailList.add(orderShipAuditAggRoot.getEmail().getEmailUrl());
			createFulfillmentOrderRequest.setNotificationEmails(notificationEmailList);
		}

		// 配置快递箱子
		List<FeatureSettings> featureConstraints = new ArrayList<>();
		if (BooleanUtil.isTrue(orderShipAuditAggRoot.getAmzOutboundProperties().getBlockAmzl())) {
			featureConstraints.add(new FeatureSettings().featureName("BLOCK_AMZL")
				.featureFulfillmentPolicy(FeatureSettings.FeatureFulfillmentPolicyEnum.REQUIRED));
		}
		if (BooleanUtil.isTrue(orderShipAuditAggRoot.getAmzOutboundProperties().getBlankBox())) {
			featureConstraints.add(new FeatureSettings().featureName("BLANK_BOX")
				.featureFulfillmentPolicy(FeatureSettings.FeatureFulfillmentPolicyEnum.REQUIRED));
		}
		createFulfillmentOrderRequest.setFeatureConstraints(featureConstraints);

		// JP站点配置 交货说明信息
		if ("JP".equals(storeAuthorizationVo.getSiteCode())) {
			if (orderShipAuditAggRoot.getAmzDeliveryProperties() != null) {
				AmzDeliveryProperties amzDeliveryProperties = orderShipAuditAggRoot.getAmzDeliveryProperties();
				if (amzDeliveryProperties.getDeliveryStartTime() != null && amzDeliveryProperties.getDeliveryEndTime() != null) {
					DeliveryWindow deliveryWindow = new DeliveryWindow();
					deliveryWindow.setStartDate(DateUtil.convertToMsUTC(amzDeliveryProperties.getDeliveryStartTime()));
					deliveryWindow.setEndDate(DateUtil.convertToMsUTC(amzDeliveryProperties.getDeliveryEndTime()));
					createFulfillmentOrderRequest.setDeliveryWindow(deliveryWindow);
				}
				if (amzDeliveryProperties.getAmzDeliveryInstruction() != null) {
					AmzDeliveryInstruction amzDeliveryInstruction = amzDeliveryProperties.getAmzDeliveryInstruction();
					DeliveryPreferences deliveryPreferences = new DeliveryPreferences();
					DropOffLocation deliveryLocation = new DropOffLocation();
					deliveryLocation.setType(DropOffLocation.TypeEnum.valueOf(amzDeliveryInstruction.getValue()));

					if (StringUtils.isNotEmpty(amzDeliveryProperties.getNeighborName())
							&& StringUtils.isNotEmpty(amzDeliveryProperties.getNeighborRoom())) {
						Map<String, String> attributes = new HashMap<>();
						attributes.put("neighborName", amzDeliveryProperties.getNeighborName());
						attributes.put("houseNumber", amzDeliveryProperties.getNeighborRoom());
						deliveryLocation.setAttributes(attributes);
					}
					deliveryPreferences.setDropOffLocation(deliveryLocation);
					createFulfillmentOrderRequest.setDeliveryPreferences(deliveryPreferences);
				}
			}
		}
		
		// 填充订单行信息
		createFulfillmentOrderRequest.items(fillItemItem(orderShipAuditAggRoot.getItems()));

		return createFulfillmentOrderRequest;
	}

	private Address buildAddress(SaleOrderAddressVO receivingAddress) {
		Address address = new Address();
		address.setName(receivingAddress.getRecipientName());
		address.setPostalCode(receivingAddress.getPostalCode());

		String phone = StrUtil.trimStart(receivingAddress.getRecipientPhone());
		phone = StrUtil.removePrefix(phone, "(+1)");
		address.setPhone(phone);

		address.setCountryCode(receivingAddress.getCountryCode());
		address.setStateOrRegion(receivingAddress.getProvince());
		address.setCity(receivingAddress.getCity());

		address.setAddressLine1(receivingAddress.getAddress1());
		address.setAddressLine2(receivingAddress.getAddress2());
		return address;
	}

	private CreateFulfillmentOrderItemList fillItemItem(List<OrderShipAuditItem> items) {
		CreateFulfillmentOrderItemList createFulfillmentOrderItems = new CreateFulfillmentOrderItemList();
		for (OrderShipAuditItem item : items) {
			CreateFulfillmentOrderItem createFulfillmentOrderItem = new CreateFulfillmentOrderItem();
			createFulfillmentOrderItem.sellerSku(item.getChannelMsku());
			createFulfillmentOrderItem.setSellerFulfillmentOrderItemId(item.getChannelStoreOrderItemId());
			createFulfillmentOrderItem.setQuantity(item.getQuantity());

			createFulfillmentOrderItems.add(createFulfillmentOrderItem);
		}
		return createFulfillmentOrderItems;
	}

	private ShopAccount getShopAccount(StoreAuthorizationVo storeAuthorizationVo) {
		String amzSellerId = storeAuthorizationVo.getAuthorization().getAmzSellerId();
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSellerId(amzSellerId);
		return shopAccount;
	}

}
