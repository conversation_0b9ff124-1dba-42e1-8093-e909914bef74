package com.renpho.erp.oms.application.ruleconfig.skuMapping.processor;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingUnique;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingUniqueEnum;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Slf4j
public class AmzVcSpecialLogicProcessor {

    public boolean isAmzVcStore(StoreVo storeVo) {
        return ChannelCode.AMZ_CHANNEL_CODE.equals(storeVo.getSalesChannelCode())
                && StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType());
    }

    public void processAmzVcSpecialLogic(SkuMappingImportExcel importExcel, StoreVo storeVo) {
        if (isAmzVcStore(storeVo)) {
            log.debug("处理AMZ+VC特殊逻辑，店铺: {}", storeVo.getStoreName());
            
            // MSKU：清空
            importExcel.setMsku(null);
            
            // 发货方式：强制平台发货
            importExcel.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
            
            // 统计FBA库存：强制为空
            importExcel.setFbaInventoryCounted(null);
        }
    }

    public String getChannelCodeForUnique(StoreVo storeVo) {
        if (isAmzVcStore(storeVo)) {
            // AMZ+VC使用EBAY的唯一性逻辑（店铺 + ASIN + 发货方式）
            return SaleChannelType.EBAY.getValue();
        }
        return storeVo.getSalesChannelCode();
    }

    public String generateUniqueKey(StoreVo storeVo, SkuMappingImportExcel importExcel) {
        String channelCodeForUnique = getChannelCodeForUnique(storeVo);
        Optional<SkuMappingUniqueEnum> strategyOpt = SkuMappingUniqueEnum.getByChannelCode(channelCodeForUnique);
        
        if (strategyOpt.isEmpty()) {
            log.warn("未找到渠道 {} 的唯一性策略", channelCodeForUnique);
            return "";
        }
        
        SkuMappingUnique unique = new SkuMappingUnique(
                storeVo.getId(), 
                importExcel.getMsku(), 
                importExcel.getAsin(), 
                importExcel.getFulfillmentType()
        );
        
        return strategyOpt.get().generateKey(unique);
    }
}