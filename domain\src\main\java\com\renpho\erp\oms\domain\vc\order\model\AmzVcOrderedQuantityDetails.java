package com.renpho.erp.oms.domain.vc.order.model;

import java.time.LocalDateTime;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订购数量详情值对象 基于Amazon SP-API orderedQuantityDetails模型设计 包含商品数量订购的详细信息
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderedQuantityDetails {

	/**
	 * 更新日期 对应API字段：updatedDate 买方更新行项目数量的日期，必须采用ISO-8601日期/时间格式
	 */
	private LocalDateTime updatedDate;

	/**
	 * 订购数量详情 对应API字段：orderedQuantity 订购数量的详细信息
	 */
	private AmzVcQuantity orderedQuantity;

	/**
	 * 取消数量详情 对应API字段：cancelledQuantity 取消数量的详细信息
	 */
	private AmzVcQuantity cancelledQuantity;

}