package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import com.renpho.erp.apiproxy.mercado.model.ShopAccount;
import com.renpho.erp.apiproxy.mercado.model.items.GetMarketplaceItemsDetailResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.MercadoMarketplaceItem;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.MercadoGlobalItemRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.MercadoMarketItemStatus;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.MercadoClient;
import com.renpho.karma.json.JSONKit;
import com.renpho.karma.web.servlet.mvc.context.StringUtils;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 美客多Listing拉取器
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class MercadoListingPuller extends AbstractListingPullerTemplate {

	private final MercadoClient mercadoClient;
	private final MercadoGlobalItemRepository mercadoGlobalItemRepository;

	/**
	 * 美客多渠道编码
	 * @return String
	 */
	@Override
	public String getSalesChannelCode() {
		return ChannelCode.MCL_CHANNEL_CODE;
	}

	/**
	 * 分批拉取美客多-原始Listing数据
	 * @param storeAuthorization 店铺授权信息
	 * @return List
	 */
	@Override
	protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
		// 全球用户ID
		String mclUserId = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
			.map(StoreAuthorizationVo::getAuthorization)
			.map(StoreAuthorizationVo.Authorization::getMclUserId)
			.orElse(null);
		if (StringUtils.isEmpty(mclUserId)) {
			return null;
		}
		// 查询市场item列表
		List<MercadoMarketplaceItem> marketplaceItemList = mercadoGlobalItemRepository.findByUserId(mclUserId,
				listingSearchQuery.getSearchAfter(), 30, MercadoMarketItemStatus.ACTIVE.getStatus());
		if (CollectionUtils.isEmpty(marketplaceItemList)) {
			return null;
		}
		// 构建店铺账号
		ShopAccount shopAccount = this.buildShopAccount(mclUserId);
		// 原始Listing数据列表
		List<SalesChannelListingSource> listingSourceList = new ArrayList<>();
		marketplaceItemList.forEach(marketplaceItem -> {
			// 查询美客多市场item
			GetMarketplaceItemsDetailResponse marketplaceItemsDetailResponse = mercadoClient.getItemsDetail(shopAccount,
					marketplaceItem.getMarketItemId());
			Optional.ofNullable(marketplaceItemsDetailResponse).ifPresent(i -> {
				// 构建销售渠道原始Listing
				SalesChannelListingSource listingSource = this.buildListingSource(storeAuthorization, i);
				listingSourceList.add(listingSource);
			});
		});
		// 构建Listing分批拉取结果
		return this.buildListingSearchDTO(marketplaceItemList, listingSourceList);
	}

	/**
	 * 构建销售渠道原始Listing
	 * @param storeAuthorization 店铺授权信息
	 * @param itemsDetailResponse 美客多市场item信息
	 * @return SalesChannelListingSource
	 */
	private @NotNull SalesChannelListingSource buildListingSource(StoreAuthorizationDTO storeAuthorization,
			GetMarketplaceItemsDetailResponse itemsDetailResponse) {
		// 构建销售渠道原始Listing
		SalesChannelListingSource listingSource = super.buildListingSource(storeAuthorization);
		// 销售渠道 listing id
		listingSource.setListingId(itemsDetailResponse.getId());
		// 原始报文
		listingSource.setContent(JSONKit.toJSONString(itemsDetailResponse));
		return listingSource;
	}

	/**
	 * 构建Listing分批拉取结果
	 * @param marketplaceItemList 市场item列表
	 * @param listingSourceList 原始Listing数据列表
	 * @return ListingSearchDTO
	 */
	private ListingSearchDTO buildListingSearchDTO(List<MercadoMarketplaceItem> marketplaceItemList,
			List<SalesChannelListingSource> listingSourceList) {
		// 上一页最后一条的id，用于检索下一页
		String setSearchAfterId = marketplaceItemList.stream()
			.map(MercadoMarketplaceItem::getId)
			.max(Integer::compareTo)
			.map(String::valueOf)
			.orElse(null);
		if (StringUtils.isEmpty(setSearchAfterId)) {
			return null;
		}
		ListingSearchDTO listingSearchDTO = new ListingSearchDTO();
		listingSearchDTO.setSearchAfter(setSearchAfterId);
		listingSearchDTO.setListingSourceList(listingSourceList);
		return listingSearchDTO;
	}

	/**
	 * 构建店铺账号
	 * @param mclUserId 全球用户ID
	 * @return ShopAccount
	 */
	private @NotNull ShopAccount buildShopAccount(String mclUserId) {
		ShopAccount shopAccount = new ShopAccount();
		// 全球用户ID
		shopAccount.setUserId(mclUserId);
		return shopAccount;
	}

	@Override
	public boolean isNeedPushMq() {
		return true;
	}
}
