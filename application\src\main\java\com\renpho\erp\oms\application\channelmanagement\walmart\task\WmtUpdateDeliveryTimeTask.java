package com.renpho.erp.oms.application.channelmanagement.walmart.task;


import com.renpho.erp.oms.application.channelmanagement.walmart.service.WmtUpdateDeliveryTimeService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;


/**
 * 沃尔玛更新收货时间
 */
@Component
@AllArgsConstructor
public class WmtUpdateDeliveryTimeTask {
    private final WmtUpdateDeliveryTimeService wmtUpdateDeliveryTimeService;

    @XxlJob("WmtUpdateDeliveryTimeTask")
    public void wmtUpdateDeliveryTimeTask() throws Exception {

        wmtUpdateDeliveryTimeService.wmtUpdateDeliveryTime();
    }

}
