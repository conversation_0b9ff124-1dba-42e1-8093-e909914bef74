package com.renpho.erp.oms.application.b2b.customer.adapter;

import com.renpho.erp.oms.domain.b2b.customer.model.UserInfo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户信息适配器，将基础设施层的OumUserInfoRes适配为领域层的UserInfo接口
 * 这是防腐层(Anti-Corruption Layer)的一部分，隔离领域模型和外部系统
 */
@RequiredArgsConstructor
public class UserInfoAdapter implements UserInfo {
    private final OumUserInfoRes userInfoRes;

    @Override
    public Integer getId() {
        return userInfoRes.getId();
    }
    
    @Override
    public String getName() {
        return userInfoRes.getName();
    }

    @Override
    public String getCode() {
        return userInfoRes.getCode();
    }

    @Override
    public List<String> getLable() {
        return userInfoRes.getLabel();
    }

    /**
     * 将OumUserInfoRes列表转换为UserInfo映射
     * 
     * @param userInfoResList OumUserInfoRes列表
     * @return UserInfo映射，Key为用户ID
     */
    public static Map<Integer, UserInfo> toUserInfoMap(List<OumUserInfoRes> userInfoResList) {
        if (userInfoResList == null) {
            return Map.of();
        }
        
        return userInfoResList.stream()
                .map(UserInfoAdapter::new)
                .collect(Collectors.toMap(UserInfo::getId, Function.identity()));
    }
} 