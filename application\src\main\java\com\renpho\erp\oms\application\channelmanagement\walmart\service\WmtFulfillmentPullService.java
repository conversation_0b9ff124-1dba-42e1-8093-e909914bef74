package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.renpho.erp.apiproxy.walmart.model.ShopAccount;
import com.renpho.erp.apiproxy.walmart.model.WalmartResponse;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusRequest;
import com.renpho.erp.apiproxy.walmart.model.fulfillment.GetFulfillmentOrdersStatusResponse;
import com.renpho.erp.mdm.client.store.command.StoreAuthorizationQuery;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.walmart.task.WmtPullFulfillmentTask;
import com.renpho.erp.oms.domain.config.model.ShopSyncConfig;
import com.renpho.erp.oms.domain.config.model.ShopSyncConfigContent;
import com.renpho.erp.oms.domain.config.repository.ShopSyncConfigRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.common.enums.WmtErroCode;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.common.util.JobShardContext;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.WalmartClient;
import com.renpho.karma.dto.R;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
@Slf4j
public class WmtFulfillmentPullService {
    /**
     * 页大小
     */
    private final Integer LIMIT = 100;
    /**
     * 排序字段
     */
    private final String SORT_BY = "orderDate";

    private final WalmartClient walmartClient;
    private final StoreClient storeClient;
    private final ShopSyncConfigRepository shopSyncConfigRepository;
    private final WmtFulfullmentService wmtFulfullmentService;

    public void pullFulfillment(WmtPullFulfillmentTask.PullFulfillmentParam pullFulfillmentParam) {
        // 获取店铺及授权信息
        StoreAuthorizationQuery storeAuthorizationQuery = new StoreAuthorizationQuery();
        storeAuthorizationQuery.setChannelCode(ChannelCode.WMT_CHANNEL_CODE);
        storeAuthorizationQuery.setStoreType(StoreTypeEnum.SC.getValue());
        // 任务自动执行时填充分片参数
        Optional.ofNullable(JobShardContext.getShardInfo()).ifPresent(jobShardInfo -> {
            storeAuthorizationQuery.setShardIndex(jobShardInfo.getShardIndex());
            storeAuthorizationQuery.setShardTotal(jobShardInfo.getShardTotal());
        });

        List<StoreAuthorizationVo> storeAuthorizationList = storeClient.getShardingStoreAuthorizations(storeAuthorizationQuery);
        if (CollectionUtil.isEmpty(storeAuthorizationList)) {
            log.info("查询店铺授权信息为空，任务结束");
            return;
        }

        for (StoreAuthorizationVo storeAuthorization : storeAuthorizationList) {
            processSingleStore(storeAuthorization, pullFulfillmentParam);
        }
    }

    /**
     * 单个店铺拉取
     *
     * @param storeAuthorization
     * @param pullFulfillmentParam
     */
    private void processSingleStore(StoreAuthorizationVo storeAuthorization, WmtPullFulfillmentTask.PullFulfillmentParam pullFulfillmentParam) {
        try {
            if (Objects.nonNull(pullFulfillmentParam)) {
                // 手动输入参数获取
                if (pullFulfillmentParam.getStoreId().equals(storeAuthorization.getId())) {
                    if (CollectionUtil.isNotEmpty(pullFulfillmentParam.getOrderNums())) {
                        pullFulfillmentParam.getOrderNums().forEach(orderNum -> {
                            doPull(storeAuthorization, null, null, orderNum,
                                    null);
                        });
                    } else {
                        doPull(storeAuthorization, pullFulfillmentParam.getStartDateUTC(), pullFulfillmentParam.getEndDateUTC(), null,
                                null);
                    }
                }
            } else {
                // 定时任务自动获取
                ShopSyncConfig shopSyncConfig = shopSyncConfigRepository.getConfigByChannelAndStore(ChannelCode.WMT_CHANNEL_CODE, storeAuthorization.getId());
                if (Objects.nonNull(shopSyncConfig)) {
                    Pair<LocalDateTime, LocalDateTime> timeRange = defaultcalPullTimeRange(shopSyncConfig);
                    doPull(storeAuthorization, timeRange.getFirst(), timeRange.getSecond(), null, shopSyncConfig);
                } else {
                    log.warn("店铺{}的同步配置为空，跳过拉单", storeAuthorization.getStoreName());
                }
            }
        } catch (Exception ex) {
            log.error("拉取店铺{}订单时发生错误: {}", storeAuthorization.getStoreName(), ex.getMessage(), ex);
        }
    }

    private Pair<LocalDateTime, LocalDateTime> defaultcalPullTimeRange(ShopSyncConfig shopSyncConfig) {
        // 计算拉取时间
        // 获取当前UTC时间(服务器所在时间为utc时间)
        LocalDateTime now = LocalDateTime.now();
        ShopSyncConfigContent configContent = shopSyncConfig.getConfigContent();
        // 抓取订单开始时间 = 上次抓取订单的结束时间
        LocalDateTime startTime = configContent.getLatestUpdateTime();
        // 抓取订单结束的时间 = 开始时间 + 时间跨度
        LocalDateTime endTime = startTime.plusMinutes(configContent.getLastUpdatedBeforeCurrOffset());
        log.info("Initial startTime: {}", startTime);
        log.info("Initial endTime: {}", endTime);
        // 处理时间交叉

        // 开始时间往前位移
        startTime = startTime.minusMinutes(configContent.getCrossStartTimeOffset());
        log.info("Adjusted startTime with crossStartTimeOffset: {}", startTime);
        // 抓单结束时间大于当前时间
        if (endTime.isAfter(LocalDateTime.now())) {
            log.info("startTime is after endTime, correcting...");
            // 抓单结束时间=当前时间 - 设置结束位移交叉时间
            endTime = now.minusMinutes(configContent.getCrossEndTimeOffset());
            // 如果 -位移时间 之后抓单结束时间小于抓单开始时间的话，抓单结束时间等于抓单开始时间
            if (endTime.isBefore(startTime)) {
                endTime = startTime;
            }
        }
        log.info("Final time range: startTime = {}, endTime = {}", startTime, endTime);
        return Pair.of(startTime, endTime);
    }

    private void doPull(StoreAuthorizationVo storeAuthorizationVo, LocalDateTime createTimeAfter,
                        LocalDateTime createTimeBefore, String orderNum, ShopSyncConfig shopSyncConfig) {
        List<GetFulfillmentOrdersStatusResponse.Order> orderList = Lists.newArrayList();
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());
        shopAccount.setShopCode(storeAuthorizationVo.getAuthorization().getWmtShopId());
        // 构建查询参数
        GetFulfillmentOrdersStatusRequest request = new GetFulfillmentOrdersStatusRequest();
        request.setLimit(LIMIT.toString());
        request.setSortBy(SORT_BY);
        request.setOffset("0");
        // 创建时间起始
        request.setFromOrderDate(DateUtil.convertToMsUTC(createTimeAfter));
        request.setToOrderDate(DateUtil.convertToMsUTC(createTimeBefore));
        request.setOrderNumber(orderNum);
        if (pull(orderList, shopAccount, request, shopSyncConfig)) {
            if (CollectionUtil.isNotEmpty(orderList)) {
                // 去重
                List<GetFulfillmentOrdersStatusResponse.Order> distinctList = orderList.stream()
                        .collect(Collectors.toMap(
                                GetFulfillmentOrdersStatusResponse.Order::getSellerOrderId, // 以sellerOrderId为key
                                wmt -> wmt, // 保留第一个遇到的元素
                                (existing, replacement) -> existing // 如果重复，保留第一个
                        ))
                        .values()
                        .stream()
                        .collect(Collectors.toList());

                wmtFulfullmentService.batchInsertOrUpdate(distinctList, shopAccount.getShopCode(), shopAccount.getSiteCode(), storeAuthorizationVo.getId());
            }
            if (Objects.nonNull(shopSyncConfig)) {
                // 记录此次拉取日志
                shopSyncConfig.getConfigContent().setLatestUpdateTime(createTimeBefore);
                shopSyncConfigRepository.update(shopSyncConfig);
            }
        }

    }

    private Boolean pull(List<GetFulfillmentOrdersStatusResponse.Order> orderList, ShopAccount shopAccount, GetFulfillmentOrdersStatusRequest request,
                         ShopSyncConfig shopSyncConfig) {
        Boolean successFlag = Boolean.TRUE;
        do {
            R<GetFulfillmentOrdersStatusResponse> fulfillmentOrdersStatusResponse = walmartClient.getFulfillmentOrdersStatus(shopAccount, request);
            log.info("查询沃尔玛履约单，返回信息为{}", JsonUtils.toJson(fulfillmentOrdersStatusResponse));
            // 失败停止拉取
            if (!fulfillmentOrdersStatusResponse.isSuccess()) {
                log.error("查询沃尔玛履约单，失败原因为{}", fulfillmentOrdersStatusResponse.getMessage());
                if (Objects.isNull(fulfillmentOrdersStatusResponse.getData()) && Objects.nonNull(shopSyncConfig)) {
                    // 记录日志
                    shopSyncConfig.setExcCause(fulfillmentOrdersStatusResponse.getMessage());
                    shopSyncConfig.setExcType(fulfillmentOrdersStatusResponse.getCode());
                    shopSyncConfigRepository.update(shopSyncConfig);
                    successFlag = Boolean.FALSE;
                    break;
                }
                // 查看翻页是否有误
                if (CollectionUtil.isNotEmpty(fulfillmentOrdersStatusResponse.getData().getErrors())) {
                    List<WalmartResponse.Error> errorList = fulfillmentOrdersStatusResponse.getData().getErrors();
                    if (errorList.stream().anyMatch(error -> WmtErroCode.PAGE_OUT_OF_RANGE.getCode().equals(error.getCode()))) {
                        // 重新构造分页查询
                        // 再次查询 设置fromOrderDate为最后一个订单的orderDate减去1秒，重置offset计数器
                        // 解析字符串为 ZonedDateTime，直接使用 Z 表示 UTC 时区
                        ZonedDateTime zonedDateTime = ZonedDateTime.parse(orderList.get(orderList.size() - 1).getOrderDate(), DateTimeFormatter.ISO_DATE_TIME);
                        // 减去 1 秒
                        ZonedDateTime newZonedDateTime = zonedDateTime.minusSeconds(1);

                        // 格式化回字符串
                        String newDateStr = newZonedDateTime.format(DateTimeFormatter.ISO_DATE_TIME);
                        request.setOffset("0");
                        request.setFromOrderDate(newDateStr);
                        continue;

                    } else if (errorList.stream().anyMatch(error -> WmtErroCode.PAGE_EMPTY.getCode().equals(error.getCode()))) {
                        log.info("店铺{}已分页完成，退出查询", shopAccount.getShopCode());
                        break;
                    } else {
                        // 记录日志
                        if (Objects.nonNull(shopSyncConfig)) {
                            shopSyncConfig.setExcCause(JSONKit.toJSONString(errorList));
                            shopSyncConfigRepository.update(shopSyncConfig);
                        }
                        successFlag = Boolean.FALSE;
                        break;
                    }
                }

            }

            if (Objects.isNull(fulfillmentOrdersStatusResponse.getData())) {
                break;
            }

            List<GetFulfillmentOrdersStatusResponse.Order> orders = fulfillmentOrdersStatusResponse.getData().getPayload();
            if (CollectionUtil.isEmpty(orders)) {
                break;
            }
            orderList.addAll(orders);
            // 翻页
            request.setOffset((Integer.valueOf(request.getOffset()) + LIMIT) + "");
        }
        while (true);
        return successFlag;
    }

}
