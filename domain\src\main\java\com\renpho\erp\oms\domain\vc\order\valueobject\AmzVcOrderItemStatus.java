package com.renpho.erp.oms.domain.vc.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单商品状态值对象 基于Amazon SP-API OrderItemStatus模型设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.OrderItemStatus
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderItemStatus {

	/**
	 * 商品序号 对应API字段：itemSequenceNumber 采购订单上商品的编号，第一个商品为1，第二个为2，以此类推
	 */
	private String itemSequenceNumber;

	/**
	 * 买方商品标识符 对应API字段：buyerProductIdentifier 买方的标准识别号（ASIN）
	 */
	private String buyerProductIdentifier;

	/**
	 * 供应商商品标识符 对应API字段：vendorProductIdentifier 供应商选择的商品标识
	 */
	private String vendorProductIdentifier;

	/**
	 * 净成本 对应API字段：netCost 每个商品或重量单位的净成本
	 */
	private AmzVcMoney netCost;

	/**
	 * 标价 对应API字段：listPrice 每个商品或重量单位的标价
	 */
	private AmzVcMoney listPrice;

	/**
	 * 订购数量 对应API字段：orderedQuantity
	 */
	private AmzVcOrderedQuantity orderedQuantity;

	/**
	 * 确认状态 对应API字段：acknowledgementStatus
	 */
	private AmzVcAcknowledgementStatus acknowledgementStatus;

	/**
	 * 接收状态 对应API字段：receivingStatus
	 */
	private AmzVcReceivingStatus receivingStatus;

	// ========== 业务方法 ==========

}
