package com.renpho.erp.oms.domain.vc.order.valueobject;

import java.math.BigDecimal;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC订单商品状态值对象 基于Amazon SP-API OrderItemStatus模型设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.OrderItemStatus
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcOrderItemStatus {

	/**
	 * 商品序号 对应API字段：itemSequenceNumber 采购订单上商品的编号，第一个商品为1，第二个为2，以此类推
	 */
	private String itemSequenceNumber;

	/**
	 * 买方商品标识符 对应API字段：buyerProductIdentifier 买方的标准识别号（ASIN）
	 */
	private String buyerProductIdentifier;

	/**
	 * 供应商商品标识符 对应API字段：vendorProductIdentifier 供应商选择的商品标识
	 */
	private String vendorProductIdentifier;

	/**
	 * 净成本 对应API字段：netCost 每个商品或重量单位的净成本
	 */
	private AmzVcMoney netCost;

	/**
	 * 标价 对应API字段：listPrice 每个商品或重量单位的标价
	 */
	private AmzVcMoney listPrice;

	/**
	 * 订购数量 对应API字段：orderedQuantity
	 */
	private AmzVcQuantity orderedQuantity;

	/**
	 * 确认状态 对应API字段：acknowledgementStatus
	 */
	private AmzVcAcknowledgementStatus acknowledgementStatus;

	/**
	 * 接收状态 对应API字段：receivingStatus
	 */
	private AmzVcReceivingStatus receivingStatus;

	// ========== 业务方法 ==========

	/**
	 * 检查商品是否已接收
	 * @return boolean
	 */
	public boolean isReceived() {
		return receivingStatus != null && receivingStatus.isReceived();
	}

	/**
	 * 检查商品是否部分接收
	 * @return boolean
	 */
	public boolean isPartiallyReceived() {
		return receivingStatus != null && receivingStatus.isPartiallyReceived();
	}

	/**
	 * 获取净成本金额
	 * @return BigDecimal
	 */
	public BigDecimal getNetCostAmount() {
		return netCost != null ? netCost.getAmount() : null;
	}

	/**
	 * 获取标价金额
	 * @return BigDecimal
	 */
	public BigDecimal getListPriceAmount() {
		return listPrice != null ? listPrice.getAmount() : null;
	}

	/**
	 * 获取订购数量值
	 * @return Integer
	 */
	public Integer getOrderedQuantityAmount() {
		return orderedQuantity != null ? orderedQuantity.getAmount() : null;
	}

	/**
	 * 获取订购数量单位
	 * @return String
	 */
	public String getOrderedQuantityUnit() {
		return orderedQuantity != null ? orderedQuantity.getUnitOfMeasure() : null;
	}

	/**
	 * 检查是否有价格信息
	 * @return boolean
	 */
	public boolean hasPriceInfo() {
		return netCost != null || listPrice != null;
	}

	/**
	 * 检查是否有数量信息
	 * @return boolean
	 */
	public boolean hasQuantityInfo() {
		return orderedQuantity != null;
	}

	/**
	 * 获取商品状态摘要
	 * @return String
	 */
	public String getStatusSummary() {
		StringBuilder summary = new StringBuilder();
		summary.append("Item ").append(itemSequenceNumber);

		if (acknowledgementStatus != null) {
			summary.append(", Ack: ").append(acknowledgementStatus.getConfirmationStatus());
		}

		if (receivingStatus != null) {
			summary.append(", Recv: ").append(receivingStatus.getReceiveStatus());
		}

		return summary.toString();
	}

}
