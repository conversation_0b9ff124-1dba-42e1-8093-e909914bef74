package com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor;

import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.SkuMappingVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMapping;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface SkuMappingVOConvertor {
	SkuMapping toDomain(SkuMappingVO param);

	SkuMappingVO toVO(SkuMapping param);
}
