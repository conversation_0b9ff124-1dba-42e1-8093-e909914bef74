package com.renpho.erp.oms.application.salemanagement.converter;

import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.domain.salemanagement.*;
import com.renpho.erp.oms.domain.salemanagement.cmd.SaleOrderCreateCmd;
import com.renpho.erp.oms.domain.upload.FileUploadDetail;
import com.renpho.erp.oms.domain.upload.FileUploadRecordAggRoot;
import com.renpho.erp.oms.domain.upload.FileUploadType;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDTO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

public class SaleOrderCreateConvertor {
    public static SaleOrderAggRoot convert(SaleOrderCreateCmd saleOrderCreateCmd, Map<String, PurchaseProductDTO> purchaseProductMap, StoreVo storeVo, boolean needShipped, OrderSource orderSource) {
        // 构建商品
        List<SaleOrderItem> saleOrderItemList = saleOrderCreateCmd.getSaleOrderItemCreateCmdList().stream().map(saleOrderItemCreateCmd -> {
            Product.ProductBuilder product = Product.builder();
            // 采购sku信息
            PurchaseProductDTO purchaseProductDTO = purchaseProductMap.get(saleOrderItemCreateCmd.getPsku());
            product.length(purchaseProductDTO.getLength());
            product.psku(saleOrderItemCreateCmd.getPsku());
            product.fnsku(saleOrderItemCreateCmd.getFnSku());
            product.width(purchaseProductDTO.getWidth());
            product.height(purchaseProductDTO.getHeight());
            product.weight(purchaseProductDTO.getWeight());
            product.imageId(purchaseProductDTO.getImageId());
            // 商品履约信息
            SaleOrderItemFulfillment saleOrderItemFulfillment = null;
            if (Objects.nonNull(saleOrderCreateCmd.getSaleOrderFulfillmentCreateCmd())) {
                SaleOrderCreateCmd.SaleOrderFulfillmentCreateCmd saleOrderFulfillmentCreateCmd = saleOrderCreateCmd.getSaleOrderFulfillmentCreateCmd();
                saleOrderItemFulfillment = SaleOrderItemFulfillment.builder().
                        carrierName(saleOrderFulfillmentCreateCmd.getCarrier())
                        .warehouseId(saleOrderFulfillmentCreateCmd.getWarehouseId())
                        .warehouseCode(saleOrderFulfillmentCreateCmd.getWarehouseCode())
                        .trackingNo(saleOrderFulfillmentCreateCmd.getTrackingNo())
                        .uploadTrackStatus(UploadTrackStatus.NOT_REQUIRED)
                        .shippedTime(saleOrderFulfillmentCreateCmd.getShippedTime())
                        .deliveredTime(saleOrderFulfillmentCreateCmd.getDeliveredTime()).build();
            }

            return SaleOrderItem.builder().quantityShipment(saleOrderItemCreateCmd.getQuantityShipment())
                    .quantityOrdered(saleOrderItemCreateCmd.getQuantityShipment())
                    .currency(saleOrderCreateCmd.getCurrency())
                    .productPrice(saleOrderItemCreateCmd.getProductPrice())
                    .productTax(saleOrderItemCreateCmd.getProductTax())
                    .productDiscount(saleOrderItemCreateCmd.getProductDiscount())
                    .productDiscountTax(saleOrderItemCreateCmd.getProductDiscountTax())
                    .freightAmount(saleOrderItemCreateCmd.getFreightAmount())
                    .freightTax(saleOrderItemCreateCmd.getFreightTax())
                    .freightDiscount(saleOrderItemCreateCmd.getFreightDiscount())
                    .freightDiscountTax(saleOrderItemCreateCmd.getFreightDiscountTax())
                    .totalAmount(saleOrderItemCreateCmd.getTotalAmount())
                    .status(OrderItemStatus.TO_BE_SHIPPED)
                    .itemFulfillment(saleOrderItemFulfillment)
                    .product(product.build()).build();
        }).collect(Collectors.toList());
        // 构建订单地址
        SaleOrderAddress saleOrderAddress = SaleOrderAddress.builder().recipientName(saleOrderCreateCmd.getRecipientName())
                .recipientPhone(saleOrderCreateCmd.getRecipientPhone())
                .postalCode(saleOrderCreateCmd.getPostalCode())
                .email(saleOrderCreateCmd.getEmail())
                .countryCode(saleOrderCreateCmd.getCountryCode())
                .province(saleOrderCreateCmd.getProvince())
                .city(saleOrderCreateCmd.getCity())
                .address1(saleOrderCreateCmd.getAddress1())
                .address2(saleOrderCreateCmd.getAddress2())
                .build();
        // 店铺信息
        Store store = Store.builder().storeId(storeVo.getId()).storeName(storeVo.getStoreName())
                // 公司
                .companyId(Integer.valueOf(storeVo.getCompanyId()))
                // 站点
                .siteCode(storeVo.getSite())
                .channelCode(storeVo.getSalesChannelCode())
                .channelName(storeVo.getSalesChannel())
                // 平台仓
                .warehouseId(storeVo.getChannelWarehouseLastId())
                .warehouseCode(storeVo.getChannelWarehouseCode())
                .build();
        // 订单履约
        SaleOrderFulfillment saleOrderFulfillment = SaleOrderFulfillment.builder()
                // 最晚发货时间，平台要求履约时效
                .fulfillmentType(FulfillmentType.MERCHANT)
                .outboundNo(needShipped ? saleOrderCreateCmd.getSaleOrderFulfillmentCreateCmd().getWarehouseOrderNo() : null)
                // 自发货待上传
                .uploadTrackStatus(UploadTrackStatus.NOT_REQUIRED)
                .build();

        // 构建订单
        SaleOrderAggRoot saleOrderAggRoot = SaleOrderAggRoot.builder()
                .orderStatus(needShipped ? OrderStatus.PENDING : OrderStatus.DRAFT)
                .orderType(OrderType.STANDARD)
                .orderSource(orderSource)
                .channelOrderNo(saleOrderCreateCmd.getChannelOrderNo())
                .referOrderNo(saleOrderCreateCmd.getReferOrderNo())
                .sampleMark(OrderIdentification.isSampleOrder(saleOrderCreateCmd.getOrderIdentification()) ? OrderSampleMark.SAMPLE : null)
                .currency(saleOrderCreateCmd.getCurrency())
                .orderedTime(saleOrderCreateCmd.getOrderedTime())
                .paidTime(saleOrderCreateCmd.getPaidTime())
                .store(store)
                .fulfillment(saleOrderFulfillment)
                .address(saleOrderAddress)
                .items(saleOrderItemList)
                .build();
        // 计算订单重量体积
        saleOrderAggRoot.getFulfillment().calculateSizeAndWeight(saleOrderAggRoot.getItems());
        // 计算金额
        saleOrderAggRoot.calculateAmount();
        return saleOrderAggRoot;
    }

    public static List<SaleOrderCreateCmd> convertListToSaleOrderCreateCmdList(FileUploadType fileUploadType, Map<String, List<FileUploadDetail>> fileDetailGroup, Map<String, StoreVo> storeVoMap, Map<String, WarehouseVo> warehouseVoMap) {
        return fileDetailGroup.values().stream().map(fileDetailList -> {
            return convertByFileDetailGroup(fileUploadType, fileDetailList, storeVoMap, warehouseVoMap);
        }).collect(Collectors.toList());
    }

    private static SaleOrderCreateCmd convertByFileDetailGroup(FileUploadType fileUploadType, List<FileUploadDetail> fileDetailGroup, Map<String, StoreVo> storeVoMap, Map<String, WarehouseVo> warehouseVoMap) {
        FileUploadDetail firstDetail = fileDetailGroup.get(0);

        // 商品明细列表构建
        List<SaleOrderCreateCmd.SaleOrderItemCreateCmd> saleOrderItemCreateCmdList = new ArrayList<>();
        for (FileUploadDetail fileUploadDetail : fileDetailGroup) {
            SaleOrderCreateCmd.SaleOrderItemCreateCmd item = new SaleOrderCreateCmd.SaleOrderItemCreateCmd();
            item.setPsku(fileUploadDetail.getPsku());
            item.setFnSku(fileUploadDetail.getFnSku());
            item.setQuantityShipment(Integer.valueOf(fileUploadDetail.getQuantityShipment()));

            // 金额
            item.setProductPrice(Objects.nonNull(fileUploadDetail.getProductPrice()) ?
                    fileUploadDetail.getProductPrice() : BigDecimal.ZERO);
            item.setProductTax(Objects.nonNull(fileUploadDetail.getProductTax()) ? fileUploadDetail.getProductTax() : BigDecimal.ZERO);
            item.setProductDiscount(Objects.nonNull(fileUploadDetail.getProductDiscount()) ? fileUploadDetail.getProductDiscount() : BigDecimal.ZERO);
            item.setProductDiscountTax(Objects.nonNull(fileUploadDetail.getProductDiscountTax()) ? fileUploadDetail.getProductDiscountTax() : BigDecimal.ZERO);
            item.setFreightAmount(Objects.nonNull(fileUploadDetail.getFreightAmount()) ? fileUploadDetail.getFreightAmount() : BigDecimal.ZERO);
            item.setFreightTax(Objects.nonNull(fileUploadDetail.getFreightTax()) ? fileUploadDetail.getFreightTax() : BigDecimal.ZERO);
            item.setFreightDiscount(Objects.nonNull(fileUploadDetail.getFreightDiscount()) ? fileUploadDetail.getFreightDiscount() : BigDecimal.ZERO);
            item.setFreightDiscountTax(Objects.nonNull(fileUploadDetail.getFreightDiscountTax()) ? fileUploadDetail.getFreightDiscountTax() : BigDecimal.ZERO);

            saleOrderItemCreateCmdList.add(item);
        }

        // 履约信息构建
        SaleOrderCreateCmd.SaleOrderFulfillmentCreateCmd fulfillment = null;
        if (fileUploadType == FileUploadType.SHIPPED_ORDER) {
            fulfillment = new SaleOrderCreateCmd.SaleOrderFulfillmentCreateCmd();
            WarehouseVo warehouse = warehouseVoMap.get(firstDetail.getWarehouseCode());
            fulfillment.setWarehouseCode(warehouse.getCode());
            fulfillment.setWarehouseId(warehouse.getId());
            fulfillment.setWarehouseOrderNo(firstDetail.getWarehouseOrderNo());
            fulfillment.setCarrier(firstDetail.getCarrier());
            fulfillment.setTrackingNo(firstDetail.getTrackingNo());
            fulfillment.setDeliveredTime(firstDetail.getDeliveredTime());
            fulfillment.setShippedTime(firstDetail.getShippedTime());
        }


        // 基础信息
        SaleOrderCreateCmd cmd = new SaleOrderCreateCmd();
        StoreVo store = storeVoMap.get(firstDetail.getStoreName());
        cmd.setStoreId(store.getId());
        cmd.setChannelCode(store.getSalesChannelCode());
        cmd.setChannelOrderNo(firstDetail.getChannelOrderNo());
        cmd.setReferOrderNo(firstDetail.getReferOrderNo());
        cmd.setOrderIdentification(Objects.nonNull(firstDetail.getOrderIdentification()) ? Arrays.asList(Integer.valueOf(firstDetail.getOrderIdentification())) : null);
        cmd.setCurrency(firstDetail.getCurrency());
        cmd.setOrderedTime(firstDetail.getOrderedTime());
        cmd.setPaidTime(firstDetail.getPaidTime());
        cmd.setRecipientName(firstDetail.getRecipientName());
        cmd.setRecipientPhone(firstDetail.getRecipientPhone());
        cmd.setPostalCode(firstDetail.getPostalCode());
        cmd.setEmail(firstDetail.getEmail());
        cmd.setCountryCode(firstDetail.getCountryCode());
        cmd.setProvince(firstDetail.getProvince());
        cmd.setCity(firstDetail.getCity());
        cmd.setAddress1(firstDetail.getAddress1());
        cmd.setAddress2(firstDetail.getAddress2());


        cmd.setSaleOrderItemCreateCmdList(saleOrderItemCreateCmdList);
        cmd.setSaleOrderFulfillmentCreateCmd(fulfillment);


        return cmd;

    }

}
