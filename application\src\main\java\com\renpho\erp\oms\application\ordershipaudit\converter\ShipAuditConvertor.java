package com.renpho.erp.oms.application.ordershipaudit.converter;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipment;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodeLanguagePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.LogisticsShipmentPO;
import org.apache.commons.lang.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.ReportingPolicy;
import org.springframework.context.i18n.LocaleContextHolder;

import com.renpho.erp.apiproxy.ebay.model.logistics.CreateShippingQuoteResponse;
import com.renpho.erp.apiproxy.temu.model.logistics.GetShippingServicesData;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseLanguageVo;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.vo.SelfWarehouseVO;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * @desc:
 * @time: 2025-04-02 15:01:27
 * @author: Alina
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface ShipAuditConvertor {
	@Mapping(target = "warehouseCode", source = "code")
	@Mapping(target = "thirdPartWarehouseCode", source = "thirdWarehouseCode")
	@Mapping(target = "warehouseName", expression = "java(getLocalizedWarehouseName(warehouseVo))")
	SelfWarehouseVO toSelfWarehouseVO(WarehouseVo warehouseVo);

	/**
	 * 将 OnlineChannel 转换为 ShippingServicesVO
	 */
	@Mapping(target = "carrier", source = "shippingCompanyName")
	@Mapping(target = "carrierCode", source = "shippingCompanyName")
	@Mapping(target = "carrierService", source = "shipLogisticsType")
	@Mapping(target = "shippingCost", source = "estimatedAmount")
	@Mapping(target = "shippingTime", source = "estimatedText", qualifiedByName = "extractShippingTime")
	@Mapping(target = "shipChannelId", source = "channelId")
	@Mapping(target = "shipCompanyId", source = "shipCompanyId")
	@Mapping(target = "currency", source = "estimatedCurrencyCode")
	@Mapping(target = "amount", expression = "java(toAmount(onlineChannel))")
	ShippingServicesVO toShippingServicesVO(GetShippingServicesData.OnlineChannel onlineChannel);

	default BigDecimal toAmount(GetShippingServicesData.OnlineChannel onlineChannel) {
		String amount = onlineChannel.getEstimatedAmount() != null ? onlineChannel.getEstimatedAmount().replaceAll("[^\\d.]", "") : "";
		if (StringUtils.isBlank(amount)) {
			return null;
		}
		return new BigDecimal(amount);
	}

	/**
	 * 提取时效（如 1-6 work days）
	 * @param estimatedText 预估信息 类似 "estimatedText": "$0.10,USD,1-6 work days",
	 * @return 时效
	 */
	@Named("extractShippingTime")
	default String extractShippingTime(String estimatedText) {
		if (estimatedText == null) {
			return "";
		}
		// 先尝试按; 或 , 分割
		String[] parts = estimatedText.split("[;,]");
		if (parts.length >= 3) {
			return parts[2];
		}
		return "";
	}

	default String getLocalizedWarehouseName(WarehouseVo warehouseVo) {
		String language = Optional.of(LocaleContextHolder.getLocale()).map(java.util.Locale::toLanguageTag).orElse("zh-CN");
		return ObjectUtil.defaultIfNull(warehouseVo.getLanguages(), Collections.<WarehouseLanguageVo> emptyList())
			.stream()
			.filter(warehouseLanguageVo -> StrUtil.equalsIgnoreCase(language, warehouseLanguageVo.getLanguage()))
			.findFirst()
			.map(WarehouseLanguageVo::getName)
			.orElse("");
	}

	/**
	 * 将 FulfillmentServiceCodePO 转换为 ShippingServicesVO
	 */
	@Mapping(target = "carrier", source = "fulfillmentServiceCodePo.carrierName")
	@Mapping(target = "carrierService", source = "newCarrierService")
	@Mapping(target = "fulfillmentServiceId", source = "fulfillmentServiceCodePo.id")
	@Mapping(target = "carrierCode", source = "fulfillmentServiceCodePo.carrierCode")
	@Mapping(target = "packageTypeCode", source = "fulfillmentServiceCodePo.packageTypeCode")
	@Mapping(target = "carrierServiceCode", source = "fulfillmentServiceCodePo.carrierServiceCode")
	ShippingServicesVO toShippingServicesVO(FulfillmentServiceCodePO fulfillmentServiceCodePo, String newCarrierService);

	@Mapping(target = "carrier", source = "shippingCarrierName")
	@Mapping(target = "carrierService", source = "shippingServiceName")
	@Mapping(target = "carrierCode", source = "shippingCarrierCode")
	@Mapping(target = "ebayRateId", source = "rateId")
	@Mapping(target = "latestDeliveryTime", expression = "java(parseDeliveryTime(rate.getMaxEstimatedDeliveryDate()))")
	@Mapping(target = "shippingCost", expression = "java(parseShippingCost(rate))")
	@Mapping(target = "currency", expression = "java(rate.getBaseShippingCost() != null ? rate.getBaseShippingCost().getCurrency() : \"\")")
	@Mapping(target = "amount", expression = "java(toAmount(rate))")
	ShippingServicesVO toShippingServicesVO(CreateShippingQuoteResponse.Rate rate);

	default BigDecimal toAmount(CreateShippingQuoteResponse.Rate rate) {
		String amount = rate.getBaseShippingCost() != null ? rate.getBaseShippingCost().getValue() : "";
		if (StringUtils.isBlank(amount)) {
			return null;
		}
		return new BigDecimal(amount);
	}

	default LocalDateTime parseDeliveryTime(String maxEstimatedDeliveryDate) {
		return DateUtil.parse(maxEstimatedDeliveryDate);
	}

	default String parseShippingCost(CreateShippingQuoteResponse.Rate rate) {
		if (rate == null || rate.getBaseShippingCost() == null) {
			return "";
		}
		return String.format("%s %s", ObjectUtil.defaultIfNull(rate.getBaseShippingCost().getCurrency(), ""),
				ObjectUtil.defaultIfNull(rate.getBaseShippingCost().getValue(), ""));
	}

	LogisticsShipment toLogisticsShipment(LogisticsShipmentPO lastLogisticsShipmentPo);
}
