package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.tiktok.model.product.SearchProductsData;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;

/**
 * Tiktok Listing解析器
 *
 * <AUTHOR>
 */
@Component
public class TiktokListingParser extends AbstractListingParserTemplate<SearchProductsData.Product> {

	/**
	 * Tiktok 渠道编码
	 *
	 * @return String
	 */
	@Override
	public String getSalesChannelCode() {
		return ChannelCode.TT_CHANNEL_CODE;
	}

	/**
	 * 解析 Tiktok 原始Listing
	 *
	 * @param source 销售渠道原始Listing
	 * @param product Tiktok Listing
	 * @return List
	 */
	@Override
	protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, SearchProductsData.Product product) {
		if (Objects.isNull(product) || CollectionUtils.isEmpty(product.getSkus())) {
			return List.of();
		}
		List<SearchProductsData.Sku> skuList = product.getSkus();
		// 销售渠道Listing
		List<SalesChannelListing> listingList = new ArrayList<>();
		// 销售sku集合
		Set<String> sellerSkuSet = skuList.stream().map(SearchProductsData.Sku::getSeller_sku).collect(Collectors.toSet());
		// 获取sku映射Map key为 店铺id+销售SKU，value为List<SkuMapping>
		Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), sellerSkuSet);
		// 采购sku图片Map，key为采购sku，value为产品封面图片
		Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(
				skuMappingListMap.values().stream().flatMap(Collection::stream).toList());
		// 遍历SKU列表
		skuList.forEach(sku -> {
			// 销售SKU为空的，不需要解析
			String sellerSku = sku.getSeller_sku();
			if (StringUtils.isBlank(sellerSku)) {
				return;
			}
			// 构建销售渠道Listing
			SalesChannelListing listing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, sellerSku,
					source.getStoreId());
			// item编码
			listing.setItem(product.getId());
			// skuId
			listing.setSkuId(sku.getId());
			// 销售SKU
			listing.setMsku(sellerSku);
			// 标题
			listing.setTitle(product.getTitle());
			// Item状态
			listing.setStatus(product.getStatus());
			// link（https://www.tiktok.com/view/product/{product_id}，product_id:
			// 外面的产品id，不是skus里的id）
			Optional.ofNullable(product.getId())
				.ifPresent(productId -> listing.setLink("https://www.tiktok.com/view/product/" + productId));
			// 币种
			listing.setCurrency(Optional.ofNullable(sku.getPrice()).map(SearchProductsData.Price::getCurrency).orElse(null));
			// 价格（tax_exclusive_price）
			listing.setPrice(Optional.ofNullable(sku.getPrice())
				.map(SearchProductsData.Price::getTax_exclusive_price)
				.map(BigDecimal::new)
				.orElse(null));
			// Item平台更新时间（时间转换，使用系统默认时区-UTC+0）
			listing.setItemUpdatedTime(LocalDateTime.ofInstant(Instant.ofEpochSecond(product.getUpdate_time()), ZoneId.systemDefault()));
			// 添加到列表
			listingList.add(listing);
		});
		return listingList;
	}

}
