package com.renpho.erp.oms.application.b2b.order.convert;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.mdm.client.vo.LanguageNameVo;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderCreateCmd;
import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerContactInfo;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderAggRoot;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderContactTypeEnum;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderStatusEnum;
import com.renpho.erp.oms.domain.b2b.order.B2bShipmentReviewStatusEnum;
import com.renpho.erp.oms.domain.b2b.order.entity.*;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bOrderFreeDetail;
import com.renpho.erp.oms.domain.common.util.ReflectionUtils;
import com.renpho.erp.oms.infrastructure.feign.dto.PurchaseProductDetailDTO;

/**
 * B2B订单命令转换器
 * <AUTHOR>
 */
@Component
public class B2bOrderCmdConvert {

	/**
	 * 将创建命令转换为聚合根
	 * @param cmd 创建命令
	 * @param pskuMap key为psku，value为PurchaseProductDetailDTO
	 * @param customerAggRoot 客户聚合根
	 * @return 聚合根
	 */
	public B2bOrderAggRoot toAggRoot(B2bOrderCreateCmd cmd, Map<String, PurchaseProductDetailDTO> pskuMap, CustomerAggRoot customerAggRoot,
			CompanyVo comany) {
		return B2bOrderAggRoot.builder()
			// 客户参考号
			.referNo(cmd.getReferNo())
			// 订单状态：待审核
			.orderStatus(B2bOrderStatusEnum.PENDING)
			// 发货审核状态：未发起
			.shipmentReviewStatus(B2bShipmentReviewStatusEnum.NOT_INITIATED)
			// 订单商品列表
			.items(this.buildItems(cmd.getItems(), pskuMap))
			// 订单金额信息
			.amount(this.buildAmount(cmd.getAmount()))
			// 订单收货地址
			.address(this.buildAddress(cmd.getAddress()))
			// 订单发货要求
			.shipmentRequirement(this.buildShipmentRequirement(cmd.getShipmentRequirement()))
			// 订单客户信息
			.customer(this.buildCustomer(cmd.getCustomer(), customerAggRoot, comany))
			// 订单客户发票信息
			.customerInvoice(this.buildCustomerInvoice(cmd.getCustomerInvoice()))
			// 订单客户联系信息
			.customerContacts(this.buildCustomerContacts(cmd.getCustomerContacts(), customerAggRoot.getContactInfos()))
			.build();
	}

	/**
	 * 构建订单商品
	 */
	private List<B2bOrderItem> buildItems(List<B2bOrderCreateCmd.Item> items, Map<String, PurchaseProductDetailDTO> pskuMap) {
		return items.stream().map(item -> {
			String psku = item.getPsku();
			// psku明细
			PurchaseProductDetailDTO productDetailDTO = Optional.ofNullable(pskuMap.get(psku))
				.orElseThrow(() -> DomainException.ofKey("PSKU_NOT_FOUND"));
			return B2bOrderItem.builder()
				// psku
				.psku(psku)
				// 图片id
				.imageId(productDetailDTO.getImageId())
				// 采购SKU-英文名称
				.pskuNameEn(productDetailDTO.getPskuNameEn())
				// 采购SKU-中文名称
				.pskuNameZh(productDetailDTO.getPskuNameZh())
				// 装箱数量
				.numberOfUnitsPerBox(productDetailDTO.getNumberOfUnitsPerBox())
				// 订购数量
				.orderedQuantity(item.getOrderedQuantity())
				// 发货数量为0
				.shippedQuantity(0)
				// 单价
				.unitPrice(item.getUnitPrice())
				// 税率
				.taxRate(item.getTaxRate())
				// 税金
				.tax(item.getTax())
				.build();
		}).toList();
	}

	/**
	 * 构建订单金额基本结构
	 */
	private B2bOrderAmount buildAmount(B2bOrderCreateCmd.Amount amount) {
		return B2bOrderAmount.builder()
			// 币种
			.currency(amount.getCurrency())
			// 运费
			.shippingFee(amount.getShippingFee())
			// 费用明细
			.feesDetails(this.buildFeesDetails(amount.getFeesDetails()))
			.build();
	}

	/**
	 * 构建费用明细
	 */
	private List<B2bOrderFreeDetail> buildFeesDetails(List<B2bOrderCreateCmd.FreeDetail> feesDetails) {
		if (CollectionUtils.isEmpty(feesDetails)) {
			return List.of();
		}
		return feesDetails.stream()
			.map(f -> B2bOrderFreeDetail.builder()
				// 费用类型
				.feeType(f.getFeeType())
				// 费用金额
				.feeAmount(f.getFeeAmount())
				.build())
			.toList();
	}

	/**
	 * 构建订单收货地址
	 */
	private B2bOrderAddress buildAddress(B2bOrderCreateCmd.Address address) {
		if (ReflectionUtils.isAllFieldsEmpty(address, "comment") && StringUtils.isNotBlank(address.getComment())) {
			throw DomainException.ofKey("ORDER_SHIPPING_ADDRESS_CANNOT_BE_FILLED_IN_WITH_JUST_THE_COMMENT");
		}
		if (Objects.isNull(address) || ReflectionUtils.isAllFieldsEmpty(address, "comment")) {
			return null;
		}
		return B2bOrderAddress.builder()
			// 收件人
			.attnTo(address.getAttnTo())
			// 收货公司
			.shippingCompany(address.getShippingCompany())
			// 地址
			.address(address.getAddress())
			// 邮编
			.zipCode(address.getZipCode())
			// 城市
			.city(address.getCity())
			// 国家
			.country(address.getCountry())
			// 联系电话
			.contactNumber(address.getContactNumber())
			// 备注
			.comment(address.getComment())
			.build();
	}

	/**
	 * 构建订单发货要求
	 */
	private B2bOrderShipmentRequirement buildShipmentRequirement(B2bOrderCreateCmd.ShipmentRequirement requirement) {
		if (Objects.isNull(requirement)) {
			return null;
		}

		return B2bOrderShipmentRequirement.builder()
			// 要求交货日期
			.requestDeliveryDate(requirement.getRequestDeliveryDate())
			// 是否需要托盘
			.palletizationRequired(requirement.getPalletizationRequired())
			// 提单类型
			.blType(requirement.getBlType())
			// 主箱标签名称
			.masterCartonLabelName(requirement.getMasterCartonLabelName())
			// 主箱标签URL
			.masterCartonLabelUrl(requirement.getMasterCartonLabelUrl())
			// 其他要求
			.otherRequirements(requirement.getOtherRequirements())
			.build();
	}

	/**
	 * 构建订单客户信息
	 */
	private B2bOrderCustomer buildCustomer(B2bOrderCreateCmd.Customer customer, CustomerAggRoot customerAggRoot, CompanyVo comany) {
		// 销售公司名称
		List<LanguageNameVo> companyNameList = Optional.ofNullable(comany.getCompanyName()).orElse(List.of());
		return B2bOrderCustomer.builder()
			// 客户ID
			.customerId(customer.getCustomerId())
			// 销售公司ID
			.salesCompanyId(customer.getSalesCompanyId())
			// 销售公司名称中文
			.salesCompanyNameCn(companyNameList.stream()
				.filter(c -> "zh-CN".equals(c.getLanguage()))
				.findFirst()
				.map(LanguageNameVo::getName)
				.orElse(null))
			// 销售公司名称英文
			.salesCompanyNameEn(companyNameList.stream()
				.filter(c -> "en-US".equals(c.getLanguage()))
				.findFirst()
				.map(LanguageNameVo::getName)
				.orElse(null))
			// 贸易条款编码
			.incotermsCode(customer.getIncotermsCode())
			// 付款条款编码
			.paymentTermsCode(customer.getPaymentTermsCode())
			// 客户公司名称
			.customerCompanyName(customerAggRoot.getCustomerCompanyName())
			// 国家
			.country(customerAggRoot.getCountry())
			// 海外区域经理用户id
			.countryManagerUserId(customerAggRoot.getCountryManagerUserId())
			// 海外区域经理工号
			.countryManagerCode(customerAggRoot.getCountryManagerCode())
			// 海外区域经理名称
			.countryManagerName(customerAggRoot.getCountryManagerName())
			// 销售助理用户id
			.salesAssistantUserId(customerAggRoot.getSalesAssistantUserId())
			// 销售助理工号
			.salesAssistantCode(customerAggRoot.getSalesAssistantCode())
			// 销售助理名称
			.salesAssistantName(customerAggRoot.getSalesAssistantName())
			.build();
	}

	/**
	 * 构建订单客户发票
	 */
	private B2bOrderCustomerInvoice buildCustomerInvoice(B2bOrderCreateCmd.CustomerInvoice invoice) {
		if (Objects.isNull(invoice) || ReflectionUtils.isAllFieldsEmpty(invoice)) {
			return null;
		}
		return B2bOrderCustomerInvoice.builder()
			// 发票收件人
			.attnTo(invoice.getAttnTo())
			// 开票公司
			.invoiceCompany(invoice.getInvoiceCompany())
			// 开票地址
			.address(invoice.getAddress())
			// 邮编
			.zipCode(invoice.getZipCode())
			// 城市
			.city(invoice.getCity())
			// 国家
			.country(invoice.getCountry())
			// 联系电话
			.contactNumber(invoice.getContactNumber())
			// 增值税号
			.vatNumber(invoice.getVatNumber())
			.build();
	}

	/**
	 * 订单客户联系信息
	 * @param customerContactIds 客户联系信息主键id
	 * @param contactList 客户联系列表
	 * @return List
	 */
	private List<B2bOrderCustomerContact> buildCustomerContacts(List<B2bOrderCreateCmd.CustomerContact> customerContacts,
			List<CustomerContactInfo> contactList) {
		// Map，key为客户联系信息主键id，value为客户联系信息
		Map<Long, CustomerContactInfo> contactMap = contactList.stream()
			.collect(Collectors.toMap(CustomerContactInfo::getId, Function.identity()));
		return customerContacts.stream().map(contactCmd -> {
			// 客户联系信息
			CustomerContactInfo contact = Optional.ofNullable(contactMap.get(contactCmd.getCustomerContactId()))
				.orElseThrow(() -> DomainException.ofKey("B2B_ORDER_CUSTOMER_CONTACT_INFO_NOT_FOUND"));
			return B2bOrderCustomerContact.builder()
				// 联系人类型
				.contactType(B2bOrderContactTypeEnum.getByValue(contactCmd.getContactType()))
				// 客户联系信息id
				.customerContactId(contact.getId())
				// 岗位
				.contactPosition(contact.getContactPosition())
				// 名字
				.contactName(contact.getContactName())
				// 邮箱
				.contactEmail(contact.getContactEmail())
				// 电话
				.contactPhone(contact.getContactPhone())
				// 备注
				.remark(contact.getRemark())
				.build();
		}).toList();
	}
}