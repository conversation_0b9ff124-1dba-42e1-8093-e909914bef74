package com.renpho.erp.oms.application.b2b.order.convert;

import com.renpho.erp.oms.application.b2b.order.dto.OrderAuditFormDto;
import com.renpho.erp.oms.infrastructure.form.B2bOrderAuditForm;
import com.renpho.erp.oms.infrastructure.form.B2bOrderShipmentAuditForm;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * @desc: B2B订单审核表单转换
 * @time: 2025-06-23 17:05:44
 * @author: <PERSON><PERSON>
 */

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface B2bOrderAuditFormConvert {


    B2bOrderShipmentAuditForm toB2bOrderShipmentAuditForm(OrderAuditFormDto orderAuditFormDto);

    B2bOrderAuditForm toB2bOrderAuditForm(OrderAuditFormDto orderAuditFormDto);
}
