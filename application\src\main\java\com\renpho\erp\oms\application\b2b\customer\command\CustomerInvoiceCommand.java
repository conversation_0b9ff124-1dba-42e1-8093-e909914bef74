package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 客户发票信息命令
 */
@Data
public class CustomerInvoiceCommand {

    private Long id;

    /**
     * 收件人
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_ATTN_TO_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_INVOICE_ATTN_TO_TOO_LONG")
    private String attnTo;

    /**
     * 发票公司名称
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_COMPANY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_INVOICE_COMPANY_TOO_LONG")
    private String invoiceCompany;

    /**
     * 地址
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_ADDRESS_REQUIRE")
    @Size(max = 255, message = "CUSTOMER_INVOICE_ADDRESS_TOO_LONG")
    private String address;

    /**
     * 邮编
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_ZIP_CODE_REQUIRE")
    @Size(max = 20, message = "CUSTOMER_INVOICE_ZIP_CODE_TOO_LONG")
    private String zipCode;

    /**
     * 城市
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_CITY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_INVOICE_CITY_TOO_LONG")
    private String city;

    /**
     * 国家
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_COUNTRY_REQUIRE")
    @Size(max = 100, message = "CUSTOMER_INVOICE_COUNTRY_TOO_LONG")
    private String country;

    /**
     * 联系电话
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_CONTACT_NUMBER_REQUIRE")
    @Size(max = 50, message = "CUSTOMER_INVOICE_CONTACT_NUMBER_TOO_LONG")
    private String contactNumber;

    /**
     * VAT号码
     */
    @NotEmpty(message = "CUSTOMER_INVOICE_VAT_NUMBER_REQUIRE")
    @Size(max = 50, message = "CUSTOMER_INVOICE_VAT_NUMBER_TOO_LONG")
    private String vatNumber;
} 