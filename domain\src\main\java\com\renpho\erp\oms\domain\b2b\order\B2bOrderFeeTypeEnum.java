package com.renpho.erp.oms.domain.b2b.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * B2B订单费用类型枚举
 * <AUTHOR> Assistant
 */
@AllArgsConstructor
@Getter
public enum B2bOrderFeeTypeEnum {
    /**
     * 推广费
     */
    PROMOTION_FEE(1, "推广费"),

    /**
     * 广告费
     */
    ADVERTISING_FEE(2, "广告费");

    private final Integer value;
    private final String desc;

    /**
     * 根据值获取枚举
     * @param value 值
     * @return B2bOrderFeeTypeEnum
     */
    public static B2bOrderFeeTypeEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (B2bOrderFeeTypeEnum feeType : B2bOrderFeeTypeEnum.values()) {
            if (feeType.getValue().equals(value)) {
                return feeType;
            }
        }
        return null;
    }
}
