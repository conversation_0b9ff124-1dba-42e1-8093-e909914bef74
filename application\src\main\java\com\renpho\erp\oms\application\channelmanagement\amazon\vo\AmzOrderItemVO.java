package com.renpho.erp.oms.application.channelmanagement.amazon.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AmzOrderItemVO {

	private String orderItemId;

	/**
	 * 亚马逊标准识别码
	 */
	private String asin;

	/**
	 * 卖方SKU
	 */
	private String sellerSku;

	/**
	 * 是否是礼物 0 否 1 是
	 */
	private Boolean isGift;

	/**
	 * 是否需要透明代码 是:true 1,否:false 0
	 */
	private Boolean isTransparency;

	/**
	 * 发货数量
	 */
	private Integer quantityShipped;

	/**
	 * 订购数量
	 */
	private Integer quantityOrdered;

	/**
	 * 适用于该项目的税收征收模式
	 */
	private String taxCollectionModel;

	/**
	 * 物品税货币种类
	 */
	private String itemTaxCurrencyCode;

	/**
	 * 商品售价（此价格为商品数量*商品单价 不包含税、运费其他价格）
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal itemPriceAmount;

	/**
	 * 折扣金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal promotionDiscountAmount;

	/**
	 * 物品税金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal itemTaxAmount;

	/**
	 * 促销折扣税金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal promotionDiscountTaxAmount;

	/**
	 * 运费价格金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingPriceAmount;

	/**
	 * 运费折扣金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingDiscountAmount;

	/**
	 * 运费税金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingTaxAmount;

	/**
	 * 运费折扣税金额
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal shippingDiscountTaxAmount;
}
