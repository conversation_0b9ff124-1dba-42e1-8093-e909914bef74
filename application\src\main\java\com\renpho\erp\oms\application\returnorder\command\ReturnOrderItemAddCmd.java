package com.renpho.erp.oms.application.returnorder.command;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * @desc: 新建RAM退货订单明细参数
 * @time: 2025-03-18 15:41:42
 * @author: <PERSON><PERSON>
 */
@Data
public class ReturnOrderItemAddCmd {

	/**
	 * 销售订单行表Id
	 */
	@NotNull(message = "SALE_ORDER_ITEM_ID_REQUIRE")
	private Long orderItemId;

	/**
	 * 采购SKU，根据映射表关联出的本地PSKU（ERP里面的SKU）
	 */
	@NotEmpty(message = "SALE_ORDER_CREATE_PSKU_REQUIRE")
	@Size(max = 64, message = "SALE_ORDER_CREATE_PSKU_TOO_LONG")
	private String psku;

	/**
	 * FNSKU，根据映射表关联出的商品条码（库存识别码）
	 */
	@NotEmpty(message = "SALE_ORDER_CREATE_FNSKU_REQUIRE")
	@Size(max = 255, message = "SALE_ORDER_CREATE_FNSKU_TOO_LONG")
	private String fnSku;

	/**
	 * 应发数量（内部psku的应发货数量）
	 */
	private Integer quantityShipment;

	/**
	 * 退货数量
	 */
	private Integer returnQuantity;
}
