package com.renpho.erp.oms.application.b2b.order.service;

import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.b2b.order.command.B2bOrderEditCmd;
import com.renpho.erp.oms.application.b2b.order.convert.B2bCompanyConvert;
import com.renpho.erp.oms.application.b2b.order.convert.B2bOrderModifyConvert;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.service.PurchaseSkuQueryService;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.repository.CustomerRepository;
import com.renpho.erp.oms.domain.b2b.order.B2bOrderAggRoot;
import com.renpho.erp.oms.domain.b2b.order.cmd.B2bOrderModifyCmd;
import com.renpho.erp.oms.domain.b2b.order.repository.B2bOrderRepository;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bCompany;
import com.renpho.erp.oms.domain.b2b.order.valueobject.B2bProduct;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;

import lombok.RequiredArgsConstructor;

/**
 * B2b订单编辑-应用服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class B2bOrderEditService {

	private final CustomerRepository customerRepository;
	private final B2bOrderRepository b2bOrderRepository;
	private final PurchaseSkuQueryService purchaseSkuQueryService;
	private final B2bOrderModifyConvert b2bOrderModifyConvert;
	private final CompanyClient companyClient;
	private final B2bCompanyConvert b2bCompanyConvert;

	/**
	 * 编辑
	 * @param cmd 指令
	 */
	@Lock4j(name = "b2b:order:edit", keys = "#cmd.orderId", acquireTimeout = 0L)
	@Transactional(rollbackFor = Exception.class)
	public void edit(B2bOrderEditCmd cmd) {
		// 查询订单
		B2bOrderAggRoot orderAggRoot = b2bOrderRepository.findById(cmd.getOrderId());
		// 检查是否能修改
		orderAggRoot.checkModify();
		// 查询客户聚合根信息
		CustomerAggRoot customerAggRoot = customerRepository.findById(cmd.getCustomer().getCustomerId());
		// 查询公司信息
		CompanyVo companyVo = companyClient.getById(cmd.getCustomer().getSalesCompanyId());
		// 查询psku Map，key为psku，value为B2bProduct
		Map<String, B2bProduct> pskuMap = purchaseSkuQueryService.b2bPurchaseList(this.getPskus(cmd));
		// 转换
		B2bOrderModifyCmd modifyCmd = b2bOrderModifyConvert.convert(cmd);
		B2bCompany company = b2bCompanyConvert.convert(companyVo);
		// 修改订单
		orderAggRoot.modify(modifyCmd, customerAggRoot, pskuMap, company);
		// 重新计算订单金额（含已收款比例）
		orderAggRoot.calculateAmount();
		// 仓储修改
		b2bOrderRepository.update(orderAggRoot);
	}

	/**
	 * 获取psku集
	 * @param cmd 订单编辑命令
	 * @return Set
	 */
	private @NotNull Set<String> getPskus(B2bOrderEditCmd cmd) {
		return cmd.getItems().stream().map(B2bOrderEditCmd.Item::getPsku).collect(Collectors.toSet());
	}

}
