package com.renpho.erp.oms.application.channelmanagement.amazon.convert;

import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderLog;
import com.renpho.erp.oms.application.channelmanagement.amazon.dto.AmzOrderPushDTO;
import com.renpho.erp.oms.application.channelmanagement.amazon.vo.AmzOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrder;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface AmzAppOrderConvertor {
	@Mapping(target = "purchaseDate", expression = "java(BaseConvertor.convertDate(param.getPurchaseDate()))")
	@Mapping(target = "lastUpdateDate", expression = "java(BaseConvertor.convertDate(param.getLastUpdateDate()))")
	@Mapping(target = "orderStatus", expression = "java(param.getOrderStatus().getValue())")
	@Mapping(target = "fulfillmentChannel",
			expression = "java(param.getFulfillmentChannel() != null ? param.getFulfillmentChannel().getValue() : null)")
	@Mapping(target = "orderTotalAmount",
			expression = "java(param.getOrderTotal() != null ? BaseConvertor.convertToBigDecimal(param.getOrderTotal().getAmount()) : null)")
	@Mapping(target = "orderTotalCurrencyCode",
			expression = "java(param.getOrderTotal() != null ? param.getOrderTotal().getCurrencyCode() : null)")
	@Mapping(target = "paymentMethod", expression = "java(param.getPaymentMethod() != null ? param.getPaymentMethod().getValue() : null)")
	@Mapping(target = "orderType", expression = "java(param.getOrderType() != null ? param.getOrderType().getValue() : null)")
	@Mapping(target = "earliestDeliveryDate", expression = "java(BaseConvertor.convertDate(param.getEarliestDeliveryDate()))")
	@Mapping(target = "latestDeliveryDate", expression = "java(BaseConvertor.convertDate(param.getLatestDeliveryDate()))")
	@Mapping(target = "promiseResponsDueDate", expression = "java(BaseConvertor.convertDate(param.getPromiseResponsDueDate()))")
	@Mapping(target = "isEstimatedShipDateSet", expression = "java(param.getIsEstimatedShipDateSet())")
	@Mapping(target = "buyerInvoicePreference",
			expression = "java(param.getBuyerInvoicePreference() != null ? param.getBuyerInvoicePreference().getValue() : null)")
	@Mapping(target = "fulfillmentSupplySourceId",
			expression = "java(param.getFulfillmentInstruction() != null ? param.getFulfillmentInstruction().getFulfillmentSupplySourceId() : null)")
	@Mapping(target = "earliestShipDate", expression = "java(BaseConvertor.convertDate(param.getEarliestShipDate()))")
	@Mapping(target = "latestShipDate", expression = "java(BaseConvertor.convertDate(param.getLatestShipDate()))")
	@Mapping(target = "shippingCity",
			expression = "java(param.getAddressFilterPII() != null ? param.getAddressFilterPII().getCity() : null)")
	@Mapping(target = "shippingStateOrRegion",
			expression = "java(param.getAddressFilterPII() != null ? param.getAddressFilterPII().getStateOrRegion() : null)")
	@Mapping(target = "shippingPostalCode",
			expression = "java(param.getAddressFilterPII() != null ? param.getAddressFilterPII().getPostalCode() : null)")
	@Mapping(target = "shippingCountryCode",
			expression = "java(param.getAddressFilterPII() != null ? param.getAddressFilterPII().getCountryCode() : null)")
	@Mapping(target = "lastMpdsSyncOrderTime", expression = "java(cn.hutool.core.util.StrUtil.isNotEmpty(param.getSyncTime()) ?org.apache.shenyu.common.utils.DateUtils.parseLocalDateTime(param.getSyncTime()) : null)")
	AmzOrder toDomain(AmzOrderPushDTO param);

	AmzOrderLog toDTO(AmzOrder param);

	AmzOrderVO toVO(AmzOrder param);

}
