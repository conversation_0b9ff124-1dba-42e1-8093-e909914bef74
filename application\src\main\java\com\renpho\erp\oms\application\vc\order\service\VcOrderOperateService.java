package com.renpho.erp.oms.application.vc.order.service;

import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.threeten.bp.DateTimeUtils;

import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.vc.order.command.SubmitOrderCmd;
import com.renpho.erp.oms.application.vc.order.command.VcOrderLabelUploadCmd;
import com.renpho.erp.oms.application.vc.order.command.VcOrderReparseSkusCmd;
import com.renpho.erp.oms.application.vc.order.convert.VcOrderConvertor;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.VcSkuMappingVO;
import com.renpho.erp.oms.domain.vc.order.SubmitSupplierChainService;
import com.renpho.erp.oms.domain.vc.order.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderInvoiceCmd;
import com.renpho.erp.oms.domain.vc.order.command.VcOrderRemarkCmd;
import com.renpho.erp.oms.domain.vc.order.model.*;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.erp.oms.infrastructure.persistence.skuMapping.mapper.SkuMappingMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.mapper.VcOrderInvoiceMapper;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.VcOrderInvoicePO;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;

import cn.hutool.extra.spring.SpringUtil;
import io.swagger.client.model.vendoRetailProcurement.orders.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: VC订单操作服务
 * @time: 2025-07-07 10:28:52
 * @author: Alina
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class VcOrderOperateService {

	private final VcOrderRepository vcOrderRepository;
	private final VcOrderConvertor vcOrderConvertor;
	private final AmazonClient amazonClient;
	private final StoreClient storeClient;
	private final SkuMappingMapper skuMappingMapper;
	private final ProductClient productClient;
	private final VcOrderInvoiceMapper vcOrderInvoiceMapper;
	private final SubmitSupplierChainService submitSupplierChainService;

	/**
	 * 上传VC订单箱唛
	 *
	 * @param cmd 上传VC订单箱唛入参
	 * @return 操作结果
	 */
	@Transactional
	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#cmd.orderId", acquireTimeout = 0L)
	public R<Void> uploadLabel(VcOrderLabelUploadCmd cmd) {
		// 1. 查询聚合根
		VcOrderAggRoot order = vcOrderRepository.findById(cmd.getOrderId());
		// 2. 转换命令对象为领域对象
		List<VcOrderItemLabel> labelList = cmd.getItemLabelList()
			.stream()
			.map(vcOrderConvertor::toVcOrderItemLabel)
			.collect(Collectors.toList());
		// 3. 领域方法生成箱唛（聚合根内部做全部校验）
		order.uploadLabels(labelList);
		// 4. 调用提交供应链接口
		submitSupplierChainService.submitSupplierChain(order);
		// 5. 仓储层保存
		vcOrderRepository.saveOrderItemLabels(order);
		return R.success();
	}

	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#submitOrderCmd.orderId", acquireTimeout = 0L)
	public void submitAcknowledgement(SubmitOrderCmd submitOrderCmd) {
		// 获取数据
		VcOrderAggRoot vcOrderAggRoot = vcOrderRepository.findById(submitOrderCmd.getOrderId());
		if (vcOrderAggRoot == null) {
			throw new BusinessException("VC_ORDER_NOT_FOUND");
		}

		// 领域层执行接单业务 校验
		List<SubmitAcknowledgmentItem> submitAcknowledgmentItemList = submitOrderCmd.getSubmitAcknowledgmentItem();
		vcOrderAggRoot.submitAcknowledgementCheck(submitAcknowledgmentItemList);

		// 调用亚马逊api接单
		String sellerId = Optional.ofNullable(storeClient.getAuthorizationByStoreId(vcOrderAggRoot.getStore().getStoreId()))
			.map(StoreAuthorizationVo::getAuthorization)
			.map(StoreAuthorizationVo.Authorization::getAmzSellerId)
			.orElse(null);
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setSellerId(sellerId);

		R<SubmitAcknowledgementResponse> r = amazonClient.submitAcknowledgement(shopAccount,
				getSubmitAcknowledgementRequest(vcOrderAggRoot, submitAcknowledgmentItemList));

		// 根据调用结果报错或者变更状态
		if (!r.isSuccess()) {
			throw new BusinessException(I18nMessageKit.getMessage("VC_ORDER_SUBMIT_ACKNOWLEDGEMENT_FAIL", r.getMessage()));
		}

		vcOrderAggRoot.submitAcknowledgement(submitAcknowledgmentItemList);
		vcOrderRepository.submitAcknowledgement(vcOrderAggRoot);
	}

	private SubmitAcknowledgementRequest getSubmitAcknowledgementRequest(VcOrderAggRoot vcOrderAggRoot,
			List<SubmitAcknowledgmentItem> submitAcknowledgmentItemList) {
		OrderAcknowledgement orderAcknowledgement = new OrderAcknowledgement();
		// 采购单号
		orderAcknowledgement.setPurchaseOrderNumber(vcOrderAggRoot.getVcPurchaseNo());
		// 发货地信息
		PartyIdentification partyIdentification = new PartyIdentification();
		partyIdentification.setPartyId(vcOrderAggRoot.getAddress().getShipFrom());
		orderAcknowledgement.setSellingParty(partyIdentification);
		// 确认时间 当前时间
		orderAcknowledgement.acknowledgementDate(org.threeten.bp.OffsetDateTime.now());

		Map<Long, SubmitAcknowledgmentItem> submitAcknowledgmentItemMap = submitAcknowledgmentItemList.stream()
			.collect(Collectors.toMap(SubmitAcknowledgmentItem::getId, Function.identity()));
		// 确认商品行信息
		for (VcOrderItem item : vcOrderAggRoot.getItems()) {
			OrderAcknowledgementItem acknowledgementItem = new OrderAcknowledgementItem();
			// 行单行号
			acknowledgementItem.itemSequenceNumber(item.getChannelOrderLineNo());
			// 亚马逊asin
			acknowledgementItem.amazonProductIdentifier(item.getAsin());
			// 下单信息
			acknowledgementItem.orderedQuantity(new ItemQuantity()
				// 下单数
				.amount(item.getOrderedQuantity())
				// 转的时候已经乘以了
				// 单位数
				.unitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES)
				// 每单位数
				.unitSize(1));

			SubmitAcknowledgmentItem submitAcknowledgmentItem = submitAcknowledgmentItemMap.get(item.getId());
			// 接单信息
			org.threeten.bp.ZonedDateTime shippingWindowEndTime = DateTimeUtils.toZonedDateTime(
					GregorianCalendar.from(ZonedDateTime.of(vcOrderAggRoot.getShippingWindow().getEndTime(), ZoneId.systemDefault())));
			acknowledgementItem.addItemAcknowledgementsItem(new OrderItemAcknowledgement()
				.acknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.ACCEPTED)
				.acknowledgedQuantity(new ItemQuantity().amount(submitAcknowledgmentItem.getAcceptedQuantity())
					.unitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES)
					.unitSize(1))
				.scheduledShipDate(
						VcOrderBusinessType.DI.equals(vcOrderAggRoot.getBusinessType()) ? shippingWindowEndTime.toOffsetDateTime() : null)
				.scheduledDeliveryDate(
						VcOrderBusinessType.DO.equals(vcOrderAggRoot.getBusinessType()) ? shippingWindowEndTime.toOffsetDateTime() : null));
			// 拒单信息
			OrderItemAcknowledgement.RejectionReasonEnum rejectionReasonEnum = null;
			if (item.getRejectionReason() != null) {
				rejectionReasonEnum = switch (item.getRejectionReason()) {
					case OUT_OF_STOCK -> OrderItemAcknowledgement.RejectionReasonEnum.TEMPORARILYUNAVAILABLE;
					case DELIST -> OrderItemAcknowledgement.RejectionReasonEnum.INVALIDPRODUCTIDENTIFIER;
					case INCORRECT_SKU -> OrderItemAcknowledgement.RejectionReasonEnum.OBSOLETEPRODUCT;
				};
			}
			acknowledgementItem.addItemAcknowledgementsItem(
					new OrderItemAcknowledgement().acknowledgementCode(OrderItemAcknowledgement.AcknowledgementCodeEnum.REJECTED)
						.rejectionReason(rejectionReasonEnum)
						.acknowledgedQuantity(new ItemQuantity().amount(submitAcknowledgmentItem.getRejectedQuantity())
							.unitOfMeasure(ItemQuantity.UnitOfMeasureEnum.EACHES)
							.unitSize(1)));

			orderAcknowledgement.addItemsItem(acknowledgementItem);
		}

		return new SubmitAcknowledgementRequest().addAcknowledgementsItem(orderAcknowledgement);

	}

	/**
	 * 批量重新解析VC订单SKU 按照订单维度加锁处理，符合DDD编排风格
	 *
	 * @param cmd 批量重新解析命令
	 * @return 操作结果
	 */
	public R<Void> reparseSkus(VcOrderReparseSkusCmd cmd) {
		List<Long> orderIds = cmd.getOrderIds();
		log.debug("开始批量重新解析VC订单SKU，订单数量：{}", orderIds.size());

		// 按订单维度逐个处理，每个订单单独加锁
		for (Long orderId : orderIds) {
			try {
				SpringUtil.getBean(getClass()).reparseSkuForSingleOrder(orderId);
				log.debug("VC订单SKU重新解析成功，订单ID：{}", orderId);
			}
			catch (Exception e) {
				log.error("VC订单SKU重新解析失败，订单ID：{}", orderId, e);
				// 继续处理其他订单，不中断整个批量操作
			}
		}

		log.debug("批量重新解析VC订单SKU完成，总订单数量：{}", orderIds.size());
		return R.success();
	}

	/**
	 * 单个订单重新解析SKU 使用分布式锁防止并发操作
	 *
	 * @param orderId 订单ID
	 */
	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#orderId")
	@Transactional
	public void reparseSkuForSingleOrder(Long orderId) {
		log.info("开始重新解析VC订单SKU，订单ID：{}", orderId);

		// 1. 查询订单聚合根
		VcOrderAggRoot order = vcOrderRepository.findById(orderId);
		if (Objects.isNull(order)) {
			throw new BusinessException("VC_ORDER_NOT_FOUND");
		}
		// todo 转换查询抽成单独的服务
		// 2. 收集订单中的ASIN列表
		List<String> asinList = order.getItems()
			.stream()
			.map(item -> item.getAsin())
			.filter(asin -> asin != null && !asin.trim().isEmpty())
			.distinct()
			.collect(Collectors.toList());

		if (CollectionUtils.isEmpty(asinList)) {
			throw new BusinessException("VC_ORDER_NO_ASIN_FOUND");
		}

		// 3. 查询SKU映射（轻量级查询）
		List<VcSkuMappingVO> skuMappingList = skuMappingMapper.listVcSkuMappingByStoreIdAndAsinList(order.getStore().getStoreId(),
				asinList);

		if (CollectionUtils.isEmpty(skuMappingList)) {
			throw new BusinessException("VC_ORDER_NO_SKU_MAPPING_FOUND");
		}

		// 4. 收集采购SKU列表
		Set<String> pskuSet = skuMappingList.stream()
			.map(VcSkuMappingVO::getPsku)
			.filter(psku -> psku != null && !psku.trim().isEmpty())
			.collect(Collectors.toSet());

		// 5. 查询采购商品信息（主要是获取图片ID）
		Map<String, String> purchaseProductMap = Map.of();
		if (CollectionUtils.isNotEmpty(pskuSet)) {
			List<PdsProductManagerBasicViewVo> purchaseProducts = productClient.getPurchaseSku(pskuSet.stream().toList());
			purchaseProductMap = purchaseProducts.stream()
				.filter(product -> product.getPurchaseSku() != null && product.getProductCoverImageId() != null)
				.collect(Collectors.toMap(PdsProductManagerBasicViewVo::getPurchaseSku,
						PdsProductManagerBasicViewVo::getProductCoverImageId, (existing, replacement) -> existing));
		}

		// 6. 构建ASIN到VcProduct的映射Map
		Map<String, VcProduct> asinToProductMap = buildAsinToProductMap(skuMappingList, purchaseProductMap);

		// 7. 调用聚合根的解析方法（DDD编排）
		order.parseSku(asinToProductMap);

		// 8. 保存SKU解析结果（只更新相关字段）
		vcOrderRepository.saveSkuParseResult(order);

		log.info("VC订单SKU重新解析完成，订单ID：{}，解析状态：{}", orderId, order.getParseStatus());
	}

	/**
	 * 构建ASIN到VcProduct的映射Map
	 * @param skuMappingList SKU映射列表
	 * @param purchaseProductMap 采购商品信息Map
	 * @return Map<String, VcProduct>
	 */
	private Map<String, VcProduct> buildAsinToProductMap(List<VcSkuMappingVO> skuMappingList, Map<String, String> purchaseProductMap) {
		return skuMappingList.stream()
			.collect(Collectors.toMap(VcSkuMappingVO::getAsin,
					mapping -> VcProduct.builder()
						.psku(mapping.getPsku())
						.barcode(mapping.getFnsku())
						.numberOfUnitsPerBox(mapping.getFnskuQuantity())
						.imageId(purchaseProductMap.get(mapping.getPsku()))
						.build(),
					(existing, replacement) -> existing));
	}

	/**
	 * 提交供应商链
	 *
	 * @param orderId 订单id
	 * @return 操作结果
	 */
	@Transactional
	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#orderId", acquireTimeout = 0L)
	public R<Void> submitSupplierChain(Long orderId) {
		// 1. 查询聚合根
		VcOrderAggRoot order = vcOrderRepository.findById(orderId);
		// 2. 调用聚合根方法进行校验和处理
		order.prepareForSupplierChain();
		// 3. 只有备货中才提交供应链
		if (order.getOrderStatus().equals(VcOrderStatus.PREPARING)) {
			submitSupplierChainService.submitSupplierChain(order);
		}
		// 4. 仓储层更新状态
		vcOrderRepository.updateOrderStatus(order);
		return R.success();
	}

	/**
	 * VC订单备注
	 *
	 * @param cmd 备注命令
	 */
	public void remark(VcOrderRemarkCmd cmd) {
		// 直接调用仓储层方法进行备注，避免加载整个聚合根，提高性能
		vcOrderRepository.remark(cmd);
	}

	/**
	 * VC订单开票
	 *
	 * @param cmd 开票命令
	 * @return 操作结果
	 */
	@Transactional
	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#cmd.orderId", acquireTimeout = 0L)
	public void invoiceOrder(VcOrderInvoiceCmd cmd) {
		// 1. 校验发票号是否重复
		LambdaQueryWrapper<VcOrderInvoicePO> queryWrapper = new LambdaQueryWrapper<VcOrderInvoicePO>().eq(VcOrderInvoicePO::getInvoiceNo,
				cmd.getInvoiceNo());
		if (vcOrderInvoiceMapper.selectCount(queryWrapper) > 0) {
			throw new BusinessException("VC_ORDER_INVOICE_NO_DUPLICATED");
		}

		// 2. 查询订单聚合根
		VcOrderAggRoot vcOrderAggRoot = vcOrderRepository.findById(cmd.getOrderId());
		if (vcOrderAggRoot == null) {
			throw new BusinessException("VC_ORDER_NOT_FOUND");
		}

		// 3. 校验
		vcOrderAggRoot.invoiceCheck(cmd);

		// 4. todo 后续调用亚马逊发票接口

		// 5. 调用领域开发票
		vcOrderAggRoot.invoice(cmd);
		vcOrderRepository.invoice(vcOrderAggRoot);
	}
}
