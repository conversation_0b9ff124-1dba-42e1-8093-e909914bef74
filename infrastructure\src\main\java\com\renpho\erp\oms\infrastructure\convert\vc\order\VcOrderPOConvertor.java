package com.renpho.erp.oms.infrastructure.convert.vc.order;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

import org.mapstruct.*;

import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.entity.*;
import com.renpho.erp.oms.domain.vc.order.enums.*;
import com.renpho.erp.oms.domain.vc.order.valueobject.*;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.*;

import lombok.Builder;
import lombok.Getter;

/**
 * @desc: VC-订单表 Convertor
 * @time: 2025-07-07 15:39:10
 * @author: Alina
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface VcOrderPOConvertor {
	/**
	 * 转换订单PO到聚合根
	 * @param order 订单PO
	 * @param dto 转换DTO
	 * @return VcOrderAggRoot
	 */
	@Mapping(source = "order.id", target = "id")
	@Mapping(source = "order.orderStatus", target = "orderStatus", qualifiedByName = "mapOrderStatus")
	@Mapping(source = "order.acceptedStatus", target = "acceptedStatus", qualifiedByName = "mapToAcceptedStatus")
	@Mapping(source = "order.acceptedMethod", target = "acceptedMethod", qualifiedByName = "mapToAcceptedMethod")
	@Mapping(source = "order.parseStatus", target = "parseStatus", qualifiedByName = "mapToParseStatus")
	@Mapping(source = "order.businessType", target = "businessType", qualifiedByName = "mapToBusinessType")
	@Mapping(source = "order", target = "store", qualifiedByName = "mapStore")
	@Mapping(source = "order", target = "shippingWindow", qualifiedByName = "mapShippingWindow")
	@Mapping(source = "order", target = "amount", qualifiedByName = "mapAmount")
	@Mapping(source = "order", target = "address", qualifiedByName = "mapAddress")
	@Mapping(source = "dto.items", target = "items")
	@Mapping(source = "dto.invoices", target = "invoices")
	@Mapping(source = "dto.di", target = "di")
	@Mapping(source = "dto.remarks", target = "remarks")
	@Mapping(source = "dto.itemLabels", target = "itemLabels")
	VcOrderAggRoot toAggRoot(VcOrderPO order, ConverterDTO dto);

	/**
	 * 映射订单状态
	 */
	@Named("mapOrderStatus")
	default VcOrderStatus mapOrderStatus(Integer value) {
		return VcOrderStatus.getByValue(value);
	}

	/**
	 * 映射接单状态
	 */
	@Named("mapToAcceptedStatus")
	default VcOrderAcceptedStatus mapToAcceptedStatus(Integer value) {
		return VcOrderAcceptedStatus.getByValue(value);
	}

	/**
	 * 映射接单方式
	 */
	@Named("mapToAcceptedMethod")
	default VcOrderAcceptedMethod mapToAcceptedMethod(Integer value) {
		return VcOrderAcceptedMethod.getByValue(value);
	}

	/**
	 * 映射解析状态
	 */
	@Named("mapToParseStatus")
	default VcOrderParseStatus mapToParseStatus(Integer value) {
		return VcOrderParseStatus.getByValue(value);
	}

	/**
	 * 映射业务类型
	 */
	@Named("mapToBusinessType")
	default VcOrderBusinessType mapToBusinessType(Integer value) {
		return VcOrderBusinessType.getByValue(value);
	}

	/**
	 * 映射店铺信息
	 */
	@Named("mapStore")
	default VcStore mapStore(VcOrderPO order) {
		if (order.getStoreId() == null) {
			return null;
		}
		return VcStore.builder().storeId(order.getStoreId()).storeName(order.getStoreName()).siteCode(order.getSiteCode()).build();
	}

	/**
	 * 映射发货窗口
	 */
	@Named("mapShippingWindow")
	default VcShippingWindow mapShippingWindow(VcOrderPO order) {
		if (order.getShippingWindowStart() == null && order.getShippingWindowEnd() == null) {
			return null;
		}
		return VcShippingWindow.builder().startTime(order.getShippingWindowStart()).endTime(order.getShippingWindowEnd()).build();
	}

	/**
	 * 映射订单金额
	 */
	@Named("mapAmount")
	default VcOrderAmount mapAmount(VcOrderPO order) {
		return VcOrderAmount.builder()
			.currency(order.getCurrency())
			.productPrice(order.getProductPrice())
			.taxRate(order.getTaxRate())
			.tax(order.getTax())
			.build();
	}

	/**
	 * 映射订单地址
	 */
	@Named("mapAddress")
	default VcOrderAddress mapAddress(VcOrderPO order) {
		return VcOrderAddress.builder()
			.shipFrom(order.getShipFrom())
			.shipTo(order.getShipTo())
			.billTo(order.getBillTo())
			.buying(order.getBuying())
			.build();
	}

	List<VcOrderItem> toItems(List<VcOrderItemPO> items);

	List<VcOrderInvoice> toInvoices(List<VcOrderInvoicePO> invoices);

	VcOrderInvoicePO toInvoicePO(VcOrderInvoice vcOrderInvoice);

	VcOrderDi toDi(VcOrderDiPO di);

	List<VcOrderRemark> toRemarks(List<VcOrderRemarkPO> remarks);

	List<VcOrderItemLabel> toItemLabels(List<VcOrderItemLabelPO> itemLabels);

	VcOrderItemLabelPO toVcOrderItemLabelPo(VcOrderItemLabel label);

	// ========== 聚合根到PO对象的转换方法 ==========

	/**
	 * 转换订单聚合根到订单PO
	 * @param order 订单聚合根
	 * @return VcOrderPO
	 */
	@Mapping(target = "storeId", source = "store", qualifiedByName = "mapStoreId")
	@Mapping(target = "storeName", source = "store", qualifiedByName = "mapStoreName")
	@Mapping(target = "siteCode", source = "store", qualifiedByName = "mapSiteCode")
	@Mapping(target = "orderStatus", source = "orderStatus", qualifiedByName = "mapOrderStatusValue")
	@Mapping(target = "acceptedStatus", source = "acceptedStatus", qualifiedByName = "mapAcceptedStatusValue")
	@Mapping(target = "acceptedMethod", source = "acceptedMethod", qualifiedByName = "mapAcceptedMethodValue")
	@Mapping(target = "parseStatus", source = "parseStatus", qualifiedByName = "mapParseStatusValue")
	@Mapping(target = "businessType", source = "businessType", qualifiedByName = "mapBusinessTypeValue")
	@Mapping(target = "shippingWindowStart", source = "shippingWindow", qualifiedByName = "mapShippingWindowStart")
	@Mapping(target = "shippingWindowEnd", source = "shippingWindow", qualifiedByName = "mapShippingWindowEnd")
	@Mapping(target = "currency", source = "amount", qualifiedByName = "mapCurrency")
	@Mapping(target = "productPrice", source = "amount", qualifiedByName = "mapProductPrice")
	@Mapping(target = "taxRate", source = "amount", qualifiedByName = "mapTaxRate")
	@Mapping(target = "tax", source = "amount", qualifiedByName = "mapTax")
	@Mapping(target = "shipFrom", source = "address", qualifiedByName = "mapShipFrom")
	@Mapping(target = "shipTo", source = "address", qualifiedByName = "mapShipTo")
	@Mapping(target = "billTo", source = "address", qualifiedByName = "mapBillTo")
	@Mapping(target = "buying", source = "address", qualifiedByName = "mapBuying")
	VcOrderPO toPo(VcOrderAggRoot order);

	/**
	 * 转换订单商品列表到商品PO列表
	 * @param items 订单商品列表
	 * @return List<VcOrderItemPO>
	 */
	List<VcOrderItemPO> toItemPos(List<VcOrderItem> items);

	/**
	 * 转换订单商品到商品PO
	 * @param item 订单商品
	 * @return VcOrderItemPO
	 */
	@Mapping(target = "psku", source = "product", qualifiedByName = "mapProductPsku")
	@Mapping(target = "barcode", source = "product", qualifiedByName = "mapProductBarcode")
	@Mapping(target = "numberOfUnitsPerBox", source = "product", qualifiedByName = "mapProductUnitsPerBox")
	@Mapping(target = "imageId", source = "product", qualifiedByName = "mapProductImageId")
	@Mapping(target = "currency", source = "amount", qualifiedByName = "mapItemCurrency")
	@Mapping(target = "unitPrice", source = "amount", qualifiedByName = "mapItemUnitPrice")
	@Mapping(target = "tax", source = "amount", qualifiedByName = "mapItemTax")
	@Mapping(target = "subTotal", source = "amount", qualifiedByName = "mapItemSubTotal")
	@Mapping(target = "rejectionReason", source = "rejectionReason", qualifiedByName = "mapRejectionReasonValue")
	VcOrderItemPO toItemPo(VcOrderItem item);

	/**
	 * 转换DI信息到DI PO
	 * @param di DI信息
	 * @return VcOrderDiPO
	 */
	VcOrderDiPO toDiPo(VcOrderDi di);

	// ========== 自定义映射方法 ==========

	// 店铺信息映射方法
	@Named("mapStoreId")
	default Integer mapStoreId(VcStore store) {
		return Optional.ofNullable(store).map(VcStore::getStoreId).orElse(null);
	}

	@Named("mapStoreName")
	default String mapStoreName(VcStore store) {
		return Optional.ofNullable(store).map(VcStore::getStoreName).orElse(null);
	}

	@Named("mapSiteCode")
	default String mapSiteCode(VcStore store) {
		return Optional.ofNullable(store).map(VcStore::getSiteCode).orElse(null);
	}

	// 状态枚举映射方法
	@Named("mapOrderStatusValue")
	default Integer mapOrderStatusValue(VcOrderStatus status) {
		return Optional.ofNullable(status).map(VcOrderStatus::getValue).orElse(null);
	}

	@Named("mapAcceptedStatusValue")
	default Integer mapAcceptedStatusValue(VcOrderAcceptedStatus status) {
		return Optional.ofNullable(status).map(VcOrderAcceptedStatus::getValue).orElse(null);
	}

	@Named("mapAcceptedMethodValue")
	default Integer mapAcceptedMethodValue(VcOrderAcceptedMethod method) {
		return Optional.ofNullable(method).map(VcOrderAcceptedMethod::getValue).orElse(null);
	}

	@Named("mapParseStatusValue")
	default Integer mapParseStatusValue(VcOrderParseStatus status) {
		return Optional.ofNullable(status).map(VcOrderParseStatus::getValue).orElse(null);
	}

	@Named("mapBusinessTypeValue")
	default Integer mapBusinessTypeValue(VcOrderBusinessType type) {
		return Optional.ofNullable(type).map(VcOrderBusinessType::getValue).orElse(null);
	}

	// 发货窗口映射方法
	@Named("mapShippingWindowStart")
	default LocalDateTime mapShippingWindowStart(VcShippingWindow shippingWindow) {
		return Optional.ofNullable(shippingWindow).map(VcShippingWindow::getStartTime).orElse(null);
	}

	@Named("mapShippingWindowEnd")
	default LocalDateTime mapShippingWindowEnd(VcShippingWindow shippingWindow) {
		return Optional.ofNullable(shippingWindow).map(VcShippingWindow::getEndTime).orElse(null);
	}

	// 金额信息映射方法
	@Named("mapCurrency")
	default String mapCurrency(VcOrderAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderAmount::getCurrency).orElse(null);
	}

	@Named("mapProductPrice")
	default BigDecimal mapProductPrice(VcOrderAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderAmount::getProductPrice).orElse(null);
	}

	@Named("mapTaxRate")
	default BigDecimal mapTaxRate(VcOrderAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderAmount::getTaxRate).orElse(null);
	}

	@Named("mapTax")
	default BigDecimal mapTax(VcOrderAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderAmount::getTax).orElse(null);
	}

	// 地址信息映射方法
	@Named("mapShipFrom")
	default String mapShipFrom(VcOrderAddress address) {
		return Optional.ofNullable(address).map(VcOrderAddress::getShipFrom).orElse(null);
	}

	@Named("mapShipTo")
	default String mapShipTo(VcOrderAddress address) {
		return Optional.ofNullable(address).map(VcOrderAddress::getShipTo).orElse(null);
	}

	@Named("mapBillTo")
	default String mapBillTo(VcOrderAddress address) {
		return Optional.ofNullable(address).map(VcOrderAddress::getBillTo).orElse(null);
	}

	@Named("mapBuying")
	default String mapBuying(VcOrderAddress address) {
		return Optional.ofNullable(address).map(VcOrderAddress::getBuying).orElse(null);
	}

	// 商品信息映射方法
	@Named("mapProductPsku")
	default String mapProductPsku(VcProduct product) {
		return Optional.ofNullable(product).map(VcProduct::getPsku).orElse(null);
	}

	@Named("mapProductBarcode")
	default String mapProductBarcode(VcProduct product) {
		return Optional.ofNullable(product).map(VcProduct::getBarcode).orElse(null);
	}

	@Named("mapProductUnitsPerBox")
	default Integer mapProductUnitsPerBox(VcProduct product) {
		return Optional.ofNullable(product).map(VcProduct::getNumberOfUnitsPerBox).orElse(null);
	}

	@Named("mapProductImageId")
	default String mapProductImageId(VcProduct product) {
		return Optional.ofNullable(product).map(VcProduct::getImageId).orElse(null);
	}

	// 商品金额映射方法
	@Named("mapItemCurrency")
	default String mapItemCurrency(VcOrderItemAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderItemAmount::getCurrency).orElse(null);
	}

	@Named("mapItemUnitPrice")
	default BigDecimal mapItemUnitPrice(VcOrderItemAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderItemAmount::getUnitPrice).orElse(null);
	}

	@Named("mapItemTax")
	default BigDecimal mapItemTax(VcOrderItemAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderItemAmount::getTax).orElse(null);
	}

	@Named("mapItemSubTotal")
	default BigDecimal mapItemSubTotal(VcOrderItemAmount amount) {
		return Optional.ofNullable(amount).map(VcOrderItemAmount::getSubTotal).orElse(null);
	}

	// 拒绝原因映射方法
	@Named("mapRejectionReasonValue")
	default Integer mapRejectionReasonValue(RejectReason rejectionReason) {
		return Optional.ofNullable(rejectionReason).map(RejectReason::getValue).orElse(null);
	}

	/**
	 * 转换订单商品PO到商品
	 * @param item 订单商品PO
	 * @return VcOrderItem
	 */
	@Mapping(source = "item", target = "product", qualifiedByName = "mapItemProduct")
	@Mapping(source = "item", target = "amount", qualifiedByName = "mapItemAmount")
	@Mapping(source = "rejectionReason", target = "rejectionReason", qualifiedByName = "mapRejectionReason")
	VcOrderItem toItem(VcOrderItemPO item);

	/**
	 * 映射商品信息
	 */
	@Named("mapItemProduct")
	default VcProduct mapItemProduct(VcOrderItemPO item) {
		return VcProduct.builder()
			.psku(item.getPsku())
			.barcode(item.getBarcode())
			.numberOfUnitsPerBox(item.getNumberOfUnitsPerBox())
			.imageId(item.getImageId())
			.build();
	}

	/**
	 * 映射商品金额
	 */
	@Named("mapItemAmount")
	default VcOrderItemAmount mapItemAmount(VcOrderItemPO item) {
		return VcOrderItemAmount.builder()
			.currency(item.getCurrency())
			.unitPrice(item.getUnitPrice())
			.tax(item.getTax())
			.subTotal(item.getSubTotal())
			.build();
	}

	/**
	 * 映射拒绝原因
	 */
	@Named("mapRejectionReason")
	default RejectReason mapRejectionReason(Integer value) {
		return RejectReason.enumOf(value);
	}

	@Builder
	@Getter
	class ConverterDTO {
		private VcStore store;
		private List<VcOrderItemPO> items;
		private List<VcOrderInvoicePO> invoices;
		private VcOrderDiPO di;
		private List<VcOrderRemarkPO> remarks;
		private List<VcOrderItemLabelPO> itemLabels;
	}
}
