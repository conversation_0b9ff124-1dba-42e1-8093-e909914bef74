package com.renpho.erp.oms.application.returnorder.command;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 手动收货入参
 * @time: 2025-03-20 15:23:23
 * @author: <PERSON><PERSON>
 */
@Data
public class ManualReceiveCmd {

    /**
     * 退货订单主键
     */
    @NotNull(message = "RETURN_ORDER_ID_NOT_NULL")
    private Long returnOrderId;

    /**
     * 承运商
     */
    private String carrierName;

    /**
     * 跟踪号
     */
    private String trackingNo;

    /**
     * 收货时间
     */
    @NotNull(message = "RECEIVE_TIME_NOT_NULL")
    private LocalDateTime receiveTime;

}
