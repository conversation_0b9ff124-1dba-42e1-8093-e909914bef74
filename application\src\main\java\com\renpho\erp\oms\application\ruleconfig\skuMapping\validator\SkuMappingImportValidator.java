package com.renpho.erp.oms.application.ruleconfig.skuMapping.validator;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.enums.StatusEnum;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingUniqueEnum;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
public class SkuMappingImportValidator {

    private static final int MAX_SKU_LENGTH = 64;
    private static final int MIN_QUANTITY = 1;

    public void validateBasicFields(SkuMappingImportExcel importExcel, StringBuilder errorMsg,
                                   Map<String, StoreVo> storeMap, Map<String, OumUserInfoRes> operatorMap) {
        validateStore(importExcel, errorMsg, storeMap);
        validateFulfillmentType(importExcel, errorMsg);
        validateOperator(importExcel, errorMsg, operatorMap);
    }

    public void processAmzVcSpecialLogic(SkuMappingImportExcel importExcel, StoreVo storeVo, StringBuilder errorMsg) {
        if (isAmzVcStore(storeVo)) {
            importExcel.setMsku(null);
            importExcel.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
            importExcel.setFbaInventoryCounted(null);
            
            if (StringUtils.isBlank(importExcel.getAsin())) {
                addError(errorMsg, I18nMessageKit.getMessage("ASIN_REQUIRED_FOR_AMZ_VC"));
            }
        } else if (SkuMappingUniqueEnum.matchMskuUnique(storeVo.getSalesChannelCode())) {
            if (StringUtils.isEmpty(importExcel.getMsku())) {
                addError(errorMsg, I18nMessageKit.getMessage("SELLER_SKU_EMPTY"));
            }
        }
    }

    public void validateSkuFields(SkuMappingImportExcel importExcel, StringBuilder errorMsg) {
        if (StringUtils.isNotEmpty(importExcel.getMsku()) && importExcel.getMsku().length() > MAX_SKU_LENGTH) {
            addError(errorMsg, I18nMessageKit.getMessage("SELLER_SKU_TOO_LONG"));
        }
    }

    public void validateAsin(String channelCode, String asin, StringBuilder errorMsg) {
        Set<String> supportedChannels = Set.of(
                ChannelCode.TT_CHANNEL_CODE,
                ChannelCode.MCL_CHANNEL_CODE,
                ChannelCode.AMZ_CHANNEL_CODE,
                ChannelCode.EBY_CHANNEL_CODE
        );
        
        if (!supportedChannels.contains(channelCode)) {
            return;
        }
        
        String asinMsgKey = getAsinMessageKey(channelCode);
        if (asinMsgKey != null && StringUtils.isEmpty(asin)) {
            addError(errorMsg, I18nMessageKit.getMessage(asinMsgKey));
        }
        
        if (StringUtils.isNotEmpty(asin) && asin.length() > MAX_SKU_LENGTH) {
            addError(errorMsg, I18nMessageKit.getMessage("ASIN_TOO_LONG"));
        }
    }

    public void validateFbaInventory(String channelCode, SkuMappingImportExcel importExcel, StringBuilder errorMsg) {
        if (ChannelCode.AMZ_CHANNEL_CODE.equals(channelCode)) {
            if (Objects.isNull(importExcel.getFbaInventoryCounted())) {
                addError(errorMsg, I18nMessageKit.getMessage("FBA_INVENTORY_COUNTED_REQUIRE"));
            } else if (importExcel.getFbaInventoryCounted() < 0 || importExcel.getFbaInventoryCounted() > 1) {
                addError(errorMsg, I18nMessageKit.getMessage("FBA_ERROR"));
            }
        } else {
            importExcel.setFbaInventoryCounted(null);
        }
    }

    public void validateSkuMappingLines(List<SkuMappingImportExcel> lineList, FulfillmentType fulfillmentType, 
                                       Set<String> validPurchaseSkus, StringBuilder errorMsg) {
        int size = lineList.size();
        
        if (FulfillmentType.PLATFORM == fulfillmentType && size > 1) {
            addError(errorMsg, I18nMessageKit.getMessage("SKUMAPPING_LINE_TOO_MANY"));
        }
        
        Set<String> lineUniqueness = new HashSet<>();
        
        for (SkuMappingImportExcel line : lineList) {
            StringBuilder lineErrorMsg = new StringBuilder();
            validateLineUniqueness(line, lineUniqueness, lineErrorMsg);
            validateLineFields(line, size, validPurchaseSkus, fulfillmentType, lineErrorMsg);
            
            if (lineErrorMsg.length() > 0) {
                String existingError = line.getErrMessage();
                line.setErrMessage(StringUtils.isEmpty(existingError) 
                    ? lineErrorMsg.toString() 
                    : existingError + "\n" + lineErrorMsg);
            }
        }
    }

    private void validateStore(SkuMappingImportExcel importExcel, StringBuilder errorMsg, Map<String, StoreVo> storeMap) {
        if (StringUtils.isEmpty(importExcel.getStore())) {
            addError(errorMsg, I18nMessageKit.getMessage("STORE_EMPTY"));
            return;
        }
        
        if (!storeMap.containsKey(importExcel.getStore())) {
            addError(errorMsg, I18nMessageKit.getMessage("STORE_NO_EXITS"));
        }
    }

    private void validateFulfillmentType(SkuMappingImportExcel importExcel, StringBuilder errorMsg) {
        if (Objects.isNull(importExcel.getFulfillmentType())) {
            addError(errorMsg, I18nMessageKit.getMessage("FULFILMENTTYPE_EMPTY"));
        } else if (Objects.isNull(FulfillmentType.enumOf(importExcel.getFulfillmentType()))) {
            addError(errorMsg, I18nMessageKit.getMessage("FULFILMENTTYPE_NOT_EXITS"));
        }
    }

    private void validateOperator(SkuMappingImportExcel importExcel, StringBuilder errorMsg, Map<String, OumUserInfoRes> operatorMap) {
        if (StringUtils.isNotEmpty(importExcel.getOperatorNo()) && !operatorMap.containsKey(importExcel.getOperatorNo())) {
            addError(errorMsg, I18nMessageKit.getMessage("OPERATOR_NO_EXITS"));
        }
    }

    private void validateLineUniqueness(SkuMappingImportExcel line, Set<String> lineUniqueness, StringBuilder errorMsg) {
        String key = line.getPsku() + "_" + line.getFnSku();
        if (!lineUniqueness.add(key)) {
            addError(errorMsg, I18nMessageKit.getMessage("SKUMAPPING_LINE_REPEAT"));
        }
    }

    private void validateLineFields(SkuMappingImportExcel line, int size, Set<String> validPurchaseSkus, 
                                   FulfillmentType fulfillmentType, StringBuilder errorMsg) {
        // 零售单价验证
        if (Objects.isNull(line.getRetailPrice()) && size > 1) {
            addError(errorMsg, I18nMessageKit.getMessage("RETAILPRICE_IS_EMPTY"));
        }
        if (Objects.nonNull(line.getRetailPrice()) && line.getRetailPrice().compareTo(BigDecimal.ZERO) < 0) {
            addError(errorMsg, I18nMessageKit.getMessage("RETAILPRICE_LESS_THAN_ZERO"));
        }
        
        // 平台发货特殊处理
        if (fulfillmentType == FulfillmentType.PLATFORM) {
            line.setRetailPrice(null);
            line.setFnskuQuantity(1);
        }
        
        // 数量验证
        if (Objects.isNull(line.getFnskuQuantity()) && size > 1) {
            addError(errorMsg, I18nMessageKit.getMessage("FNSKUQUANTITY_IS_EMPTY"));
        }
        if (Objects.nonNull(line.getFnskuQuantity()) && size > 1 && line.getFnskuQuantity() < MIN_QUANTITY) {
            addError(errorMsg, I18nMessageKit.getMessage("FNSKUQUANTITY_LESS_THAN_ONE"));
        }
        
        // FN SKU验证
        if (StringUtils.isEmpty(line.getFnSku())) {
            addError(errorMsg, I18nMessageKit.getMessage("FN_SKU_REQUIRE"));
        } else if (line.getFnSku().length() > MAX_SKU_LENGTH) {
            addError(errorMsg, I18nMessageKit.getMessage("FN_SKU_TOO_LONG"));
        }
        
        // 采购SKU验证
        if (StringUtils.isEmpty(line.getPsku())) {
            addError(errorMsg, I18nMessageKit.getMessage("PURCHASE_SKU_EMPTY"));
        } else if (line.getPsku().length() > MAX_SKU_LENGTH) {
            addError(errorMsg, I18nMessageKit.getMessage("PURCHASE_SKU_TOO_LONG"));
        } else if (!validPurchaseSkus.contains(line.getPsku())) {
            addError(errorMsg, I18nMessageKit.getMessage("PURCHASE_SKU_NO_EXITS"));
        }
    }

    private boolean isAmzVcStore(StoreVo storeVo) {
        return ChannelCode.AMZ_CHANNEL_CODE.equals(storeVo.getSalesChannelCode())
                && StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType());
    }

    private String getAsinMessageKey(String channelCode) {
        return switch (channelCode) {
            case ChannelCode.AMZ_CHANNEL_CODE -> "ASIN_REQUIRE";
            case ChannelCode.TT_CHANNEL_CODE -> "TIKTOK_ASIN_REQUIRE";
            case ChannelCode.EBY_CHANNEL_CODE -> "EBY_ASIN_REQUIRE";
            case ChannelCode.MCL_CHANNEL_CODE -> "MERCADO_ASIN_REQUIRE";
            default -> null;
        };
    }

    private void addError(StringBuilder errorMsg, String message) {
        errorMsg.append(message).append("\n");
    }
}