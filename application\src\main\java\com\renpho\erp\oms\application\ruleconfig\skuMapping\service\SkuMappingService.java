package com.renpho.erp.oms.application.ruleconfig.skuMapping.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingUnique;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingUniqueEnum;
import com.renpho.erp.oms.infrastructure.common.util.HttpUtil;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.lock.annotation.Lock4j;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingAddCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.command.SkuMappingUpdateCmd;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor.SkuMappingAppConvertor;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor.SkuMappingCmdConvertor;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor.SkuMappingExcelConvertor;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.convertor.SkuMappingImportExcelConvertor;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.validator.SkuMappingImportValidator;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.loader.SkuMappingDataLoader;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.processor.AmzVcSpecialLogicProcessor;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingExportExcel;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.dto.SkuMappingImportExcel;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.optlog.SkuMappingAddSnapSource;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.optlog.SkuMappingBusinessModule;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.optlog.SkuMappingUpdateSnapSource;
import com.renpho.erp.oms.application.ruleconfig.skuMapping.vo.SkuMappingVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingFnListQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingPageQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingUniqueQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.enums.StatusEnum;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.model.SkuMappingAggRoot;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingLineVO;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingPageVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.enums.StoreTypeEnum;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.convert.skuMapping.SkuMappingLineConvertor;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.oms.infrastructure.persistence.skuMapping.mapper.SkuMappingLineMapper;
import com.renpho.erp.oms.infrastructure.persistence.skuMapping.mapper.SkuMappingMapper;
import com.renpho.erp.oms.infrastructure.persistence.skuMapping.po.SkuMappingLinePO;
import com.renpho.erp.oms.infrastructure.persistence.skuMapping.po.SkuMappingPO;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.erp.pds.client.vo.PdsProductManagerBasicViewVo;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@AllArgsConstructor
public class SkuMappingService {
    private final SkuMappingLineConvertor skuMappingLineConvertor;
    private final SkuMappingRepository skuMappingRepository;
    private final SkuMappingAppConvertor skuMappingAppConvertor;
    private final SkuMappingCmdConvertor skuMappingCmdConvertor;
    private final SkuMappingExcelConvertor skuMappingExcelConvertor;
    private final StoreClient storeClient;
    private final UserClient userClient;
    private final ProductClient productClient;
    private final FileClient fileClient;
    private final I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;
    private final SkuMappingMapper skuMappingMapper;
    private final SkuMappingLineMapper skuMappingLineMapper;
    private final SkuMappingImportValidator importValidator;
    private final SkuMappingDataLoader dataLoader;
    private final AmzVcSpecialLogicProcessor amzVcProcessor;

    @Lock4j(name = "oms:addSkuMapping", keys = {"#skuMappingAddCmd.channelId", "#skuMappingAddCmd.storeId", "#skuMappingAddCmd.msku"})
    public R<Long> addSkuMapping(SkuMappingAddCmd skuMappingAddCmd) {
        // 校验店铺信息
        StoreVo storeVo = validateStore(skuMappingAddCmd);

        // AMZ+VC特殊逻辑处理
        handleAmzVcSpecialLogic(storeVo, skuMappingAddCmd);

        // 校验平台 + 店铺 + 销售 SKU 的唯一性
        validateUniqueSkuMapping(storeVo, skuMappingAddCmd);
        // 校验映射行
        validateSkuMappingLines(skuMappingAddCmd.getSkuMappingLineAddCmdList(),
                FulfillmentType.enumOf(skuMappingAddCmd.getFulfillmentType()));
        // 校验操作员信息（如果有）
        validateOperator(skuMappingAddCmd.getOperatorId());

        // 保存映射并返回结果
        SkuMappingAggRoot skuMappingAggRoot = skuMappingCmdConvertor.toDomain(skuMappingAddCmd);
        skuMappingAggRoot.setChannelCode(storeVo.getSalesChannelCode());
        skuMappingAggRoot.setStoreType(storeVo.getStoreType());
        skuMappingAggRoot.setSkuMappingLineList(
                skuMappingAddCmd.getSkuMappingLineAddCmdList().stream().map(skuMappingCmdConvertor::toDomain).collect(Collectors.toList()));
        skuMappingAggRoot.validateBaseInfo(storeVo.getSalesChannelCode(), storeVo.getStoreType(), errorMessage -> {
            throw new BusinessException(I18nMessageKit.getMessage(errorMessage));
        });

        return SpringUtil.getBean(SkuMappingService.class).doInsert(skuMappingAggRoot);
    }


    private StoreVo validateStore(SkuMappingAddCmd skuMappingAddCmd) {
        List<StoreVo> storeVOS = storeClient.getByStoreIds(Collections.singletonList(skuMappingAddCmd.getStoreId()));
        if (CollectionUtil.isEmpty(storeVOS)) {
            throw new BusinessException(I18nMessageKit.getMessage("STORE_NO_EXITS"));
        }
        StoreVo storeVo = storeVOS.get(0);
        if (!storeVo.getSalesChannelId().equals(skuMappingAddCmd.getChannelId())) {
            throw new BusinessException(I18nMessageKit.getMessage("CHANNEL_NO_EXITS"));
        }
        return storeVo;
    }

    /**
     * 处理AMZ+VC的特殊逻辑
     * 当销售渠道=="AMZ" 且 店铺类型=="VC"时：
     * - MSKU：清空且禁用
     * - ASIN必填
     * - 发货方式：强制平台发货，不可修改
     * - 统计FBA库存：强制为空，不可修改
     */
    private void handleAmzVcSpecialLogic(StoreVo storeVo, SkuMappingAddCmd skuMappingAddCmd) {
        // 检查是否为AMZ渠道且VC店铺类型
        if (ChannelCode.AMZ_CHANNEL_CODE.equals(storeVo.getSalesChannelCode())
                && StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType())) {

            // MSKU：清空
            skuMappingAddCmd.setMsku(null);

            // ASIN必填验证
            if (StringUtils.isBlank(skuMappingAddCmd.getAsin())) {
                throw new BusinessException(I18nMessageKit.getMessage("ASIN_REQUIRED_FOR_AMZ_VC"));
            }

            // 发货方式：强制平台发货
            skuMappingAddCmd.setFulfillmentType(FulfillmentType.PLATFORM.getValue());

            // 统计FBA库存：强制为空
            skuMappingAddCmd.setFbaInventoryCounted(null);
        }
    }

    /**
     * 处理AMZ+VC的特殊逻辑（更新时）
     * 当销售渠道=="AMZ" 且 店铺类型=="VC"时：
     * - MSKU：清空且禁用
     * - ASIN必填
     * - 发货方式：强制平台发货，不可修改
     * - 统计FBA库存：强制为空，不可修改
     */
    private void handleAmzVcSpecialLogicForUpdate(StoreVo storeVo, SkuMappingUpdateCmd skuMappingUpdateCmd, SkuMappingAggRoot originalSkuMapping) {
        // 检查是否为AMZ渠道且VC店铺类型
        if (ChannelCode.AMZ_CHANNEL_CODE.equals(storeVo.getSalesChannelCode())
                && StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType())) {

            // MSKU：清空
            skuMappingUpdateCmd.setMsku(null);

            // ASIN必填验证
            if (StringUtils.isBlank(skuMappingUpdateCmd.getAsin())) {
                throw new BusinessException(I18nMessageKit.getMessage("ASIN_REQUIRED_FOR_AMZ_VC"));
            }

            // 统计FBA库存：强制为空
            skuMappingUpdateCmd.setFbaInventoryCounted(null);
        }
    }

    private void validateUniqueSkuMapping(StoreVo storeVo, SkuMappingAddCmd skuMappingAddCmd) {
        // 对于AMZ+VC的特殊情况，使用特殊的唯一性检查逻辑
        String channelCodeForUnique = storeVo.getSalesChannelCode();
        if (ChannelCode.AMZ_CHANNEL_CODE.equals(storeVo.getSalesChannelCode())
                && StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType())) {
            // AMZ+VC使用EBAY的唯一性逻辑（店铺 + ASIN + 发货方式）
            channelCodeForUnique = SaleChannelType.EBAY.getValue();
        }

        Boolean exist = skuMappingRepository.uniqueIndexExist(channelCodeForUnique,
                SkuMappingUniqueQuery.builder()
                        .asin(skuMappingAddCmd.getAsin())
                        .msku(skuMappingAddCmd.getMsku())
                        .fulfillmentType(FulfillmentType.enumOf(skuMappingAddCmd.getFulfillmentType()))
                        .storeId(skuMappingAddCmd.getStoreId())
                        .build());
        if (exist) {
            throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_EXITS"));
        }
    }

    private void validateSkuMappingLines(List<SkuMappingAddCmd.SkuMappingLineAddCmd> skuMappingLineAddCmdList,
                                         FulfillmentType fulfillmentType) {
        if (Objects.isNull(fulfillmentType)) {
            throw new BusinessException(I18nMessageKit.getMessage("FULFILMENTTYPE_EMPTY"));
        }
        if (CollectionUtil.isEmpty(skuMappingLineAddCmdList)) {
            throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_LINE_EMPTY"));
        }

        int size = skuMappingLineAddCmdList.size();
        if (FulfillmentType.PLATFORM == fulfillmentType && size > 1) {
            throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_LINE_TOO_MANY"));
        }

        Set<String> lineUniqueness = new HashSet<>();
        Set<String> purchaseSkuSet = fetchValidPurchaseSkuSet(skuMappingLineAddCmdList);

        for (SkuMappingAddCmd.SkuMappingLineAddCmd line : skuMappingLineAddCmdList) {
            validateLineUniqueness(line, lineUniqueness);
            validateLineFields(line, size, purchaseSkuSet, fulfillmentType);
        }
    }

    private Set<String> fetchValidPurchaseSkuSet(List<SkuMappingAddCmd.SkuMappingLineAddCmd> lines) {
        List<String> distinctPurchaseSkus = lines.stream()
                .map(SkuMappingAddCmd.SkuMappingLineAddCmd::getPsku)
                .distinct()
                .collect(Collectors.toList());

        return productClient.getPurchaseSku(distinctPurchaseSkus)
                .stream()
                .map(PdsProductManagerBasicViewVo::getPurchaseSku)
                .collect(Collectors.toSet());
    }

    private void validateLineUniqueness(SkuMappingAddCmd.SkuMappingLineAddCmd line, Set<String> lineUniqueness) {
        String key = line.getPsku() + "_" + line.getFnsku();
        if (!lineUniqueness.add(key)) {
            throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_LINE_REPEAT"));
        }
    }

    private void validateLineFields(SkuMappingAddCmd.SkuMappingLineAddCmd line, int size, Set<String> purchaseSkuSet, FulfillmentType fulfillmentType) {
        if (Objects.isNull(line.getRetailPrice()) && size > 1) {
            throw new BusinessException(I18nMessageKit.getMessage("RETAILPRICE_IS_EMPTY"));
        }

        if (fulfillmentType == FulfillmentType.PLATFORM) {
            line.setRetailPrice(null);
            line.setFnskuQuantity(1);
        }
        if (Objects.nonNull(line.getRetailPrice()) && line.getRetailPrice().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(I18nMessageKit.getMessage("RETAILPRICE_LESS_THAN_ZERO"));
        }

        if (Objects.isNull(line.getFnskuQuantity()) && size > 1) {
            throw new BusinessException(I18nMessageKit.getMessage("FNSKUQUANTITY_IS_EMPTY"));
        }

        if (Objects.nonNull(line.getFnskuQuantity()) && size > 1 && line.getFnskuQuantity() < 1) {
            throw new BusinessException(I18nMessageKit.getMessage("FNSKUQUANTITY_LESS_THAN_ONE"));
        }

        validateField("FN_SKU_REQUIRE", Objects.isNull(line.getFnsku()));
        validateField("FN_SKU_TOO_LONG", line.getFnsku().length() > 64);
        validateField("PURCHASE_SKU_EMPTY", StringUtils.isEmpty(line.getPsku()));
        validateField("PURCHASE_SKU_TOO_LONG", line.getPsku().length() > 64);
        validateField("PURCHASE_SKU_NO_EXITS", !purchaseSkuSet.contains(line.getPsku()));
    }

    private void validateAsinForSpecificChannels(String channelCode, String asin) {
        validateAsin(channelCode, asin, (msgKey, condition) -> {
            if (condition) {
                validateField(msgKey, true);
            }
        });
    }

    /**
     * 通用的 ASIN 校验逻辑
     */
    private void validateAsin(String channelCode, String asin, BiConsumer<String, Boolean> errorHandler) {
        Set<String> supportedChannels = Set.of(
                ChannelCode.TT_CHANNEL_CODE,
                ChannelCode.MCL_CHANNEL_CODE,
                ChannelCode.AMZ_CHANNEL_CODE,
                ChannelCode.EBY_CHANNEL_CODE
        );
        if (!supportedChannels.contains(channelCode)) {
            return;
        }
        String asinMsgKey = switch (channelCode) {
            case ChannelCode.AMZ_CHANNEL_CODE -> "ASIN_REQUIRE";
            case ChannelCode.TT_CHANNEL_CODE -> "TIKTOK_ASIN_REQUIRE";
            case ChannelCode.EBY_CHANNEL_CODE -> "EBY_ASIN_REQUIRE";
            case ChannelCode.MCL_CHANNEL_CODE -> "MERCADO_ASIN_REQUIRE";
            default -> null;
        };
        if (asinMsgKey != null) {
            errorHandler.accept(asinMsgKey, StringUtils.isEmpty(asin));
        }
        errorHandler.accept("ASIN_TOO_LONG", StringUtils.isNotEmpty(asin) && asin.length() > 64);
    }

    private void validateOperator(Integer operatorId) {
        if (Objects.nonNull(operatorId)) {
            List<OumUserInfoRes> users = userClient.getUserByIds(Collections.singletonList(operatorId));
            if (CollectionUtil.isEmpty(users)) {
                throw new BusinessException(I18nMessageKit.getMessage("OPERATOR_NO_EXITS"));
            }
        }
    }

    private void validateField(String messageKey, boolean condition) {
        if (condition) {
            throw new BusinessException(I18nMessageKit.getMessage(messageKey));
        }
    }

    public R<SkuMappingVO> getById(Long id) {
        SkuMappingAggRoot skuMappingAggRoot = skuMappingRepository.getById(id);
        if (Objects.nonNull(skuMappingAggRoot)) {
            SkuMappingVO skuMappingVO = skuMappingAppConvertor.toVO(skuMappingAggRoot);
            skuMappingVO.setSkuMappingLineVOList(skuMappingAggRoot.getSkuMappingLineList().stream().map(skuMappingLine -> {
                return skuMappingAppConvertor.toVO(skuMappingLine);
            }).collect(Collectors.toList()));
            // 查询店铺信息
            StoreVo storeVO = storeClient.getByStoreIds(Arrays.asList(skuMappingVO.getStoreId())).get(0);
            if (Objects.nonNull(storeVO)) {
                skuMappingVO.setStore(storeVO.getStoreName());
                skuMappingVO.setChannel(storeVO.getSalesChannel());
            }
            if (Objects.nonNull(skuMappingVO.getOperatorId())) {
                // 查询用户信息
                OumUserInfoRes oumUserInfo = userClient.getUserByIds(Arrays.asList(skuMappingVO.getOperatorId())).get(0);
                if (Objects.nonNull(oumUserInfo)) {
                    skuMappingVO.setOperator(oumUserInfo.getName());
                    skuMappingVO.setOperatorNo(oumUserInfo.getCode());
                }
            }
            return R.success(skuMappingVO);
        }
        return R.fail(new BusinessException(I18nMessageKit.getMessage("DATA_NOT_EXITS")));
    }

    public R<Integer> update(SkuMappingUpdateCmd skuMappingUpdateCmd) {
        SkuMappingAggRoot skuMapping = skuMappingRepository.getById(skuMappingUpdateCmd.getId());
        if (Objects.isNull(skuMapping)) {
            throw new BusinessException(I18nMessageKit.getMessage("SKUMAPPING_NO_EXITS"));
        }
        // 远程调用查询店铺信息
        StoreVo storeVO = storeClient.getByStoreIds(Arrays.asList(skuMapping.getStoreId())).get(0);
        skuMapping.setStoreType(storeVO.getStoreType());

        // AMZ+VC特殊逻辑处理
        handleAmzVcSpecialLogicForUpdate(storeVO, skuMappingUpdateCmd, skuMapping);

        // 唯一值不允许修改，其他字段可以修改
        if (SkuMappingUniqueEnum.matchMskuUnique(storeVO.getSalesChannelCode())) {
            // MSKU 为原来的值
            skuMappingUpdateCmd.setMsku(skuMapping.getMsku());
        }
        if (SkuMappingUniqueEnum.matchAsinUnique(storeVO.getSalesChannelCode())) {
            // ASKU 为原来的值
            skuMappingUpdateCmd.setAsin(skuMapping.getAsin());
        }

        // 校验映射行
        validateSkuMappingLines(skuMappingUpdateCmd.getSkuMappingLineUpdateCmdList(), skuMapping.getFulfillmentType());

        // 校验操作员信息（如果有）
        validateOperator(skuMappingUpdateCmd.getOperatorId());

        SkuMappingAggRoot skuMappingAggRoot = skuMappingCmdConvertor.toDomain(skuMappingUpdateCmd);
        skuMappingAggRoot.setChannelCode(storeVO.getSalesChannelCode());
        skuMappingAggRoot.setStoreType(storeVO.getStoreType());
        skuMappingAggRoot.setSkuMappingLineList(skuMappingUpdateCmd.getSkuMappingLineUpdateCmdList().stream().map(skuMappingCmdConvertor::toDomain).collect(Collectors.toList()));
        skuMappingAggRoot.validateBaseInfo(storeVO.getSalesChannelCode(), storeVO.getStoreType(), errorMessage -> {
            throw new BusinessException(I18nMessageKit.getMessage(errorMessage));
        });
        return R.success(SpringUtil.getBean(SkuMappingService.class).doUpdate(skuMapping, skuMappingAggRoot));

    }

    @Transactional
    public Integer doUpdate(SkuMappingAggRoot oldSkuMapping, SkuMappingAggRoot newSkuMapping) {
        // 设置旧版本为禁用
        SpringUtil.getBean(SkuMappingService.class).changeStatus(oldSkuMapping.getId(), StatusEnum.DISABLE, oldSkuMapping.getStatus());
        newSkuMapping.setVersion(skuMappingRepository.getLastVersionByUniquxLine(oldSkuMapping.getUniquxLine()) + 1);
        newSkuMapping.setStatus(StatusEnum.ENABLE);
        newSkuMapping.setStoreId(oldSkuMapping.getStoreId());
        newSkuMapping.setChannelId(oldSkuMapping.getChannelId());
        newSkuMapping.setFulfillmentType(oldSkuMapping.getFulfillmentType());
        SpringUtil.getBean(SkuMappingService.class).doInsert(newSkuMapping);
        return newSkuMapping.getVersion();
    }

    public R<Paging<SkuMappingPageVO>> page(SkuMappingPageQuery skuMappingPageQuery) {
        IPage<SkuMappingPageVO> page = skuMappingRepository.page(skuMappingPageQuery);
        List<SkuMappingPageVO> pageRecords = page.getRecords();
        if (CollectionUtil.isEmpty(pageRecords)) {
            return R.success(Paging.of(Lists.newArrayList(), (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));
        }
        List<Integer> storeIds = pageRecords.stream().map(SkuMappingPageVO::getStoreId).distinct().collect(Collectors.toList());
        List<Integer> userIds = pageRecords.stream()
                .filter(skuMapping -> Objects.nonNull(skuMapping.getOperatorId()))
                .map(SkuMappingPageVO::getOperatorId)
                .collect(Collectors.toList());
        List<Integer> updateUserIds = pageRecords.stream().map(SkuMappingPageVO::getUpdateBy).distinct().collect(Collectors.toList());
        userIds.addAll(updateUserIds);
        List<Integer> allUserIds = userIds.stream().distinct().collect(Collectors.toList());
        // 查询店铺信息
        Map<Integer, StoreVo> storeVOMap = storeClient.getByStoreIds(storeIds)
                .stream()
                .collect(Collectors.toMap(StoreVo::getId, Function.identity()));
        // 查询用户信息
        Map<Integer, OumUserInfoRes> oumUserInfoResMap = userClient.getUserByIds(allUserIds)
                .stream()
                .collect(Collectors.toMap(OumUserInfoRes::getId, Function.identity()));
        pageRecords.stream().map(skuMappingPageVO -> {
            StoreVo storeVO = storeVOMap.get(skuMappingPageVO.getStoreId());
            if (Objects.nonNull(storeVO)) {
                skuMappingPageVO.setStore(storeVO.getStoreName());
                skuMappingPageVO.setChannel(storeVO.getSalesChannel());
            }
            if (Objects.nonNull(skuMappingPageVO.getOperatorId())) {
                // 用户信息
                OumUserInfoRes oumUserInfo = oumUserInfoResMap.get(skuMappingPageVO.getOperatorId());
                if (Objects.nonNull(oumUserInfo)) {
                    skuMappingPageVO.setOperator(oumUserInfo.getName());
                    skuMappingPageVO.setOperatorNo(oumUserInfo.getCode());
                }
            }
            OumUserInfoRes updateUserInfoRes = oumUserInfoResMap.get(skuMappingPageVO.getUpdateBy());
            if (Objects.nonNull(updateUserInfoRes)) {
                skuMappingPageVO.setUpdater(updateUserInfoRes.getName());
                skuMappingPageVO.setUpdaterNo(updateUserInfoRes.getCode());
            }
            return skuMappingPageVO;
        }).collect(Collectors.toList());
        return R.success(Paging.of(pageRecords, (int) page.getTotal(), (int) page.getSize(), (int) page.getCurrent()));

    }

    /**
     * 分页导出Excel，分页count优化：仅第一页count，后续分页不再重复count，total复用第一页结果
     */
    /**
     * 分页导出Excel，分页count优化：仅第一页count，后续分页不再重复count，total复用第一页结果
     */
    public void exportExcel(SkuMappingPageQuery skuMappingPageQuery, HttpServletResponse response) {

        try {
            String fileName = "销售SKU映射.xlsx";
            HttpUtil.setExportResponseHeader(response, fileName);
            try (ServletOutputStream out = response.getOutputStream();
                 ExcelWriter excelWriter = EasyExcel.write(out, SkuMappingExportExcel.class)
                         .registerWriteHandler(i18nHeaderCellWriteHandler)
                         .build()) {

                WriteSheet writeSheet = EasyExcel.writerSheet("sheet").build();
                skuMappingPageQuery.setPageSize(1000);
                int pageIndex = 1;
                long total = -1; // 记录第一页total

                while (true) {
                    skuMappingPageQuery.setPageIndex(pageIndex);
                    // 仅第一页count，后续分页跳过count
                    if (pageIndex == 1) {
                        // 默认count
                        skuMappingPageQuery.setSkipCount(false);
                    } else {
                        skuMappingPageQuery.setSkipCount(true);
                        skuMappingPageQuery.setTotal(total); // 复用第一页total
                    }
                    Paging<SkuMappingPageVO> page = this.page(skuMappingPageQuery).getData();

                    if (pageIndex == 1) {
                        total = page.getTotalCount();
                    }

                    List<SkuMappingExportExcel> excelData = page.getRecords().stream().map(skuMapping -> {
                        SkuMappingExportExcel skuMappingExportExcel = skuMappingExcelConvertor.toExcel(skuMapping);
                        Optional.ofNullable(skuMappingExportExcel.getUpdateTime())
                                .map(LocalDateTimeTransKits::tansByUserZoneId)
                                .ifPresent(skuMappingExportExcel::setUpdateTime);
                        return skuMappingExportExcel;
                    }).collect(Collectors.toList());

                    if (excelData.isEmpty()) {
                        break;
                    }

                    excelWriter.write(excelData, writeSheet);
                    pageIndex++;
                }

                excelWriter.finish();
            }
        } catch (IOException e) {
            log.error("导出Excel失败", e);
            throw new BusinessException(I18nMessageKit.getMessage("FILE_EXPORT_EXCEPTION"));
        }
    }

    @Transactional
    public R importExcel(List<SkuMappingImportExcel> skuMappingImportExcels) {
        log.info("开始处理Excel导入，总记录数: {}", skuMappingImportExcels.size());

        // 第一步：加载所有必需的基础数据
        SkuMappingDataLoader.LoadedData loadedData = dataLoader.loadRequiredData(skuMappingImportExcels);

        // 第二步：基础字段验证和特殊逻辑处理
        ImportValidationResult validationResult = validateAndProcessImportData(skuMappingImportExcels, loadedData);

        // 第三步：按唯一键分组处理
        ImportGroupingResult groupingResult = groupAndValidateImportData(validationResult.getValidRecords(), loadedData);

        // 第四步：收集所有错误并返回错误文件（如果有错误）
        List<SkuMappingImportExcel> allErrors = collectAllErrors(validationResult.getErrorRecords(), groupingResult);
        if (CollectionUtil.isNotEmpty(allErrors)) {
            log.warn("导入过程中发现 {} 个错误记录", allErrors.size());
            return fileClient.getErrorExcelUrl(allErrors, SkuMappingImportExcel.class);
        }

        // 第五步：执行数据库操作
        executeDataOperations(groupingResult, loadedData);

        log.info("Excel导入成功完成");
        return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
    }

    // 内部数据结构定义
    private static class ImportValidationResult {
        private final List<SkuMappingImportExcel> validRecords;
        private final List<SkuMappingImportExcel> errorRecords;

        public ImportValidationResult(List<SkuMappingImportExcel> validRecords, List<SkuMappingImportExcel> errorRecords) {
            this.validRecords = validRecords;
            this.errorRecords = errorRecords;
        }

        public List<SkuMappingImportExcel> getValidRecords() {
            return validRecords;
        }

        public List<SkuMappingImportExcel> getErrorRecords() {
            return errorRecords;
        }
    }

    private static class ImportGroupingResult {
        private final List<List<SkuMappingImportExcel>> insertGroups;
        private final List<List<SkuMappingImportExcel>> updateGroups;

        public ImportGroupingResult(List<List<SkuMappingImportExcel>> insertGroups, List<List<SkuMappingImportExcel>> updateGroups) {
            this.insertGroups = insertGroups;
            this.updateGroups = updateGroups;
        }

        public List<List<SkuMappingImportExcel>> getInsertGroups() {
            return insertGroups;
        }

        public List<List<SkuMappingImportExcel>> getUpdateGroups() {
            return updateGroups;
        }
    }

    private ImportValidationResult validateAndProcessImportData(List<SkuMappingImportExcel> importExcels,
                                                                SkuMappingDataLoader.LoadedData loadedData) {
        List<SkuMappingImportExcel> validRecords = new ArrayList<>();
        List<SkuMappingImportExcel> errorRecords = new ArrayList<>();

        for (SkuMappingImportExcel importExcel : importExcels) {
            StringBuilder errorMsg = new StringBuilder();

            // 基础字段验证
            importValidator.validateBasicFields(importExcel, errorMsg, loadedData.getStoreMap(), loadedData.getOperatorMap());

            // 处理AMZ+VC特殊逻辑
            if (loadedData.getStoreMap().containsKey(importExcel.getStore())) {
                StoreVo storeVo = loadedData.getStoreMap().get(importExcel.getStore());
                amzVcProcessor.processAmzVcSpecialLogic(importExcel, storeVo);
                importValidator.processAmzVcSpecialLogic(importExcel, storeVo, errorMsg);
            }

            // SKU字段验证
            importValidator.validateSkuFields(importExcel, errorMsg);

            if (errorMsg.length() > 0) {
                importExcel.setErrMessage(errorMsg.toString());
                errorRecords.add(importExcel);
            } else {
                validRecords.add(importExcel);
            }
        }

        return new ImportValidationResult(validRecords, errorRecords);
    }

    private ImportGroupingResult groupAndValidateImportData(List<SkuMappingImportExcel> validRecords,
                                                            SkuMappingDataLoader.LoadedData loadedData) {
        List<List<SkuMappingImportExcel>> insertGroups = new ArrayList<>();
        List<List<SkuMappingImportExcel>> updateGroups = new ArrayList<>();

        // 按唯一键分组
        Map<String, List<SkuMappingImportExcel>> groupedByUniqueKey = validRecords.stream()
                .collect(Collectors.groupingBy(item -> {
                    StoreVo storeVo = loadedData.getStoreMap().get(item.getStore());
                    return amzVcProcessor.generateUniqueKey(storeVo, item);
                }));

        // 处理每个分组
        for (List<SkuMappingImportExcel> group : groupedByUniqueKey.values()) {
            if (group.isEmpty()) continue;

            SkuMappingImportExcel firstRecord = group.get(0);
            StoreVo storeVo = loadedData.getStoreMap().get(firstRecord.getStore());

            // 验证分组内的映射行
            StringBuilder groupErrorMsg = new StringBuilder();
            validateGroupMappingLines(group, storeVo, loadedData.getValidPurchaseSkus(), groupErrorMsg);

            // 检查是否已存在
            boolean exists = checkUniqueKeyExists(firstRecord, storeVo);

            // 设置错误信息
            if (groupErrorMsg.length() > 0) {
                for (SkuMappingImportExcel record : group) {
                    String existingError = record.getErrMessage();
                    record.setErrMessage(StringUtils.isEmpty(existingError)
                            ? groupErrorMsg.toString()
                            : existingError + "\n" + groupErrorMsg);
                }
            }

            if (exists) {
                updateGroups.add(group);
            } else {
                insertGroups.add(group);
            }
        }

        return new ImportGroupingResult(insertGroups, updateGroups);
    }

    private void validateGroupMappingLines(List<SkuMappingImportExcel> group, StoreVo storeVo,
                                           Set<String> validPurchaseSkus, StringBuilder errorMsg) {
        SkuMappingImportExcel firstRecord = group.get(0);
        FulfillmentType fulfillmentType = FulfillmentType.enumOf(firstRecord.getFulfillmentType());

        importValidator.validateSkuMappingLines(group, fulfillmentType, validPurchaseSkus, errorMsg);
        importValidator.validateAsin(storeVo.getSalesChannelCode(), firstRecord.getAsin(), errorMsg);
        if(!StoreTypeEnum.VC.getValue().equals(storeVo.getStoreType())) {
            importValidator.validateFbaInventory(storeVo.getSalesChannelCode(), firstRecord, errorMsg);
        }

    }

    private boolean checkUniqueKeyExists(SkuMappingImportExcel record, StoreVo storeVo) {
        String channelCodeForUnique = amzVcProcessor.getChannelCodeForUnique(storeVo);
        return skuMappingRepository.uniqueIndexExist(channelCodeForUnique,
                SkuMappingUniqueQuery.builder()
                        .asin(record.getAsin())
                        .msku(record.getMsku())
                        .fulfillmentType(FulfillmentType.enumOf(record.getFulfillmentType()))
                        .storeId(storeVo.getId())
                        .build());
    }

    private List<SkuMappingImportExcel> collectAllErrors(List<SkuMappingImportExcel> validationErrors,
                                                         ImportGroupingResult groupingResult) {
        List<SkuMappingImportExcel> allErrors = new ArrayList<>(validationErrors);

        // 收集插入组中的错误
        allErrors.addAll(groupingResult.getInsertGroups().stream()
                .flatMap(Collection::stream)
                .filter(record -> StringUtils.isNotEmpty(record.getErrMessage()))
                .collect(Collectors.toList()));

        // 收集更新组中的错误
        allErrors.addAll(groupingResult.getUpdateGroups().stream()
                .flatMap(Collection::stream)
                .filter(record -> StringUtils.isNotEmpty(record.getErrMessage()))
                .collect(Collectors.toList()));

        return allErrors;
    }

    private void executeDataOperations(ImportGroupingResult groupingResult, SkuMappingDataLoader.LoadedData loadedData) {
        // 执行新增操作
        for (List<SkuMappingImportExcel> insertGroup : groupingResult.getInsertGroups()) {
            if (insertGroup.stream().anyMatch(record -> StringUtils.isNotEmpty(record.getErrMessage()))) {
                continue; // 跳过有错误的组
            }

            SkuMappingImportExcel firstRecord = insertGroup.get(0);
            StoreVo storeVo = loadedData.getStoreMap().get(firstRecord.getStore());
            OumUserInfoRes operator = loadedData.getOperatorMap().get(firstRecord.getOperatorNo());

            SkuMappingAggRoot aggRoot = SkuMappingImportExcelConvertor.convertByImportExcleList(operator, storeVo, insertGroup);
            SpringUtil.getBean(SkuMappingService.class).doInsert(aggRoot);
        }

        // 执行更新操作
        for (List<SkuMappingImportExcel> updateGroup : groupingResult.getUpdateGroups()) {
            if (updateGroup.stream().anyMatch(record -> StringUtils.isNotEmpty(record.getErrMessage()))) {
                continue; // 跳过有错误的组
            }

            SkuMappingImportExcel firstRecord = updateGroup.get(0);
            StoreVo storeVo = loadedData.getStoreMap().get(firstRecord.getStore());
            OumUserInfoRes operator = loadedData.getOperatorMap().get(firstRecord.getOperatorNo());

            // 获取原记录
            String channelCodeForUnique = amzVcProcessor.getChannelCodeForUnique(storeVo);
            SkuMappingAggRoot oldMapping = skuMappingRepository.getByUniqueIndexExist(channelCodeForUnique,
                    SkuMappingUniqueQuery.builder()
                            .asin(firstRecord.getAsin())
                            .msku(firstRecord.getMsku())
                            .fulfillmentType(FulfillmentType.enumOf(firstRecord.getFulfillmentType()))
                            .storeId(storeVo.getId())
                            .build());

            SkuMappingAggRoot newMapping = SkuMappingImportExcelConvertor.convertByImportExcleList(operator, storeVo, updateGroup);
            SpringUtil.getBean(SkuMappingService.class).doUpdate(oldMapping, newMapping);
        }
    }


    public R<List<SkuMappingFulfillmentVO>> list(SkuMappingQuery skuMappingQuery) {
        return R.success(skuMappingRepository.list(skuMappingQuery));
    }

    public R<List<String>> listFnsku() {
        return R.success(skuMappingRepository.listFnsku());
    }

    @OpLog(snaptSource = SkuMappingAddSnapSource.class, title = "新增销售SKU映射", businessType = BusinessType.INSERT,
            businessModule = SkuMappingBusinessModule.class, systemModule = OmsSystemModule.class)
    public R<Long> doInsert(SkuMappingAggRoot skuMappingAggRoot) {
        return R.success(skuMappingRepository.addSkuMapping(skuMappingAggRoot));
    }

    @OpLog(snaptSource = SkuMappingUpdateSnapSource.class, title = "更新销售SKU映射", businessType = BusinessType.UPDATE,
            businessModule = SkuMappingBusinessModule.class, systemModule = OmsSystemModule.class)
    public R<Long> changeStatus(Long id, StatusEnum newStatus, StatusEnum oldStatus) {
        if (skuMappingRepository.changeStatus(id, newStatus, oldStatus)) {
            return R.success(id);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("UPDATE_FAIL"));
        }
    }

    public R<Long> enable(Long id, StatusEnum newStatus, StatusEnum oldStatus) {
        return SpringUtil.getBean(SkuMappingService.class).changeStatus(id, newStatus, oldStatus);
    }

    public R<Long> disable(Long id, StatusEnum newStatus, StatusEnum oldStatus) {
        return SpringUtil.getBean(SkuMappingService.class).changeStatus(id, newStatus, oldStatus);
    }

    public List<SkuMappingLineVO> fnList(SkuMappingFnListQuery skuMappingQuery) {

        List<Integer> channelIdList = skuMappingQuery.getChannelIdList();
        List<Integer> storeIdList = skuMappingQuery.getStoreIdList();
        List<String> pskuList = skuMappingQuery.getPskuList();
        List<String> mskuList = skuMappingQuery.getMskuList();
//        if (CollectionUtils.isEmpty(channelIdList)) {
//            return List.of();
//        }
//        if (CollectionUtils.isEmpty(storeIdList)) {
//            return List.of();
//        }
//        if (CollectionUtils.isEmpty(pskuList)) {
//            return List.of();
//        }
        List<SkuMappingPO> skuMappingPOS = skuMappingMapper.selectList(Wrappers.lambdaQuery(SkuMappingPO.class)
                .in(CollectionUtils.isNotEmpty(channelIdList), SkuMappingPO::getChannelId, channelIdList)
                .in(CollectionUtils.isNotEmpty(storeIdList), SkuMappingPO::getStoreId, storeIdList)
                .in(CollectionUtils.isNotEmpty(mskuList), SkuMappingPO::getMsku, mskuList)
                .eq(skuMappingQuery.getFulfillmentType() != null, SkuMappingPO::getFulfillmentType, skuMappingQuery.getFulfillmentType())
                .eq(SkuMappingPO::getStatus, 1)
                .eq(SkuMappingPO::getIsDeleted, 0)
        );
        Set<Long> skuMappingIdSet = skuMappingPOS.stream().map(SkuMappingPO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(skuMappingIdSet)) {
            return List.of();
        }
        List<SkuMappingLinePO> skuMappingLinePOS = skuMappingLineMapper.selectList(Wrappers.lambdaQuery(SkuMappingLinePO.class)
                .in(SkuMappingLinePO::getSkuMappingId, skuMappingIdSet)
                .in(CollectionUtils.isNotEmpty(pskuList), SkuMappingLinePO::getPsku, pskuList)
                .eq(SkuMappingLinePO::getIsDeleted, 0)
        );
        if (CollectionUtils.isEmpty(skuMappingLinePOS)) {
            return List.of();
        }

        List<SkuMappingLineVO> skuMappingLineVOS = skuMappingLineConvertor.poList2VoList(skuMappingLinePOS);
        Map<Long, SkuMappingPO> dataMap = skuMappingPOS.stream().collect(Collectors.toMap(SkuMappingPO::getId, Function.identity(), (v1, v2) -> v2));
        for (SkuMappingLineVO skuMappingLineVO : skuMappingLineVOS) {
            SkuMappingPO skuMappingPO = dataMap.get(skuMappingLineVO.getSkuMappingId());
            if (skuMappingPO != null) {
                skuMappingLineVO.setStoreId(skuMappingPO.getStoreId());
                skuMappingLineVO.setChannelId(skuMappingPO.getChannelId());
            }

        }
        return skuMappingLineVOS;


    }
}
