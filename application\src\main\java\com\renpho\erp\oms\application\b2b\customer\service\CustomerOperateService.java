package com.renpho.erp.oms.application.b2b.customer.service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.oms.application.b2b.customer.adapter.UserInfoAdapter;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerSalesCompanyTermsInfo;
import com.renpho.erp.oms.domain.b2b.customer.model.UserInfo;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.CustomerInfoPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.CustomerInfoService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.IncotermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.PaymentTermsConfigService;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.erp.smc.client.dto.OumUserInfoRes;
import com.renpho.karma.i18n.I18nMessageKit;

import lombok.RequiredArgsConstructor;

/**
 * 客户操作服务
 */
@Service
@RequiredArgsConstructor
public class CustomerOperateService {
	private static final String PRIMARY_CONTACT_POSITION = "Primary";

	private final UserClient userClient;

	private final CompanyClient companyClient;

	private final PaymentTermsConfigService paymentTermsConfigService;

	private final IncotermsConfigService incotermsConfigService;

	private final CustomerInfoService customerInfoService;

	/**
	 * 验证并填充新客户信息
	 *
	 * @param customerAggRoot 客户聚合根
	 */
	public void validateAndFillNewCustomer(CustomerAggRoot customerAggRoot) {
		// 客户名称判断唯一
		CustomerInfoPO customerInfoPO = customerInfoService.existsByCustomerCompanyName(customerAggRoot.getCustomerCompanyName());
		if (Objects.nonNull(customerInfoPO) && !customerInfoPO.getId().equals(customerAggRoot.getId())) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_NAME_EXIST"));
		}

		// 查询用户身份
		List<OumUserInfoRes> userInfoResList = userClient
			.getUserByIds(Arrays.asList(customerAggRoot.getCountryManagerUserId(), customerAggRoot.getSalesAssistantUserId())
				.stream()
				.distinct()
				.collect(Collectors.toList()));

		// 转换用户信息为UserInfo映射
		Map<Integer, UserInfo> userInfoMap = UserInfoAdapter.toUserInfoMap(userInfoResList);

		// 填充并验证客户信息
		fillAndValidateCustomerInfo(customerAggRoot, userInfoMap);

		// 校验保险权限
		validInsuranceInfo(customerAggRoot);

		// 校验条约
		validTermsInfo(customerAggRoot);

		// 增加发票信息、联系人信息、发货信息重复校验
		// 校验发票信息是否重复
		if (customerAggRoot.hasDuplicateInvoiceInfos()) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_INVOICE_INFO_DUPLICATE"));
		}

		// 校验联系人信息是否重复
		if (customerAggRoot.hasDuplicateContactInfos()) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_CONTACT_INFO_DUPLICATE"));
		}

		// 校验发货信息是否重复
		if (customerAggRoot.hasDuplicateShippingInfos()) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_SHIPPING_INFO_DUPLICATE"));
		}
	}

	/**
	 * 填充并验证客户信息
	 */
	private void fillAndValidateCustomerInfo(CustomerAggRoot customerAggRoot, Map<Integer, UserInfo> userInfoMap) {
		// 填充管理者信息
		customerAggRoot.fillManagerInfoFromMap(userInfoMap);

		// 检查是否有主要联系人
		if (!customerAggRoot.hasPrimaryContact(PRIMARY_CONTACT_POSITION)) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_PRIMARY_CONTACT_MISSING"));
		}

		// 验证联系人信息
		if (!customerAggRoot.validateContactInfos()) {
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_CONTACT_INFO_MISSING"));
		}
	}

	private void validInsuranceInfo(CustomerAggRoot customerAggRoot) {
		// 校验保险权限
		Authentication authentication = SecurityUtils.getAuthentication();
		if (!SecurityUtils.isAdmin() && (authentication == null || !authentication.getAuthorities()
			.stream()
			.anyMatch(authority -> authority.getAuthority().equals(PermissionConstant.Customer.INSURANCE_UPDATE)))) {
			// 如果没有权限，将insuranceCreditInfo设置为null
			customerAggRoot.clearInsuranceCreditInfo();
		}
	}

	private void validTermsInfo(CustomerAggRoot customerAggRoot) {
		// 付款条款
		if (Objects.nonNull(customerAggRoot.getSalesCompanyTermsInfo())) {
			CustomerSalesCompanyTermsInfo salesCompanyTermsInfo = customerAggRoot.getSalesCompanyTermsInfo();
			// 付款条款
			if (StringUtils.isNotEmpty(salesCompanyTermsInfo.getPaymentTermsCode())
					&& !paymentTermsConfigService.existsByCode(salesCompanyTermsInfo.getPaymentTermsCode())) {
				throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_CREATE_INCOTERMS_NOT_EXIST"));
			}
			// 贸易条款
			if (StringUtils.isNotEmpty(salesCompanyTermsInfo.getIncotermsCode())
					&& !incotermsConfigService.existsByCode(salesCompanyTermsInfo.getIncotermsCode())) {
				throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_CREATE_PAYMENT_TERMS_NOT_EXIST"));
			}
			// 公司
			if (Objects.nonNull(salesCompanyTermsInfo.getSalesCompanyId())
					&& !existsByCompanyAbbreviationAndAccountId(salesCompanyTermsInfo.getSalesCompanyId())) {
				throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_CREATE_COMPANY_NOT_EXIST"));
			}

		}
	}

	/**
	 * 检查公司缩写和账户ID是否存在
	 *
	 * @param salesCompanyId 销售公司ID
	 * @return 是否存在
	 */
	private boolean existsByCompanyAbbreviationAndAccountId(Integer salesCompanyId) {
		List<CompanyVo> companyVos = companyClient.getCompanyListWithB2BAccount(1);
		if (CollectionUtils.isEmpty(companyVos)) {
			return false;
		}
		return companyVos.stream().filter(Objects::nonNull).anyMatch(companyVo -> salesCompanyId.equals(companyVo.getId()));
	}
}
