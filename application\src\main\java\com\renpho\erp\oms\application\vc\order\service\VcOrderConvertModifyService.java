package com.renpho.erp.oms.application.vc.order.service;

import org.springframework.stereotype.Service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.domain.vc.order.AmzVcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.VcOrderAggRoot;
import com.renpho.erp.oms.domain.vc.order.repository.VcOrderRepository;
import com.renpho.erp.oms.domain.vc.order.service.VcOrderModifyDomainService;
import com.renpho.erp.oms.infrastructure.common.constant.Constants;

import lombok.RequiredArgsConstructor;

/**
 * VC订单转换修改-应用服务
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class VcOrderConvertModifyService {

	private final VcOrderRepository vcOrderRepository;
	private final VcOrderModifyDomainService vcOrderModifyDomainService;

	/**
	 * 转换-修改VC订单
	 * @param amzVcOrder 亚马逊VC订单
	 * @param orderId VC订单id
	 */
	@Lock4j(name = Constants.VC_ORDER_OPERATE, keys = "#orderId")
	public void modify(AmzVcOrderAggRoot amzVcOrder, Long orderId) {
		// 查询DB-VC订单
		VcOrderAggRoot oldVcOrder = vcOrderRepository.findById(orderId);
		// 是否无需修改
		if (oldVcOrder.isNoNeedToModify()) {
			return;
		}
		// 转换修改VC订单（生成新聚合根）
		VcOrderAggRoot newVcOrder = vcOrderModifyDomainService.convertAndModify(amzVcOrder, oldVcOrder);
		// 仓储更新
		vcOrderRepository.update(newVcOrder);
	}
}
