package com.renpho.erp.oms.adapter.web.controller.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingImportExcel;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
public class MulSkuMappingExcelListener extends AnalysisEventListener<MulSkuMappingImportExcel> {
    private final List<MulSkuMappingImportExcel> mulSkuMappingImportExcelList = new ArrayList<>();
    /**
     * 中文表头
     */
    public static final Map<Integer, String> importCNHeadMap = new HashMap<>();

    /**
     * 英文表头
     *
     * @param data
     * @param context
     */
    public static final Map<Integer, String> importUSHeadMap = new HashMap<>();

    static {
        importCNHeadMap.put(0, "订单渠道");
        importCNHeadMap.put(1, "订单店铺");
        importCNHeadMap.put(2, "订单PSKU");
        importCNHeadMap.put(3, "多渠道类型（7:FBA多渠道 8:WFS多渠道）");
        importCNHeadMap.put(4, "多渠道店铺");
        importCNHeadMap.put(5, "多渠道MSKU");
        importCNHeadMap.put(6, "优先级");

        importUSHeadMap.put(0, "OrderChannel");
        importUSHeadMap.put(1, "OrderStore");
        importUSHeadMap.put(2, "OrderPSKU");
        importUSHeadMap.put(3, "Multi-channel type (7: FBA multi-channel 8: WFS multi-channel)");
        importUSHeadMap.put(4, "Multi-channel store");
        importUSHeadMap.put(5, "Multi-channel MSKU");
        importUSHeadMap.put(6, "Priority");
    }


    @Override
    public void invoke(MulSkuMappingImportExcel data, AnalysisContext context) {
        mulSkuMappingImportExcelList.add(data);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (headMap.equals(importCNHeadMap)) {
            // 国际化转换
            ExcelUtil.buildUpdateHeadAgain(context, importCNHeadMap, MulSkuMappingImportExcel.class);
        } else if (headMap.equals(importUSHeadMap)) {
            ExcelUtil.buildUpdateHeadAgain(context, importUSHeadMap, MulSkuMappingImportExcel.class);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("HEAD_NOT_MATCH"));
        }

    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("多渠道sku映射导入excel解析数据为", JsonUtils.toJson(mulSkuMappingImportExcelList));

    }
}
