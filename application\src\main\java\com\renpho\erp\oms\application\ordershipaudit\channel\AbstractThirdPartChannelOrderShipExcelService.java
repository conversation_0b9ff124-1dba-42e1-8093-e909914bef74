package com.renpho.erp.oms.application.ordershipaudit.channel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseLanguageVo;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipExcelService;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.OrderStatus;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelListener;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.mapper.FulfillmentServiceCodeLanguageMapper;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.mapper.FulfillmentServiceCodeMapper;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodeLanguagePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderFulfillmentMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderPO;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Cleanup;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
public abstract class AbstractThirdPartChannelOrderShipExcelService implements ChannelOrderShipExcelService {
    @Autowired
    private WarehouseClient warehouseClient;
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private Validator validator;
    @Autowired
    private SaleOrderMapper saleOrderMapper;
    @Autowired
    private SaleOrderFulfillmentMapper saleOrderFulfillmentMapper;
    @Autowired
    private SaleOrderItemMapper saleOrderItemMapper;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private OrderShipAuditCreateService orderShipAuditCreateService;
    @Autowired
    protected FulfillmentServiceCodeMapper fulfillmentServiceCodeMapper;
    @Autowired
    protected FulfillmentServiceCodeLanguageMapper fulfillmentServiceCodeLanguageMapper;

    @Override
    public R<String> readExcel(InputStream inputStream) {
        List<ThirdPartOrderShipExcelDto> thirdPartOrderShipExcelDtoList = EasyExcel.read(inputStream)
                .head(ThirdPartOrderShipExcelDto.class)
                .registerReadListener(new LanguageExcelListener<>(List.of("errorInfo")))
                .sheet(0)
                .doReadSync();
        if (CollUtil.isEmpty(thirdPartOrderShipExcelDtoList)) {
            throw new BusinessException(I18nMessageKit.getMessage("FILE_CANNOT_BE_EMPTY"));
        }

        Map<String, StoreVo> storeMap = new HashMap<>();
        Map<String, SaleOrderPO> saleOrderPOMap = new HashMap<>();
        Map<String, WarehouseVo> warehouseMap = new HashMap<>();
        Set<Long> orderIdSet = new HashSet<>();
        boolean failFlag = checkData(thirdPartOrderShipExcelDtoList, storeMap, saleOrderPOMap, orderIdSet, warehouseMap);
        if (!failFlag) {
            // 校验服务编码
            failFlag = checkCarrierServiceCode(thirdPartOrderShipExcelDtoList, warehouseMap);
        }
        if (failFlag) {
            thirdPartOrderShipExcelDtoList.forEach(ThirdPartOrderShipExcelDto::buildErrorInfo);

            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(byteArrayOutputStream)
                    .head(ThirdPartOrderShipExcelDto.class)
                    .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                    .sheet()
                    .doWrite(thirdPartOrderShipExcelDtoList);
            return fileClient.uploadExcel(byteArrayOutputStream);
        }

        Multimap<Long, Long> itemMultimap = HashMultimap.create();
        for (List<Long> orderIds : CollUtil.split(orderIdSet, 100)) {
            saleOrderItemMapper.selectList(Wrappers.<SaleOrderItemPO>lambdaQuery()
                            .select(SaleOrderItemPO::getOrderId, SaleOrderItemPO::getId)
                            .in(SaleOrderItemPO::getOrderId, orderIds)
                            .eq(SaleOrderItemPO::getDeleted, false))
                    .forEach(item -> itemMultimap.put(item.getOrderId(), item.getId()));
        }

        for (ThirdPartOrderShipExcelDto thirdPartOrderShipExcelDto : thirdPartOrderShipExcelDtoList) {
            SaleOrderPO saleOrder = saleOrderPOMap.get(thirdPartOrderShipExcelDto.getOrderNo());
            WarehouseVo warehouseVo = warehouseMap.get(thirdPartOrderShipExcelDto.getImsWarehouseCode());


            OrderShipAuditDTO orderShipAuditDTO = new OrderShipAuditDTO();
            orderShipAuditDTO.setOrderId(saleOrder.getId());
            orderShipAuditDTO.setFulfillmentServiceType(getFulfillmentServiceType().getValue());
            orderShipAuditDTO.setWarehouseId(warehouseVo.getId());
            orderShipAuditDTO.setWarehouseCode(warehouseVo.getCode());
            orderShipAuditDTO.setThirdPartWarehouseCode(warehouseVo.getThirdWarehouseCode());
            orderShipAuditDTO.setJdOrderType(thirdPartOrderShipExcelDto.getJdOrderType());
            orderShipAuditDTO.setCarrierServiceCode(thirdPartOrderShipExcelDto.getPlatformWarehouseServiceCode());

            List<OrderShipAuditDTO.Item> items = new ArrayList<>();
            for (Long itemId : itemMultimap.get(saleOrder.getId())) {
                items.add(OrderShipAuditDTO.Item.builder().itemId(itemId).build());
            }
            orderShipAuditDTO.setItems(items);

            orderShipAuditCreateService.create(orderShipAuditDTO, true);
        }


        return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));
    }

    protected boolean checkCarrierServiceCode(List<ThirdPartOrderShipExcelDto> thirdPartOrderShipExcelDtoList, Map<String, WarehouseVo> warehouseMap) {
        boolean failFlag = false;
        Set<String> carrierServiceCodeSet = thirdPartOrderShipExcelDtoList.stream().map(ThirdPartOrderShipExcelDto::getPlatformWarehouseServiceCode).collect(Collectors.toSet());
        Set<Integer> wareHouseIdSet = warehouseMap.values().stream().map(WarehouseVo::getId).collect(Collectors.toSet());
        Map<Integer, Set<String>> warehouseIdCarrierServiceCodeMap = fulfillmentServiceCodeMapper.selectList(Wrappers.<FulfillmentServiceCodePO>lambdaQuery()
                        .in(FulfillmentServiceCodePO::getWarehouseId, wareHouseIdSet)
                        .in(FulfillmentServiceCodePO::getCarrierServiceCode, carrierServiceCodeSet)
                        .eq(FulfillmentServiceCodePO::getFulfillmentServiceType, getFulfillmentServiceType().getValue())
                        .eq(FulfillmentServiceCodePO::getIsDeleted, false))
                .stream()
                .collect(Collectors.groupingBy(FulfillmentServiceCodePO::getWarehouseId, Collectors.mapping(FulfillmentServiceCodePO::getCarrierServiceCode, Collectors.toSet())));

        for (ThirdPartOrderShipExcelDto thirdPartOrderShipExcelDto : thirdPartOrderShipExcelDtoList) {
            String warehouseCode = thirdPartOrderShipExcelDto.getImsWarehouseCode();
            WarehouseVo warehouseVo = warehouseMap.get(warehouseCode);
            Integer wareHouseId = warehouseVo.getId();
            if (!warehouseIdCarrierServiceCodeMap.getOrDefault(wareHouseId, Collections.emptySet()).contains(thirdPartOrderShipExcelDto.getPlatformWarehouseServiceCode())) {
                failFlag = true;
                thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_CARRIER_SERVICE_CODE_INVALIDATE"));
            }
        }
        return failFlag;
    }

    private boolean checkData(List<ThirdPartOrderShipExcelDto> thirdPartOrderShipExcelDtoList,  Map<String, StoreVo> storeMap, Map<String, SaleOrderPO> saleOrderPOMap,
                              Set<Long> orderIdSet, Map<String, WarehouseVo> warehouseMap) {
        boolean failFlag = false;
        Map<String, List<ThirdPartOrderShipExcelDto>> orderNoExcelDtoMap = thirdPartOrderShipExcelDtoList.stream()
                .collect(Collectors.groupingBy(ThirdPartOrderShipExcelDto::getOrderNo));

        // 销售订单号不能填写重复
        for (Map.Entry<String, List<ThirdPartOrderShipExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
            List<ThirdPartOrderShipExcelDto> oneOrderNoExcelDtoList = entry.getValue();
            if (oneOrderNoExcelDtoList.size() > 1) {
                failFlag = true;
                oneOrderNoExcelDtoList.forEach(thirdPartOrderShipExcelDto ->
                        thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NO_REPEAT")));
            }
        }

        // 获取仓库信息
        Set<String> warehouseCodeSet = thirdPartOrderShipExcelDtoList.stream().map(ThirdPartOrderShipExcelDto::getImsWarehouseCode).collect(Collectors.toSet());;
        warehouseMap.putAll(warehouseClient.getByCode(warehouseCodeSet));
        // 获取门店信息
        Set<String> storeNameSet = thirdPartOrderShipExcelDtoList.stream().map(ThirdPartOrderShipExcelDto::getShop).collect(Collectors.toSet());
        storeClient.getByStoreNames(new ArrayList<>(storeNameSet))
                .forEach(storeVo -> storeMap.put(storeVo.getStoreName(), storeVo));

        for (ThirdPartOrderShipExcelDto thirdPartOrderShipExcelDto : thirdPartOrderShipExcelDtoList) {
            // jsr303校验
            Set<ConstraintViolation<ThirdPartOrderShipExcelDto>> errorInfos = validator.validate(thirdPartOrderShipExcelDto);
            if (!errorInfos.isEmpty()) {
                failFlag = true;
                errorInfos.forEach(constraintViolation -> thirdPartOrderShipExcelDto.addErrorInfo(constraintViolation.getMessage()));
            }

            // 店铺名是否填写错误
            StoreVo storeVo = storeMap.get(thirdPartOrderShipExcelDto.getShop());
            if (storeVo == null) {
                failFlag = true;
                thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SHOP_NOT_EXISTS"));
            }

            // 仓库是否填写错误
            WarehouseVo warehouseVo = warehouseMap.get(thirdPartOrderShipExcelDto.getImsWarehouseCode());
            if (warehouseVo == null) {
                failFlag = true;
                thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_WAREHOUSE_CODE_INVALIDATE"));
            }

            SaleOrderPO saleOrderPO = saleOrderMapper.selectOne(Wrappers.<SaleOrderPO>lambdaQuery()
                    .eq(SaleOrderPO::getOrderNo, thirdPartOrderShipExcelDto.getOrderNo())
                    .eq(SaleOrderPO::getDeleted, false)
                    .last("limit 1"));
            // 订单是否存在
            if (saleOrderPO == null) {
                failFlag = true;
                thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_EXISTS"));
            }
            if (saleOrderPO != null) {
                saleOrderPOMap.put(thirdPartOrderShipExcelDto.getOrderNo(), saleOrderPO);
                orderIdSet.add(saleOrderPO.getId());
                // 订单是否属于这个店铺
                if (storeVo != null && !Objects.equals(saleOrderPO.getStoreId(), storeVo.getId())) {
                    failFlag = true;
                    thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_BELONG_SHOP"));
                }
                SaleOrderFulfillmentPO saleOrderFulfillmentPO = saleOrderFulfillmentMapper.selectOne(Wrappers.<SaleOrderFulfillmentPO>lambdaQuery()
                        .eq(SaleOrderFulfillmentPO::getOrderId, saleOrderPO.getId())
                        .last("limit 1"));
                // 订单是否自发货订单
                if (saleOrderFulfillmentPO != null && !Objects.equals(saleOrderFulfillmentPO.getFulfillmentType(), FulfillmentType.MERCHANT.getValue())) {
                    failFlag = true;
                    thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_FULFILLMENT_BY_MERCHANT"));
                }
                // 订单状态是否可以创建发货审核
                boolean flag = OrderStatus.canCreateOrderShipAudit().stream().anyMatch(orderStatus -> orderStatus.getValue().equals(saleOrderPO.getOrderStatus()));
                if (!flag) {
                    failFlag = true;
                    thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_STATUS_NOT_CANT_AUDIT"));
                }
            }
        }

        return failFlag;
    }


    @Override
    public void writeExcelTemplate(OutputStream outputStream) {
        @Cleanup
        ExcelWriter excelWriter = EasyExcelFactory.write()
                .file(outputStream)
                .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                .build();

        excelWriter.write(Collections.emptyList(), EasyExcelFactory.writerSheet(0)
                .head(ThirdPartOrderShipExcelDto.class)
                .excludeColumnFieldNames(List.of("errorInfo"))
                .build());


        String language = Optional.of(LocaleContextHolder.getLocale()).map(Locale::toLanguageTag).orElse("zh-CN");
        List<WarehouseVo> thirdPartWarehouseInfoList = warehouseClient.getThirdPartWarehouseInfo(getFulfillmentServiceType().getValue());
        List<ThirdPartOrderShipExcelDto.PlatformWarehouse> platformWarehouseList = new ArrayList<>();
        for (WarehouseVo warehouseVo : thirdPartWarehouseInfoList) {
            ThirdPartOrderShipExcelDto.PlatformWarehouse platformWarehouse = new ThirdPartOrderShipExcelDto.PlatformWarehouse();
            platformWarehouse.setPlatformWarehouseCode(warehouseVo.getCode());

            String warehouseName = getWarehouseName(warehouseVo, language);
            platformWarehouse.setPlatformWarehouseName(warehouseName);
            platformWarehouseList.add(platformWarehouse);
        }

        excelWriter.write(platformWarehouseList, EasyExcelFactory.writerSheet(1)
                .head(ThirdPartOrderShipExcelDto.PlatformWarehouse.class)
                .build());

        writeCarrierServiceCodeInfo(excelWriter, language, thirdPartWarehouseInfoList);

    }



    protected void writeCarrierServiceCodeInfo(ExcelWriter excelWriter, String language, List<WarehouseVo> thirdPartWarehouseInfoList) {
        Set<Integer> warehouseIdSet = thirdPartWarehouseInfoList.stream().map(WarehouseVo::getId).collect(Collectors.toSet());
        if (warehouseIdSet.isEmpty()) {
            excelWriter.write(Collections.emptyList(), EasyExcelFactory.writerSheet(2)
                    .head(ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode.class)
                    .build());
            return;
        }
        Map<Integer, WarehouseVo> warehouseIdMap = thirdPartWarehouseInfoList.stream().collect(Collectors.toMap(WarehouseVo::getId, Function.identity()));
        Set<Long> codeIdSet = new HashSet<>();

        Map<Integer, List<FulfillmentServiceCodePO>> warehouseIdCarrierServiceCodeMap = fulfillmentServiceCodeMapper.selectList(Wrappers.<FulfillmentServiceCodePO>lambdaQuery()
                        .in(FulfillmentServiceCodePO::getWarehouseId, warehouseIdSet)
                        .eq(FulfillmentServiceCodePO::getFulfillmentServiceType, getFulfillmentServiceType().getValue())
                        .eq(FulfillmentServiceCodePO::getIsDeleted, false))
                .stream()
                .peek(code -> codeIdSet.add(code.getId()))
                .collect(Collectors.groupingBy(FulfillmentServiceCodePO::getWarehouseId));
        if (codeIdSet.isEmpty()) {
            excelWriter.write(Collections.emptyList(), EasyExcelFactory.writerSheet(2)
                    .head(ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode.class)
                    .build());
            return;
        }


        Map<Long, String> codeNameMap = fulfillmentServiceCodeLanguageMapper.selectList(Wrappers.<FulfillmentServiceCodeLanguagePO>lambdaQuery()
                        .in(FulfillmentServiceCodeLanguagePO::getCodeId, codeIdSet)
                        .eq(FulfillmentServiceCodeLanguagePO::getLanguage, language)
                        .eq(FulfillmentServiceCodeLanguagePO::getIsDeleted, false))
                .stream()
                .collect(Collectors.toMap(FulfillmentServiceCodeLanguagePO::getCodeId, FulfillmentServiceCodeLanguagePO::getName, (k1, k2) -> k2));


        List<ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode> platformWarehouseServiceCodeList = new ArrayList<>();
        warehouseIdCarrierServiceCodeMap.forEach((warehouseId, fulfillmentServiceCodeList) -> {
            String warehouseName = getWarehouseName(warehouseIdMap.get(warehouseId), language);

            for (FulfillmentServiceCodePO fulfillmentServiceCode : fulfillmentServiceCodeList) {
                ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode platformWarehouseServiceCode = new ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode();
                platformWarehouseServiceCode.setPlatformWarehouseName(warehouseName);
                platformWarehouseServiceCode.setPlatformWarehouseServiceCode(fulfillmentServiceCode.getCarrierServiceCode());
                platformWarehouseServiceCode.setPlatformWarehouseServiceDesc(codeNameMap.getOrDefault(fulfillmentServiceCode.getId(), ""));
                platformWarehouseServiceCodeList.add(platformWarehouseServiceCode);
            }
        });

        excelWriter.write(platformWarehouseServiceCodeList, EasyExcelFactory.writerSheet(2)
                .head(ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode.class)
                .build());

    }

    protected String getWarehouseName(WarehouseVo warehouseVo, String language) {
        String warehouseName = warehouseVo.getLanguages()
                .stream()
                .filter(warehouseLanguageVo -> StrUtil.equalsIgnoreCase(language, warehouseLanguageVo.getLanguage()))
                .findFirst()
                .map(WarehouseLanguageVo::getName)
                .orElse("");
        return warehouseName;
    }
}
