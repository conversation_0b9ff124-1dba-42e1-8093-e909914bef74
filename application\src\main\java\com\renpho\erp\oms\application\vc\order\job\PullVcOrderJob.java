package com.renpho.erp.oms.application.vc.order.job;

import com.renpho.erp.oms.application.vc.order.command.PullVcSourceOrderCmd;
import com.renpho.erp.oms.application.vc.order.service.PullVcSourceOrderService;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/7 15:34
 */
@Component
@RequiredArgsConstructor
public class PullVcOrderJob {

    private final PullVcSourceOrderService pullVcSourceOrderService;

    /**
     * 拉取vc订单
     */
    @XxlJob("pullVcOrder")
    public void pullVcOrder() {
        String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
        PullVcSourceOrderCmd pullVcSourceOrderCmd = JSONKit.parseObject(jobParam, PullVcSourceOrderCmd.class);
        pullVcSourceOrderService.pullVcSourceOrder(pullVcSourceOrderCmd);
    }
}
