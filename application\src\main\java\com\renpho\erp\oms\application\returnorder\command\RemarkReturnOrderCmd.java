package com.renpho.erp.oms.application.returnorder.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @desc: 退货订单备注入参
 * @time: 2025-03-21 11:27:11
 * @author: <PERSON><PERSON>
 */
@Data
public class RemarkReturnOrderCmd {

	/**
	 * 退货订单主键
	 */
	@NotNull(message = "RETURN_ORDER_ID_NOT_NULL")
	private Long returnOrderId;

	/**
	 * 退货订单备注
	 */
	@NotBlank(message = "REMARK_NOT_NULL")
	private String remark;
}
