package com.renpho.erp.oms.application.channelmanagement.tiktok.optlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.channelmanagement.tiktok.service.TtOrderService;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderLog;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * *
 */
@Component
@AllArgsConstructor
public class TtOrderUpdateSnapSource implements SnapshotDatatSource {

	private final TtOrderService ttOrderService;

	@Override
	public JSONObject getOldData(Object[] args) {
		// 将对象转换为 Json 字符串
		Long id = ((JSONObject) JSONObject.toJSON(args[0])).getObject("id", Long.class);

		return JSON.parseObject(JSON.toJSONString(ttOrderService.getLogById(id)));
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		TtOrderLog ttOrderLog = ttOrderService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(ttOrderLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
