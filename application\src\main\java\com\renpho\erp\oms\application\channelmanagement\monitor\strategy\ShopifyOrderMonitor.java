package com.renpho.erp.oms.application.channelmanagement.monitor.strategy;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.channelmanagement.monitor.AbstractChannelOrderMonitor;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderRepository;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * Shopify订单监视器
 * <AUTHOR>
 */
@Component
public class ShopifyOrderMonitor extends AbstractChannelOrderMonitor {

	private final SpfOrderRepository spfOrderRepository;

	public ShopifyOrderMonitor(StoreClient storeClient, RedisTemplate<String, String> redisTemplate, DingTalkClient dingTalkClient,
			SpfOrderRepository spfOrderRepository) {
		super(storeClient, redisTemplate, dingTalkClient);
		this.spfOrderRepository = spfOrderRepository;
	}

	@Override
	public String getChannelCode() {
		return ChannelCode.SPF_CHANNEL_CODE;
	}

	@Override
	protected Long countOrder(StoreAuthorization storeAuthorization, LocalDateTime start, LocalDateTime end) {
		return spfOrderRepository.count(storeAuthorization.getSpfShopUrl(), start, end);
	}

	@Override
	protected String getCacheKey(StoreAuthorization storeAuthorization) {
		return storeAuthorization.getSpfShopUrl();
	}

	@Override
	protected List<StoreAuthorization> buildStoreAuthorization(List<StoreAuthorizationVo> storeAuthorizationList,
			MonitorCmd.Monitor monitor) {
		Set<String> storeNames = monitor.getStoreNames();
		// 按照Shopify，店铺URL统计
		return storeAuthorizationList.stream()
			.filter(s -> storeNames.contains(s.getStoreName()))
			.map(StoreAuthorizationVo::getAuthorization)
			.filter(Objects::nonNull)
			// Shopify，店铺URL
			.map(StoreAuthorizationVo.Authorization::getSpfShopUrl)
			.filter(StringUtils::isNotBlank)
			.distinct()
			.map(spfShopUrl -> {
				StoreAuthorization storeAuthorization = new StoreAuthorization();
				storeAuthorization.setSpfShopUrl(spfShopUrl);
				// 店铺名称
				storeAuthorization.setStoreNames(storeAuthorizationList.stream()
					.filter(s -> Objects.nonNull(s.getAuthorization())
							&& StringUtils.equals(s.getAuthorization().getSpfShopUrl(), spfShopUrl))
					.map(StoreAuthorizationVo::getStoreName)
					.collect(Collectors.toSet()));
				return storeAuthorization;
			})
			.toList();

	}
}
