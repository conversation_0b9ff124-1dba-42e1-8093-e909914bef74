package com.renpho.erp.oms.application.ordershipaudit.channel.amz;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.*;
import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ordershipaudit.ChannelOrderShipExcelService;
import com.renpho.erp.oms.application.salemanagement.dto.OrderShipAuditDTO;
import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditCreateService;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.ordershipaudit.amz.AmzDeliveryInstruction;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.OrderItemStatus;
import com.renpho.erp.oms.domain.salemanagement.OrderStatus;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelHeaderWriterHandler;
import com.renpho.erp.oms.infrastructure.common.excel.LanguageExcelListener;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderFulfillmentMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderItemMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.mapper.SaleOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderItemPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderPO;
import com.renpho.karma.dto.R;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.Cleanup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/4
 */
@Slf4j
@Component
public class AmzFbaChannelOrderShipExcelService implements ChannelOrderShipExcelService {
    @Autowired
    private StoreClient storeClient;
    @Autowired
    private Validator validator;
    @Autowired
    private SaleOrderMapper saleOrderMapper;
    @Autowired
    private SaleOrderFulfillmentMapper saleOrderFulfillmentMapper;
    @Autowired
    private SaleOrderItemMapper saleOrderItemMapper;
    @Autowired
    private SkuMappingRepository skuMappingRepository;
    @Autowired
    private FileClient fileClient;
    @Autowired
    private OrderShipAuditCreateService orderShipAuditCreateService;

    @Override
    public FulfillmentServiceType getFulfillmentServiceType() {
        return FulfillmentServiceType.FBA_MULTI_CHANNEL;
    }

    @Override
    public R<String> readExcel(InputStream inputStream) {
        List<AmzFbaOrderShipExcelDto> allAmzFbaOrderShipExcelDtoList = EasyExcel.read(inputStream)
                .head(AmzFbaOrderShipExcelDto.class)
                .registerReadListener(new LanguageExcelListener<>(List.of("errorInfo")))
                .sheet(0)
                .doReadSync();
        if (CollUtil.isEmpty(allAmzFbaOrderShipExcelDtoList)) {
            throw new BusinessException(I18nMessageKit.getMessage("FILE_CANNOT_BE_EMPTY"));
        }

        Map<String, List<AmzFbaOrderShipExcelDto>> orderNoExcelDtoMap = allAmzFbaOrderShipExcelDtoList.stream()
                .collect(Collectors.groupingBy(AmzFbaOrderShipExcelDto::getOrderNo, Collectors.toList()));
        Map<String, StoreVo> amzStoreMap = new HashMap<>();
        Map<String, SaleOrderPO> saleOrderPOMap = new HashMap<>();
        // row orderNO, column channelMsku , value itemIdList
        Table<String, String, List<Long>> itemIdCahnnelMskuTable = HashBasedTable.create();
        // 校验数据
        boolean failFlag = checkData(orderNoExcelDtoMap, amzStoreMap, saleOrderPOMap, itemIdCahnnelMskuTable);
        // 存在错误数据上传到文件服务
        if (failFlag) {
            allAmzFbaOrderShipExcelDtoList.forEach(AmzFbaOrderShipExcelDto::buildErrorInfo);
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcelFactory.write(byteArrayOutputStream)
                    .head(AmzFbaOrderShipExcelDto.class)
                    .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                    .sheet()
                    .doWrite(allAmzFbaOrderShipExcelDtoList);
            return fileClient.uploadExcel(byteArrayOutputStream);
        }

        for (Map.Entry<String, List<AmzFbaOrderShipExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
            String orderNo = entry.getKey();
            List<AmzFbaOrderShipExcelDto> oneOrderExcelDtoList = entry.getValue();

            StoreVo amzStoreVo = amzStoreMap.get(oneOrderExcelDtoList.get(0).getAmazonStoreName());
            SaleOrderPO saleOrderPO = saleOrderPOMap.get(orderNo);

            OrderShipAuditDTO orderShipAuditDTO = new OrderShipAuditDTO();
            orderShipAuditDTO.setOrderId(saleOrderPO.getId());
            orderShipAuditDTO.setFulfillmentServiceType(FulfillmentServiceType.FBA_MULTI_CHANNEL.getValue());
            orderShipAuditDTO.setChannelStoreId(amzStoreVo.getId());
            orderShipAuditDTO.setWarehouseId(amzStoreVo.getChannelWarehouseLastId());
            orderShipAuditDTO.setWarehouseCode(amzStoreVo.getChannelWarehouseCode());
            orderShipAuditDTO.setShippingSpeedCategory(oneOrderExcelDtoList.get(0).getShipSpeedCategory());
            orderShipAuditDTO.setNoticeEmail(oneOrderExcelDtoList.get(0).getEmail());
            orderShipAuditDTO.setRemark(oneOrderExcelDtoList.get(0).getRemark());
            orderShipAuditDTO.setBlockAmzl(BooleanUtil.toBooleanObject(oneOrderExcelDtoList.get(0).getBlockAmzl()));
            orderShipAuditDTO.setBlankBox(BooleanUtil.toBooleanObject(oneOrderExcelDtoList.get(0).getBlankBox()));
			if (StringUtils.isNotEmpty(oneOrderExcelDtoList.get(0).getDeliveryStartTime())) {
				orderShipAuditDTO.setDeliveryStartTime(DateUtil.convertStrToUTC(oneOrderExcelDtoList.get(0).getDeliveryStartTime(), true));
			}
			if (StringUtils.isNotEmpty(oneOrderExcelDtoList.get(0).getDeliveryEndTime())) {
				orderShipAuditDTO.setDeliveryEndTime(DateUtil.convertStrToUTC(oneOrderExcelDtoList.get(0).getDeliveryEndTime(), false));
			}
			AmzDeliveryInstruction amzDeliveryInstruction = AmzDeliveryInstruction
				.enumOfName(oneOrderExcelDtoList.get(0).getDeliveryInstruction());
			if (ObjectUtil.isNotNull(amzDeliveryInstruction)) {
				orderShipAuditDTO.setDeliveryInstruction(amzDeliveryInstruction.getValue());
				orderShipAuditDTO.setNeighborName(oneOrderExcelDtoList.get(0).getNeighborName());
				orderShipAuditDTO.setNeighborRoom(oneOrderExcelDtoList.get(0).getNeighborRoom());
			}

            // key channelMsku,value itemIdList
            List<OrderShipAuditDTO.Item> itemList = new ArrayList<>();
            Map<String, List<Long>> channelMskuItemIdMap = itemIdCahnnelMskuTable.row(orderNo);
            channelMskuItemIdMap.forEach((channelMsku, itemIdList) ->
                    itemIdList.forEach(itemId -> itemList.add(OrderShipAuditDTO.Item.builder().itemId(itemId).channelMsku(channelMsku).build())));
            orderShipAuditDTO.setItems(itemList);
            orderShipAuditCreateService.create(orderShipAuditDTO, true);
        }



        return R.success(I18nMessageKit.getMessage("IMPORT_SUCCESS"));

    }

    private boolean checkData(Map<String, List<AmzFbaOrderShipExcelDto>> orderNoExcelDtoMap, Map<String, StoreVo> amzStoreMap, Map<String, SaleOrderPO> saleOrderPOMap, Table<String, String, List<Long>> itemIdCahnnelMskuTable) {
        boolean failFlag = false;
        Set<String> amzStoreNameSet = new HashSet<>();
        Set<String> storeNameSet = new HashSet<>();

        // 获取所有店铺头的门店信息,后面校验
        orderNoExcelDtoMap.values().forEach(list -> list.forEach(amzFbaOrderShipExcelDto -> {
            storeNameSet.add(amzFbaOrderShipExcelDto.getStoreName());
            amzStoreNameSet.add(amzFbaOrderShipExcelDto.getAmazonStoreName());
        }));

        Map<String, StoreVo> storeNameMap = storeClient.getByStoreNames(new ArrayList<>(storeNameSet))
                .stream()
                .collect(Collectors.toMap(StoreVo::getStoreName, Function.identity()));
        storeClient.getByStoreNames(new ArrayList<>(amzStoreNameSet)).forEach(storeVo -> amzStoreMap.put(storeVo.getStoreName(), storeVo));


        // 按照orderNo归并,按订单号校验
        for (Map.Entry<String, List<AmzFbaOrderShipExcelDto>> entry : orderNoExcelDtoMap.entrySet()) {
            String orderNo = entry.getKey();
            List<AmzFbaOrderShipExcelDto> oneOrderExcelDtoList = entry.getValue();

            boolean oneOrderError = checkOneOrder(amzStoreMap, saleOrderPOMap, itemIdCahnnelMskuTable, oneOrderExcelDtoList, failFlag, orderNo, storeNameMap);
            if (oneOrderError) {
                failFlag = true;
            }
        }
        return failFlag;
    }

    private boolean checkOneOrder(Map<String, StoreVo> amzStoreMap, Map<String, SaleOrderPO> saleOrderPOMap, Table<String, String, List<Long>> itemIdCahnnelMskuTable, List<AmzFbaOrderShipExcelDto> oneOrderExcelDtoList, boolean failFlag, String orderNo, Map<String, StoreVo> storeNameMap) {
        // 一个订单下订单msku跟订单psku只能填一个
        Map<String, Set<String>> mskuChannelMskuMap = oneOrderExcelDtoList.stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getMsku()))
                .collect(Collectors.groupingBy(AmzFbaOrderShipExcelDto::getMsku, Collectors.mapping(AmzFbaOrderShipExcelDto::getChannelMsku, Collectors.toSet())));
        Map<String, Set<String>> pskuChannelMskuMap = oneOrderExcelDtoList.stream()
                .filter(dto -> StrUtil.isNotBlank(dto.getPsku()))
                .collect(Collectors.groupingBy(AmzFbaOrderShipExcelDto::getPsku, Collectors.mapping(AmzFbaOrderShipExcelDto::getChannelMsku, Collectors.toSet())));
        if (!mskuChannelMskuMap.isEmpty() && !pskuChannelMskuMap.isEmpty()) {
            failFlag = true;
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_MSKU_PSKU")));
        }

        // 一个订单亚马逊配置选项要填写一样
        if (oneOrderExcelDtoList.stream().map(AmzFbaOrderShipExcelDto::amzConfigKey).collect(Collectors.toSet()).size() > 1) {
            failFlag = true;
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_FBA_CONFIG")));
        }

        // 校验亚马逊门店填写是否一致
        Set<String> oneOrderAmzStoreNameSet = oneOrderExcelDtoList.stream()
                .map(AmzFbaOrderShipExcelDto::getAmazonStoreName)
                .collect(Collectors.toSet());
        if (oneOrderAmzStoreNameSet.size() > 1) {
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SHOP_NAME_CONSISTENT")));
        }


        // 获取sku映射信息用于后面校验
        Set<String> amzMskuSet = new HashSet<>();
        mskuChannelMskuMap.values().forEach(amzMskuSet::addAll);
        pskuChannelMskuMap.values().forEach(amzMskuSet::addAll);
        SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
        skuMappingQuery.setFulfillmentType(FulfillmentType.PLATFORM.getValue());
        skuMappingQuery.setSellerSkuList(new ArrayList<>(amzMskuSet));
        List<Integer> oneOrderAmzStoreId = oneOrderAmzStoreNameSet.stream()
                .map(amzStoreMap::get)
                .filter(Objects::nonNull)
                .map(StoreVo::getId)
                .distinct()
                .toList();
        skuMappingQuery.setStoreIdList(oneOrderAmzStoreId);
        Map<Integer, Set<String>> amzStoreIdMskuMap = skuMappingRepository.list(skuMappingQuery)
                .stream()
                .collect(Collectors.groupingBy(SkuMappingFulfillmentVO::getStoreId, Collectors.mapping(SkuMappingFulfillmentVO::getSellerSku, Collectors.toSet())));

        // 订单是否存在
        SaleOrderPO saleOrderPO = saleOrderMapper.selectOne(Wrappers.<SaleOrderPO>lambdaQuery()
                .eq(SaleOrderPO::getOrderNo, orderNo)
                .eq(SaleOrderPO::getDeleted, false)
                .last("limit 1"));
        if (saleOrderPO == null) {
            failFlag = true;
            oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_EXISTS")));
        }
        if (saleOrderPO != null) {
            saleOrderPOMap.put(saleOrderPO.getOrderNo(), saleOrderPO);
            // 订单是否自发货
            SaleOrderFulfillmentPO saleOrderFulfillment = saleOrderFulfillmentMapper.selectOne(Wrappers.<SaleOrderFulfillmentPO>lambdaQuery()
                    .eq(SaleOrderFulfillmentPO::getOrderId, saleOrderPO.getId())
                    .eq(SaleOrderFulfillmentPO::getDeleted, false)
                    .last("limit 1"));
            if (saleOrderFulfillment != null && !FulfillmentType.MERCHANT.getValue().equals(saleOrderFulfillment.getFulfillmentType())) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto ->
                        amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_FULFILLMENT_BY_MERCHANT")));
            }
            // 订单状态是否可以创建发货审核
            boolean flag = OrderStatus.canCreateOrderShipAudit().stream().anyMatch(orderStatus -> orderStatus.getValue().equals(saleOrderPO.getOrderStatus()));
            if (!flag) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto ->
                        amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_STATUS_NOT_CANT_AUDIT")));
            }

            // 订单规格信息校验
            List<SaleOrderItemPO> saleOrderItemList = saleOrderItemMapper.selectList(Wrappers.<SaleOrderItemPO>lambdaQuery()
                    .eq(SaleOrderItemPO::getOrderId, saleOrderPO.getId())
                    .eq(SaleOrderItemPO::getDeleted, false));
            // excel的订单商品行比数据库多
            if (saleOrderItemList.size() < oneOrderExcelDtoList.size()) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ITEM_EXCEED_DB")));
            }

            ListMultimap<String, Long> orderMskuItemIdMap = ArrayListMultimap.create();
            ListMultimap<String, Long> orderPskuItemIdMap = ArrayListMultimap.create();
            Set<Integer> alreadyShipOrderItemStatus = OrderItemStatus.alreadyShipStatusSet().stream().map(OrderItemStatus::getValue).collect(Collectors.toSet());
            // 部分商品是否已发货
            flag = saleOrderItemList.stream().map(SaleOrderItemPO::getStatus).anyMatch(alreadyShipOrderItemStatus::contains);
            if (flag) {
                failFlag = true;
                oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto -> amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SOME_BE_SHIP")));
            }
            // 校验excel里的msku,psku是否跟系统里一致
            for (SaleOrderItemPO saleOrderItemPO : saleOrderItemList) {
                orderMskuItemIdMap.put(saleOrderItemPO.getMsku(), saleOrderItemPO.getId());
                orderPskuItemIdMap.put(saleOrderItemPO.getPsku(), saleOrderItemPO.getId());
            }
            for (AmzFbaOrderShipExcelDto amzFbaOrderShipExcelDto : oneOrderExcelDtoList) {
                // excel填的msuk,数据库没有
                if (StrUtil.isNotBlank(amzFbaOrderShipExcelDto.getMsku())) {
                    if (!orderMskuItemIdMap.containsKey(amzFbaOrderShipExcelDto.getMsku())) {
                        failFlag = true;
                        amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_EXCEL_ORDER_MSKU_NOT_EXISTS"));
                    } else {
                        itemIdCahnnelMskuTable.put(amzFbaOrderShipExcelDto.getOrderNo(), amzFbaOrderShipExcelDto.getChannelMsku(), orderMskuItemIdMap.get(amzFbaOrderShipExcelDto.getMsku()));
                    }
                }
                // excel填的psuk,数据库没有
                if (StrUtil.isNotBlank(amzFbaOrderShipExcelDto.getPsku())) {
                    if (!orderPskuItemIdMap.containsKey(amzFbaOrderShipExcelDto.getPsku())) {
                        failFlag = true;
                        amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_EXCEL_ORDER_PSKU_NOT_EXISTS"));
                    } else {
                        itemIdCahnnelMskuTable.put(amzFbaOrderShipExcelDto.getOrderNo(), amzFbaOrderShipExcelDto.getChannelMsku(), orderPskuItemIdMap.get(amzFbaOrderShipExcelDto.getPsku()));
                    }
                }
            }
            // 是否存在数据库订单行的msku没有匹配excel里的msku
            if (CollUtil.isNotEmpty(mskuChannelMskuMap)) {
                Collection<String> notMappingMskuCollection = CollUtil.subtract(orderMskuItemIdMap.keySet(), mskuChannelMskuMap.keySet());
                if (CollUtil.isNotEmpty(notMappingMskuCollection)) {
                    oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto ->
                            amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SYSTEM_ORDER_MSKU_NOT_EXISTS")));
                }
            }
            // 是否存在数据库订单行的psku没有匹配excel里的psku
            if (CollUtil.isNotEmpty(pskuChannelMskuMap)) {
                Collection<String> notMappingPskuCollection = CollUtil.subtract(orderPskuItemIdMap.keySet(), pskuChannelMskuMap.keySet());
                if (CollUtil.isNotEmpty(notMappingPskuCollection)) {
                    oneOrderExcelDtoList.forEach(amzFbaOrderShipExcelDto ->
                            amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SYSTEM_ORDER_PSKU_NOT_EXISTS")));
                }
            }
        }


        for (AmzFbaOrderShipExcelDto amzFbaOrderShipExcelDto : oneOrderExcelDtoList) {
            StoreVo storeVo = storeNameMap.get(amzFbaOrderShipExcelDto.getStoreName());
            if (storeVo == null) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SHOP_NOT_EXISTS"));
            } else if (saleOrderPO != null && !Objects.equals(saleOrderPO.getStoreId(), storeVo.getId())) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_ORDER_NOT_BELONG_SHOP"));
            }


            // jsr303校验必填项
            Set<ConstraintViolation<AmzFbaOrderShipExcelDto>> errorInfoSet = validator.validate(amzFbaOrderShipExcelDto);
            if (CollUtil.isNotEmpty(errorInfoSet)) {
                failFlag = true;
                errorInfoSet.forEach(errorInfo -> amzFbaOrderShipExcelDto.addErrorInfo(errorInfo.getMessage()));
            }

            // 亚马逊发货店铺
            StoreVo amzStore = amzStoreMap.get(amzFbaOrderShipExcelDto.getAmazonStoreName());
            if (amzStore == null) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_EXISTS"));
            } else if (!SaleChannelType.AMAZON.getValue().equals(amzStore.getSalesChannelCode())){
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_AMZ"));
            }
            if (amzStore != null && !amzStoreIdMskuMap.getOrDefault(amzStore.getId(), Collections.emptySet()).contains(amzFbaOrderShipExcelDto.getChannelMsku())) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_AMZ_SHOP_NOT_SKU_MAPPING"));
            }


            // 校验相同的excel msku与psku映射是否一致
            if (StrUtil.isNotBlank(amzFbaOrderShipExcelDto.getMsku()) && mskuChannelMskuMap.getOrDefault(amzFbaOrderShipExcelDto.getMsku(), Collections.emptySet()).size() > 1) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SAME_EXCEL_MSKU_NOT_CONSISTENT"));
            }
            if (StrUtil.isNotBlank(amzFbaOrderShipExcelDto.getPsku()) && pskuChannelMskuMap.getOrDefault(amzFbaOrderShipExcelDto.getPsku(), Collections.emptySet()).size() > 1) {
                failFlag = true;
                amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_SAME_EXCEL_PSKU_NOT_CONSISTENT"));
            }

			// 时间必须成对填写或都不填写
			boolean hasStartTime = StringUtils.isNotEmpty(amzFbaOrderShipExcelDto.getDeliveryStartTime());
			boolean hasEndTime = StringUtils.isNotEmpty(amzFbaOrderShipExcelDto.getDeliveryEndTime());

			if (hasStartTime != hasEndTime) {
				failFlag = true;
				amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("AMAZON_DELIVERY_TIME_PAIR_REQUIRED"));
			}

			// 校验交货开始时间和结束时间格式正确
			LocalDateTime startTime = null;
			LocalDateTime endTime = null;
			if (StringUtils.isNotEmpty(amzFbaOrderShipExcelDto.getDeliveryStartTime())
					&& StringUtils.isNotEmpty(amzFbaOrderShipExcelDto.getDeliveryEndTime())) {
				try {
					startTime = DateUtil.convertStrToUTC(amzFbaOrderShipExcelDto.getDeliveryStartTime(), true);
					endTime = DateUtil.convertStrToUTC(amzFbaOrderShipExcelDto.getDeliveryEndTime(), false);
				}
				catch (Exception e) {
					failFlag = true;
					amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("AMAZON_DELIVERY_TIME_ERROR"));
				}
				if (Objects.nonNull(startTime) && Objects.nonNull(endTime) && startTime.compareTo(endTime) >= 0) {
                    failFlag = true;
                    amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("AMAZON_DELIVERY_START_AFTER_END"));
				}
			}
            
			// 交货说明不为空 且 不是选择的无首选项
			if (StringUtils.isNotEmpty(amzFbaOrderShipExcelDto.getDeliveryInstruction())
					&& !AmzDeliveryInstruction.isNoPreference(amzFbaOrderShipExcelDto.getDeliveryInstruction())) {
				AmzDeliveryInstruction amzDeliveryInstruction = AmzDeliveryInstruction.enumOfName(amzFbaOrderShipExcelDto.getDeliveryInstruction());
				if (Objects.isNull(amzDeliveryInstruction)) {
					failFlag = true;
					amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("AMAZON_DELIVERY_INSTRUCTION_ERROR"));
				}
				if (AmzDeliveryInstruction.FALLBACK_NEIGHBOR_DELIVERY.equals(amzDeliveryInstruction)) {
					if (StringUtils.isEmpty(amzFbaOrderShipExcelDto.getNeighborName()) || StringUtils.isEmpty(amzFbaOrderShipExcelDto.getNeighborRoom())) {
						failFlag = true;
						amzFbaOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("AMAZON_JP_NEIGHBOR_REQUIRE"));
					}
				}
			}


        }
        return failFlag;
    }

    @Override
    public void writeExcelTemplate(OutputStream outputStream) {
        @Cleanup
        ExcelWriter excelWriter = EasyExcelFactory.write()
                .file(outputStream)
                .registerWriteHandler(new LanguageExcelHeaderWriterHandler())
                .build();

        excelWriter.write(Collections.emptyList(), EasyExcelFactory.writerSheet(0)
                .head(AmzFbaOrderShipExcelDto.class)
                .excludeColumnFieldNames(ListUtil.toList("errorInfo"))
                .build());


    }
}
