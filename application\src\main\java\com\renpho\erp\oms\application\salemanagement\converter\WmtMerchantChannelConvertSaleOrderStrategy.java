package com.renpho.erp.oms.application.salemanagement.converter;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.SaleOrderInfoConverterDto;
import com.renpho.erp.oms.domain.channelmanagement.walmart.enums.WmtFulfillmentType;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtOrderRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;

/**
 * 沃尔玛渠道订单转销售订单策略实现类.
 * <AUTHOR>
 * @date 2025/1/5 16:42
 */
@Component
public class WmtMerchantChannelConvertSaleOrderStrategy extends AbstractChannelConvertSaleOrderStrategy {

	private final WmtOrderRepository wmtOrderRepository;

	public WmtMerchantChannelConvertSaleOrderStrategy(WmtOrderRepository wmtOrderRepository, SaleOrderRepository saleOrderRepository,
			StoreClient storeClient, SkuMappingRepository skuMappingRepository, ProductClient productClient) {
		super(saleOrderRepository, storeClient, skuMappingRepository, productClient);
		this.wmtOrderRepository = wmtOrderRepository;
	}

	@Override
	public SaleChannelType getChannelType() {
		return SaleChannelType.WALMART;
	}

	@Override
	public FulfillmentType getFulfillmentType() {
		return FulfillmentType.MERCHANT;
	}

	@Override
	public List<SaleOrderInfoConverterDto> getChannelOrders(StoreDTO storeDTO, Set<String> channelOrderIds, Integer pageSize, Long lastId) {
		return wmtOrderRepository.findUnCompletedOrders(storeDTO.getStoreId(), channelOrderIds, pageSize, lastId,
				WmtFulfillmentType.SELLERFULFILLED.getValue());
	}

	@Override
	protected Set<String> findSellerSkusByChannelOrderId(String channelOrderNo, Integer storeId) {
		// 查询渠道订单的销售sku集
		return wmtOrderRepository.findSellerSkusById(channelOrderNo, storeId);
	}

}
