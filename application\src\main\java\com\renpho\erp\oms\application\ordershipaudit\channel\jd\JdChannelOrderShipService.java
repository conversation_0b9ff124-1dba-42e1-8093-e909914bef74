package com.renpho.erp.oms.application.ordershipaudit.channel.jd;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.renpho.erp.apiproxy.jd.fop.FopResult;
import com.renpho.erp.apiproxy.jd.fop.JdResponse;
import com.renpho.erp.apiproxy.jd.fop.JdResponseExt;
import com.renpho.erp.apiproxy.jd.fop.model.outstock.DeliveryOutstockRequest;
import com.renpho.erp.apiproxy.jd.fop.model.outstock.OutStockResponseDto;
import com.renpho.erp.apiproxy.jd.fop.model.outstock.WaybillPackDTO;
import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractThirdWarehouseOrderShipService;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.ChannelItemFulfillmentInfo;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditItem;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.enums.JDServiceBillStatus;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.erp.oms.infrastructure.feign.dto.WarehouseConfigDto;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.proxy.JDClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceConfigService;
import com.renpho.karma.dto.R;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/12
 */
@Slf4j
@Component
public class JdChannelOrderShipService extends AbstractThirdWarehouseOrderShipService {
	@Autowired
	private JDClient jdClient;
	@Autowired
	private FulfillmentServiceConfigService fulfillmentServiceConfigService;

	protected JdChannelOrderShipService(SaleOrderQueryService saleOrderQueryService, WarehouseClient warehouseClient) {
		super(saleOrderQueryService, warehouseClient);
	}

	@Override
	public Set<FulfillmentServiceType> getFulfillmentServiceTypes() {
		return Set.of(FulfillmentServiceType.JD_WAREHOUSE);
	}

	@Override
	protected void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, WarehouseConfigDto warehouseConfigDto) {
		DeliveryOutstockRequest deliveryOutstockRequest;
		try {
			deliveryOutstockRequest = getDeliveryOutstockRequest(orderShipAuditAggRoot, saleOrderAggRoot, receivingAddress,
					warehouseConfigDto);
		}
		catch (Exception e) {
			log.error("组装京东创建销售出库单请求参数报错, 发货审核id是 {}, customerCode 是{}", orderShipAuditAggRoot.getId(), warehouseConfigDto, e);
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}

		R<JdResponseExt<String, List<WaybillPackDTO>>> r;
		try {
			r = jdClient.deliveryOutstock(warehouseConfigDto.getCustomerCode(), deliveryOutstockRequest);
		}
		catch (Exception e) {
			log.error(String.format("调用京东创建销售单接口发生错误, 发货审核id是%s", orderShipAuditAggRoot.getId()), e);
			orderShipAuditAggRoot.createFail(e.getMessage());
			return;
		}
		handleResult(orderShipAuditAggRoot, r);
	}

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, WarehouseConfigDto warehouseConfigDto) {
		JdResponse<List<OutStockResponseDto>> jdResponse;
		try {
			jdResponse = jdClient.queryOutStockList(warehouseConfigDto.getCustomerCode(), orderShipAuditAggRoot.getThirdPartWarehouseCode(),
					orderShipAuditAggRoot.getChannelStoreOrderId());
		}
		catch (Exception e) {
			log.error("调用京东同步销售出库单同步信息发生错误, customerCode是{}, 三方仓编码是{}, customerBillCode是{}", warehouseConfigDto.getCustomerCode(),
					orderShipAuditAggRoot.getThirdPartWarehouseCode(), orderShipAuditAggRoot.getAuditNo(), e);
			orderShipAuditAggRoot.syncFail(e.getMessage());
			return;
		}
		// 京东那边发生错误
		if (jdResponse.getError_response() != null) {
			log.error("调用京东同步销售出库单同步信息,京东返回错误信息, 错误编码是{}, 错误信息是{}", jdResponse.getError_response().getCode(),
					jdResponse.getError_response().getZh_desc());
			orderShipAuditAggRoot.syncFail(jdResponse.getError_response().getCode() + "___" + jdResponse.getError_response().getZh_desc());
			return;
		}

		JdResponseExt.Response<List<OutStockResponseDto>, Object> response = jdResponse.getResponse();
		FopResult<List<OutStockResponseDto>, Object> content = response.getContent();
		if (!Objects.equals(content.getErrorCode(), 200)) {
			log.error("调用京东同步销售出库单同步信息,京东返回错误信息, 错误编码是{}, 错误信息是{}", content.getErrorCode(), content.getErrorMsg());
			orderShipAuditAggRoot.syncFail(content.getErrorCode() + "___" + content.getErrorMsg());
			return;
		}

		List<OutStockResponseDto> outStockResponseDtoList = content.getData();
		if (CollUtil.isEmpty(outStockResponseDtoList)) {
			log.info("调用京东同步销售出库单获取不到数据, customerCode是{}, 三方仓编码是{}, customerBillCode是{}", warehouseConfigDto.getCustomerCode(),
					orderShipAuditAggRoot.getThirdPartWarehouseCode(), orderShipAuditAggRoot.getAuditNo());
			// 用户在京东仓管理后台取消下单后就不会返回订单了
			orderShipAuditAggRoot.cancel("京东仓管理后台取消下单");
			return;
		}
		OutStockResponseDto outStockResponseDto = outStockResponseDtoList.get(0);
		String serviceBillStatus = outStockResponseDto.getServiceBillStatus();
		boolean shipExcetionFlag = JDServiceBillStatus.isShipExcetion(Integer.valueOf(serviceBillStatus));
		if (shipExcetionFlag) {
			log.info("京东销售出库单发货异常, customerCode是{}, 三方仓编码是{}, customerBillCode是{}", warehouseConfigDto.getCustomerCode(),
					orderShipAuditAggRoot.getThirdPartWarehouseCode(), orderShipAuditAggRoot.getAuditNo());
			orderShipAuditAggRoot.shipException();
			return;
		}
		// 没有包裹信息不更新
		if (CollUtil.isEmpty(outStockResponseDto.getOutstockPackageDtoList())) {
			return;
		}
		// 承运商
		FulfillmentServiceCodePO fulfillmentServiceConfig = fulfillmentServiceConfigService.getByFulfillmentServiceTypeAndServiceCode(
				orderShipAuditAggRoot.getFulfillmentServiceType().getValue(), orderShipAuditAggRoot.getCarrierServiceCode());
		String actualCarrierName = Objects.nonNull(fulfillmentServiceConfig) ? fulfillmentServiceConfig.getCarrierName() : null;
		// 签收时间
		Date signingTime = outStockResponseDto.getSigningTime();
		// 出库时间
		Date outboundTime = outStockResponseDto.getOutboundTime();
		// 承运商单号,多个用逗号隔开
		String trackNo = outStockResponseDto.getOutstockPackageDtoList()
			.stream()
			.map(OutStockResponseDto.OutstockPackageDto::getShipWaybill)
			.distinct()
			.collect(Collectors.joining(","));
		for (OrderShipAuditItem item : orderShipAuditAggRoot.getItems()) {
			orderShipAuditAggRoot.markShipping(item.getChannelStoreOrderItemId(), item.getOrderItemId(), item.getQuantity(),
					ChannelItemFulfillmentInfo.builder()
						.shippingDate(DateUtil.dateToLocalDateTime(outboundTime))
						.trackingNo(trackNo)
						.carrierName(actualCarrierName)
						.estimatedArrivalDate(DateUtil.dateToLocalDateTime(signingTime))
						.build());
		}
		orderShipAuditAggRoot.calculateStatus();
	}

	private void handleResult(OrderShipAuditAggRoot orderShipAuditAggRoot, R<JdResponseExt<String, List<WaybillPackDTO>>> r) {
		if (!r.isSuccess()) {
			orderShipAuditAggRoot.createFail(r.getCode() + "___" + r.getMessage());
			return;
		}
		JdResponseExt<String, List<WaybillPackDTO>> jdResponse = r.getData();
		if (jdResponse.getError_response() != null) {
			JdResponseExt.ErrorResponse errorResponse = jdResponse.getError_response();
			orderShipAuditAggRoot.createFail(errorResponse.getCode() + "___" + errorResponse.getZh_desc());
			return;
		}

		JdResponseExt.Response<String, List<WaybillPackDTO>> response = jdResponse.getResponse();
		FopResult<String, List<WaybillPackDTO>> content = response.getContent();
		if (!content.isSuccess()) {
			orderShipAuditAggRoot.createFail(content.getErrorCode() + "___" + content.getErrorMsg());
			return;
		}
		// 创建成功
		orderShipAuditAggRoot.createSuccess(orderShipAuditAggRoot.getAuditNo());
	}

	private DeliveryOutstockRequest getDeliveryOutstockRequest(OrderShipAuditAggRoot orderShipAuditAggRoot,
			SaleOrderAggRoot saleOrderAggRoot, SaleOrderAddressVO saleOrderAddressVO, WarehouseConfigDto warehouseConfigDto) {
		DeliveryOutstockRequest deliveryOutstockRequest = new DeliveryOutstockRequest();
		// 销售出库单
		deliveryOutstockRequest.setOutstockType(20);
		// 货主id
		deliveryOutstockRequest.setCargoOwnerId(warehouseConfigDto.getOwnerCode());
		// 服务编码
		deliveryOutstockRequest.setCustomerCode(warehouseConfigDto.getCustomerCode());
		// 客户出库单编码
		deliveryOutstockRequest.setCustomerBillCode(orderShipAuditAggRoot.getAuditNo());
		// 下单账户 京东要求必传 老系统也是传固定值
		deliveryOutstockRequest.setOrderAccount("renpho_oms");
		// 订单类型
		deliveryOutstockRequest.setOrderType(orderShipAuditAggRoot.getJdOrderType());

		// 出库服务编码
		DeliveryOutstockRequest.PerformanceSpDTO performanceSpDTO = new DeliveryOutstockRequest.PerformanceSpDTO();
		performanceSpDTO.setCode(orderShipAuditAggRoot.getCarrierServiceCode());
		deliveryOutstockRequest.setPerformanceSpDTOList(ListUtil.toList(performanceSpDTO));
		// 仓库编码
		deliveryOutstockRequest.setWarehouseCode(orderShipAuditAggRoot.getThirdPartWarehouseCode());

		// 收货信息
		deliveryOutstockRequest.setReceiverName(saleOrderAddressVO.getRecipientName());
		deliveryOutstockRequest.setReceiverMobile(saleOrderAddressVO.getRecipientPhone());
		deliveryOutstockRequest.setReceiverCountry(saleOrderAddressVO.getCountryCode());
		deliveryOutstockRequest.setReceiverProvince(saleOrderAddressVO.getProvince());
		deliveryOutstockRequest.setReceiverCity(saleOrderAddressVO.getCity());
		deliveryOutstockRequest.setReceiverAddress(saleOrderAddressVO.getAddress1());
		deliveryOutstockRequest.setReceiverZipCode(saleOrderAddressVO.getPostalCode());
		// 英国特殊处理要传GB
		if (StrUtil.equalsIgnoreCase(deliveryOutstockRequest.getReceiverCountry(), "UK")) {
			deliveryOutstockRequest.setReceiverCountry("GB");
		}

		// 订单信息
		// 销售单号
		deliveryOutstockRequest.setScOrderNo(saleOrderAggRoot.getOrderNo());
		// 下单时间
		deliveryOutstockRequest.setScOrderCreateTime(DateUtil.parseToTimestamp(saleOrderAggRoot.getOrderedTime()));

		// 订单行
		// 京东一样的fnsku合并到一个订单行
		Map<String, List<OrderShipAuditItem>> fnskuMap = orderShipAuditAggRoot.getItems()
			.stream()
			.collect(Collectors.groupingBy(OrderShipAuditItem::getFnSku));
		List<DeliveryOutstockRequest.OutstockGoodsDto> goodsDtoList = new ArrayList<>(fnskuMap.size());
		fnskuMap.forEach((fnsku, orderShipAuditItemList) -> {
			Integer quantity = orderShipAuditItemList.stream().map(OrderShipAuditItem::getQuantity).reduce(0, Integer::sum);

			DeliveryOutstockRequest.OutstockGoodsDto outstockGoodsDto = new DeliveryOutstockRequest.OutstockGoodsDto();
			outstockGoodsDto.setLineNumber(orderShipAuditItemList.get(0).getChannelStoreOrderItemId());
			outstockGoodsDto.setNum(quantity);
			outstockGoodsDto.setCurrency(saleOrderAggRoot.getCurrency());
			outstockGoodsDto.setOutUnit(1);
			outstockGoodsDto.setSku(fnsku);
			goodsDtoList.add(outstockGoodsDto);
		});
		deliveryOutstockRequest.setGoodsDtoList(goodsDtoList);
		return deliveryOutstockRequest;
	}

}
