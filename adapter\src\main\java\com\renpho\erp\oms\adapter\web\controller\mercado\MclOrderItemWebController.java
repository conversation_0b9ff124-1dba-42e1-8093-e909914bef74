package com.renpho.erp.oms.adapter.web.controller.mercado;

import com.renpho.erp.oms.application.channelmanagement.mercado.service.MclOrderItemService;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderItemVO;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ShenyuSpringCloudClient("/mercado/**")
@RequestMapping("/mercado/item")
@AllArgsConstructor
public class MclOrderItemWebController {
	private final MclOrderItemService mclOrderItemService;

	/**
	 * 美客多订单商品列表
	 * @param orderId
	 * @return R<List<MclOrderItemVO>>
	 */
	@GetMapping("/list")
	public R<List<MclOrderItemVO>> listByOrderId(@RequestParam Long orderId) {
		return mclOrderItemService.listByOrderId(orderId);
	}
}
