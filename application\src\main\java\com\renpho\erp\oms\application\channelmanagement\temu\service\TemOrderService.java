package com.renpho.erp.oms.application.channelmanagement.temu.service;

import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.channelmanagement.temu.convert.TemOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.temu.convert.TemOrderItemAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.temu.dto.TemOrderPushDTO;
import com.renpho.erp.oms.domain.channelmanagement.TransOmsStatus;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderAggRoot;
import com.renpho.erp.oms.domain.channelmanagement.temu.repository.TemOrderRepository;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
@AllArgsConstructor
public class TemOrderService {
    private final TemOrderRepository temOrderRepository;

    @Lock4j(name = "oms:temu:sync", keys = {"#temOrderPushDTO.parentOrderMap.parentOrderSn", "#temOrderPushDTO.shopId"})
    public void createOrUpdate(TemOrderPushDTO temOrderPushDTO) {
        // 解析成平台单
        TemOrderAggRoot temOrderAggRoot = TemOrderAppConvertor.toDomain(temOrderPushDTO);
        temOrderAggRoot.setTemOrderItemList(TemOrderItemAppConvertor.toDomainList(temOrderPushDTO));
        // 查询有没有
        TemOrderAggRoot exitsOrder = temOrderRepository.getByUniqueIndex(temOrderPushDTO.getParentOrderMap().getParentOrderSn(), temOrderPushDTO.getShopId());
        // 更新插入
        if (Objects.nonNull(exitsOrder)) {
            temOrderAggRoot.setId(exitsOrder.getId());
            temOrderAggRoot.setTransOmsStatus(TransOmsStatus.TO_BE_TRANS);
            temOrderRepository.update(temOrderAggRoot ,exitsOrder);
        } else {
            temOrderRepository.save(temOrderAggRoot);
        }
    }

} 