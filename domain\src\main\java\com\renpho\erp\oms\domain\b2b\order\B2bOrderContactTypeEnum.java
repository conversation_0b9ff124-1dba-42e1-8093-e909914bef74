package com.renpho.erp.oms.domain.b2b.order;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * B2B联系人类型枚举
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum B2bOrderContactTypeEnum {
	/**
	 * 主联系人
	 */
	MAIN_CONTACT(1, "主联系人"),

	/**
	 * 采购
	 */
	PURCHASING(2, "采购"),

	/**
	 * 物流
	 */
	LOGISTICS(3, "物流"),

	/**
	 * 财务
	 */
	FINANCE(4, "财务");

	private final Integer value;
	private final String desc;

	public static B2bOrderContactTypeEnum getByValue(Integer value) {
		if (value == null) {
			return null;
		}
		for (B2bOrderContactTypeEnum type : B2bOrderContactTypeEnum.values()) {
			if (type.getValue().equals(value)) {
				return type;
			}
		}
		return null;
	}
}