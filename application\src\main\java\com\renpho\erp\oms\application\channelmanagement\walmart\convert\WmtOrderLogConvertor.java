package com.renpho.erp.oms.application.channelmanagement.walmart.convert;

import com.renpho.erp.oms.application.channelmanagement.walmart.dto.WmtOrderLog;
import com.renpho.erp.oms.application.channelmanagement.walmart.vo.WmtOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrder;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface WmtOrderLogConvertor {

	WmtOrderLog toLog(WmtOrder wmtOrder);

	WmtOrderVO toVO(WmtOrder wmtOrder);
}
