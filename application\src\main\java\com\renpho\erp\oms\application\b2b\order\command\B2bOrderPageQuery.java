package com.renpho.erp.oms.application.b2b.order.command;


import com.renpho.karma.dto.PageQuery;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * B2B订单分页查询参数
 */
@Data
public class B2bOrderPageQuery extends PageQuery {

    /**
     * 订单号集合，支持多个精确搜索
     */
    private Set<String> orderNos;

    /**
     * 客户参考号集合，支持多个精确搜索
     */
    private Set<String> referNos;

    /**
     * 发票号集合，支持多个精确搜索
     */
    private Set<String> invoiceNos;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 创建时间-开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间-结束
     */
    private LocalDateTime createTimeEnd;

    /**
     * 发票时间-开始
     */
    private LocalDateTime invoiceTimeStart;

    /**
     * 发票时间-结束
     */
    private LocalDateTime invoiceTimeEnd;

    /**
     * 发货时间-开始
     */
    private LocalDateTime shippedTimeStart;

    /**
     * 发货时间-结束
     */
    private LocalDateTime shippedTimeEnd;

    /**
     * 订单状态 1-待处理 2-订单审核中 3-备货中 4-部分发货 5-已发货 6-已完结 7-已取消
     */
    private Integer orderStatus;
}
