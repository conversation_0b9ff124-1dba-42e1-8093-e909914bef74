package com.renpho.erp.oms.application.ordershipaudit.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseLanguageVo;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import lombok.Data;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;

/**
 *
 *
 * <AUTHOR>
 * @since 2025/3/5
 */
@Data
public class ThirdpartWarehouseVo {
    /**
     * 仓库id
     */
    private Integer id;
    /**
     * 仓库名字,根据语言头返回
     */
    private String warehouseName;
    /**
     * 仓库编码
     */
    private String warehouseCode;

    /**
     * 第三方仓库编码
     */
    private String thirdPartWarehouseCode;

    private List<ServiceCodeInfo> serviceCodeInfos = new ArrayList<>();

    @Data
    public static class ServiceCodeInfo {
        /**
         * 服务编码
         */
        private String serviceCode;
        /**
         * 名字,根据语言头返回
         */
        private String serviceName;

        /**
         * 京东订单类型
         */
        private Integer jdOrderType;

    }


}

