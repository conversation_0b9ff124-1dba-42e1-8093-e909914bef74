package com.renpho.erp.oms.application.b2b.customer.vo;

import lombok.Data;

/**
 * 客户联系人视图对象
 */
@Data
public class CustomerContactVO {
    /**
     * ID
     */
    private Long id;
    
    /**
     * 客户ID
     */
    private Long customerId;
    
    /**
     * 联系人名称
     */
    private String contactName;
    
    /**
     * 联系人职位
     */
    private String contactPosition;
    
    /**
     * 联系人邮箱
     */
    private String contactEmail;
    
    /**
     * 联系人电话
     */
    private String contactPhone;
    
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否默认联系人
     */
    private Boolean isDefault;
} 