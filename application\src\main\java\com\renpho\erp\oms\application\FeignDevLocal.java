package com.renpho.erp.oms.application;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.cloud.client.DefaultServiceInstance;
import org.springframework.cloud.client.ServiceInstance;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.IdUtil;

/**
 * feign本地调试工具
 *
 * <AUTHOR>
 * @since 2024/12/31
 */
@Aspect
@Component
@Profile("local")
public class FeignDevLocal {

	private static final Map<String, Integer> PORT_MAP = MapBuilder.<String, Integer> create()
		.put("erp-pms", 31392)
		.put("erp-mpds", 31189)
		.put("erp-ims", 31187)
		.put("erp-ftm", 31186)
		.put("erp-smc", 31182)
		.put("erp-oms", 31188)
		.put("erp-uac", 31181)
		.put("erp-mdm", 31184)
		.put("erp-pds", 31183)
		.put("erp-bpm", 31191)
		.put("erp-srm", 31185)
		.build();

	@Around("execution(* org.springframework.cloud.client.discovery.composite.CompositeDiscoveryClient.getInstances(*)))")
	public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
		List<ServiceInstance> serviceInstanceList = (List<ServiceInstance>) joinPoint.proceed();
		if (CollUtil.isNotEmpty(serviceInstanceList)) {
			return serviceInstanceList;
		}

		// 当本地没有启动对应服务的时候,生成一个shenyu gateway服务实例,让feign去调用
		String serviceId = (String) joinPoint.getArgs()[0];

		DefaultServiceInstance serviceInstance = new DefaultServiceInstance();
		serviceInstance.setInstanceId(IdUtil.fastUUID());
		serviceInstance.setServiceId(serviceId);
		serviceInstance.setHost("*************");
		serviceInstance.setPort(PORT_MAP.get(serviceId));
		serviceInstanceList = new ArrayList<>();
		serviceInstanceList.add(serviceInstance);
		return serviceInstanceList;
	}

	@Around("execution(* org.springframework.cloud.client.loadbalancer.LoadBalancerClient.choose(String, org.springframework.cloud.client.loadbalancer.Request))")
	public Object around1(ProceedingJoinPoint joinPoint) throws Throwable {
		Object proceed = joinPoint.proceed();
		if (proceed != null) {
			return proceed;
		}
		String serviceId = (String) joinPoint.getArgs()[0];
		DefaultServiceInstance serviceInstance = new DefaultServiceInstance();
		serviceInstance.setInstanceId(IdUtil.fastUUID());
		serviceInstance.setServiceId(serviceId);
		serviceInstance.setHost("*************");
		serviceInstance.setPort(PORT_MAP.get(serviceId));
		return serviceInstance;
	}

	// @Around("execution(*
	// org.springframework.cloud.client.loadbalancer.LoadBalancerClient.reconstructURI(org.springframework.cloud.client.ServiceInstance,
	// java.net.URI))")
	// public Object around2(ProceedingJoinPoint joinPoint) throws Throwable {
	// URI uri = (URI) joinPoint.proceed();
	// String urlStr = StrUtil.removeAll(uri.toString(), ":0");
	// return URLUtil.url(urlStr).toURI();
	// }
}
