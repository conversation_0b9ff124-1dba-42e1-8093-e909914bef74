package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import com.renpho.erp.oms.infrastructure.feign.dingtalk.DingTalkClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditInventoryService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.repository.LogisticsShipmentRepository;
import com.renpho.erp.oms.domain.ordershipaudit.repository.OrderShipAuditRepository;
import com.renpho.erp.oms.infrastructure.config.NacosOrderConfig;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.service.SaleOrderService;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractLogisticsStrategy implements LogisticsStrategy {

	@Autowired
	private OrderShipAuditInventoryService inventoryService;
	@Autowired
	private SaleOrderService saleOrderService;
	@Autowired
	protected OrderShipAuditRepository orderShipAuditRepository;
	@Autowired
	private LogisticsShipmentRepository logisticsShipmentRepository;
	@Autowired
	private NacosOrderConfig nacosOrderConfig;
	@Autowired
	private DingTalkClient dingTalkClient;

	public void processLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		// 处理物流下单结果(除了temu都是上传s3,捕获所有异常增加上传失败数)
		try {
			this.doProcessLogisticsResult(logisticsShipmentAggRoot);
		}
		catch (Exception e) {
			log.error("订单{},物流单号{},处理上传面单文件失败", logisticsShipmentAggRoot.getOrderId(), logisticsShipmentAggRoot.getLogisticsOrderNo(), e);
			logisticsShipmentAggRoot.uploadFail();
		}
		// 更新物流下单状态
		SpringUtil.getBean(getClass()).doUpdate(logisticsShipmentAggRoot);
		// 失败次数超过阈值报警
		if (!LogisticsShipmentStatus.DOWNLOAD_WAYBILL_FILE_SUCCESS.getValue().equals(logisticsShipmentAggRoot.getStatus()) &&
				logisticsShipmentAggRoot.getWaybillFileCurrentFailCount() > nacosOrderConfig.getLogisticAlarmThreshold()) {
			dingTalkClient.sendAlarmMessage(String.format("订单%s 物流单号 %s 上传面单文件失败，请及时处理。", logisticsShipmentAggRoot.getOrderId(),
					logisticsShipmentAggRoot.getLogisticsOrderNo()));
		}
	}

	@Transactional
	public void doUpdate(LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
		OrderShipAuditAggRoot orderShipAuditAggRoot = orderShipAuditRepository.getById(logisticsShipmentAggRoot.getAuditId());
		if (LogisticsShipmentStatus.FAILED.getValue().equals(logisticsShipmentAggRoot.getStatus())) {
			// 设置为发货异常;
			saleOrderService.markShipExcetion(logisticsShipmentAggRoot.getOrderId(), logisticsShipmentAggRoot.getFailMsg());
			// 释放库存
			inventoryService.unlockInventory(orderShipAuditAggRoot);
			// 发货审核 设置为失败
			orderShipAuditAggRoot.createFail(logisticsShipmentAggRoot.getFailMsg());
			orderShipAuditRepository.updateStatus(orderShipAuditAggRoot);
		}
		// 更新物流下单状态等信息
		logisticsShipmentRepository.updateStatus(logisticsShipmentAggRoot);

		if (logisticsShipmentAggRoot.isDownloadSuccess()) {
			// 修改发货审核为待创建WMS出库单
			orderShipAuditRepository.toBeCreateWMS(orderShipAuditAggRoot.getId());
		}
	}

	public abstract void doProcessLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot);

}
