package com.renpho.erp.oms.application.ruleconfig.skuMapping.command;

import java.math.BigDecimal;
import java.util.List;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

@Data
public class SkuMappingAddCmd {
	/**
	 * 店铺Id
	 */
	@NotNull(message = "{STORE_EMPTY}")
	private Integer storeId;

	/**
	 * 渠道Id
	 */
	@NotNull(message = "{CHANNLE_EMPTY}")
	private Integer channelId;

	/**
	 * 销售SKU
	 */
	private String msku;

	private String asin;

	/**
	 * 运营人员ID
	 */
	private Integer operatorId;

	/**
	 * 统计FBA库存
	 */
	private Integer fbaInventoryCounted;

	/**
	 * 发货方式
	 */
	@NotNull(message = "{FULFILMENTTYPE_EMPTY}")
	private Integer fulfillmentType;

	/**
	 * 映射行
	 */
	private List<SkuMappingLineAddCmd> skuMappingLineAddCmdList;

	@Data
	public static class SkuMappingLineAddCmd {
		/**
		 * 采购SKU
		 */
		private String psku;

		/**
		 * 条形码
		 */
		private String fnsku;

		/**
		 * 条形码商品数量
		 */
		private Integer fnskuQuantity;

		/**
		 * 零售单价
		 */
		private BigDecimal retailPrice;
	}

}
