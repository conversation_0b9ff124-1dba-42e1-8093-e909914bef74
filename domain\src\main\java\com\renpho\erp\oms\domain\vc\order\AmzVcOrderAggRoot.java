package com.renpho.erp.oms.domain.vc.order;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.jmolecules.ddd.annotation.AggregateRoot;
import org.jmolecules.ddd.annotation.Identity;

import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.enums.AmzVcPurchaseOrderState;
import com.renpho.erp.oms.domain.vc.order.valueobject.*;

import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 亚马逊VC订单聚合根 基于Amazon SP-API getPurchaseOrders和getPurchaseOrdersStatus接口设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.Order
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.OrderStatus
 * <AUTHOR>
 */
@Slf4j
@Getter
@Builder
@AggregateRoot
public class AmzVcOrderAggRoot {

	@Identity
	private Long id;

	/**
	 * 店铺ID（内部字段，非API字段）
	 */
	private Integer storeId;

	// ========== getPurchaseOrders接口字段 ==========

	/**
	 * 采购订单号 对应API字段：purchaseOrderNumber
	 */
	private String purchaseOrderNumber;

	/**
	 * 采购订单状态
	 */
	private AmzVcPurchaseOrderState purchaseOrderState;

	/**
	 * 订单详情 对应API字段：orderDetails
	 */
	private AmzVcOrderDetails orderDetails;

	/**
	 * 订单商品列表 对应API字段：items
	 */
	private List<AmzVcOrderItem> items;

	// ========== getPurchaseOrdersStatus接口字段 ==========

	/**
	 * 订单状态信息 对应getPurchaseOrdersStatus接口的返回数据
	 */
	private AmzVcOrdersStatus ordersStatus;

	// ========== 内部业务字段 ==========

	/**
	 * 创建时间（内部字段）
	 */
	private LocalDateTime createdTime;

	/**
	 * 更新时间（内部字段）
	 */
	private LocalDateTime updatedTime;

	// ========== 业务方法 ==========

	/**
	 * 获取订单商品列表（不可修改）
	 * @return List<AmzVcOrderItem>
	 */
	public List<AmzVcOrderItem> getItems() {
		return items == null ? Collections.emptyList() : Collections.unmodifiableList(items);
	}

	/**
	 * 获取Asin列表
	 * @return String
	 */
	public List<String> getAsinList() {
		if (CollectionUtils.isEmpty(items)) {
			return List.of();
		}
		return items.stream().map(AmzVcOrderItem::getAmazonProductIdentifier).toList();
	}

	/**
	 * 检查订单是否已确认
	 * @return boolean
	 */
	public boolean isAcknowledged() {
		return ordersStatus != null && (ordersStatus.isPurchaseOrderAcknowledged() || ordersStatus.isOrderAcknowledged());
	}

	/**
	 * 检查订单是否已关闭
	 * @return boolean
	 */
	public boolean isClosed() {
		return ordersStatus != null && ordersStatus.isPurchaseOrderClosed();
	}

	/**
	 * 检查订单是否已取消
	 * @return boolean
	 */
	public boolean isCancelled() {
		return ordersStatus != null && ordersStatus.isPurchaseOrderCancelled();
	}

	/**
	 * 检查商品是否已完全接收
	 * @return boolean
	 */
	public boolean isItemsFullyReceived() {
		return ordersStatus != null && ordersStatus.isItemsFullyReceived();
	}

	/**
	 * 检查商品是否已完全确认
	 * @return boolean
	 */
	public boolean isItemsFullyConfirmed() {
		return ordersStatus != null && ordersStatus.isItemsFullyConfirmed();
	}

	/**
	 * 获取下单数量
	 * <p>
	 * 优先从订单状态的详情中获取下单数量,如果无法从状态详情中获取，则回退到使用订单项本身的下单数量
	 * </p>
	 * @param itemSequenceNumber Amazon商品行号
	 * @return 下单数量
	 */
	public Integer getOrderedQuantity(String itemSequenceNumber) {
		// 通过Amazon商品行号 获取 AmzVcOrderItemStatus
		AmzVcOrderItemStatus itemStatus = this.getItemStatusBySequenceNumber(itemSequenceNumber);
		// 优先从订单状态的详情中获取下单数量
		if (Objects.nonNull(itemStatus.getOrderedQuantity())
				&& CollectionUtils.isNotEmpty(itemStatus.getOrderedQuantity().getOrderedQuantityDetails())) {
			// 第一次下单的数量
			return itemStatus.getOrderedQuantity().getOrderedQuantityDetails().get(0).getQuantity();
		}
		// 如果无法从状态详情中获取，则回退到使用订单项本身的下单数量（最新的下单数量）
		if (Objects.nonNull(itemStatus.getOrderedQuantity())) {
			return itemStatus.getOrderedQuantity().getQuantity();
		}
		throw DomainException.of("No ordered quantity found for sequence number: " + itemSequenceNumber);
	}

	/**
	 * 通过Amazon商品行号 获取 AmzVcOrderItemStatus
	 * @param itemSequenceNumber Amazon商品行号
	 * @return AmzVcOrderItemStatus
	 */
	private @NotNull AmzVcOrderItemStatus getItemStatusBySequenceNumber(String itemSequenceNumber) {
		// 将商品状态列表转换为Map，以便通过itemSequenceNumber快速查找
		Map<String, AmzVcOrderItemStatus> itemStatusMap = (ordersStatus == null || CollectionUtils.isEmpty(ordersStatus.getItemStatuses()))
				? new HashMap<>()
				: ordersStatus.getItemStatuses()
					.stream()
					.collect(Collectors.toMap(AmzVcOrderItemStatus::getItemSequenceNumber, Function.identity()));
		// 亚马逊订单商品行状态
		AmzVcOrderItemStatus itemStatus = itemStatusMap.get(itemSequenceNumber);
		if (Objects.isNull(itemStatus)) {
			throw DomainException.of("No order status found for sequence number: " + itemSequenceNumber);
		}
		return itemStatus;
	}

	/**
	 * 获取接受数量,如果为空则返回0
	 * @param itemSequenceNumber Amazon商品行号
	 * @return Integer
	 */
	public Integer getAcceptedQuantity(String itemSequenceNumber) {
		// 通过Amazon商品行号 获取 AmzVcOrderItemStatus
		AmzVcOrderItemStatus itemStatus = this.getItemStatusBySequenceNumber(itemSequenceNumber);
		// 接受数量
		return Optional.ofNullable(itemStatus.getAcknowledgementStatus())
			.map(AmzVcAcknowledgementStatus::getAcceptedQuantity)
			.map(AmzVcQuantity::getQuantity)
			.orElse(0);
	}

	/**
	 * 获取拒绝数量，如果为空则返回0
	 * @param itemSequenceNumber Amazon商品行号
	 * @return Integer 可能为空
	 */
	public Integer getRejectedQuantity(String itemSequenceNumber) {
		// 通过Amazon商品行号 获取 AmzVcOrderItemStatus
		AmzVcOrderItemStatus itemStatus = this.getItemStatusBySequenceNumber(itemSequenceNumber);
		// 拒绝数量，
		return Optional.ofNullable(itemStatus.getAcknowledgementStatus())
			.map(AmzVcAcknowledgementStatus::getRejectedQuantity)
			.map(AmzVcQuantity::getQuantity)
			.orElse(0);
	}

	/**
	 * 获取接收数量，如果为空则直接返回
	 * @param itemSequenceNumber Amazon商品行号
	 * @return Integer 可能为空
	 */
	public Integer getReceivedQuantity(String itemSequenceNumber) {
		// 通过Amazon商品行号 获取 AmzVcOrderItemStatus
		AmzVcOrderItemStatus itemStatus = this.getItemStatusBySequenceNumber(itemSequenceNumber);
		// 接收数量
		return Optional.ofNullable(itemStatus.getReceivingStatus())
			.map(AmzVcReceivingStatus::getReceivedQuantity)
			.map(AmzVcQuantity::getQuantity)
			.orElse(null);
	}

	/**
	 * 获取订单商品行金额
	 * @param itemSequenceNumber 亚马逊订单商品行号
	 * @return BigDecimal
	 */
	public BigDecimal getNetCostAmount(String itemSequenceNumber) {
		// 通过Amazon商品行号 获取 AmzVcOrderItemStatus
		AmzVcOrderItemStatus itemStatus = this.getItemStatusBySequenceNumber(itemSequenceNumber);
		return Optional.ofNullable(itemStatus.getNetCost())
			.map(AmzVcMoney::getAmount)
			.orElseThrow(() -> DomainException.of("Amazon netCost is null"));
	}

}
