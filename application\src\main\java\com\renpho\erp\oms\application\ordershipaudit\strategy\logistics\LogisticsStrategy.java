package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import java.util.Set;

import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

/**
 * @desc: 物流下单策略接口
 * @time: 2025-04-03 14:15:28
 * @author: Alina
 */

public interface LogisticsStrategy {

	/**
	 * 获取履约服务类型.
	 * @return SaleChannelType
	 */
	Set<FulfillmentServiceType> getFulfillmentServiceType();

	/**
	 * 物流下单
	 * @param orderShipAuditAggRoot 发货审核领域驱动对象
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 订单收货地址
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 */
	void createLogisticsShipment(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, LogisticsShipmentAggRoot logisticsShipmentAggRoot);

	/**
	 * 获取并处理物流下单结果
	 * @param logisticsShipmentAggRoot 物流下单聚合根
	 */
	void processLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot);
}
