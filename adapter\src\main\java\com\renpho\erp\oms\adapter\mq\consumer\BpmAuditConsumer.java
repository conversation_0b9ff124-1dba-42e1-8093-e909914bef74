package com.renpho.erp.oms.adapter.mq.consumer;

import com.renpho.erp.bpm.api.dto.ProcessResultDto;
import com.renpho.erp.oms.application.b2b.order.service.B2bOrderAuditService;
import com.renpho.erp.oms.infrastructure.common.constant.MQConstant;
import com.renpho.erp.oms.infrastructure.common.mq.MqMessageHandlerDecorator;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.messaging.Message;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;


/**
 * 消费BPM审核消息
 */
@Component
@AllArgsConstructor
@Slf4j
public class BpmAuditConsumer {

    private B2bOrderAuditService b2bOrderAuditService;

    @Bean
    public Consumer<Message<String>> auditB2bOrder() {
        return MqMessageHandlerDecorator.wrap(msg -> {
            log.info("收到BPM回传的初始消息 - msg header: {}, json: {}", msg.getHeaders(), msg.getPayload());
            String tag = msg.getHeaders().get("ROCKET_TAGS").toString();
            String message = msg.getPayload();
            log.info("接收到BPM系统同步至OMS的信息{} ,tag为{}", message, tag);
            switch (tag) {
                // 确认消息
                case MQConstant.TAG.B2B_ORDER_AUDIT:
                    ProcessResultDto processResultDto = JSONKit.parseObject(message, ProcessResultDto.class);
                    b2bOrderAuditService.processOrderAuditResult(processResultDto);
                    break;
                case MQConstant.TAG.B2B_ORDER_SHIPMENT_AUDIT:
                    ProcessResultDto shipmentProcessResultDto = JSONKit.parseObject(message, ProcessResultDto.class);
                    b2bOrderAuditService.processOrderShipmentAuditResult(shipmentProcessResultDto);
                    break;
                default:
                    throw new RuntimeException("未知tag");
            }
        });
    }

}
