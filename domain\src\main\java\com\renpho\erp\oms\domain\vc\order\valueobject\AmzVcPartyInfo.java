package com.renpho.erp.oms.domain.vc.order.valueobject;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC参与方信息值对象
 * 基于Amazon SP-API PartyIdentification模型设计
 * 参考：io.swagger.client.model.vendoRetailProcurement.orders.PartyIdentification
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcPartyInfo {

	/**
	 * 参与方ID
	 * 对应API字段：partyId
	 * 参与方的唯一标识符
	 */
	private String partyId;

	/**
	 * 地址信息
	 * 对应API字段：address
	 */
	private AmzVcAddress address;

	/**
	 * 税务登记详情
	 * 对应API字段：taxRegistrationDetails
	 */
	private AmzVcTaxRegistrationDetails taxRegistrationDetails;

	/**
	 * 检查是否有地址信息
	 * @return boolean
	 */
	public boolean hasAddress() {
		return address != null;
	}

	/**
	 * 检查是否有税务登记信息
	 * @return boolean
	 */
	public boolean hasTaxRegistration() {
		return taxRegistrationDetails != null;
	}

	/**
	 * 获取参与方名称
	 * @return String 参与方名称，如果地址为null则返回null
	 */
	public String getName() {
		return hasAddress() ? address.getName() : null;
	}

	/**
	 * 获取国家代码
	 * @return String 国家代码，如果地址为null则返回null
	 */
	public String getCountryCode() {
		return hasAddress() ? address.getCountryCode() : null;
	}

	/**
	 * 获取完整地址字符串
	 * @return String 完整地址，如果地址为null则返回null
	 */
	public String getFullAddress() {
		return hasAddress() ? address.getFullAddress() : null;
	}

	/**
	 * 获取税务登记号
	 * @return String 税务登记号，如果税务信息为null则返回null
	 */
	public String getTaxRegistrationNumber() {
		return hasTaxRegistration() ? taxRegistrationDetails.getTaxRegistrationNumber() : null;
	}

}
