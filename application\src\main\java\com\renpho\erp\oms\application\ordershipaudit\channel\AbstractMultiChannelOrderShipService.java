package com.renpho.erp.oms.application.ordershipaudit.channel;

import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.salemanagement.service.SaleOrderQueryService;
import com.renpho.erp.oms.application.salemanagement.vo.SaleOrderAddressVO;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * 抽象多渠道-发货 应用服务
 * <AUTHOR>
 */
public abstract class AbstractMultiChannelOrderShipService extends AbstractOrderShipService {

	private final StoreClient storeClient;

	protected AbstractMultiChannelOrderShipService(SaleOrderQueryService saleOrderQueryService, StoreClient storeClient) {
		super(saleOrderQueryService);
		this.storeClient = storeClient;
	}

	/**
	 * 创建履约单（仓储物流）
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 */
	@Override
	public void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress) {
		// 获取多渠道店铺授权信息
		StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(orderShipAuditAggRoot.getChannelStoreId());
		// 创建履约单（仓储物流）
		this.createFulfillmentOrder(orderShipAuditAggRoot, saleOrderAggRoot, receivingAddress, storeAuthorizationVo);
	}

	/**
	 * 创建出库单
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param saleOrderAggRoot 订单聚合根
	 * @param receivingAddress 敏感地址信息
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	protected abstract void createFulfillmentOrder(OrderShipAuditAggRoot orderShipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot,
			SaleOrderAddressVO receivingAddress, StoreAuthorizationVo storeAuthorizationVo);

	@Override
	public void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot) {
		StoreAuthorizationVo storeAuthorizationVo = storeClient.getAuthorizationByStoreId(orderShipAuditAggRoot.getChannelStoreId());
		this.syncShipmentInfo(orderShipAuditAggRoot, storeAuthorizationVo);
	}

	/**
	 * 同步平台发货信息
	 * @param orderShipAuditAggRoot 发货审核聚合根
	 * @param storeAuthorizationVo 店铺授权信息
	 */
	protected abstract void syncShipmentInfo(OrderShipAuditAggRoot orderShipAuditAggRoot, StoreAuthorizationVo storeAuthorizationVo);
}
