package com.renpho.erp.oms.application.channelmanagement.walmart.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WmtOrderVO {
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺名称
	 */
	private String storeName;

	private String shopId;

	/**
	 * 卖家订单号
	 */
	private String purchaseOrderId;

	private String customerOrderId;

	private String customerEmailId;
	/**
	 * 客户提交销售订单的日期
	 */
	private LocalDateTime orderDate;

	/**
	 * 发货方式类型
	 */
	private String shipNodeType;

	/**
	 * 发货城市
	 */
	private String postalAddressCity;

	/**
	 * 发货区
	 */
	private String postalAddressState;

	/**
	 * 发货邮政编码
	 */
	private String postalAddressPostalCode;

	/**
	 * 发国家
	 */
	private String postalAddressCountry;

	/**
	 * 发货方式 Standard Express OneDay Freight WhiteGlove Value
	 */
	private String shippingInfoMethodCode;

	/**
	 * 预计送达时间
	 */
	private LocalDateTime shippingInfoEstimatedDeliveryDate;

	/**
	 * 预计发货时间
	 */
	private LocalDateTime shippingInfoEstimatedShipDate;
}
