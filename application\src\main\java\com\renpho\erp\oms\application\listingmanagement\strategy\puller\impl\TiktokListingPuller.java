package com.renpho.erp.oms.application.listingmanagement.strategy.puller.impl;

import com.renpho.erp.apiproxy.tiktok.model.ShopAccount;
import com.renpho.erp.apiproxy.tiktok.model.product.SearchProductsData;
import com.renpho.erp.apiproxy.tiktok.model.product.SearchProductsRequest;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchDTO;
import com.renpho.erp.oms.application.listingmanagement.dto.ListingSearchQuery;
import com.renpho.erp.oms.application.listingmanagement.strategy.puller.AbstractListingPullerTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreAuthorizationDTO;
import com.renpho.erp.oms.infrastructure.feign.proxy.TiktokClient;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Tiktok Listing拉取器
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
@Slf4j
public class TiktokListingPuller extends AbstractListingPullerTemplate {

	private TiktokClient tiktokClient;

	/**
	 * Tiktok 渠道编码
	 * @return String
	 */
	@Override
	public String getSalesChannelCode() {
		return ChannelCode.TT_CHANNEL_CODE;
	}

	/**
	 * 分批拉取Tiktok-原始Listing数据
	 * @param storeAuthorization 店铺授权信息
	 * @param listingSearchQuery Listing分批拉取查询参数
	 * @return List
	 */
	@Override
	protected ListingSearchDTO pullSourceListing(StoreAuthorizationDTO storeAuthorization, ListingSearchQuery listingSearchQuery) {
		// 构建店铺账号
		ShopAccount shopAccount = this.buildShopAccount(storeAuthorization);
		if (Objects.isNull(shopAccount)) {
			return null;
		}
		// 构建分页查询条件
		SearchProductsRequest.Query query = this.buildQuery(listingSearchQuery);
		// 查询tiktok产品数据列表
		SearchProductsData searchProductsData = tiktokClient.searchProducts(shopAccount, query, new SearchProductsRequest.Body());
		if (Objects.isNull(searchProductsData) || CollectionUtils.isEmpty(searchProductsData.getProducts())) {
			return null;
		}
		// 构建原始Listing数据列表
		List<SalesChannelListingSource> listingSourceList = this.buildListingSourceList(storeAuthorization, searchProductsData);
		// 构建Listing分批拉取结果
		return this.buildListingSearchDTO(searchProductsData, listingSourceList);
	}

	/**
	 * 构建Listing分批拉取结果
	 * @param searchProductsData tiktok产品数据
	 * @param listingSourceList 原始Listing数据列表
	 * @return ListingSearchDTO
	 */
	private @NotNull ListingSearchDTO buildListingSearchDTO(SearchProductsData searchProductsData,
			List<SalesChannelListingSource> listingSourceList) {
		ListingSearchDTO listingSearchDTO = new ListingSearchDTO();
		// 上一页返回的标记，用于检索下一页
		listingSearchDTO.setSearchAfter(searchProductsData.getNext_page_token());
		listingSearchDTO.setListingSourceList(listingSourceList);
		return listingSearchDTO;
	}

	/**
	 * 构建原始Listing数据列表
	 * @param storeAuthorization 店铺授权信息
	 * @param searchProductsData tiktok产品数据
	 * @return List
	 */
	private @NotNull List<SalesChannelListingSource> buildListingSourceList(StoreAuthorizationDTO storeAuthorization,
			SearchProductsData searchProductsData) {
		List<SearchProductsData.Product> products = searchProductsData.getProducts();
		return products.stream().map(product -> {
			// 构建销售渠道原始Listing
			SalesChannelListingSource listingSource = super.buildListingSource(storeAuthorization);
			// 销售渠道 listing id
			listingSource.setListingId(product.getId());
			// 原始报文
			listingSource.setContent(JSONKit.toJSONString(product));
			return listingSource;
		}).toList();
	}

	/**
	 * 构建分页查询条件
	 * @param listingSearchQuery Listing分批拉取查询参数
	 * @return SearchProductsRequest.Query
	 */
	private SearchProductsRequest.Query buildQuery(ListingSearchQuery listingSearchQuery) {
		SearchProductsRequest.Query query = new SearchProductsRequest.Query();
		query.setPage_size(30);
		// 上一页返回的标记，用于检索下一页（Tiktok，第一页不需要它）
		query.setPage_token(listingSearchQuery.getSearchAfter());
		return query;
	}

	/**
	 * 构建店铺账号
	 * @param storeAuthorization 店铺授权信息
	 * @return ShopAccount
	 */
	private ShopAccount buildShopAccount(StoreAuthorizationDTO storeAuthorization) {
		String shopId = Optional.ofNullable(storeAuthorization.getStoreAuthorization())
			.map(StoreAuthorizationVo::getAuthorization)
			.map(StoreAuthorizationVo.Authorization::getTtkShopId)
			.orElse(null);
		if (StringUtils.isBlank(shopId)) {
			return null;
		}
		ShopAccount shopAccount = new ShopAccount();
		shopAccount.setShopId(shopId);
		return shopAccount;
	}

	@Override
	public boolean isNeedPushMq() {
		return true;
	}
}
