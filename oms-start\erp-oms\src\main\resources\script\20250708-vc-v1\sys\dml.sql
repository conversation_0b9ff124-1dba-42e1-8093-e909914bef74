-- VC订单管理菜单
INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Catalogue' AND route_address = 'oms' AND is_deleted = 0) tmp), 'OMS', '[{"name": "VC订单管理", "language": "zh-CN"}, {"name": "VC Order Manager", "language": "en-US"}]', '', 'Menu', 'oms/vcOrderManager/index', 'vcOrderManager', '', '', 6, 1, 0, 0, 3357, 3357, now(), now());

-- VC订单菜单
INSERT INTO sys_menu_info
(parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrderManager' AND is_deleted = 0) tmp), 'OMS', '[{"name": "VC订单", "language": "zh-CN"}, {"name": "VC Orders", "language": "en-US"}]', '', 'Menu', 'oms/vcOrderManager/vcOrder/index', 'vcOrder', '', '', 1, 1, 0, 0, 3357, 3357, now(), now());

-- VC订单管理菜单多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'vcOrderManager' AND menu_type = 'Menu' AND is_deleted = 0), 'en-US', 'VC Order Manager', 3357, NOW(), 3357, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'vcOrderManager' AND menu_type = 'Menu' AND is_deleted = 0), 'zh-CN', 'VC订单管理', 3357, NOW(), 3357, NOW(), 0);

-- VC订单菜单多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'vcOrder' AND menu_type = 'Menu' AND is_deleted = 0), 'en-US', 'VC Orders', 3357, NOW(), 3357, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE route_address = 'vcOrder' AND menu_type = 'Menu' AND is_deleted = 0), 'zh-CN', 'VC订单', 3357, NOW(), 3357, NOW(), 0);

-- VC订单按钮权限

-- 1. 列表按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "列表", "language": "zh-CN"}, {"name": "List", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:page', 1, 1, 0, 0, 0, 0, now(), now());

-- 2. 导出按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "导出", "language": "zh-CN"}, {"name": "Export", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:export', 2, 1, 0, 0, 0, 0, now(), now());

-- 3. 接单按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "接单", "language": "zh-CN"}, {"name": "Submit Order", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:submitOrder', 3, 1, 0, 0, 0, 0, now(), now());

-- 4. 重新解析按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "重新解析", "language": "zh-CN"}, {"name": "Reparse SKUs", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:reparseSkus', 4, 1, 0, 0, 0, 0, now(), now());

-- 5. 备注按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "备注", "language": "zh-CN"}, {"name": "Remark", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:remark', 5, 1, 0, 0, 0, 0, now(), now());

-- 6. 上传箱唛按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "上传箱唛", "language": "zh-CN"}, {"name": "Upload Label", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:uploadLabel', 6, 1, 0, 0, 0, 0, now(), now());

-- 7. 提交供应链按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "提交供应链", "language": "zh-CN"}, {"name": "Submit Supplier Chain", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:submitSupplierChain', 7, 1, 0, 0, 0, 0, now(), now());

-- 8. 开票按钮
INSERT INTO sys_menu_info (parent_id, system_module, multi_language_names, menu_icon, menu_type, component_path, route_address, route_params, perms, sort, status, is_deleted, is_cached, update_by, create_by, update_time, create_time)
VALUES ((SELECT tmp.id FROM (SELECT id FROM sys_menu_info WHERE menu_type = 'Menu' AND route_address = 'vcOrder' AND is_deleted = 0) tmp), 'OMS', '[{"name": "开票", "language": "zh-CN"}, {"name": "Invoice", "language": "en-US"}]', '', 'Button', '', '', '', 'oms:vcOrder:invoice', 8, 1, 0, 0, 0, 0, now(), now());

-- VC订单按钮多语言

-- 列表按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:page' AND menu_type = 'Button' AND is_deleted = 0), 'en-US', 'List', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:page' AND menu_type = 'Button' AND is_deleted = 0), 'zh-CN', '列表', 0, NOW(), 0, NOW(), 0);

-- 导出按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:export' AND is_deleted = 0), 'en-US', 'Export', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:export' AND is_deleted = 0), 'zh-CN', '导出', 0, NOW(), 0, NOW(), 0);

-- 接单按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:submitOrder' AND is_deleted = 0), 'en-US', 'Submit Order', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:submitOrder' AND is_deleted = 0), 'zh-CN', '接单', 0, NOW(), 0, NOW(), 0);

-- 重新解析按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:reparseSkus' AND is_deleted = 0), 'en-US', 'Reparse SKUs', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:reparseSkus' AND is_deleted = 0), 'zh-CN', '重新解析', 0, NOW(), 0, NOW(), 0);

-- 备注按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:remark' AND is_deleted = 0), 'en-US', 'Remark', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:remark' AND is_deleted = 0), 'zh-CN', '备注', 0, NOW(), 0, NOW(), 0);

-- 上传箱唛按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:uploadLabel' AND is_deleted = 0), 'en-US', 'Upload Label', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:uploadLabel' AND is_deleted = 0), 'zh-CN', '上传箱唛', 0, NOW(), 0, NOW(), 0);

-- 提交供应链按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:submitSupplierChain' AND is_deleted = 0), 'en-US', 'Submit Supplier Chain', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:submitSupplierChain' AND is_deleted = 0), 'zh-CN', '提交供应链', 0, NOW(), 0, NOW(), 0);

-- 开票按钮多语言
INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:invoice' AND is_deleted = 0), 'en-US', 'Invoice', 0, NOW(), 0, NOW(), 0);

INSERT INTO sys_menu_language (menu_id, `language`, name, create_by, create_time, update_by, update_time, is_deleted)
VALUES ((SELECT id FROM sys_menu_info WHERE perms = 'oms:vcOrder:invoice' AND is_deleted = 0), 'zh-CN', '开票', 0, NOW(), 0, NOW(), 0);

