package com.renpho.erp.oms.application.channelmanagement.shopify.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.renpho.erp.oms.infrastructure.common.serialization.BigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SpfOrderShippingLineVO {

	/**
	 * 发货id
	 */
	private Long shippingLineId;

	/**
	 * 标题
	 */
	private String title;

	/**
	 * 币种
	 */
	private String currency;
	/**
	 * 价格
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal price;

	/**
	 * 折扣
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal discount;

	/**
	 * 税费
	 */
	@JsonSerialize(using = BigDecimalSerializer.class)
	private BigDecimal tax;

}
