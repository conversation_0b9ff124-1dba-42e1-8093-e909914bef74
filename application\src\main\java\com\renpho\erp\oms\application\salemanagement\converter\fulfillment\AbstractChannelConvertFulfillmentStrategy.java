package com.renpho.erp.oms.application.salemanagement.converter.fulfillment;

import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.ChannelFulfillment;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.domain.salemanagement.convert.fulfillment.ChannelFulfillmentConvertServiceFactory;
import com.renpho.erp.oms.domain.salemanagement.convert.fulfillment.ChannelFulfillmentConvertServiceStrategy;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

import cn.hutool.extra.spring.SpringUtil;
import lombok.RequiredArgsConstructor;

/**
 * 渠道订单转销售订单策略抽象类.
 * <AUTHOR>
 * @date 2025/1/5 16:43
 */
@RequiredArgsConstructor
public abstract class AbstractChannelConvertFulfillmentStrategy implements ChannelConvertFulfillmentStrategy, InitializingBean {

	protected final SaleOrderRepository saleOrderRepository;
	protected final StoreClient storeClient;

	@Override
	public List<StoreDTO> getStores(SaleChannelType channelType, Set<Integer> storeIds) {
		Assert.notNull(channelType, "获取店铺列表，渠道类型不能为空");
		return storeClient.getByChannelCode(channelType.getValue(), storeIds);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void convert(ChannelFulfillment channelFulfillment) {
		ChannelFulfillmentConvertServiceStrategy converter = ChannelFulfillmentConvertServiceFactory.get(this.getChannelType(),
				this.getFulfillmentType());
		// 转换销售订单履约信息
		converter.convert(channelFulfillment);
	}

	@Override
	public void afterPropertiesSet() {
		// 使用代理对象，不用this，避免事务失效等问题
		ChannelFulfillmentConverterFactory.register(getChannelType(), getFulfillmentType(), SpringUtil.getBean(this.getClass()));
	}

}
