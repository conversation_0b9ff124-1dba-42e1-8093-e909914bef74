package com.renpho.erp.oms.domain.vc.order.valueobject;

import com.renpho.erp.oms.domain.vc.order.enums.RejectReason;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class SubmitAcknowledgmentItem {
    /**
     * 订单行id
     */
    private Long id;
    /**
     * 接单数
     */
    private Integer acceptedQuantity;
    /**
     * 拒单数
     */
    private Integer rejectedQuantity;
    /**
     * 拒绝原因
     */
    private RejectReason rejectSon;
}
