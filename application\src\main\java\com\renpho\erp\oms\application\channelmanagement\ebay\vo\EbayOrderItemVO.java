package com.renpho.erp.oms.application.channelmanagement.ebay.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @desc: 订单行出参
 * @time: 2025-04-14 09:51:39
 * @author: <PERSON><PERSON>
 */
@Data
public class EbayOrderItemVO {

	/**
	 * 订单项ID
	 */
	private String lineItemId;

	/**
	 * Item ID
	 */
	private String legacyItemId;

	/**
	 * MSKU
	 */
	private String sku;

	/**
	 * 状态 - 第一行 订单项履约状态
	 */
	private String lineItemFulfillmentStatus;

	/**
	 * 状态 - 第二行 fromBestOffer，buyerProtection ，soldViaAdCampaign 等标签 json格式
	 */
	private ItemProperties properties;

	/**
	 * 数量
	 */
	private Integer quantity;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 商品收入 商品价格金额
	 */
	private BigDecimal lineItemCostValue;

	/**
	 * 折扣 金额
	 */
	private BigDecimal discountAmount;

	/**
	 * 运费收入 金额
	 */
	private BigDecimal shippingPrice;

	/**
	 * eBay代收税 金额
	 */
	private BigDecimal collectAndRemitTax;
	/**
	 * 退款金额 金额
	 */
	private BigDecimal refundAmount;

	/**
	 * 发货时间
	 */
	private LocalDateTime shipmentTime;

	@Data
	public static class ItemProperties {
		private Boolean fromBestOffer;
		private Boolean buyerProtection;
		private Boolean soldViaAdCampaign;
	}
}
