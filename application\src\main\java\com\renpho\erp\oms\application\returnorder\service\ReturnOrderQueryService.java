package com.renpho.erp.oms.application.returnorder.service;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.core.trans.anno.TransMethodResult;
import com.renpho.erp.data.trans.kit.LocalDateTimeTransKits;
import com.renpho.erp.ftm.client.response.FileDetailResponse;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.returnorder.converter.OrderInfoVOConvertor;
import com.renpho.erp.oms.application.returnorder.converter.ReturnOrderRemarkVOConvertor;
import com.renpho.erp.oms.application.returnorder.converter.ReturnOrderVoConvertor;
import com.renpho.erp.oms.application.returnorder.dto.ReturnOrderExportExcel;
import com.renpho.erp.oms.application.returnorder.vo.OrderInfoVO;
import com.renpho.erp.oms.application.returnorder.vo.OrderItemInfoVO;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderItemVO;
import com.renpho.erp.oms.application.returnorder.vo.ReturnOrderPageVO;
import com.renpho.erp.oms.domain.returnorder.model.ReturnOrderExcel;
import com.renpho.erp.oms.domain.returnorder.query.OrderStoreQuery;
import com.renpho.erp.oms.domain.returnorder.query.ReturnOrderIdQuery;
import com.renpho.erp.oms.domain.returnorder.query.ReturnOrderPageQuery;
import com.renpho.erp.oms.domain.returnorder.repository.ReturnOrderItemRepository;
import com.renpho.erp.oms.domain.returnorder.repository.ReturnOrderRemarkRepository;
import com.renpho.erp.oms.domain.returnorder.repository.ReturnOrderRepository;
import com.renpho.erp.oms.domain.salemanagement.SaleOrder;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.domain.salemanagement.repository.SaleOrderItemRepository;
import com.renpho.erp.oms.infrastructure.common.component.I18nHeaderCellWriteHandler;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.smc.UserClient;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.mapper.ReturnOrderMapper;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.SaleOrderFulfillmentPO;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.service.SaleOrderFulfillmentService;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.i18n.I18nMessageKit;
import jakarta.servlet.http.HttpServletResponse;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @desc: 退货订单查询服务
 * @time: 2025-03-18 09:58:20
 * @author: Alina
 */
@Slf4j
@Component
@AllArgsConstructor
public class ReturnOrderQueryService {

	private final ReturnOrderRepository returnOrderRepository;
	private final ReturnOrderItemRepository returnOrderItemRepository;
	private final SaleOrderRepository saleOrderRepository;
	private final SaleOrderItemRepository saleOrderItemRepository;
	private final ReturnOrderRemarkRepository returnOrderRemarkRepository;

	private final ReturnOrderVoConvertor returnOrderVoConvertor;
	private final OrderInfoVOConvertor orderInfoVOConvertor;
	private final ReturnOrderRemarkVOConvertor returnOrderRemarkVOConvertor;
	private final FileClient fileClient;
	private final WarehouseClient warehouseClient;
	private final UserClient userClient;
	private final I18nHeaderCellWriteHandler i18nHeaderCellWriteHandler;
	private final ReturnOrderMapper returnOrderMapper;
	private final SaleOrderFulfillmentService saleOrderFulfillmentService;

	@TransMethodResult
	public Paging<ReturnOrderPageVO> page(ReturnOrderPageQuery query) {
		// 查询分页数据并转换为 VO 对象
		IPage<ReturnOrderPageVO> page = returnOrderRepository.page(query).convert(returnOrderVoConvertor::toPageVO);
		if (page.getTotal() == 0) {
			return Paging.of();
		}
		// 获取所有 imageId 和 warehouseId
		Set<String> imageIdSet = page.getRecords().stream().map(order -> order.getItem().getImageId()).collect(Collectors.toSet());
		// 获取图片信息
		Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);
		// 填充图片URL和店铺名
		for (ReturnOrderPageVO record : page.getRecords()) {
			Optional.ofNullable(record.getItem())
				.map(ReturnOrderPageVO.Item::getImageId)
				.map(fileMap::get)
				.map(FileDetailResponse::getUrl)
				.ifPresent(imageUrl -> record.getItem().setImageUrl(imageUrl));
		}
		return Paging.of(page.getRecords(), ((int) page.getTotal()), ((int) page.getPages()), ((int) page.getCurrent()));
	}

	/**
	 * 退货订单商品行-列表查询.
	 *
	 * @param query 参数
	 * @return List<ReturnOrderItemVO>
	 */
	public List<ReturnOrderItemVO> listItem(ReturnOrderIdQuery query) {
		Set<String> imageIdSet = new HashSet<>();
		List<ReturnOrderItemVO> returnOrderItemVos = returnOrderItemRepository.listItem(query.getReturnOrderId())
			.stream()
			.map(returnOrderVoConvertor::toReturnOrderItemVO)
			.peek(item -> imageIdSet.add(item.getImageId()))
			.collect(Collectors.toList());
		// 设置图片url信息
		Map<String, FileDetailResponse> fileMap = fileClient.getFileMap(imageIdSet);
		for (ReturnOrderItemVO record : returnOrderItemVos) {
			Optional.ofNullable(fileMap.get(record.getImageId())).map(FileDetailResponse::getUrl).ifPresent(record::setImageUrl);
		}
		return returnOrderItemVos;
	}

	/**
	 * 根据店铺Id，店铺单号获取所有销售订单以及明细
	 *
	 * @param query 参数
	 * @return OrderInfoVO
	 */
	public OrderInfoVO getOrderInfo(OrderStoreQuery query) {
		// 校验店铺单号
		List<SaleOrder> saleOrders = saleOrderRepository.findByChannelOrderNo(query.getStoreId(), query.getChannelOrderNo());
		if (CollectionUtils.isEmpty(saleOrders)) {
			throw new BusinessException(I18nMessageKit.getMessage("RETURN_ORDER_CHANNEL_ORDER_NO_NOT_EXIST"));
		}
		// 获取所有销售订单的明细
		List<Long> saleOrderIds = saleOrders.stream().map(SaleOrder::getId).collect(Collectors.toList());
		List<OrderItemInfoVO> orderItemInfoVOList = saleOrderItemRepository.listItemByOrderIds(saleOrderIds)
			.stream()
			.map(orderInfoVOConvertor::toOrderItemInfoVO)
			.collect(Collectors.toList());
		// 为订单明细设置销售单号
		Map<Long, String> orderNoMap = saleOrders.stream().collect(Collectors.toMap(SaleOrder::getId, SaleOrder::getOrderNo));
		orderItemInfoVOList.forEach(e -> e.setOrderNo(orderNoMap.get(e.getOrderId())));
		// 获取最新的发货履约信息
		SaleOrderFulfillmentPO saleOrderFulfillmentPO = saleOrderFulfillmentService.getByOrderIds(saleOrderIds);
		// 组装返回结果
		OrderInfoVO orderInfoVO = orderInfoVOConvertor.toOrderInfoVO(saleOrders.get(0), orderItemInfoVOList, saleOrderFulfillmentPO);
		return orderInfoVO;
	}

	/**
	 * 导出
	 *
	 * @param query 查询参数
	 * @param response 响应
	 */
	public void export(ReturnOrderPageQuery query, HttpServletResponse response) {
		ExcelUtil.export(
				// 文件名称
				"导出退货订单.xlsx",
				// Excel导出模版头信息
				ReturnOrderExportExcel.class,
				// 查询条件
				query,
				// 响应
				response,
				// 处理Excel 头部中英文转换
				i18nHeaderCellWriteHandler,
				// 分批查询的函数
				(ReturnOrderPageQuery queryTemp) -> SpringUtil.getBean(this.getClass()).getReturnOrderExportExcels(queryTemp));
	}

	/**
	 * 获取退货订单需要导出Excel的数据
	 * @param queryTemp 查询列表入参
	 * @return 退货订单需要导出Excel的数据
	 */
	@TransMethodResult
	public List<ReturnOrderExportExcel> getReturnOrderExportExcels(ReturnOrderPageQuery queryTemp) {
		Page<ReturnOrderExcel> pageParam = new Page<>(queryTemp.getPageIndex(), queryTemp.getPageSize(), false);
		List<ReturnOrderExcel> returnOrderExcelList = returnOrderMapper.excelPage(pageParam, queryTemp).getRecords();
		if (returnOrderExcelList.isEmpty()) {
			return List.of();
		}
		Set<Integer> warehouseIds = returnOrderExcelList.stream().map(ReturnOrderExcel::getWarehouseId).collect(Collectors.toSet());
		Map<Integer, WarehouseVo> warehouseVoMap = warehouseClient.getById(warehouseIds);

		return returnOrderExcelList.stream().map(returnOrder -> {
			ReturnOrderExportExcel excelData = returnOrderVoConvertor.toReturnOrderExportExcel(returnOrder);
			excelData.setCreateTime(Optional.ofNullable(excelData.getCreateTime()).map(LocalDateTimeTransKits::tansByUserZoneId).orElse(null));
			excelData.setReceiveTime(Optional.ofNullable(excelData.getReceiveTime()).map(LocalDateTimeTransKits::tansByUserZoneId).orElse(null));
			excelData.setWarehouseName(
					Optional.ofNullable(warehouseVoMap.get(returnOrder.getWarehouseId())).map(WarehouseVo::getName).orElse(""));
			return excelData;
		}).collect(Collectors.toList());
	}

}
