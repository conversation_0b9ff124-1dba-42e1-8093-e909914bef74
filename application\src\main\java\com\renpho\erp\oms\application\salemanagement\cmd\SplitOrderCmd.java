package com.renpho.erp.oms.application.salemanagement.cmd;

import java.util.List;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 拆单cmd
 * <AUTHOR>
 */
@Data
public class SplitOrderCmd {

	/**
	 * 原订单id
	 */
	@NotNull(message = "原订单id不能为空")
	private Long orderId;

	/**
	 * 拆分出的订单
	 */
	@NotEmpty(message = "拆分出的订单不能为空")
	@Valid
	private List<SplitOrder> splitOrders;

	@Data
	public static class SplitOrder {

		/**
		 * 订单行
		 */
		@NotEmpty(message = "{SALE_ORDER_SPLIT_ORDER_LIME_EMPTY_CHECK}")
		@Valid
		private List<OrderItem> orderItems;

		@Data
		public static class OrderItem {

			/**
			 * 销售订单行主键
			 */
			@NotNull(message = "订单行主键不能为空")
			private Long orderItemId;

			/**
			 * 拆分出的数量
			 */
			@NotNull(message = "拆分出的数量不能为空")
			@Min(value = 1L, message = "{SALE_ORDER_SPLIT_ORDER_QUANTITY_MIN}")
			private Integer quantityShipment;
		}
	}
}
