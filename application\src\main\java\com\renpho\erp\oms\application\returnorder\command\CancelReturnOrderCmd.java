package com.renpho.erp.oms.application.returnorder.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @desc: 退货订单取消入参
 * @time: 2025-03-21 10:11:57
 * @author: <PERSON><PERSON>
 */
@Data
public class CancelReturnOrderCmd {

	/**
	 * 退货订单主键
	 */
	@NotNull(message = "RETURN_ORDER_ID_NOT_NULL")
	private Long returnOrderId;

	/**
	 * 取消原因
	 */
	@NotBlank(message = "SALE_ORDER_CANCEL_REASON_EMPTY_CHECK")
	private String cancelReason;
}
