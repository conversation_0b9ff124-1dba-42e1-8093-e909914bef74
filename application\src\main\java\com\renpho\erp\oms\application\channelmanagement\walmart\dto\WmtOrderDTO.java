package com.renpho.erp.oms.application.channelmanagement.walmart.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WmtOrderDTO {
	/**
	 * 同步时间
	 */
	private String syncTime;
	private Integer storeId;
	private String shopId;
	private String purchaseOrderId;
	private String customerOrderId;
	private String customerEmailId;
	private String orderType;
	private String originalCustomerOrderID;
	private Long orderDate;
	private String buyerId;
	private String mart;
	private Boolean isGuest;
	private ShippingInfo shippingInfo;
	private OrderLines orderLines;
	private String[] paymentTypes;
	private OrderSummary orderSummary;
	private List<PickupPerson> pickupPersons;
	private ShipNode shipNode;

	@Data
	public static class ShippingInfo {

		private String phone;
		private Long estimatedDeliveryDate;
		private Long estimatedShipDate;
		private String methodCode;
		private PostalAddress postalAddress;

	}

	@Data
	public static class PostalAddress {
		private String name;
		private String address1;
		private String address2;
		private String city;
		private String state;
		private String postalCode;
		private String country;
		private String addressType;
	}

	@Data
	public static class OrderLines {

		private List<OrderLine> orderLine;
	}

	@Data
	public static class OrderLine {
		private String lineNumber;
		private Item item;
		private Charges charges;
		private OrderLineQuantity orderLineQuantity;
		private Long statusDate;
		private OrderLineStatuses orderLineStatuses;
		private String returnOrderId;
		private Refund refund;
		private String originalCarrierMethod;
		private String referenceLineId;
		private Fulfillment fulfillment;
		private List<String> serialNumbers;
		private String intentToCancel;
		private String configId;
		private String sellerOrderId;
	}

	@Data
	public static class Fulfillment {
		private String fulfillmentOption;
		private String shipMethod;
		private String storeId;
		private Long pickUpDateTime;
		private String pickUpBy;
		private String shippingProgramType;
	}

	@Data
	public static class Refund {
		private String refundId;
		private String refundComments;
		private RefundCharges refundCharges;
	}

	@Data
	public static class RefundCharges {
		private List<RefundCharge> refundCharge;
	}

	@Data
	public static class RefundCharge {
		private String refundReason;
		private Charge charge;
	}

	@Data
	public static class Item {
		private String productName;
		private String sku;
		private String condition;
		private String imageUrl;
		private Weight weight;
	}

	@Data
	public static class Weight {
		private String value;
		private String unit;
	}

	@Data
	public static class Charges {

		private List<Charge> charge;
	}

	@Data
	public static class Charge {
		private String chargeType;
		private String chargeName;
		private Amount chargeAmount;
		private Tax tax;
	}

	@Data
	public static class Amount {
		private String currency;
		private BigDecimal amount;
	}

	@Data
	public static class Tax {
		private String taxName;
		private Amount taxAmount;
	}

	@Data
	public static class OrderLineQuantity {
		private String unitOfMeasurement;
		private String amount;
	}

	@Data
	public static class OrderLineStatuses {
		private List<OrderLineStatus> orderLineStatus;
	}

	@Data
	public static class OrderLineStatus {
		private String status;
		private StatusQuantity statusQuantity;
		private String cancellationReason;
		private TrackingInfo trackingInfo;
		private ReturnCenterAddress returnCenterAddress;
	}

	@Data
	public static class StatusQuantity {
		private String unitOfMeasurement;
		private String amount;
	}

	@Data
	public static class TrackingInfo {
		private Long shipDateTime;
		private CarrierName carrierName;
		private String methodCode;
		private String trackingNumber;
		private String trackingURL;
	}

	@Data
	public static class CarrierName {
		private String otherCarrier;
		private String carrier;
	}

	@Data
	public static class ReturnCenterAddress {
		private String name;
		private String address1;
		private String address2;
		private String city;
		private String state;
		private String postalCode;
		private String country;
		private String dayPhone;
		private String emailId;
	}

	@Data
	public static class OrderSummary {

		private Money totalAmount;

		private List<Money> orderSubTotals;
	}

	@Data
	public static class Money {

		private String currencyUnit;

		private BigDecimal currencyAmount;
	}

	@Data
	public static class PickupPerson {

		private PersonName name;

		private PersonPhone phone;
	}

	@Data
	public static class PersonPhone {
		private String id;
		private String areaCode;
		private String extension;
		private String completeNumber;
		private String type;
		private String subscriberNumber;
		private String countryCode;
		private PhoneValidity phoneValidity;
	}

	@Data
	public static class PhoneValidity {
		private String validationType;
		private String validationStatus;
		private String validatedDate;
		private String validatedBy;
	}

	@Data
	public static class PersonName {
		private String completeName;
		private String firstName;
		private String middleName;
		private String lastName;
		private String generalSuffix;
		private String maturitySuffix;
		private String titleOfRespect;
		private boolean empty;
	}

	@Data
	public static class ShipNode {
		private String type;
		private String name;
		private String id;
	}
}
