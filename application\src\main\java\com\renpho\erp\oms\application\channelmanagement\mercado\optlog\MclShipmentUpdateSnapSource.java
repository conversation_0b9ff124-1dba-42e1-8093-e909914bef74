package com.renpho.erp.oms.application.channelmanagement.mercado.optlog;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclShipmentLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.service.MclShipmentService;
import com.renpho.erp.oplog.log.SnapshotDatatSource;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * *
 */
@Component
@AllArgsConstructor
public class MclShipmentUpdateSnapSource implements SnapshotDatatSource {

	private final MclShipmentService mclShipmentService;

	@Override
	public JSONObject getOldData(Object[] args) {
		// 将对象转换为 Json 字符串
		Long id = ((JSONObject) JSONObject.toJSON(args[0])).getObject("id", Long.class);

		return JSON.parseObject(JSON.toJSONString(mclShipmentService.getLogById(id)));
	}

	@Override
	public JSONObject getNewData(Object[] args, JSONObject result) {
		MclShipmentLog mclShipmentLog = mclShipmentService.getLogById(result.getObject("data", Long.class));
		return JSON.parseObject(JSON.toJSONString(mclShipmentLog));
	}

	@Override
	public String getBsId(Object[] args, JSONObject result) {
		return String.valueOf(result.getObject("data", Long.class));
	}

}
