package com.renpho.erp.oms.infrastructure.persistence.receiptOrder.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.tangzc.mpe.annotation.InsertFillTime;
import com.tangzc.mpe.annotation.UpdateFillTime;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/20 10:10
 */
@TableName("oms_b2b_receipt_order")
@Data
public class B2bReceiptOrderPO {

    /**
     * id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 自定义收款单号
     */
    private String receiptOrderNo;

    /**
     * 银行参考号
     */
    private String bankReferNo;

    /**
     * 收款所属的订单号(B2B订单号)
     */
    private String b2bOrderNo;

    /**
     * b2b订单id
     */
    private Long b2bOrderId;


    /**
     * 状态 1-待确认,2-已确认,3-已取消
     */
    private Integer status;


    /**
     * 银行账号
     */
    private Long fmsFundAccountCurrencyId;


    /**
     * 客户付款金额
     */
    private BigDecimal customerPaidAmount;

    /**
     * 客户付款时间
     */
    private LocalDateTime customerPaidTime;

    /**
     * 客戶付款信息文件url
     */
    private String customerPaidInfoFileUrl;

    /**
     * 客戶付款信息文件名
     */
    private String customerPaidInfoFileName;

    /**
     * 收款信息文件url
     */
    private String receiptInfoFileUrl;

    /**
     * 收款信息文件名
     */
    private String receiptInfoFileName;

    /**
     * 收款金额
     */
    private BigDecimal receiptAmount;

    /**
     * 费用金额
     */
    private BigDecimal feesAmount;

    /**
     * 收款时间
     */
    private LocalDateTime receiptTime;


    /**
     * 创建人
     */
    private Integer createBy;

    private String fmsFundAccountCurrencyCnname;

    private String fmsFundAccountCurrencyEnname;

    /**
     * 创建时间
     */
    @InsertFillTime
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private Integer updateBy;

    /**
     * 更新时间
     */
    @UpdateFillTime(override = false)
    private LocalDateTime updateTime;

    /**
     * 是否删除，0=未删除，1=删除
     */
    @TableField(value = "is_deleted")
    private Integer isDeleted;

}
