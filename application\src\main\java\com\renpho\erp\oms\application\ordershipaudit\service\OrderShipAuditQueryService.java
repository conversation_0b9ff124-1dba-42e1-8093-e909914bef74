package com.renpho.erp.oms.application.ordershipaudit.service;

import com.renpho.erp.ims.client.feign.warehouse.query.WarehouseQuery;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.converter.ShipAuditConvertor;
import com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice.ShippingServiceFactory;
import com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice.ShippingServiceStrategy;
import com.renpho.erp.oms.application.ordershipaudit.vo.AllowModifyShipAuditVO;
import com.renpho.erp.oms.application.ordershipaudit.vo.SelfWarehouseVO;
import com.renpho.erp.oms.application.ordershipaudit.vo.ShippingServicesVO;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentStatus;
import com.renpho.erp.oms.domain.ordershipaudit.query.AllowModifyShipAuditQuery;
import com.renpho.erp.oms.domain.ordershipaudit.query.ShippingServicesQuery;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.LogisticsShipmentPO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceConfigService;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.FulfillmentServiceWarehouseService;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.LogisticsShipmentPOService;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.service.OrderShipAuditPOService;
import com.renpho.erp.oms.infrastructure.persistence.saleorder.po.OrderShipAuditPO;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @desc: 订单发货审核-操作 查询服务
 * @time: 2025-04-01 15:30:53
 * @author: Alina
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderShipAuditQueryService {

	private final FulfillmentServiceWarehouseService fulfillmentWarehouseService;
	private final FulfillmentServiceConfigService fulfillmentServiceConfigService;
	private final LogisticsShipmentPOService logisticsShipmentPOService;
	private final OrderShipAuditPOService orderShipAuditPOService;

	private final WarehouseClient warehouseClient;
	private final ShipAuditConvertor shipAuditConvertor;

	/**
	 * 根据发货服务类型获取自建发货仓库
	 *
	 * @param fulfillmentServiceType 参数 发货服务类型
	 * @return List<SelfWarehouseVO>
	 */
	public List<SelfWarehouseVO> getSelfWarehouseInfo(Integer fulfillmentServiceType) {
		// 获取对应发货类型的仓库id集合
		Set<Integer> warehouseIds;
		if (FulfillmentServiceType.TEMU_ONLINE_LABEL.getValue().equals(fulfillmentServiceType)) {
			warehouseIds = fulfillmentWarehouseService.getByFulfillmentServiceType(fulfillmentServiceType);
		} else {
			warehouseIds = fulfillmentServiceConfigService.getByFulfillmentServiceType(fulfillmentServiceType);
		}
		if (CollectionUtils.isEmpty(warehouseIds)) {
			throw new BusinessException(I18nMessageKit.getMessage("WAREHOUSE_SETTING_NOT_FOUND"));
		}
		WarehouseQuery warehouseQuery = new WarehouseQuery();
		warehouseQuery.setIdList(warehouseIds.stream().toList());
		warehouseQuery.setStatus(1);
		warehouseQuery.setPageSize(100);
		warehouseQuery.setPageIndex(1);
		List<WarehouseVo> warehouseVoList = warehouseClient.list(warehouseQuery);
		return Optional.of(warehouseVoList)
			.map(list -> list.stream().map(shipAuditConvertor::toSelfWarehouseVO).collect(Collectors.toList()))
			.orElse(Collections.emptyList());
	}

	/**
	 * 根据所选仓库查询发货方式报价表
	 * @param query 参数
	 * @return List<ShippingServicesVO>
	 */
	public List<ShippingServicesVO> getShippingServices(ShippingServicesQuery query) {
		ShippingServiceStrategy shippingServiceStrategy = ShippingServiceFactory.get(FulfillmentServiceType.enumOf(query.getFulfillmentServiceType()));
		return shippingServiceStrategy.getShippingServices(query);
	}

	/**
	 * TEMU线上面单是否允许修改发货审核信息接口
	 *
	 * @param query 参数
	 * @return boolean true-允许修改 false-不允许修改
	 */
	public AllowModifyShipAuditVO allowModifyShipAudit(AllowModifyShipAuditQuery query) {
		AllowModifyShipAuditVO allowModifyShipAuditVo = new AllowModifyShipAuditVO();
		LogisticsShipmentPO logisticsShipmentPo = logisticsShipmentPOService.getLastLogisticsShipmentByOrderId(query.getOrderId());
		if (logisticsShipmentPo == null || StringUtils.isEmpty(logisticsShipmentPo.getLogisticsOrderNo())) {
			allowModifyShipAuditVo.setAllowModify(true);
		} else if (LogisticsShipmentStatus.FAILED.getValue().equals(logisticsShipmentPo.getStatus())) {
			allowModifyShipAuditVo.setAllowModify(true);
		} else {
			allowModifyShipAuditVo.setAllowModify(false);
			OrderShipAuditPO orderShipAuditPo = orderShipAuditPOService.getById(logisticsShipmentPo.getAuditId());
			allowModifyShipAuditVo.setWarehouseId(orderShipAuditPo.getWarehouseId());
			allowModifyShipAuditVo.setShipCompanyId(orderShipAuditPo.getShipCompanyId());
			allowModifyShipAuditVo.setShipChannelId(orderShipAuditPo.getShipChannelId());
		}
		return allowModifyShipAuditVo;
	}
}
