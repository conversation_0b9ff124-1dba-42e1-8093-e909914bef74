package com.renpho.erp.oms.application.vc;

import com.renpho.erp.oms.application.vc.order.service.VcOrderQueryService;
import com.renpho.erp.oms.application.vc.order.vo.VcOrderDetailVO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <AUTHOR>
 * @description
 * @date 2025/7/8 15:32
 */
@SpringBootTest
@Slf4j
public class VcOrderQueryServiceTest {

    @Resource
    private VcOrderQueryService vcOrderQueryService;

    @Test
    public void getVcOrderDetail() {
        VcOrderDetailVO vcOrderDetail = vcOrderQueryService.getVcOrderDetail(1L);
        log.info("" + vcOrderDetail);
    }
}
