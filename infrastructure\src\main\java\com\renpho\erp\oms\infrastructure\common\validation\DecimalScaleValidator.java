package com.renpho.erp.oms.infrastructure.common.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.math.BigDecimal;
/**
 * <AUTHOR>
 * @description 小数位校验
 * @date 2025/7/1 11:22
 */
public class DecimalScaleValidator implements ConstraintValidator<DecimalScale, BigDecimal> {

    private int scale;

    @Override
    public void initialize(DecimalScale constraintAnnotation) {
        this.scale = constraintAnnotation.scale();
    }

    /**
     * 校验小数位不能超过scale
     * @param value
     * @param constraintValidatorContext
     * @return
     */
    @Override
    public boolean isValid(BigDecimal value, ConstraintValidatorContext constraintValidatorContext) {
        if (value == null) {
            return true;
        }
        return value.scale() <= scale;
    }


}
