package com.renpho.erp.oms.application.ordershipaudit.channel.eccang.kingspark;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractThirdPartChannelOrderShipExcelService;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Component
public class KingSparkChannelOrderShipExcelService extends AbstractThirdPartChannelOrderShipExcelService {
	@Override
	public FulfillmentServiceType getFulfillmentServiceType() {
		return FulfillmentServiceType.KING_SPARK_WAREHOUSE;
	}
}
