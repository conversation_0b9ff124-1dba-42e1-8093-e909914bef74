package com.renpho.erp.oms.application.ordershipaudit.channel.jd;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.ims.client.feign.warehouse.vo.WarehouseVo;
import com.renpho.erp.oms.application.ordershipaudit.channel.AbstractThirdPartChannelOrderShipExcelService;
import com.renpho.erp.oms.application.ordershipaudit.channel.ThirdPartOrderShipExcelDto;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodeLanguagePO;
import com.renpho.erp.oms.infrastructure.persistence.ordershipaudit.po.FulfillmentServiceCodePO;
import com.renpho.karma.i18n.I18nMessageKit;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * desc
 *
 * <AUTHOR>
 * @since 2025/3/11
 */
@Component
public class JdChannelOrderShipExcelServiceAbstract extends AbstractThirdPartChannelOrderShipExcelService {
    @Override
    public FulfillmentServiceType getFulfillmentServiceType() {
        return FulfillmentServiceType.JD_WAREHOUSE;
    }

    @Override
    protected boolean checkCarrierServiceCode(List<ThirdPartOrderShipExcelDto> thirdPartOrderShipExcelDtoList, Map<String, WarehouseVo> warehouseMap) {
        boolean failFlag = false;
        Set<String> carrierServiceCodeSet = thirdPartOrderShipExcelDtoList.stream().map(ThirdPartOrderShipExcelDto::getPlatformWarehouseServiceCode).collect(Collectors.toSet());
        Map<String, FulfillmentServiceCodePO> codeMap = fulfillmentServiceCodeMapper.selectList(Wrappers.<FulfillmentServiceCodePO>lambdaQuery()
                        .in(FulfillmentServiceCodePO::getCarrierServiceCode, carrierServiceCodeSet)
                        .eq(FulfillmentServiceCodePO::getFulfillmentServiceType, getFulfillmentServiceType().getValue())
                        .eq(FulfillmentServiceCodePO::getIsDeleted, false))
                .stream()
                .collect(Collectors.toMap(FulfillmentServiceCodePO::getCarrierServiceCode, Function.identity()));
        for (ThirdPartOrderShipExcelDto thirdPartOrderShipExcelDto : thirdPartOrderShipExcelDtoList) {
            FulfillmentServiceCodePO fulfillmentServiceCode = codeMap.get(thirdPartOrderShipExcelDto.getPlatformWarehouseServiceCode());
            if (fulfillmentServiceCode == null) {
                failFlag = true;
                thirdPartOrderShipExcelDto.addErrorInfo(I18nMessageKit.getMessage("ORDER_SHIP_AUDIT_CARRIER_SERVICE_CODE_INVALIDATE"));
                continue;
            }
            thirdPartOrderShipExcelDto.setJdOrderType(fulfillmentServiceCode.getJdOrderType());
        }
        return failFlag;
    }

    @Override
    protected void writeCarrierServiceCodeInfo(ExcelWriter excelWriter, String language, List<WarehouseVo> thirdPartWarehouseInfoList) {
        List<FulfillmentServiceCodePO> fulfillmentServiceCodeList = fulfillmentServiceCodeMapper.selectList(Wrappers.<FulfillmentServiceCodePO>lambdaQuery()
                .eq(FulfillmentServiceCodePO::getFulfillmentServiceType, getFulfillmentServiceType().getValue())
                .eq(FulfillmentServiceCodePO::getIsDeleted, false));

        Set<Long> codeIdSet = fulfillmentServiceCodeList.stream().map(FulfillmentServiceCodePO::getId).collect(Collectors.toSet());
        if (codeIdSet.isEmpty()) {
            excelWriter.write(Collections.emptyList(), EasyExcelFactory.writerSheet(2)
                    .head(ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode.class)
                    .excludeColumnFieldNames(List.of("platformWarehouseName"))
                    .build());
        }

        Map<Long, String> codeNameMap = fulfillmentServiceCodeLanguageMapper.selectList(Wrappers.<FulfillmentServiceCodeLanguagePO>lambdaQuery()
                        .in(FulfillmentServiceCodeLanguagePO::getCodeId, codeIdSet)
                        .eq(FulfillmentServiceCodeLanguagePO::getLanguage, language)
                        .eq(FulfillmentServiceCodeLanguagePO::getIsDeleted, false))
                .stream()
                .collect(Collectors.toMap(FulfillmentServiceCodeLanguagePO::getCodeId, FulfillmentServiceCodeLanguagePO::getName, (k1, k2) -> k2));

        List<ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode> platformWarehouseServiceCodeList = new ArrayList<>();
        for (FulfillmentServiceCodePO fulfillmentServiceCode : fulfillmentServiceCodeList) {

            ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode platformWarehouseServiceCode = new ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode();
            platformWarehouseServiceCode.setPlatformWarehouseServiceCode(fulfillmentServiceCode.getCarrierServiceCode());
            platformWarehouseServiceCode.setPlatformWarehouseServiceDesc(codeNameMap.getOrDefault(fulfillmentServiceCode.getId(), ""));

            platformWarehouseServiceCodeList.add(platformWarehouseServiceCode);
        }

        excelWriter.write(platformWarehouseServiceCodeList, EasyExcelFactory.writerSheet(2)
                .head(ThirdPartOrderShipExcelDto.PlatformWarehouseServiceCode.class)
                .excludeColumnFieldNames(List.of("platformWarehouseName"))
                .build());
    }
}
