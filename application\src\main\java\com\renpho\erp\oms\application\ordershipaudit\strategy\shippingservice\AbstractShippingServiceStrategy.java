package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import cn.hutool.extra.spring.SpringUtil;
import com.renpho.erp.oms.application.ordershipaudit.converter.ShipAuditConvertor;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.ims.WarehouseClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Set;

/**
 * @desc: 查询发货方式报价策略抽象类
 * @time: 2025-04-22 18:02:50
 * @author: Alina
 */

public abstract class AbstractShippingServiceStrategy implements ShippingServiceStrategy, InitializingBean {
	@Autowired
	protected StoreClient storeClient;
	@Autowired
	protected ShipAuditConvertor shipAuditConvertor;
	@Autowired
	protected WarehouseClient warehouseClient;
	@Autowired
	protected SaleOrderRepository saleOrderRepository;
	@Override
	public void afterPropertiesSet() {
		// 使用代理对象，不用this，避免事务失效等问题
		Set<FulfillmentServiceType> serviceTypes = this.getFulfillmentServiceType();
		serviceTypes.forEach(serviceType -> ShippingServiceFactory.register(serviceType, SpringUtil.getBean(this.getClass())));
	}

}
