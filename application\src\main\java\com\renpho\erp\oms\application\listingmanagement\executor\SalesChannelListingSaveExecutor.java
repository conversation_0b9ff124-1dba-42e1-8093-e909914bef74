package com.renpho.erp.oms.application.listingmanagement.executor;

import com.renpho.erp.oms.application.listingmanagement.service.SalesChannelListingService;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingRepository;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourceResolveStatusEnum;
import com.renpho.karma.json.JSONKit;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.time.LocalDateTime;
import java.util.Objects;
import java.util.Set;

/**
 * Listing保存执行器
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class SalesChannelListingSaveExecutor {

	private final SalesChannelListingRepository salesChannelListingRepository;
	private final SalesChannelListingService salesChannelListingService;
	private final SalesChannelListingSourceRepository salesChannelListingSourceRepository;

	/**
	 * 对比关键字段是否变动，保存修改处理（含日志）
	 * @param listing Listing
	 */
	@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
	public void saveSalesChannelListing(SalesChannelListing listing) {
		try {
			// 对比关键字段是否变动，保存修改处理（含日志）
			this.saveListing(listing);
			// 更新解析状态为成功
			this.updateResolveStatusSuccess(listing.getListingSourceId());
		}
		catch (Exception e) {
			log.error("保存Listing数据:{},异常：", JSONKit.toJSONString(listing), e);
			// 手动回滚saveSalesChannelListing方法的事务
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			// 更新解析状态为失败（独立事务，不受当前方法事务回滚影响）
			salesChannelListingSourceRepository.updateResolveStatusFail(listing.getListingSourceId(), e.getMessage());
		}
	}

	/**
	 * 更新解析状态为成功
	 * @param listingSourceId 原始Listing主键id
	 */
	private void updateResolveStatusSuccess(Long listingSourceId) {
		SalesChannelListingSource updateSource = new SalesChannelListingSource();
		updateSource.setId(listingSourceId);
		updateSource.setResolveStatus(ListingSourceResolveStatusEnum.ALREADY_RESOLVE.getStatus());
		updateSource.setResoleTime(LocalDateTime.now());
		salesChannelListingSourceRepository.updateById(updateSource);
	}

	/**
	 * 对比关键字段是否变动，保存修改处理（含日志）
	 * @param listing Listing
	 */
	private void saveListing(SalesChannelListing listing) {
		// 根据唯一值查询最新记录
		SalesChannelListing db = salesChannelListingRepository.getLastListingByUniqueCode(listing.getBsUniqueCode());
		// 不存在直接插入
		if (Objects.isNull(db)) {
			salesChannelListingService.saveNewListing(listing);
			return;
		}
		// 存在时，检查关键字段是否有变更
		if (this.checkKeyFieldsChanged(db, listing)) {
			// 关键字段有更新，记录日志
			salesChannelListingService.saveNewListingFromOld(listing, db);
		}
		else {
			// 更新时间，不记日志
			db.setUpdateTime(LocalDateTime.now());
			salesChannelListingRepository.updateById(db);
		}
	}

	/**
	 * 检查关键字段是否有变更
	 * @param oldRecord 旧数据
	 * @param newRecord 新数据
	 * @return boolean
	 */
	private boolean checkKeyFieldsChanged(SalesChannelListing oldRecord, SalesChannelListing newRecord) {
		if (Objects.isNull(oldRecord)) {
			return true;
		}
		// 新旧价格是否相等
		boolean priceEq = ObjectUtils.allNull(oldRecord.getPrice(), newRecord.getPrice())
				|| (ObjectUtils.allNotNull(oldRecord.getPrice(), newRecord.getPrice())
						&& oldRecord.getPrice().compareTo(newRecord.getPrice()) == 0);
		boolean flag = !StringUtils.equals(oldRecord.getPsku(), newRecord.getPsku())
				|| !StringUtils.equals(oldRecord.getTitle(), newRecord.getTitle())
				|| !StringUtils.equals(oldRecord.getStatus(), newRecord.getStatus())
				|| !priceEq
				|| !StringUtils.equals(oldRecord.getCurrency(), newRecord.getCurrency());
        // temu需要对比销售sku
		if (Set.of(SaleChannelType.TEMU.getValue(), SaleChannelType.EBAY.getValue()).contains(newRecord.getSalesChannelCode())) {
            flag = flag || !StringUtils.equals(oldRecord.getMsku(), newRecord.getMsku());
        }
		return flag;
	}
}
