package com.renpho.erp.oms.application.channelmanagement.ebay.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @desc: Eby渠道单
 * @time: 2025-04-11 14:21:21
 * @author: <PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
public class EbyOrderVO {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 店铺id
	 */
	private Integer storeId;

	/**
	 * 订单ID
	 */
	private String ebyOrderId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 取消状态：NONE_REQUESTED未请求取消、CANCELED已取消、IN_PROGRESS有取消请求
	 */
	private String cancelStatus;

	/**
	 * 付款状态
	 */
	private String orderPaymentStatus;

	/**
	 * 发货状态
	 */
	private String orderFulfillmentStatus;

	/**
	 * 创建时间
	 */
	private LocalDateTime creationDate;

	/**
	 * 更新时间
	 */
	private LocalDateTime lastModifiedDate;

	/**
	 * 商品收入 商品行 lineItemCost 合计
	 */
	private BigDecimal itemPrice;

	/**
	 * 折扣 商品行 appliedPromotions里的discountAmount合计
	 */
	private BigDecimal discount;

	/**
	 * 运费收入 商品行 deliveryCost.shippingCost 的合计
	 */
	private BigDecimal shippingPrice;

	/**
	 * 退款金额 商品行 refunds里面的amount合计
	 */
	private BigDecimal refundAmount;

	/**
	 * 税代收代缴
	 */
	private Boolean ebayCollectAndRemitTax;

	/**
	 * eBay代收税 商品行 ebayCollectAndRemitTaxes的amount合计
	 */
	private BigDecimal collectAndRemitTax;

	/**
	 * 总平台费用
	 */
	private String totalMarketplaceFee;

	/**
	 * 币种
	 */
	private String currency;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 州
	 */
	private String state;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 配送类型
	 */
	private String fulfillmentInstructionsType;

	/**
	 * 最早签收时间
	 */
	private LocalDateTime minEstimatedDeliveryDate;

	/**
	 * 最晚签收时间
	 */
	private LocalDateTime maxEstimatedDeliveryDate;

	/**
	 * 承运商
	 */
	private String shippingCarrierCode;

	/**
	 * 承运商服务
	 */
	private String shippingServiceCode;

	/**
	 * 买家结账备注
	 */
	private String buyerCheckoutNotes;

}
