package com.renpho.erp.oms.infrastructure.form;

import com.renpho.erp.bpm.api.annotation.Form;
import com.renpho.erp.bpm.api.annotation.form.FieldDate;
import com.renpho.erp.bpm.api.annotation.form.FieldInput;
import com.renpho.erp.bpm.api.annotation.form.FieldNumber;
import com.renpho.erp.bpm.api.annotation.form.FieldSelect;
import com.renpho.erp.bpm.api.dto.KeyAndValueDto;
import com.renpho.erp.oms.infrastructure.common.constant.MQConstant;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @desc: B2B订单发货审核表单
 * @time: 2025-06-23 14:10:11
 * @author: Alina
 */

@Data
@Form(key = "b2b-order-shipment", name = "b2b订单发货审核表单", callBackTopic = MQConstant.TOPIC.BPM_AUDIT, callBackMessageTag = MQConstant.TAG.B2B_ORDER_SHIPMENT_AUDIT)
public class B2bOrderShipmentAuditForm {


    /**
     * 订单号
     */
    @FieldInput(key = "orderNo", name = "订单号", nameEn = "Order No")
    private String orderNo;

    /**
     * 客户名称
     */
    @FieldSelect(key = "customerCompanyName", name = "客户名称", nameEn = "Customer Name", remoteData = "erp-oms.CustomerController.getCustomerSelectList")
    private List<KeyAndValueDto> customerCompanyName;

    /**
     * 订单金额
     */
    @FieldNumber(key = "totalAmount", name = "订单金额", nameEn = "Total Amount")
    private BigDecimal totalAmount;

    /**
     * 已付款金额
     */
    @FieldNumber(key = "paymentAmount", name = "已付款金额", nameEn = "Payment Amount")
    private BigDecimal paymentAmount;

    /**
     * 保险公司
     */
    @FieldInput(key = "insuranceCompany", name = "保险公司", nameEn = "Insurance Company")
    private String insuranceCompany;

    /**
     * 保险额度
     */
    @FieldNumber(key = "insuredAmount", name = "保险额度", nameEn = "Insured Amount")
    private BigDecimal insuredAmount;

    /**
     * 已用额度
     */
    @FieldNumber(key = "usedCreditAmount", name = "已用额度", nameEn = "Used Credit Amount")
    private BigDecimal usedCreditAmount;

    /**
     * 剩余额度
     */
    @FieldNumber(key = "remainingAmount", name = "剩余额度", nameEn = "Remaining Amount")
    private BigDecimal remainingAmount;


    /**
     * 发货协议
     */
    @FieldSelect(key = "incotermsCode", name = "发货协议", nameEn = "Incoterms", remoteData = "erp-oms.CustomerController.getIncotermsList")
    private List<KeyAndValueDto> incotermsCode;

    /**
     * 付款协议
     */
    @FieldSelect(key = "paymentTermsCode", name = "付款协议", nameEn = "Payment Terms", remoteData = "erp-oms.CustomerController.getPaymentTermsList")
    private List<KeyAndValueDto> paymentTermsCode;

    /**
     * 订单创建时间
     */
    @FieldDate(key = "createTime", name = "订单创建时间", nameEn = "Create Time")
    private LocalDateTime createTime;

    /**
     * 海外区域经理用户id
     */
    @FieldSelect(key = "countryManagerUserId", name = "海外区域经理用户", nameEn = "Country Manager", remoteData = "erp-smc.OumUserInfoController.getSimpleList")
    private List<KeyAndValueDto> countryManagerUserId;

    /**
     * 销售助理用户id
     */
    @FieldSelect(key = "salesAssistantUserId", name = "销售助理用户", nameEn = "Sales Assistant", remoteData = "erp-smc.OumUserInfoController.getSimpleList")
    private List<KeyAndValueDto> salesAssistantUserId;
}
