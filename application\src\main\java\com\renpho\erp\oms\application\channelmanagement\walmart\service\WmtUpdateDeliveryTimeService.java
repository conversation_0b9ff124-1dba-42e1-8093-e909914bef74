package com.renpho.erp.oms.application.channelmanagement.walmart.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtFulfillmentShipment;
import com.renpho.erp.oms.domain.channelmanagement.walmart.repository.WmtFulfillmentShipmentRepository;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderItemFulfillment;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.config.WalmartConfig;
import com.renpho.karma.json.JSONKit;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class WmtUpdateDeliveryTimeService {
    private final SaleOrderRepository saleOrderRepository;
    private final WmtFulfillmentShipmentRepository wmtFulfillmentShipmentRepository;
    private final WalmartConfig walmartConfig;

    public void wmtUpdateDeliveryTime() {
        int limit = walmartConfig.getPageSize(); // 每页查询条数
        int offset = 0;  // 起始偏移量
        long startMillis = System.currentTimeMillis();
        while (true) {
            // 查询分页数据
            List<SaleOrderItemFulfillment> saleOrderItemFulfillments = saleOrderRepository.listWmtEmptyDeliveredTime(limit, offset);

            // 如果没有数据，结束循环
            if (CollectionUtil.isEmpty(saleOrderItemFulfillments)) {
                log.info("没有更多需要补充签收时间的数据，任务结束");
                break;
            }

            // 获取唯一的 trackingNo
            List<String> trackNo = saleOrderItemFulfillments.stream()
                    .map(SaleOrderItemFulfillment::getTrackingNo)
                    .distinct()
                    .collect(Collectors.toList());
            log.info("查询到需要补充签收时间的数据为：{}", JSONKit.toJSONString(trackNo));

            // 根据 trackNo 查询
            List<WmtFulfillmentShipment> wmtFulfillmentShipmentList = wmtFulfillmentShipmentRepository.listByTrackNo(trackNo);
            log.info("查询到的更新数据条数：{}", wmtFulfillmentShipmentList.size());

            // 将 wmtFulfillmentShipmentList 转换为 Map<TrackingNo, WmtFulfillmentShipment>，提升查找效率
            Map<String, WmtFulfillmentShipment> shipmentMap = wmtFulfillmentShipmentList.stream()
                    .filter(shipment -> shipment.getDeliveryDate().isBefore(LocalDateTime.now())) // 仅保留符合时间条件的
                    .collect(Collectors.toMap(WmtFulfillmentShipment::getTrackingNo, shipment -> shipment, (a, b) -> b));

            // 匹配并生成更新数据列表
            List<SaleOrderItemFulfillment> updateList = saleOrderItemFulfillments.stream()
                    .map(saleOrderItemFulfillment -> {
                        WmtFulfillmentShipment matchingShipment = shipmentMap.get(saleOrderItemFulfillment.getTrackingNo());
                        if (matchingShipment != null) {
                            return SaleOrderItemFulfillment.builder()
                                    .id(saleOrderItemFulfillment.getId())
                                    .deliveredTime(matchingShipment.getDeliveryDate())
                                    .orderId(saleOrderItemFulfillment.getOrderId())
                                    .build();
                        }
                        return null; // 不匹配返回 null
                    })
                    .filter(Objects::nonNull) // 移除 null
                    .collect(Collectors.toList());


            log.info("匹配到的更新数据条数：{}", CollectionUtil.isNotEmpty(updateList) ? updateList.size() : 0);

            // 执行更新操作
            if (CollectionUtil.isNotEmpty(updateList)) {
                saleOrderRepository.updateSaleOrderItemFulfillment(updateList);
            }
            // 超时中断逻辑
            if (System.currentTimeMillis() - startMillis > walmartConfig.getTimeoutSeconds()) {
                log.warn("扫描更新沃尔玛签收时间超时，中断本次处理, 总耗时: {} ms.", System.currentTimeMillis() - startMillis);
                break;
            }
            // 增加偏移量，准备查询下一页
            offset += limit;
        }

    }
}
