package com.renpho.erp.oms.adapter.web.controller.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.fastjson.JSON;
import com.renpho.erp.oms.application.salemanagement.dto.SaleOrderImportExcel;
import com.renpho.erp.oms.domain.upload.FileUploadType;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.util.ExcelUtil;
import com.renpho.karma.i18n.I18nMessageKit;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.common.utils.JsonUtils;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.*;

@Slf4j
@Getter
public class SaleOrderExcelListener extends AnalysisEventListener<SaleOrderImportExcel> {
    private final List<SaleOrderImportExcel> saleOrderImportExcelList = new ArrayList<>();
    /**
     * 中文表头
     */
    public static final Map<Integer, String> importCNHeadMap = new HashMap<>();

    /**
     * 待发货中文表头
     */
    public static final Map<Integer, String> importToBeShippedCNHeadMap = new HashMap<>();

    /**
     * 已发货中文表头
     */
    public static final Map<Integer, String> importShippedCNHeadMap = new HashMap<>();

    /**
     * 英文表头
     *
     * @param data
     * @param context
     */
    public static final Map<Integer, String> importUSHeadMap = new HashMap<>();

    /**
     * 待发货英文表头
     */
    public static final Map<Integer, String> importToBeShippedUSHeadMap = new HashMap<>();

    /**
     * 已发货英文表头
     */
    public static final Map<Integer, String> importShippedUSHeadMap = new HashMap<>();

    private FileUploadType importType;

    static {
        importCNHeadMap.put(0, "店铺");
        importCNHeadMap.put(1, "店铺单号");
        importCNHeadMap.put(2, "参考单号");
        importCNHeadMap.put(3, "订单标识");
        importCNHeadMap.put(4, "币种");
        importCNHeadMap.put(5, "下单时间");
        importCNHeadMap.put(6, "付款时间");
        importCNHeadMap.put(7, "收件人");
        importCNHeadMap.put(8, "手机号");
        importCNHeadMap.put(9, "邮编");
        importCNHeadMap.put(10, "邮箱");
        importCNHeadMap.put(11, "国家/地区");
        importCNHeadMap.put(12, "州/省");
        importCNHeadMap.put(13, "城市");
        importCNHeadMap.put(14, "地址1");
        importCNHeadMap.put(15, "地址2");
        importCNHeadMap.put(16, "PSKU");
        importCNHeadMap.put(17, "FNSKU");
        importCNHeadMap.put(18, "发货数量");
        importCNHeadMap.put(19, "商品收入(不含税)");
        importCNHeadMap.put(20, "商品折扣");
        importCNHeadMap.put(21, "商品税收入");
        importCNHeadMap.put(22, "商品税折扣");
        importCNHeadMap.put(23, "运费收入(不含税)");
        importCNHeadMap.put(24, "运费折扣");
        importCNHeadMap.put(25, "运费税收入");
        importCNHeadMap.put(26, "运费税折扣");

        importToBeShippedCNHeadMap.putAll(importCNHeadMap);

        importShippedCNHeadMap.putAll(importCNHeadMap);
        importShippedCNHeadMap.put(27, "发货仓库");
        importShippedCNHeadMap.put(28, "仓库单号");
        importShippedCNHeadMap.put(29, "承运商");
        importShippedCNHeadMap.put(30, "跟踪号");
        importShippedCNHeadMap.put(31, "发货时间");
        importShippedCNHeadMap.put(32, "签收时间");

        importUSHeadMap.put(0, "Shop");
        importUSHeadMap.put(1, "Shop Order #");
        importUSHeadMap.put(2, "Refer #");
        importUSHeadMap.put(3, "Order Tags");
        importUSHeadMap.put(4, "Currency");
        importUSHeadMap.put(5, "Ordered Time");
        importUSHeadMap.put(6, "Paid Time");
        importUSHeadMap.put(7, "Receiver");
        importUSHeadMap.put(8, "Tel.");
        importUSHeadMap.put(9, "Postal Code");
        importUSHeadMap.put(10, "Email");
        importUSHeadMap.put(11, "Country or Region");
        importUSHeadMap.put(12, "State");
        importUSHeadMap.put(13, "City");
        importUSHeadMap.put(14, "Address1");
        importUSHeadMap.put(15, "Address2");
        importUSHeadMap.put(16, "PSKU");
        importUSHeadMap.put(17, "FNSKU");
        importUSHeadMap.put(18, "Quantity");
        importUSHeadMap.put(19, "Product Principal (excluding Tax)");
        importUSHeadMap.put(20, "Product Discount");
        importUSHeadMap.put(21, "Product Tax");
        importUSHeadMap.put(22, "Product Tax Discount");
        importUSHeadMap.put(23, "Shipping Charge (excluding Tax)");
        importUSHeadMap.put(24, "Shipping Discount");
        importUSHeadMap.put(25, "Shipping Tax");
        importUSHeadMap.put(26, "Shipping Tax Discount");

        importToBeShippedUSHeadMap.putAll(importUSHeadMap);

        importShippedUSHeadMap.putAll(importUSHeadMap);
        importShippedUSHeadMap.put(27, "Warehouse");
        importShippedUSHeadMap.put(28, "Warehouse Order #");
        importShippedUSHeadMap.put(29, "Carrier");
        importShippedUSHeadMap.put(30, "Tracking #");
        importShippedUSHeadMap.put(31, "Shipped Time");
        importShippedUSHeadMap.put(32, "Delivered Time");
    }

    public SaleOrderExcelListener(FileUploadType importType) {
        this.importType = importType;
    }

    @Override
    public void invoke(SaleOrderImportExcel data, AnalysisContext context) {
        saleOrderImportExcelList.add(data);
    }

    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
        // 不读取1级表头
        if (context.readRowHolder().getRowIndex() == 0) return;
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        if (headMap.equals(importToBeShippedCNHeadMap) && importType == FileUploadType.TO_BE_SHIPPED_ORDER) {
            // 国际化转换
            ExcelUtil.buildUpdateHeadAgain(context, importToBeShippedCNHeadMap, SaleOrderImportExcel.class);
            LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        } else if (headMap.equals(importToBeShippedUSHeadMap) && importType == FileUploadType.TO_BE_SHIPPED_ORDER) {
            ExcelUtil.buildUpdateHeadAgain(context, importToBeShippedUSHeadMap, SaleOrderImportExcel.class);
            LocaleContextHolder.setLocale(Locale.US);
        } else if (headMap.equals(importShippedCNHeadMap) && importType == FileUploadType.SHIPPED_ORDER) {
            ExcelUtil.buildUpdateHeadAgain(context, importShippedCNHeadMap, SaleOrderImportExcel.class);
            LocaleContextHolder.setLocale(Locale.SIMPLIFIED_CHINESE);
        } else if (headMap.equals(importShippedUSHeadMap) && importType == FileUploadType.SHIPPED_ORDER) {
            ExcelUtil.buildUpdateHeadAgain(context, importShippedUSHeadMap, SaleOrderImportExcel.class);
            LocaleContextHolder.setLocale(Locale.US);
        } else {
            throw new BusinessException(I18nMessageKit.getMessage("HEAD_NOT_MATCH"));
        }

    }

    @Override
    public void onException(Exception exception, AnalysisContext context) throws Exception{
        if (exception instanceof ExcelDataConvertException) {
            throw new BusinessException(I18nMessageKit.getMessage("DATA_FORMAT_ERROR"));
        }
        throw exception;
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("销售订单导入excel解析数据为", JsonUtils.toJson(saleOrderImportExcelList));

    }
}
