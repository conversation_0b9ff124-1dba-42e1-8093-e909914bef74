package com.renpho.erp.oms.domain.vc.order.converter;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;

import com.renpho.erp.oms.domain.DomainException;
import com.renpho.erp.oms.domain.vc.order.model.*;

import lombok.Builder;
import lombok.Getter;

/**
 * VC订单创建-转换者
 * <AUTHOR>
 */
public class VcOrderCreateConverter {

	private VcOrderCreateConverter() {
	}

	/**
	 * 转换生成VC订单
	 * @param amzVcOrder 亚马逊订单
	 * @param valueObject 值对象
	 * @return VcOrderAggRoot
	 */
	public static VcOrderAggRoot convertOrder(AmzVcOrder amzVcOrder, ValueObject valueObject) {
		// 亚马逊订单详情
		AmzVcOrderDetails orderDetails = Optional.ofNullable(amzVcOrder.getOrderDetails())
			.orElseThrow(() -> DomainException.of("Amz orderDetails is null"));
		return VcOrderAggRoot.builder()
			// vc采购单号
			.vcPurchaseNo(amzVcOrder.getPurchaseOrderNumber())
			// 店铺
			.store(valueObject.getStore())
			// 业务类型（DI：ShipWindow DO：DeliveryWindow）
			.businessType(orderDetails.isDirectImport() ? VcOrderBusinessType.DI : VcOrderBusinessType.DO)
			// 订单类型
			.orderType(orderDetails.getPurchaseOrderType())
			// 发票类型
			.invoiceType(orderDetails.getPaymentMethod())
			// 下单时间
			.orderedTime(orderDetails.getPurchaseOrderDate())
			// 接单时间
			.acceptedTime(amzVcOrder.getAcknowledgementDate())
			// 订单地址
			.address(buildAddress(orderDetails))
			// 交货窗口
			.shippingWindow(orderDetails.getShippingWindow())
			// 订单DI信息
			.di(buildDi(orderDetails))
			// 订单商品列表
			.items(buildItems(amzVcOrder))
			.build();
	}

	/**
	 * 构建订单商品列表
	 * @param amzVcOrder Amazon VC订单
	 * @return List<VcOrderItem> 内部VC订单商品列表
	 * @throws DomainException 当商品列表为空时抛出异常
	 */
	private static List<VcOrderItem> buildItems(AmzVcOrder amzVcOrder) {
		List<AmzVcOrderItem> items = amzVcOrder.getItems();
		if (CollectionUtils.isEmpty(items)) {
			throw DomainException.of("AmzVcOrder orderDetails items is empty");
		}
		return items.stream()
			.map(i -> VcOrderItem.builder()
				// 渠道订单行号：Amazon商品行号
				.channelOrderLineNo(i.getItemSequenceNumber())
				// MSKU：供应商商品标识符
				.msku(i.getVendorProductIdentifier())
				// ASIN：亚马逊商品标识符
				.asin(i.getAmazonProductIdentifier())
				// 下单数量
				.orderedQuantity(amzVcOrder.getOrderedQuantity(i.getItemSequenceNumber()))
				// 接受数量
				.acceptedQuantity(amzVcOrder.getAcceptedQuantity(i.getItemSequenceNumber()))
				// 拒绝数量
				.rejectedQuantity(amzVcOrder.getRejectedQuantity(i.getItemSequenceNumber()))
				// 接收数量
				.receivedQuantity(amzVcOrder.getReceivedQuantity(i.getItemSequenceNumber()))
				// 商品金额信息
				.amount(buildItemAmount(i.getNetCost()))
				.build())
			.toList();
	}

	/**
	 * 构建订单商品金额信息
	 * @param netCost Amazon VC订单商品行金额
	 * @return VcOrderItemAmount 商品金额对象
	 */
	private static VcOrderItemAmount buildItemAmount(AmzVcOrderItemMoney netCost) {
		if (Objects.isNull(netCost)) {
			throw DomainException.of("Amazon netCost is null");
		}
		return VcOrderItemAmount.builder()
			// 货币代码：取netCost中的currencyCode
			.currency(netCost.getCurrencyCode())
			// 单价：取netCost中的amount作为单价
			.unitPrice(netCost.getAmount())
			// 税额和小计暂不设置，后续业务流程中计算
			.build();
	}

	/**
	 * 构建订单DI信息
	 * @param orderDetails 订单详情
	 * @return VcOrderDi
	 */
	private static VcOrderDi buildDi(AmzVcOrderDetails orderDetails) {
		if (Objects.isNull(orderDetails.getImportDetails())) {
			return null;
		}

		AmzVcImportDetails importDetails = orderDetails.getImportDetails();
		return VcOrderDi.builder()
			// 付款条款：从进口详情的付款方式映射
			.paymentMethod(importDetails.getMethodOfPayment())
			// 贸易条款：国际商业条款
			.incoterms(importDetails.getInternationalCommercialTerms())
			// 集装箱规格：进口集装箱信息
			.containerType(importDetails.getImportContainers())
			// 发货描述：发货说明
			.shippingInstructions(importDetails.getShippingInstructions())
			.build();
	}

	/**
	 * 构建订单地址
	 * @param orderDetails 亚马逊VC订单详情
	 * @return VcOrderAddress
	 */
	private static VcOrderAddress buildAddress(AmzVcOrderDetails orderDetails) {
		if (Objects.isNull(orderDetails)) {
			return null;
		}

		return VcOrderAddress.builder()
			// 发货地：供应商发货仓库地址（从sellingParty获取）
			.shipFrom(orderDetails.getSellingPartyId())
			// 收货地：亚马逊接收仓库地址（从shipToParty获取）
			.shipTo(orderDetails.getShipToPartyId())
			// 账单地址：财务开票地址（从billToParty获取）
			.billTo(orderDetails.getBillToPartyId())
			// 买家地址：亚马逊采购方地址（从buyingParty获取）
			.buying(orderDetails.getBuyingPartyId())
			.build();
	}

	/**
	 * 值对象
	 */
	@Getter
	@Builder
	public static class ValueObject {

		/**
		 * 商品值对象Map，key为asin
		 */
		private Map<String, VcProduct> asinToProductMap;

		/**
		 * 店铺值对象
		 */
		private VcStore store;
	}
}