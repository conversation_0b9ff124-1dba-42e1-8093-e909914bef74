package com.renpho.erp.oms.application.ordershipaudit.job;

import com.renpho.erp.oms.application.ordershipaudit.service.LogisticsShipmentService;
import com.renpho.erp.oms.domain.ordershipaudit.query.LogisticsShipmentQuery;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @desc: 发货审核物流下单定时任务
 * @time: 2025-04-07 16:41:32
 * @author: Alina
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderShipAuditLogisticsJob {

	private final LogisticsShipmentService logisticsShipmentService;

	/**
	 * 定时获取物流下单结果 并重试
	 */
	@XxlJob("logisticsShipmentResultProcessorJob")
	public void logisticsShipmentResultProcessorJob() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		Param param = JSONKit.parseObject(jobParam, Param.class);
		LogisticsShipmentQuery query = buildLogisticsShipmentQuery(param);
		logisticsShipmentService.logisticsShipmentResultProcessor(query);
	}

	/**
	 * 定时任务 查询ecom物流跟踪信息
	 */
	@XxlJob("fetchEcomShipmentStatusJob")
	public void fetchEcomShipmentStatusJob() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		Param param = JSONKit.parseObject(jobParam, Param.class);
		LogisticsShipmentQuery query = buildLogisticsShipmentQuery(param);
		logisticsShipmentService.fetchEcomShipmentStatus(query);
	}

	private LogisticsShipmentQuery buildLogisticsShipmentQuery(Param param) {
		LogisticsShipmentQuery query = new LogisticsShipmentQuery();
		query.setIds(param.getIds());
		query.setEndTime(param.getEndTime());
		query.setStartTime(param.getStartTime());
		return query;
	}

	@Data
	public static class Param {
		/**
		 * 开始时间(yyyy-MM-dd HH:mm:ss)
		 */
		private String startTime;

		/**
		 * 结束时间（yyyy-MM-dd HH:mm:ss）
		 */
		private String endTime;

		/**
		 * ids
		 */
		private List<Long> ids;

	}
}
