package com.renpho.erp.oms.application.channelmanagement.amazon.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class AmzOrderLog {
	private Long id;

	/**
	 * 亚马逊订单号
	 */
	private String amazonOrderId;

	/**
	 * 卖家账号
	 */
	private String sellerId;

	/**
	 * 卖家订单号
	 */
	private String sellerOrderId;

	/**
	 * 创建订单的日期
	 */
	private LocalDateTime purchaseDate;

	/**
	 * 上次更新订单的日期
	 */
	private LocalDateTime lastUpdateDate;

	/**
	 * 订单状态
	 * Pending,Unshipped,PartiallyShipped,Shipped,Canceled,Unfulfillable,InvoiceUnconfirmed,PendingAvailability
	 */
	private String orderStatus;

	/**
	 * 订单是由亚马逊 (AFN) 还是由卖家 (MFN) 完成的 MFN,AFN
	 */
	private String fulfillmentChannel;

	/**
	 * 市场ID
	 */
	private String marketplaceId;

	/**
	 * 销售渠道
	 */
	private String salesChannel;

	/**
	 * 订单渠道
	 */
	private String orderChannel;

	/**
	 * 订单的发货服务级别
	 */
	private String shipServiceLevel;

	/**
	 * 此订单的总费用
	 */
	private BigDecimal orderTotalAmount;

	/**
	 * 此订单的货币代码
	 */
	private String orderTotalCurrencyCode;

	/**
	 * 发货数量
	 */
	private Integer numberOfItemsShipped;

	/**
	 * 未发货数量
	 */
	private Integer numberOfItemsUnshipped;

	/**
	 * 付款方式 COD:货到付款,CVS:便利店,Other:其他方式
	 */
	private String paymentMethod;

	/**
	 * 订单的发货服务级别类别 Expedited, FreeEconomy, NextDay, Priority, SameDay, SecondDay,
	 * Scheduled, and Standard
	 */
	private String shipmentServiceLevelCategory;

	/**
	 * 亚亚马逊 Easy Ship 订单的状态。此属性仅适用于 Amazon Easy Ship 订单
	 */
	private String easyShipShipmentStatus;

	/**
	 * 亚马逊结账 (CBA) 的自定义发货标签
	 */
	private String cbaDisplayableShippingLabel;

	/**
	 * 订单的类型 StandardOrder,LongLeadTimeOrder,Preorder,BackOrder,SourcingOnDemandOrder
	 */
	private String orderType;

	/**
	 * 最早交货日期
	 */
	private LocalDateTime earliestDeliveryDate;

	/**
	 * 最新交货日期
	 */
	private LocalDateTime latestDeliveryDate;

	/**
	 * 是否是AmazonBusiness订单 0 否 1 是
	 */
	private Boolean isBusinessOrder;

	/**
	 * 订单是否优先配送 0 否 1 是
	 */
	private Boolean isPremiumOrder;

	/**
	 * 是否Amazon Prime订单 0 否 1 是
	 */
	private Boolean isPrime;

	/**
	 * 是否GlobalExpress订单 0 否 1 是
	 */
	private Boolean isGlobalExpressEnabled;

	/**
	 * 正在被替换的订单的订单 ID 值 仅当 IsReplacementOrder = true 时返回
	 */
	private String replacedOrderId;

	/**
	 * 是否替换订单 0 否 1 是
	 */
	private Boolean isReplacementOrder;

	/**
	 * 承诺答复截止日期
	 */
	private LocalDateTime promiseResponsDueDate;

	/**
	 * 预计发货日期是否已确定 0 否 1 是
	 */
	private Boolean isEstimatedShipDateSet;

	/**
	 * 是否由ABEU转售 0 否 1 是
	 */
	private Boolean isSoldByAb;

	/**
	 * 是否由ABEU转售 0 否 1 是
	 */
	private Boolean isIba;

	/**
	 * 买家的发票偏好
	 */
	private String buyerInvoicePreference;

	/**
	 * 包含有关履行的说明
	 */
	private String fulfillmentSupplySourceId;

	/**
	 * 订单被标记为从商店取货而不是交付 0 否 1 是
	 */
	private Boolean isIspu;

	/**
	 * 订单是否已送达标记点 0 否 1 是
	 */
	private Boolean isAccessPointOrder;

	/**
	 * 卖家在市场上注册的友好名称
	 */
	private String sellerDisplayName;

	/**
	 * 最早发货日期
	 */
	private LocalDateTime earliestShipDate;

	/**
	 * 最晚发货日期
	 */
	private LocalDateTime latestShipDate;

	/**
	 * 是否由亚马逊自动生成的配送设置 0 否 1 是
	 */
	private Boolean hasAutomatedShippingSettings;

	/**
	 * 为 SSA 订单自动生成的承运人
	 */
	private String automatedCarrier;

	/**
	 * 为 SSA 订单自动生成的发货方式
	 */
	private String automatedShipMethod;

	/**
	 * 是否有管制物品 0 否 1 是
	 */
	private Boolean hasRegulatedItems;

	/**
	 * 电票状态 NotRequired,NotFound,Processing,Errored,Accepted
	 */
	private String electronicInvoiceStatus;

	/**
	 * 发货城市
	 */
	private String shippingCity;

	/**
	 * 发货区
	 */
	private String shippingStateOrRegion;

	/**
	 * 发货邮政编码
	 */
	private String shippingPostalCode;

	/**
	 * 发货国家代码
	 */
	private String shippingCountryCode;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	private List<AmzOrderItemLog> amzOrderItemLogList;
}
