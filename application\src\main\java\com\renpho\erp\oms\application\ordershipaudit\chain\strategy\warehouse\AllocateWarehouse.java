package com.renpho.erp.oms.application.ordershipaudit.chain.strategy.warehouse;

import java.util.List;
import java.util.Set;

import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.SkuCombinationType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.AutoShipAuditConfig;

/**
 * 分仓
 * <AUTHOR>
 */
public interface AllocateWarehouse {

	/**
	 * 获取sku组合类型集
	 * @return Set
	 */
	Set<SkuCombinationType> getSkuCombinationTypes();

	/**
	 * 分仓
	 * @param orderAutoShipAuditDTO 订单-自动发货审核DTO
	 * @param autoShipAuditConfigs 自动发货审核配置
	 * @return String 分仓失败原因
	 */
	String allocateWarehouse(OrderAutoShipAuditDTO orderAutoShipAuditDTO, List<AutoShipAuditConfig> autoShipAuditConfigs);
}
