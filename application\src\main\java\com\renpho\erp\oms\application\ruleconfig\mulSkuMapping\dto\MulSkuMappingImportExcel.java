package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;


@Data
public class MulSkuMappingImportExcel {
    /**
     * 订单渠道
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.ORDER_CHANNEL")
    private String orderChannel;

    /**
     * 订单店铺
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.ORDER_STORE")
    private String orderStore;

    /**
     * 订单PSKU
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.ORDER_PSKU")
    private String orderPsku;

    /**
     * 多渠道类型
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL_IMPORT.MUL_CHANNEL_TYPE")
    private Integer mulChannelType;

    /**
     * 多渠道店铺
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.MUL_CHANNEL_STORE")
    private String mulChannelStore;

    /**
     * 多渠道MSKU
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.MUL_CHANNEL_MSKU")
    private String mulChannelMsku;

    /**
     * 优先级
     */
    @ExcelProperty(value = "MUL_SKU_MAPPING_EXCEL.PRIORITY")
    private String priority;

    @ExcelProperty("MUL_SKU_MAPPING_EXCEL_IMPORT.ERRORMESSAGE")
    private String errMessage;

}
