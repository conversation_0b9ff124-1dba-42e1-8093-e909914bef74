package com.renpho.erp.oms.application.salemanagement.converter;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.SaleOrderInfoConverterDto;
import com.renpho.erp.oms.domain.channelmanagement.temu.repository.TemOrderRepository;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.repository.SkuMappingRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;
import com.renpho.erp.oms.infrastructure.feign.pds.ProductClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Slf4j
@Component
public class TemuChannelConvertSaleOrderStrategy extends AbstractChannelConvertSaleOrderStrategy {
    @Autowired
    private TemOrderRepository temOrderRepository;

    public TemuChannelConvertSaleOrderStrategy(SaleOrderRepository saleOrderRepository, StoreClient storeClient, SkuMappingRepository skuMappingRepository, ProductClient productClient) {
        super(saleOrderRepository, storeClient, skuMappingRepository, productClient);
    }

    @Override
    public SaleChannelType getChannelType() {
        return SaleChannelType.TEMU;
    }

    @Override
    public FulfillmentType getFulfillmentType() {
        return FulfillmentType.MERCHANT;
    }

    @Override
    protected Set<String> findSellerSkusByChannelOrderId(String channelOrderNo, Integer storeId) {
        return 	temOrderRepository.findSellerSkusById(channelOrderNo, storeId);
    }

    @Override
    public List<SaleOrderInfoConverterDto> getChannelOrders(StoreDTO storeDTO, Set<String> channelOrderIds, Integer pageSize, Long lastId) {
        return temOrderRepository.findUnCompletedOrders(storeDTO.getStoreId(), channelOrderIds, pageSize, lastId);
    }
}
