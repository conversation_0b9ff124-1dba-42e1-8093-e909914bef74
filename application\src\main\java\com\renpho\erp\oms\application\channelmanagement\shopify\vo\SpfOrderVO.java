package com.renpho.erp.oms.application.channelmanagement.shopify.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SpfOrderVO {

	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 店铺id
	 */
	private String shopId;

	/**
	 * 名称
	 */
	private String name;

	/**
	 * Shopify订单id
	 */
	private Long shopifyOrderId;

	/**
	 * 支付状态
	 */
	private String financialStatus;

	/**
	 * 履约状态
	 */
	private String fulfillmentStatus;

	/**
	 * Shopify订单创建时间
	 */
	private LocalDateTime createdAt;

	/**
	 * 处理时间
	 */
	private LocalDateTime processedAt;

	/**
	 * 取消时间
	 */
	private LocalDateTime cancelledAt;

	/**
	 * 关闭时间
	 */
	private LocalDateTime closedAt;

	/**
	 * 更新时间
	 */
	private LocalDateTime updatedAt;

	/**
	 * 城市
	 */
	private String city;

	/**
	 * 邮编
	 */
	private String zip;

	/**
	 * 省份
	 */
	private String province;

	/**
	 * 国家
	 */
	private String country;

	/**
	 * 国家编码
	 */
	private String countryCode;

}
