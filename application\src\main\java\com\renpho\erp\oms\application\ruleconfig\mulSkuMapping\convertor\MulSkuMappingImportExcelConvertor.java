package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.convertor;

import com.renpho.erp.mdm.client.store.vo.StoreVo;
import com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.dto.MulSkuMappingImportExcel;

import com.renpho.erp.oms.domain.ruleconfig.mulSkuMapping.model.MulSkuMappingAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;

import java.util.*;

public class MulSkuMappingImportExcelConvertor {

    public static MulSkuMappingAggRoot convertByImportExcleList(Map<String, StoreVo> storeMap,
                                                                List<MulSkuMappingImportExcel> mulSkuMappingImportExcelList) {
        MulSkuMappingImportExcel mulSkuMappingImportExcel = mulSkuMappingImportExcelList.get(0);
        MulSkuMappingAggRoot mulSkuMappingAggRoot = new MulSkuMappingAggRoot();
        mulSkuMappingAggRoot.setOrderStoreId(storeMap.get(mulSkuMappingImportExcel.getOrderStore()).getId());
        mulSkuMappingAggRoot.setOrderChannelId(storeMap.get(mulSkuMappingImportExcel.getOrderStore()).getSalesChannelId());
        mulSkuMappingAggRoot.setOrderPsku(mulSkuMappingImportExcel.getOrderPsku());

        mulSkuMappingAggRoot.setMulSkuMappingLineList(convertLineByImportExcleList(storeMap, mulSkuMappingImportExcelList));
        return mulSkuMappingAggRoot;
    }

    public static List<MulSkuMappingAggRoot.MulSkuMappingLine> convertLineByImportExcleList(Map<String, StoreVo> storeMap, List<MulSkuMappingImportExcel> mulSkuMappingImportExcelList) {
        // 排序设置优先级
        mulSkuMappingImportExcelList.sort(Comparator.comparing(MulSkuMappingImportExcel::getPriority, new Comparator<String>() {
            @Override
            public int compare(String p1, String p2) {
                // 处理 p1 和 p2 为 null 的情况
                if (p1 == null && p2 == null) return 0;
                if (p1 == null) return -1; // null 排在前面
                if (p2 == null) return 1;  // null 排在前面

                // 检查 p1 和 p2 是否为数字
                boolean p1IsNumeric = p1.matches("\\d+");
                boolean p2IsNumeric = p2.matches("\\d+");

                if (p1IsNumeric && p2IsNumeric) {
                    // 都是数字，按大小排序
                    return Integer.compare(Integer.parseInt(p1), Integer.parseInt(p2));
                } else if (p1IsNumeric) {
                    return -1; // 数字排在前面
                } else if (p2IsNumeric) {
                    return 1; // 数字排在前面
                } else {
                    return 0; // 都是非数字，保持顺序
                }
            }
        }));
        List<MulSkuMappingAggRoot.MulSkuMappingLine> mulSkuMappingLineList = new ArrayList<>();
        for (int i = 0; i < mulSkuMappingImportExcelList.size(); i++) {
            MulSkuMappingAggRoot.MulSkuMappingLine mulSkuMappingLine = new MulSkuMappingAggRoot.MulSkuMappingLine();
            mulSkuMappingLine.setPriority(i+1);
            mulSkuMappingLine.setMulChannelMsku(mulSkuMappingImportExcelList.get(i).getMulChannelMsku());
            mulSkuMappingLine.setMulChannelStoreId(storeMap.get(mulSkuMappingImportExcelList.get(i).getMulChannelStore()).getId());
            mulSkuMappingLine.setMulChannelType(FulfillmentServiceType.enumOf(mulSkuMappingImportExcelList.get(i).getMulChannelType()));
            mulSkuMappingLineList.add(mulSkuMappingLine);
        }
        return mulSkuMappingLineList;

    }

}
