package com.renpho.erp.oms.application.vc.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * VC订单备注历史VO
 *
 * <AUTHOR>
 * @since 2025/07/09
 */
@Data
@Schema(description = "VC订单备注历史VO")
public class VcOrderRemarkHistoryVO {

    /**
     * 备注内容
     */
    @Schema(description = "备注内容")
    private String remark;

    /**
     * 创建人名称
     */
    @Schema(description = "创建人名称")
    private String creatorName;

    /**
     * creatorCode
     */
    @Schema(description = "创建人工号")
    private String creatorCode;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}