package com.renpho.erp.oms.application.platform.convert;

import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.amazon.model.AmzOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.ebay.model.EbyOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.tiktok.model.TtOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.walmart.model.WmtOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderPage;
import com.renpho.erp.oms.domain.channelmanagement.temu.model.TemOrderPage;
import com.renpho.erp.oms.infrastructure.common.enums.TemuOrderEnums;
import org.mapstruct.*;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface PlatformOrderConvertor {
	PlatformOrderVO amazonOrderToVO(AmzOrderPage param);

	PlatformOrderVO walmartOrderToVO(WmtOrderPage param);

	PlatformOrderVO titTokOrderToVO(TtOrderPage param);

	PlatformOrderVO mercadoOrderToVO(MclOrderPage para);

	PlatformOrderVO shopifyOrderToVO(SpfOrderPage para);

	@Mapping(source = "orderStatus", target = "orderStatus", qualifiedByName = "mapOrderStatus")
	PlatformOrderVO temuOrderToVO(TemOrderPage para);

	PlatformOrderVO ebayOrderToVO(EbyOrderPage ebyOrderPage);


	@Named("mapOrderStatus")
	default String mapOrderStatus(String value) {
		return value == null ? null : TemuOrderEnums.getByValue(Integer.valueOf(value));
	}
}
