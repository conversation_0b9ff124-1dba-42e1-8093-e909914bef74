package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import cn.hutool.core.collection.ListUtil;
import com.renpho.erp.apiproxy.temu.model.goods.GetGoodsListData;
import com.renpho.erp.apiproxy.temu.model.goods.GetGoodsPriceListData;
import com.renpho.erp.apiproxy.tiktok.model.product.SearchProductsData;
import com.renpho.erp.oms.application.listingmanagement.dto.temu.TemuListingPullDto;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
public class TemuListingParser extends AbstractListingParserTemplate<TemuListingPullDto> {
    @Override
    protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, TemuListingPullDto temuListingPullDto) {
        log.info("解析temu listing开始, 解析SalesChannelListingSource id 是{}, 平台内容是{}", source.getId(), source.getContent());
        GetGoodsListData.Goods good = temuListingPullDto.goods();
        // listing是否绑定在美国站,site=100时美国
        boolean flag = Optional.ofNullable(good.getProductSemiManaged())
                .map(GetGoodsListData.ProductSemiManaged::getBindSites)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(site -> Objects.equals(site.getSiteId(), 100));
        if (!flag) {
            return Collections.emptyList();
        }
        // 按照item编码归并供应价信息
        Map<Long, GetGoodsPriceListData.SkuSupplierPrice> skuSupplierPriceMap = temuListingPullDto.skuSupplierPriceList()
                .stream()
                .collect(Collectors.toMap(GetGoodsPriceListData.SkuSupplierPrice::getProductSkuId, Function.identity(), (v1, v2) -> v1));

        Set<String> mskuSet = good.getProductSkuSummaries().stream()
                .map(GetGoodsListData.ProductSkuSummary::getExtCode)
                .collect(Collectors.toSet());
        Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = super.getSkuMappingListMap(source.getStoreId(), mskuSet);
        Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(skuMappingListMap.values().stream().flatMap(Collection::stream).toList());

        List<SalesChannelListing> salesChannelListingList = new ArrayList<>(good.getProductSkuSummaries().size());
        for (GetGoodsListData.ProductSkuSummary productSkuSummary : good.getProductSkuSummaries()) {
            SalesChannelListing salesChannelListing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, productSkuSummary.getExtCode(), source.getStoreId());
            // item编码
            salesChannelListing.setItem(String.valueOf(productSkuSummary.getProductSkuId()));
            // 销售sku
            salesChannelListing.setMsku(String.valueOf(productSkuSummary.getExtCode()));
            // 标题
            salesChannelListing.setTitle(good.getProductName());
            // 状态
            salesChannelListing.setStatus(String.valueOf(good.getSkcSiteStatus()));
            // 货币
            Optional.ofNullable(skuSupplierPriceMap.get(productSkuSummary.getProductSkuId()))
                    .map(GetGoodsPriceListData.SkuSupplierPrice::getCurrencyType)
                    .ifPresent(salesChannelListing::setCurrency);
            // 价格
            Optional.ofNullable(skuSupplierPriceMap.get(productSkuSummary.getProductSkuId()))
                    .map(GetGoodsPriceListData.SkuSupplierPrice::getSiteSupplierPrices)
                    .orElse(Collections.emptyList())
                    .stream()
                    // 站点为美国时
                    .filter(siteSupplierPrice -> Objects.equals(siteSupplierPrice.getSiteId(), 100))
                    .findFirst()
                    .ifPresent(siteSupplierPrice -> {
                        // temu价格单位是分
                        salesChannelListing.setPrice(new BigDecimal(siteSupplierPrice.getSupplierPrice()).divide(BigDecimal.valueOf(100),2, RoundingMode.HALF_DOWN));
                    });

            // 上架时间
            if (good.getCreatedAt() != null) {
                LocalDateTime createAt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(good.getCreatedAt()), ZoneId.systemDefault())
                        .toLocalDateTime();
                salesChannelListing.setPublishedTime(createAt);
            }
            salesChannelListingList.add(salesChannelListing);
        }


        return salesChannelListingList;
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.TEM_CHANNEL_CODE;
    }
}
