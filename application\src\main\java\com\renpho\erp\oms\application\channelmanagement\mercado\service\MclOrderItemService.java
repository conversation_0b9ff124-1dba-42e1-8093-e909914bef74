package com.renpho.erp.oms.application.channelmanagement.mercado.service;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.oms.application.channelmanagement.mercado.convert.MclOrderItemLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderItemVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderItem;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclOrderItemRepository;
import com.renpho.karma.dto.R;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class MclOrderItemService {

	private final MclOrderItemRepository mclOrderItemRepository;
	private final MclOrderItemLogConvertor mclOrderItemLogConvertor;

	public R<List<MclOrderItemVO>> listByOrderId(Long orderId) {
		List<MclOrderItem> mclOrderItemList = mclOrderItemRepository.listByOrderId(orderId);
		if (CollectionUtil.isNotEmpty(mclOrderItemList)) {
			return R.success(mclOrderItemList.stream()
				.map(mclOrderItem -> mclOrderItemLogConvertor.toVO(mclOrderItem))
				.collect(Collectors.toList()));
		}
		return R.success();
	}
}
