package com.renpho.erp.oms.application.channelmanagement.temu.vo;

import lombok.Data;

import java.util.List;

@Data
public class TemOrderItemVO {

    /**
     * 订单编号
     */
    private String orderSn;

    /**
     * 缩略图URL
     */
    private String thumbUrl;

    /**
     * 商品状态
     */
    private Integer orderStatus;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 原始订单数量
     */
    private Integer originalOrderQuantity;

    /**
     * 发货前取消的数量
     */
    private Integer canceledQuantityBeforeShipment;

    /**
     * SPU ID
     */
    private Long productId;

    /**
     * SKU ID
     */
    private Long productSkuId;

    /**
     * MSKU
     */
    private String extCode;

    /**
     * 履约类型
     */
    private String fulfillmentType;

    /**
     * 包裹单号
     */
    private List<String> packageSnList;

    /**
     * 标签
     */
    private List<String> labelList;
}
