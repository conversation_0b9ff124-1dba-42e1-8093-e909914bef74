package com.renpho.erp.oms.application.vc.order.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;

/**
 * VC订单标签上传命令
 * <AUTHOR>
 */
@Data
public class VcOrderLabelUploadCmd {

    /**
     * VC订单ID
     */
    @NotNull(message = "VC_ORDER_LABEL_UPLOAD_ORDER_ID_NOTNULL")
    private Long orderId;

    /**
     * 订单商品标签列表
     */
    @NotEmpty(message = "VC_ORDER_LABEL_UPLOAD_ITEM_LABEL_LIST_NOT_EMPTY")
    @Valid
    private List<@Valid VcOrderItemLabelCmd> itemLabelList;

    /**
     * VC订单商品标签命令
     */
    @Data
    public static class VcOrderItemLabelCmd {

        /**
         * 订单商品ID
         */
        @NotNull(message = "VC_ORDER_LABEL_UPLOAD_ITEM_ID_NOTNULL")
        private Long orderItemId;

        /**
         * 物流标URL
         */
        @NotEmpty(message = "VC_ORDER_LABEL_UPLOAD_ITEM_ASIN_LABEL_URL_NOT_EMPTY")
        private String asinLabelUrl;

        /**
         * 物流标文件名
         */
        @NotEmpty(message = "VC_ORDER_LABEL_UPLOAD_ITEM_ASIN_LABEL_NAME_NOT_EMPTY")
        private String asinLabelName;

        /**
         * 外箱标URL
         */
        @NotEmpty(message = "VC_ORDER_LABEL_UPLOAD_ITEM_CARTON_LABEL_URL_NOT_EMPTY")
        private String cartonLabelUrl;

        /**
         * 外箱标文件名
         */
        @NotEmpty(message = "VC_ORDER_LABEL_UPLOAD_ITEM_CARTON_LABEL_NAME_NOT_EMPTY")
        private String cartonLabelName;
    }
} 