package com.renpho.erp.oms.application.returndorder.job;

import cn.hutool.core.util.ObjectUtil;
import com.renpho.erp.oms.application.returnorder.service.AmzReturnOrderOperateService;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AmzFbaReturnParseJob {
    @Autowired
    private AmzReturnOrderOperateService amzReturnOrderOperateService;

    @XxlJob("amzFbaReturnParseJob")
    public void handle() {
        String jobParam = XxlJobHelper.getJobParam();
        Param param = JSONKit.parseObject(jobParam, Param.class);

        int pageSize = ObjectUtil.defaultIfNull(param.getPageSize(), 1000);
        amzReturnOrderOperateService.reparseAll(pageSize);

    }

    @Data
    public static class Param {
        private Integer pageSize;
    }
}
