package com.renpho.erp.oms.application.channelmanagement.mercado.convert;

import com.renpho.erp.oms.application.channelmanagement.mercado.dto.MclOrderPaymentLog;
import com.renpho.erp.oms.application.channelmanagement.mercado.vo.MclOrderPaymentVO;
import com.renpho.erp.oms.domain.channelmanagement.mercado.model.MclOrderPayment;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = MappingConstants.ComponentModel.SPRING)
public interface MclOrderPaymentLogConvertor {
	MclOrderPaymentLog toLog(MclOrderPayment param);

	MclOrderPaymentVO toVO(MclOrderPayment param);
}
