package com.renpho.erp.oms.application.returnorder.command;

import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.util.List;

/**
 * @desc: 新建RAM补发订单参数
 * @time: 2025-03-19 14:39:06
 * @author: Alina
 */
@Data
public class ResendOrderAddCmd {
	/**
	 * 渠道编码
	 */
	@NotEmpty(message = "SALE_ORDER_CREATE_CHANNEL_CODE_REQUIRE")
	private String channelCode;

	/**
	 * 销售渠道名称
	 */
	private String channelName;

	/**
	 * 店铺ID
	 */
	@NotNull(message = "SALE_ORDER_CREATE_STORE_ID_REQUIRE")
	private Integer storeId;

	/**
	 * 店铺名称
	 */
	private String storeName;

	/**
	 * 店铺单号（渠道单号）
	 */
	@NotEmpty(message = "SALE_ORDER_CREATE_CHANNEL_ORDER_NO_REQUIRE")
	@Size(max = 32, message = "SALE_ORDER_CREATE_CHANNEL_ORDER_NO_TOO_LONG")
	private String channelOrderNo;

	/**
	 * 备注字段
	 */
	private String remark;

	/**
	 * 订单行
	 */
	@Valid
	@NotEmpty(message = "ITEM_DETAIL_NOT_EMPTY")
	private List<ResendOrderItemAddCmd> items;

	@AssertTrue(message = "RESEND_QUANTITY_REQUIRE")
	public boolean isValidItems() {
		return items != null && items.stream().anyMatch(item -> item.getResendQuantity() != null && item.getResendQuantity() > 0);
	}
}
