<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.renpho.erp.oms.infrastructure.persistence.b2b.order.mapper.B2bOrderItemMapper">



    <!-- 根据订单ID集合批量查询所有商品信息 -->
    <select id="getByOrderIds" resultType="com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderItemPO">
        SELECT
            id,
            order_id,
            psku,
            image_id,
            psku_name_zh,
            psku_name_en,
            barcode,
            ordered_quantity,
            shipped_quantity,
            number_of_units_per_box,
            unit_price,
            tax_rate,
            tax,
            sub_total,
            create_time,
            create_by,
            update_time,
            update_by,
            is_deleted
        FROM oms_b2b_order_item
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND is_deleted = 0
        ORDER BY order_id, id
    </select>

    <!-- 根据订单ID统计商品数量 -->
    <select id="getItemCountByOrderIds" resultType="com.renpho.erp.oms.infrastructure.persistence.b2b.order.po.B2bOrderItemPO">
        SELECT
            order_id,
            COUNT(*) as ordered_quantity
        FROM oms_b2b_order_item
        WHERE order_id IN
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>
        AND is_deleted = 0
        GROUP BY order_id
    </select>

</mapper>
