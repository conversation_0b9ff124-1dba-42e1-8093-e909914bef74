package com.renpho.erp.oms.application.vc;

import com.renpho.erp.apiproxy.amazon.model.ShopAccount;
import com.renpho.erp.apiproxy.amazon.model.vendororders.GetPurchaseOrdersRequest;
import com.renpho.erp.apiproxy.amazon.model.vendororders.GetPurchaseOrdersStatusRequest;
import com.renpho.erp.oms.application.vc.order.job.PullVcOrderJob;
import com.renpho.erp.oms.infrastructure.feign.proxy.AmazonClient;
import com.renpho.karma.dto.R;
import io.swagger.client.model.vendoRetailProcurement.orders.GetPurchaseOrdersResponse;
import io.swagger.client.model.vendoRetailProcurement.orders.GetPurchaseOrdersStatusResponse;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * <AUTHOR>
 * @description
 * @date 2025/7/7 15:42
 */
@SpringBootTest
public class PullVcOrderJobTest {

    @Resource
    private PullVcOrderJob pullVcOrderJob;

    @Resource
    private  AmazonClient amazonClient;

    @Test
    void pullVcOrderJob() {
        pullVcOrderJob.pullVcOrder();
//        ShopAccount shopAccount = new ShopAccount();
//        shopAccount.setSellerId("amzn1.vg.7414282");
//        GetPurchaseOrdersRequest getPurchaseOrdersRequest = new GetPurchaseOrdersRequest();
//        R<GetPurchaseOrdersResponse> purchaseOrders = amazonClient.getPurchaseOrders(shopAccount, getPurchaseOrdersRequest);
//
//        GetPurchaseOrdersStatusRequest request = new GetPurchaseOrdersStatusRequest();
//        request.setPurchaseOrderNumber("8OC3K4NU");
//
//        R<GetPurchaseOrdersStatusResponse> purchaseOrdersStatus = amazonClient.getPurchaseOrdersStatus(shopAccount, request);
//        System.out.printf(purchaseOrdersStatus+"");
    }
}
