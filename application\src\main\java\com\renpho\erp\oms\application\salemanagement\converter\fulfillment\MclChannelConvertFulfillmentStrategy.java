package com.renpho.erp.oms.application.salemanagement.converter.fulfillment;

import java.util.List;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.channelmanagement.ChannelFulfillment;
import com.renpho.erp.oms.domain.channelmanagement.mercado.repository.MclShipmentRepository;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderRepository;
import com.renpho.erp.oms.infrastructure.feign.dto.StoreDTO;
import com.renpho.erp.oms.infrastructure.feign.mdm.StoreClient;

/**
 * 美客多渠道订单履约信息转换-策略实现类
 * <AUTHOR>
 */
@Component
public class MclChannelConvertFulfillmentStrategy extends AbstractChannelConvertFulfillmentStrategy {

	private final MclShipmentRepository mclShipmentRepository;

	public MclChannelConvertFulfillmentStrategy(MclShipmentRepository mclShipmentRepository, SaleOrderRepository saleOrderRepository,
			StoreClient storeClient) {
		super(saleOrderRepository, storeClient);
		this.mclShipmentRepository = mclShipmentRepository;
	}

	@Override
	public SaleChannelType getChannelType() {
		return SaleChannelType.MERCADO;
	}

	@Override
	public FulfillmentType getFulfillmentType() {
		return FulfillmentType.PLATFORM;
	}

	@Override
	public List<ChannelFulfillment> getChannelFulfillmentList(StoreDTO storeDTO, Set<String> channelFulfillmentIds, Integer pageSize,
			Long lastId) {
		return mclShipmentRepository.findUnCompletedShipments(storeDTO.getStoreId(), channelFulfillmentIds, pageSize, lastId);
	}

}
