package com.renpho.erp.oms.application.common.remark.fetcher;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.renpho.erp.oms.application.common.remark.convert.OrderRemarkHistoryConvert;
import com.renpho.erp.oms.application.common.remark.vo.OrderRemarkHistoryVO;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.mapper.ReturnOrderRemarkMapper;
import com.renpho.erp.oms.infrastructure.persistence.returnorder.po.ReturnOrderRemarkPO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @desc: 退货订单备注获取器
 * @time: 2025-06-20 15:01:21
 * @author: Alina
 */
@Component
@RequiredArgsConstructor
public class ReturnOrderRemarkFetcher implements RemarkFetcher<ReturnOrderRemarkPO, OrderRemarkHistoryVO> {
    private final ReturnOrderRemarkMapper returnOrderRemarkMapper;

    private final OrderRemarkHistoryConvert convert;

    /**
     * 订单备注获取
     *
     * @param bizId 订单ID
     * @return 订单备注
     */
    @Override
    public List<ReturnOrderRemarkPO> fetchRemarks(Long bizId) {
        return returnOrderRemarkMapper.selectList(Wrappers.<ReturnOrderRemarkPO>lambdaQuery()
                .eq(ReturnOrderRemarkPO::getReturnOrderId, bizId)
                .orderByAsc(ReturnOrderRemarkPO::getId));
    }

    /**
     * 订单备注转换
     *
     * @param remark 订单备注
     * @return 订单备注
     */
    @Override
    public OrderRemarkHistoryVO toVO(ReturnOrderRemarkPO remark) {
        return convert.toVO(remark);
    }
}
