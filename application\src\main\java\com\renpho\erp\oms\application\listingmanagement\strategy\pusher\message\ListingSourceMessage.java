package com.renpho.erp.oms.application.listingmanagement.strategy.pusher.message;

import lombok.Getter;
import lombok.Setter;

/**
 * Listing MQ消息体
 * <AUTHOR>
 */
@Getter
@Setter
public class ListingSourceMessage<T> {

	/**
	 * 主键id
	 */
	private Long id;

	/**
	 * 销售渠道ID
	 */
	private Integer salesChannelId;

	/**
	 * 销售渠道编码
	 */
	private String salesChannelCode;

	/**
	 * 店铺ID
	 */
	private Integer storeId;

	/**
	 * 销售渠道 listing id
	 */
	private String listingId;

	/**
	 * 具体渠道对应的报文，例：美可多：MercadoListingSkuMessage，Tiktok：TiktokListingSkuMessage
	 */
	private T t;

}
