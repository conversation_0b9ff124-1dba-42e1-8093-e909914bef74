package com.renpho.erp.oms.application.ordershipaudit.strategy.logistics;

import com.renpho.erp.apiproxy.ebay.model.ShopAccount;
import com.renpho.erp.apiproxy.ebay.model.logistics.CreateFromShippingQuoteRequest;
import com.renpho.erp.apiproxy.ebay.model.logistics.CreateFromShippingQuoteResponse;
import com.renpho.erp.apiproxy.ebay.model.logistics.DownloadLabelFileResponse;
import com.renpho.erp.mdm.client.store.vo.StoreAuthorizationVo;
import com.renpho.erp.oms.application.ordershipaudit.service.UploadPdfService;
import com.renpho.erp.oms.domain.ordershipaudit.OrderShipAuditAggRoot;
import com.renpho.erp.oms.domain.ordershipaudit.model.LogisticsShipmentAggRoot;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.feign.proxy.EbayClient;
import com.renpho.karma.dto.R;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Set;

@Slf4j
@Component
@RequiredArgsConstructor
public class EbayLogisticsStrategy extends AbstractPlatformLogisticsStrategy {
    private final EbayClient ebayClient;
    private final UploadPdfService uploadPdfService;


    @Override
    public Set<FulfillmentServiceType> getFulfillmentServiceType() {
        return Set.of(FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE);
    }


    @Override
    protected void createLogisticsShipment(OrderShipAuditAggRoot shipAuditAggRoot, SaleOrderAggRoot saleOrderAggRoot, StoreAuthorizationVo storeAuthorizationVo, LogisticsShipmentAggRoot logisticsShipmentAggRoot) {
        // 构建参数
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setSellerId(storeAuthorizationVo.getAuthorization().getEbaySellerId());
        shopAccount.setSiteCode(storeAuthorizationVo.getSiteCode());

        CreateFromShippingQuoteRequest createFromShippingQuoteRequest = new CreateFromShippingQuoteRequest();
        createFromShippingQuoteRequest.setShippingQuoteId(shipAuditAggRoot.getShippingQuoteId());
        createFromShippingQuoteRequest.setRateId(shipAuditAggRoot.getRateId());
        R<CreateFromShippingQuoteResponse> createFromShippingQuoteResponseR = ebayClient.createFromShippingQuote(shopAccount, createFromShippingQuoteRequest);


        if (!createFromShippingQuoteResponseR.isSuccess() || Objects.isNull(createFromShippingQuoteResponseR.getData()) || CollectionUtils.isNotEmpty(createFromShippingQuoteResponseR.getData().getErrors())) {
            log.error("订单{}创建ebay面单失败，原因为{}", saleOrderAggRoot.getOrderNo(), createFromShippingQuoteResponseR.getMessage());
            throw new BusinessException("创建eBay面单失败");
        }
        CreateFromShippingQuoteResponse data = createFromShippingQuoteResponseR.getData();
        // 存储 shipmentId 物流单号
        //shipmentTrackingNumber 跟踪号
        logisticsShipmentAggRoot.toSuccess(data.getShipmentId(), data.getShipmentTrackingNumber(), data.getLabelDownloadUrl());
        shipAuditAggRoot.createSuccess(null);


    }

    @Override
    protected void processLogisticsResult(LogisticsShipmentAggRoot logisticsShipmentAggRoot, StoreAuthorizationVo storeAuthorizationVo) {
        ShopAccount shopAccount = new ShopAccount();
        shopAccount.setSellerId(storeAuthorizationVo.getAuthorization().getEbaySellerId());
        R<DownloadLabelFileResponse> downLoadResponse = ebayClient.downloadLabelFile(shopAccount, logisticsShipmentAggRoot.getLogisticsOrderNo());
        if (!downLoadResponse.isSuccess() || Objects.isNull(downLoadResponse.getData()) || CollectionUtils.isNotEmpty(downLoadResponse.getData().getErrors())) {
            log.error("eBay面单下载失败，物流单号{}，原因{}", logisticsShipmentAggRoot.getLogisticsOrderNo(), downLoadResponse.getMessage());
            throw new BusinessException("eBay面单下载失败");
        }
        String s3Url = uploadPdfService.uploadPdfFromBytes(ArrayUtils.toObject(downLoadResponse.getData().getContent()), logisticsShipmentAggRoot.getWaybillFilePlatformUrl());
        if (StringUtils.isEmpty(s3Url)) {
            throw new BusinessException("eBay面单上传失败");
        }

        logisticsShipmentAggRoot.markSuccess(s3Url, null);
    }

}
