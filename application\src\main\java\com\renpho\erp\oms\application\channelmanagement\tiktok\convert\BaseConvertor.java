package com.renpho.erp.oms.application.channelmanagement.tiktok.convert;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.oms.application.channelmanagement.tiktok.dto.TtOrderDTO;
import com.renpho.erp.oms.infrastructure.common.enums.TtAddressLevelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;

import java.util.Optional;

@Slf4j
public class BaseConvertor {
	private static ObjectMapper objectMapper = new ObjectMapper();

	static String convertState(String districtInfos) {
		return convertAddress(districtInfos, TtAddressLevelEnum.L1.name());
	}

	static String convertCity(String districtInfos) {
		return convertAddress(districtInfos, TtAddressLevelEnum.L3.name());
	}

	static String convertAddress(String districtInfos, String level) {
		if (StringUtils.isEmpty(districtInfos)) {
			return null;
		}

		List<TtOrderDTO.DistrictInfo> districtInfoList = null;
		try {
			districtInfoList = objectMapper.readValue(districtInfos, new TypeReference<>() {
			});
		}
		catch (JsonProcessingException e) {
			log.error("地址信息转换失败{}", e);
			return null;
		}
		Optional<TtOrderDTO.DistrictInfo> l1DistrictInfo = districtInfoList.stream()
			.filter(districtInfo -> level.equals(districtInfo.getAddress_level()))
			.findFirst(); // 用 findFirst() 代替 isPresent()

		// 如果找到符合条件的 DistrictInfo，设置对应的省/州名称
		if (l1DistrictInfo.isPresent()) {
			return l1DistrictInfo.get().getAddress_name();
		}
		return null;
	}

	static BigDecimal convertTax(String taxInfo) {
		if (StringUtils.isEmpty(taxInfo)) {
			return BigDecimal.ZERO;
		}

		List<TtOrderDTO.Tax> taxList = null;
		try {
			taxList = objectMapper.readValue(taxInfo, new TypeReference<>() {
			});
		}
		catch (JsonProcessingException e) {
			log.error("地址信息转换失败{}", e);
			return null;
		}
		if (CollectionUtil.isNotEmpty(taxList)) {
			return taxList.stream()
				.map(tax -> new BigDecimal(Optional.ofNullable(tax.getTax_amount()).orElse("0")))
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		}

		return BigDecimal.ZERO;
	}

}
