package com.renpho.erp.oms.application.b2b.customer.command;

import jakarta.validation.constraints.Min;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 客户保险授信信息命令
 */
@Data
public class CustomerInsuranceCreditCommand {

    private Long id;

    /**
     * 保险公司
     */
    private String insuranceCompany;

    /**
     * 投保金额
     */
    @Min(value = 0 , message = "CUSTOMER_CREATE_INSURANCE_CREDIT_INSURED_AMOUNT_LESS_THAN_ZERO")
    private BigDecimal insuredAmount;

    /**
     * 投保币种
     */
    private String insuredCurrency;

    /**
     * 有效期开始时间
     */
    private LocalDateTime validTimeStart;

    /**
     * 有效期结束时间
     */
    private LocalDateTime validTimeEnd;
} 