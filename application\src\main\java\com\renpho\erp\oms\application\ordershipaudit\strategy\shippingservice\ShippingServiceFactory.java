package com.renpho.erp.oms.application.ordershipaudit.strategy.shippingservice;

import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import org.springframework.util.Assert;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

/**
 * @desc: 查询发货方式报价工厂
 * @time: 2025-04-22 18:05:32
 * @author: Alina
 */

public class ShippingServiceFactory {

	private static final Map<Integer, ShippingServiceStrategy> STRATEGY_MAP = new HashMap<>(16);

	/**
	 * 注册.
	 * @param fulfillmentServiceType 履约服务类型
	 */
	public static void register(FulfillmentServiceType fulfillmentServiceType, ShippingServiceStrategy shippingServiceStrategy) {
		Assert.notNull(fulfillmentServiceType, "");
		STRATEGY_MAP.put(fulfillmentServiceType.getValue(), shippingServiceStrategy);
	}

	/**
	 * 获取
	 * @param fulfillmentServiceType 履约服务类型
	 * @return ShippingServiceStrategy
	 */
	public static ShippingServiceStrategy get(FulfillmentServiceType fulfillmentServiceType) {
		Assert.notNull(fulfillmentServiceType, "获取查询发货方式报价策略，履约服务类型不能为空");
		if (STRATEGY_MAP.containsKey(fulfillmentServiceType.getValue())) {
			return STRATEGY_MAP.get(fulfillmentServiceType.getValue());
		}
		throw new RuntimeException(MessageFormat.format("获取查询发货方式报价策略，找不到 {0} 转换器", fulfillmentServiceType.getName()));
	}
}
