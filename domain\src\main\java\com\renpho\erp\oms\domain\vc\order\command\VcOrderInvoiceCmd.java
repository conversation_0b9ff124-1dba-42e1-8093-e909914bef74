package com.renpho.erp.oms.domain.vc.order.command;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * VC订单开票命令
 *
 * <AUTHOR>
 */
@Data
public class VcOrderInvoiceCmd {

    /**
     * 订单ID
     */
    @NotNull(message = "订单ID不能为空")
    private Long orderId;

    /**
     * 发票号
     */
    @NotBlank(message = "发票号不能为空")
    private String invoiceNo;

    /**
     * 税率
     */
    @NotNull(message = "税率不能为空")
    @Positive(message = "税率必须为正数")
    private BigDecimal taxRate;

    /**
     * 开票时间
     */
    @NotNull(message = "开票时间不能为空")
    private LocalDate invoiceDate;

    /**
     * 发票金额
     */
    @NotNull(message = "发票金额不能为空")
    @Positive(message = "发票金额必须为正数")
    private BigDecimal invoiceAmount;
}