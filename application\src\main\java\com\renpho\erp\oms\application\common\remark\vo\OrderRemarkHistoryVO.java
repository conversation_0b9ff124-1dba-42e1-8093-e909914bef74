package com.renpho.erp.oms.application.common.remark.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.vo.VO;
import com.renpho.erp.oms.infrastructure.common.trans.RemoteTransType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: 订单备注历史VO
 * @time: 2025-06-20 14:39:33
 * @author: Alina
 */
@Data
public class OrderRemarkHistoryVO implements VO {
    /**
     * 主键
     */
    private Long id;
    /**
     * 更新时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人ID
     */
    @Trans(type = RemoteTransType.REMOTE, targetClassName = "OumUserInfoRes.class", refs = {"creator", "creatorNo"}, fields = {"name", "code"},
            uniqueField = "id")
    private Integer createBy;

    /**
     * 更新人
     */
    private String creator;

    /**
     * 更新人工号
     */
    private String creatorNo;

    /**
     * 备注信息
     */
    private String msg;
}
