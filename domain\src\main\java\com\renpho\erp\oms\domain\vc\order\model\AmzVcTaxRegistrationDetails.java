package com.renpho.erp.oms.domain.vc.order.model;

import org.jmolecules.ddd.annotation.ValueObject;

import lombok.Builder;
import lombok.Getter;

/**
 * 亚马逊VC税务登记详情值对象 基于Amazon SP-API TaxRegistrationDetails模型设计
 * <AUTHOR>
 */
@Getter
@Builder
@ValueObject
public class AmzVcTaxRegistrationDetails {

	/**
	 * 税务登记类型 对应API字段：taxRegistrationType 可能的值：VAT, GST, TIN, PAN, CPF, CNPJ等
	 */
	private String taxRegistrationType;

	/**
	 * 税务登记号 对应API字段：taxRegistrationNumber
	 */
	private String taxRegistrationNumber;

	/**
	 * 税务登记地址 对应API字段：taxRegistrationAddress
	 */
	private AmzVcAddress taxRegistrationAddress;

	/**
	 * 检查税务登记信息是否完整
	 * @return boolean
	 */
	public boolean isComplete() {
		return taxRegistrationType != null && !taxRegistrationType.trim().isEmpty() && taxRegistrationNumber != null
				&& !taxRegistrationNumber.trim().isEmpty();
	}

	/**
	 * 检查是否为VAT登记
	 * @return boolean
	 */
	public boolean isVATRegistration() {
		return "VAT".equals(taxRegistrationType);
	}

	/**
	 * 检查是否为GST登记
	 * @return boolean
	 */
	public boolean isGSTRegistration() {
		return "GST".equals(taxRegistrationType);
	}

	/**
	 * 检查是否为TIN登记
	 * @return boolean
	 */
	public boolean isTINRegistration() {
		return "TIN".equals(taxRegistrationType);
	}

	/**
	 * 检查是否有税务登记地址
	 * @return boolean
	 */
	public boolean hasTaxRegistrationAddress() {
		return taxRegistrationAddress != null;
	}

	/**
	 * 获取税务登记信息摘要
	 * @return String
	 */
	public String getTaxRegistrationSummary() {
		if (!isComplete()) {
			return null;
		}

		StringBuilder summary = new StringBuilder();
		summary.append(taxRegistrationType).append(": ").append(taxRegistrationNumber);

		if (hasTaxRegistrationAddress()) {
			String address = taxRegistrationAddress.getShortAddress();
			if (address != null && !address.trim().isEmpty()) {
				summary.append(" (").append(address).append(")");
			}
		}

		return summary.toString();
	}

}
