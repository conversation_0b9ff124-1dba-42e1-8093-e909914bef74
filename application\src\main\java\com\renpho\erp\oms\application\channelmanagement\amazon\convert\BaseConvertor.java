package com.renpho.erp.oms.application.channelmanagement.amazon.convert;

import cn.hutool.core.collection.CollectionUtil;

import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.shenyu.common.utils.JsonUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

public class BaseConvertor {
	// 自定义方法
	static LocalDateTime convertDate(String param) {
		if (StringUtils.isEmpty(param)) {
			return null;
		}
		return DateUtil.parse(param);
	}

	static String convertJsonStr(List param) {
		if (CollectionUtil.isEmpty(param)) {
			return null;
		}
		return JsonUtils.toJson(param);
	}

	static String convertJsonStr(Object param) {
		if (Objects.isNull(param)) {
			return null;
		}
		return JsonUtils.toJson(param);
	}

	static BigDecimal convertToBigDecimal(String param) {
		if (StringUtils.isEmpty(param)) {
			return null;
		}
		return new BigDecimal(param);
	}

	static Integer convertToInteger(String param) {
		if (StringUtils.isEmpty(param)) {
			return null;
		}
		return Integer.valueOf(param);
	}

	static Boolean stringToBoolean(String value) {
		if (StringUtils.isEmpty(value)) {
			return null;
		}
		if ("true".equalsIgnoreCase(value)) {
			return true;
		}
		else if ("false".equalsIgnoreCase(value)) {
			return false;
		}
		else {
			return null;
		}
	}

}
