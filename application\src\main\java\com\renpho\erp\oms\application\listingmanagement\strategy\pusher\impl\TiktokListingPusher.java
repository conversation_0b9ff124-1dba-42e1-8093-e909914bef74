package com.renpho.erp.oms.application.listingmanagement.strategy.pusher.impl;

import com.renpho.erp.apiproxy.tiktok.model.product.SearchProductsData;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.AbstractListingPusherTemplate;
import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.message.TiktokListingSkuMessage;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * Tiktok 推送器
 * <AUTHOR>
 */
@Component
public class TiktokListingPusher extends AbstractListingPusherTemplate<SearchProductsData.Product, List<TiktokListingSkuMessage>> {

	/**
	 * Tiktok 渠道编码
	 * @return String
	 */
	@Override
	public String getSalesChannelCode() {
		return ChannelCode.TT_CHANNEL_CODE;
	}

	@Override
	protected List<TiktokListingSkuMessage> parseListingSource(SearchProductsData.Product product) {
		// sku列表
		List<SearchProductsData.Sku> skus = Optional.ofNullable(product).map(SearchProductsData.Product::getSkus).orElse(List.of());
		return skus.stream().map(s -> {
			TiktokListingSkuMessage tiktokListingSkuMessage = new TiktokListingSkuMessage();
			tiktokListingSkuMessage.setSkuId(s.getId());
			return tiktokListingSkuMessage;
		}).toList();
	}
}
