package com.renpho.erp.oms.adapter.web.controller.b2b;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.renpho.erp.fms.client.ap.fundaccount.command.FundAccountClientQuery;
import com.renpho.erp.fms.client.ap.fundaccount.vo.FundAccountClientCurrencyVO;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.AddReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.CancelReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.ConfirmReceiptOrderCmd;
import com.renpho.erp.oms.application.b2b.receiptOrder.command.SearchB2bOrderQuery;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.B2bReceiptOrderQueryService;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.B2bReceiptOrderService;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.query.B2bReceiptOrderPageQuery;
import com.renpho.erp.oms.application.b2b.receiptOrder.service.vo.B2bReceiptOrderPageVo;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.ReceiptOrderDetailVO;
import com.renpho.erp.oms.application.b2b.receiptOrder.vo.SearchB2bOrderBaseInfoVO;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.feign.fms.FmsFundAccountClient;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.shenyu.client.springcloud.annotation.ShenyuSpringCloudClient;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * b2b收款单
 * <AUTHOR>
 * @description
 * @date 2025/6/20 10:36
 */
@Slf4j
@RestController
@ShenyuSpringCloudClient("/b2bReceiptOrder/**")
@RequestMapping("/b2bReceiptOrder")
@AllArgsConstructor
@Tag(name = "b2b收款单", description = "b2b收款单")
public class B2bReceiptOrderController {

    private B2bReceiptOrderQueryService b2bReceiptOrderQueryService;

    private FmsFundAccountClient fmsFundAccountClient;

    private final B2bReceiptOrderService b2bReceiptOrderService;

    /**
     * b2b收款单列表分页查询
     * @param query 查询条件
     * @return 是否成功
     */
    @PostMapping("/page")
    @PreAuthorize(PermissionConstant.B2BReceiptOrder.PAGE)
    public R<Paging<B2bReceiptOrderPageVo>> page(@RequestBody B2bReceiptOrderPageQuery query) {
        return R.success(b2bReceiptOrderQueryService.page(query));
    }


    /**
     * 导出
     * @param query
     * @param response
     * @throws IOException
     */
    @PostMapping("/export")
    @PreAuthorize(PermissionConstant.B2BReceiptOrder.EXPORT)
    public void export(@RequestBody B2bReceiptOrderPageQuery query, HttpServletResponse response) throws IOException {
        b2bReceiptOrderQueryService.export(query, response);
    }

    /**
     * 付款账号
     * @param fundAccountClientQuery
     * @return
     */
    @PostMapping("/getFundAccountCurrency")
    public R<List<FundAccountClientCurrencyVO>> bankList(@RequestBody FundAccountClientQuery fundAccountClientQuery) {
        List<FundAccountClientCurrencyVO> fundAccountClientCurrencyVOS = ObjectUtil.defaultIfNull(fmsFundAccountClient.getFundAccountCurrency(fundAccountClientQuery), Collections.emptyList());
        fundAccountClientCurrencyVOS.sort(Comparator.comparing(item -> BooleanUtil.toInt(Objects.equals(((FundAccountClientCurrencyVO) item).getFundAccountStatus(), 1))).reversed());
        return R.success(fundAccountClientCurrencyVOS);
    }


    /**
     * 查询b2b基础信息
     * @param searchB2bOrderQuery
     * @return
     */
    @PostMapping("/searchB2bOrderBaseInfo")
    public R<SearchB2bOrderBaseInfoVO> searchB2bOrderBaseInfo(@RequestBody @Valid SearchB2bOrderQuery searchB2bOrderQuery) {
        return R.success(b2bReceiptOrderService.searchB2bOrderBaseInfo(searchB2bOrderQuery));
    }


    /**
     * 添加收款单
     * @param addReceiptOrderCmd
     * @return
     */
    @PostMapping("/add")
    @PreAuthorize(PermissionConstant.B2BReceiptOrder.ADD)
    public R addReceiptOrder(@RequestBody @Valid AddReceiptOrderCmd addReceiptOrderCmd) {
        b2bReceiptOrderService.addReceiptOrder(addReceiptOrderCmd);
        return R.success();
    }

    /**
     * 查询收款详情
     * @param id
     * @return
     */
    @GetMapping("/detail")
    public R<ReceiptOrderDetailVO> queryReceiptOrderDetailVO(@RequestParam Long id) {
        return R.success( b2bReceiptOrderService.queryReceiptOrderDetailVO(id));
    }

    /**
     * 确认收款单
     * @param confirmReceiptOrderCmd
     * @return
     */
    @PostMapping("/confirm")
    @PreAuthorize(PermissionConstant.B2BReceiptOrder.CONFIRM)
    public R confirmReceiptOrder(@RequestBody @Valid ConfirmReceiptOrderCmd confirmReceiptOrderCmd) {
        b2bReceiptOrderService.confirmReceiptOrder(confirmReceiptOrderCmd);
        return R.success();
    }

    /**
     * 取消收款单
     * @param cancelReceiptOrderCmd
     * @return
     */
    @PostMapping("/cancel")
    @PreAuthorize(PermissionConstant.B2BReceiptOrder.CANCEL)
    public R cancelReceiptOrder(@RequestBody @Valid CancelReceiptOrderCmd cancelReceiptOrderCmd) {
        b2bReceiptOrderService.cancelReceiptOrder(cancelReceiptOrderCmd);
        return R.success();
    }

}
