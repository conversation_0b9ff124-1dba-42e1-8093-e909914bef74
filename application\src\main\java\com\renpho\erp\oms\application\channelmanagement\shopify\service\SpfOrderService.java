package com.renpho.erp.oms.application.channelmanagement.shopify.service;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.lock.annotation.Lock4j;
import com.renpho.erp.oms.application.optlog.OmsSystemModule;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderLineItemAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderLineItemLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderShippingLineAppConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.convert.SpfOrderShippingLineLogConvertor;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderDTO;
import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderLog;
import com.renpho.erp.oms.application.channelmanagement.shopify.optlog.SpfOrderAddSnapSource;
import com.renpho.erp.oms.application.channelmanagement.shopify.optlog.SpfOrderBusinessModule;
import com.renpho.erp.oms.application.channelmanagement.shopify.optlog.SpfOrderUpdateSnapSource;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrder;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderLineItem;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrderShippingLine;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderLineItemRepository;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderRepository;
import com.renpho.erp.oms.domain.channelmanagement.shopify.repository.SpfOrderShippingLineRepository;
import com.renpho.erp.oplog.log.OpLog;
import com.renpho.erp.oplog.log.enums.BusinessType;
import com.renpho.karma.dto.R;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@AllArgsConstructor
public class SpfOrderService {
	private final SpfOrderRepository spfOrderRepository;
	private final SpfOrderLineItemRepository spfOrderLineItemRepository;
	private final SpfOrderShippingLineRepository spfOrderShippingLineRepository;
	private final SpfOrderLogConvertor spfOrderLogConvertor;
	private final SpfOrderLineItemLogConvertor spfOrderLineItemLogConvertor;
	private final SpfOrderShippingLineLogConvertor spfOrderShippingLineLogConvertor;

	@Lock4j(name = "oms:shopify:sync", keys = { "#spfOrderDTO.id", "#spfOrderDTO.shopUrl" })
	public void createOrUpdate(SpfOrderDTO spfOrderDTO) {
		// 解析成平台单
		SpfOrder spfOrder = SpfOrderAppConvertor.toDomain(spfOrderDTO);
		List<SpfOrderLineItem> spfOrderLineItemList = SpfOrderLineItemAppConvertor.toDomainList(spfOrderDTO.getLine_items());
		List<SpfOrderShippingLine> spfOrderShippingLineList = SpfOrderShippingLineAppConvertor
			.toDomainList(spfOrderDTO.getShipping_lines());
		// 查询有没有
		SpfOrder exitsOrder = this.spfOrderRepository.getByUniqueIndex(spfOrderDTO.getId(), spfOrderDTO.getShopUrl());
		// 更新插入
		if (Objects.nonNull(exitsOrder)) {
			spfOrder.setId(exitsOrder.getId());
			SpringUtil.getBean(SpfOrderService.class).doUpdate(spfOrder, spfOrderLineItemList, spfOrderShippingLineList);
		}
		else {
			SpringUtil.getBean(SpfOrderService.class).doCreate(spfOrder, spfOrderLineItemList, spfOrderShippingLineList);
		}

	}

	@Transactional
	@OpLog(snaptSource = SpfOrderAddSnapSource.class, title = "新增Shopify订单", businessType = BusinessType.INSERT,
			businessModule = SpfOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doCreate(SpfOrder spfOrder, List<SpfOrderLineItem> itemList, List<SpfOrderShippingLine> spfOrderShippingLineList) {
		// 插入订单
		Long orderId = this.spfOrderRepository.insert(spfOrder);
		// 插入商品
		this.spfOrderLineItemRepository.batchInsert(itemList, orderId);
		// 插入发货行
		this.spfOrderShippingLineRepository.batchInsert(spfOrderShippingLineList, orderId);
		// 返回主键增加日志
		return R.success(orderId);
	}

	@Transactional
	@OpLog(snaptSource = SpfOrderUpdateSnapSource.class, title = "更新Shopify订单", businessType = BusinessType.UPDATE,
			businessModule = SpfOrderBusinessModule.class, systemModule = OmsSystemModule.class)
	public R<Long> doUpdate(SpfOrder spfOrder, List<SpfOrderLineItem> itemList, List<SpfOrderShippingLine> spfOrderShippingLineList) {
		// 先删除原有商品
		this.spfOrderLineItemRepository.deleteByOrderId(spfOrder.getId());
		// 先删除原有发货行
		this.spfOrderShippingLineRepository.deleteByOrderId(spfOrder.getId());
		// 插入商品
		this.spfOrderLineItemRepository.batchInsert(itemList, spfOrder.getId());
		// 插入发货行
		this.spfOrderShippingLineRepository.batchInsert(spfOrderShippingLineList, spfOrder.getId());
		// 更新订单
		Long orderId = this.spfOrderRepository.update(spfOrder);
		// 返回主键增加日志
		return R.success(orderId);
	}

	public SpfOrderLog getLogById(Long orderId) {
		SpfOrder spfOrder = this.spfOrderRepository.getById(orderId);
		if (Objects.isNull(spfOrder)) {
			return null;
		}
		else {
			SpfOrderLog spfOrderLog = this.spfOrderLogConvertor.toLog(spfOrder);
			List<SpfOrderLineItem> spfOrderLineItemList = this.spfOrderLineItemRepository.listByOrderId(orderId);
			spfOrderLog.setSpfOrderLineItemLogList(spfOrderLineItemList.stream().map((spfOrderLineItem) -> {
				return spfOrderLineItemLogConvertor.toLog(spfOrderLineItem);
			}).collect(Collectors.toList()));
			List<SpfOrderShippingLine> spfOrderShippingLineList = this.spfOrderShippingLineRepository.listByOrderId(orderId);
			spfOrderLog.setSpfOrderShippingLineLogList(spfOrderShippingLineList.stream().map((spfOrderShippingLine) -> {
				return spfOrderShippingLineLogConvertor.toLog(spfOrderShippingLine);
			}).collect(Collectors.toList()));
			return spfOrderLog;
		}
	}
}