package com.renpho.erp.oms.application.b2b.order.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @desc: B2B订单备注
 * @time: 2025-06-20 10:37:49
 * @author: <PERSON><PERSON>
 */
@Data
public class B2bOrderRemarkVO {
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private Integer createBy;
    /**
     * 更新人
     */
    private String creator;

    /**
     * 更新人工号
     */
    private String creatorNo;

    /**
     * 备注信息
     */
    private String remark;
}
