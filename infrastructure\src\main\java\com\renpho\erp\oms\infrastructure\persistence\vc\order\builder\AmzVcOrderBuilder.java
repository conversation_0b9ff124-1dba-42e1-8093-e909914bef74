package com.renpho.erp.oms.infrastructure.persistence.vc.order.builder;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.StreamSupport;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.renpho.erp.oms.domain.vc.order.model.*;
import com.renpho.erp.oms.infrastructure.persistence.vc.order.po.AmzVcOrderSourceJsonPO;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 亚马逊VC订单-值对象 构建器
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AmzVcOrderBuilder {

	private final ObjectMapper objectMapper;

	/**
	 * 解析JSON并构建亚马逊VC订单
	 * @param po PO对象
	 * @return AmzVcOrder
	 */
	public AmzVcOrder build(AmzVcOrderSourceJsonPO po) {
		try {
			// 初始化Builder
			AmzVcOrder.AmzVcOrderBuilder builder = AmzVcOrder.builder()
				.id(po.getId())
				.storeId(po.getStoreId())
				.purchaseOrderNumber(po.getPurchaseOrderNumber())
				.failCount(po.getFailCount());

			// 解析订单JSON
			if (po.getOrderJson() != null) {
				JsonNode orderNode = objectMapper.readTree(po.getOrderJson());
				this.fillOrderInfo(builder, orderNode);
			}

			// 解析订单状态JSON
			if (po.getOrderStatusJson() != null) {
				JsonNode statusNode = objectMapper.readTree(po.getOrderStatusJson());
				this.fillOrderStatus(builder, statusNode);
			}

			return builder.build();
		}
		catch (Exception e) {
			log.error("Failed to parse JSON for order {}", po.getPurchaseOrderNumber(), e);
			return null;
		}
	}

	/**
	 * 填充订单信息
	 * @param builder 聚合根Builder
	 * @param orderNode 订单JSON节点
	 */
	private void fillOrderInfo(AmzVcOrder.AmzVcOrderBuilder builder, JsonNode orderNode) {
		// 解析订单状态
		String purchaseOrderStateStr = this.getTextValue(orderNode, "purchaseOrderState");
		if (purchaseOrderStateStr != null) {
			builder.purchaseOrderState(AmzVcPurchaseOrderState.fromValue(purchaseOrderStateStr));
		}
		// 解析订单详情
		this.parseOrderDetails(builder, orderNode);

	}

	/**
	 * 填充订单状态
	 * @param builder 聚合根Builder
	 * @param statusNode 状态JSON节点
	 */
	private void fillOrderStatus(AmzVcOrder.AmzVcOrderBuilder builder, JsonNode statusNode) {
		// 构建订单状态对象
		AmzVcOrdersStatus ordersStatus = AmzVcOrdersStatus.builder()
			.purchaseOrderNumber(this.getTextValue(statusNode, "purchaseOrderNumber"))
			.purchaseOrderStatus(this.getTextValue(statusNode, "purchaseOrderStatus"))
			.purchaseOrderAcknowledgmentStatus(this.getTextValue(statusNode, "purchaseOrderAcknowledgmentStatus"))
			.itemReceiptStatus(this.getTextValue(statusNode, "itemReceiptStatus"))
			.itemConfirmationStatus(this.getTextValue(statusNode, "itemConfirmationStatus"))
			.orderAcknowledgmentStatus(this.getTextValue(statusNode, "orderAcknowledgmentStatus"))
			.orderItemStatus(this.getTextValue(statusNode, "orderItemStatus"))
			.purchaseOrderDate(this.getTextValue(statusNode, "purchaseOrderDate"))
			.lastUpdatedDate(this.getTextValue(statusNode, "lastUpdatedDate"))
			.sellingParty(this.parsePartyInfo(statusNode.get("sellingParty")))
			.shipToParty(this.parsePartyInfo(statusNode.get("shipToParty")))
			// 解析并填充订单商品状态列表
			.itemStatus(this.parseItemStatuses(statusNode.get("itemStatus")))
			.build();

		builder.ordersStatus(ordersStatus);
	}

	/**
	 * 解析订单商品
	 * @param itemsNode 商品JSON节点
	 * @return List<AmzVcOrderItem>
	 */
	private List<AmzVcOrderItem> parseOrderItems(JsonNode itemsNode) {
		if (itemsNode == null || !itemsNode.isArray()) {
			return Collections.emptyList();
		}

		List<AmzVcOrderItem> items = new ArrayList<>();
		for (JsonNode itemNode : itemsNode) {
			AmzVcOrderItem item = AmzVcOrderItem.builder()
				.itemSequenceNumber(this.getTextValue(itemNode, "itemSequenceNumber"))
				.amazonProductIdentifier(this.getTextValue(itemNode, "amazonProductIdentifier"))
				.vendorProductIdentifier(this.getTextValue(itemNode, "vendorProductIdentifier"))
				.orderedQuantity(this.parseOrderedQuantity(itemNode.get("orderedQuantity")))
				.isBackOrderAllowed(this.getBooleanValue(itemNode, "isBackOrderAllowed"))
				.netCost(this.parseMoney(itemNode.get("netCost")))
				.listPrice(this.parseMoney(itemNode.get("listPrice")))
				.build();
			items.add(item);
		}
		return items;
	}

	/**
	 * 解析订单商品状态列表
	 * @param itemStatusesNode 商品状态JSON节点
	 * @return List<AmzVcOrderItemStatus>
	 */
	private List<AmzVcOrderItemStatus> parseItemStatuses(JsonNode itemStatusesNode) {
		if (itemStatusesNode == null || !itemStatusesNode.isArray()) {
			return Collections.emptyList();
		}

		List<AmzVcOrderItemStatus> itemStatuses = new ArrayList<>();
		for (JsonNode itemStatusNode : itemStatusesNode) {
			AmzVcOrderItemStatus itemStatus = AmzVcOrderItemStatus.builder()
				.itemSequenceNumber(this.getTextValue(itemStatusNode, "itemSequenceNumber"))
				.buyerProductIdentifier(this.getTextValue(itemStatusNode, "buyerProductIdentifier"))
				.vendorProductIdentifier(this.getTextValue(itemStatusNode, "vendorProductIdentifier"))
				.netCost(this.parseStatusMoney(itemStatusNode.get("netCost")))
				.listPrice(this.parseStatusMoney(itemStatusNode.get("listPrice")))
				.orderedQuantity(this.parseOrderedQuantity(itemStatusNode.get("orderedQuantity")))
				.acknowledgementStatus(this.parseAcknowledgementStatus(itemStatusNode.get("acknowledgementStatus")))
				.receivingStatus(this.parseReceivingStatus(itemStatusNode.get("receivingStatus")))
				.build();
			itemStatuses.add(itemStatus);
		}
		return itemStatuses;
	}

	/**
	 * 解析单个参与方信息
	 * @param partyNode 参与方JSON节点
	 * @return AmzVcPartyInfo
	 */
	private AmzVcPartyInfo parsePartyInfo(JsonNode partyNode) {
		if (partyNode == null) {
			return null;
		}

		return AmzVcPartyInfo.builder()
			.partyId(this.getTextValue(partyNode, "partyId"))
			.address(this.parseAddress(partyNode.get("address")))
			.taxRegistrationDetails(this.parseTaxRegistrationDetails(partyNode.get("taxRegistrationDetails")))
			.build();
	}

	/**
	 * 解析地址信息
	 * @param addressNode 地址JSON节点
	 * @return AmzVcAddress
	 */
	private AmzVcAddress parseAddress(JsonNode addressNode) {
		if (addressNode == null) {
			return null;
		}

		return AmzVcAddress.builder()
			.name(this.getTextValue(addressNode, "name"))
			.addressLine1(this.getTextValue(addressNode, "addressLine1"))
			.addressLine2(this.getTextValue(addressNode, "addressLine2"))
			.addressLine3(this.getTextValue(addressNode, "addressLine3"))
			.city(this.getTextValue(addressNode, "city"))
			.county(this.getTextValue(addressNode, "county"))
			.district(this.getTextValue(addressNode, "district"))
			.stateOrRegion(this.getTextValue(addressNode, "stateOrRegion"))
			.postalCode(this.getTextValue(addressNode, "postalCode"))
			.countryCode(this.getTextValue(addressNode, "countryCode"))
			.phone(this.getTextValue(addressNode, "phone"))
			.build();
	}

	/**
	 * 解析税务登记详情
	 * @param taxNode 税务登记JSON节点
	 * @return AmzVcTaxRegistrationDetails
	 */
	private AmzVcTaxRegistrationDetails parseTaxRegistrationDetails(JsonNode taxNode) {
		if (taxNode == null) {
			return null;
		}

		return AmzVcTaxRegistrationDetails.builder()
			.taxRegistrationType(this.getTextValue(taxNode, "taxRegistrationType"))
			.taxRegistrationNumber(this.getTextValue(taxNode, "taxRegistrationNumber"))
			.taxRegistrationAddress(this.parseAddress(taxNode.get("taxRegistrationAddress")))
			.build();
	}

	/**
	 * 解析订单详情
	 * @param builder 聚合根Builder
	 * @param orderNode 订单JSON节点
	 */
	private void parseOrderDetails(AmzVcOrder.AmzVcOrderBuilder builder, JsonNode orderNode) {
		if (orderNode.has("orderDetails")) {
			JsonNode detailsNode = orderNode.get("orderDetails");

			AmzVcOrderDetails.AmzVcOrderDetailsBuilder detailsBuilder = AmzVcOrderDetails.builder()
				.purchaseOrderDate(this.getLocalDateTimeValue(detailsNode, "purchaseOrderDate"))
				.purchaseOrderChangedDate(this.getLocalDateTimeValue(detailsNode, "purchaseOrderChangedDate"))
				.purchaseOrderStateChangedDate(this.getLocalDateTimeValue(detailsNode, "purchaseOrderStateChangedDate"))
				.purchaseOrderType(this.getTextValue(detailsNode, "purchaseOrderType"))
				.dealCode(this.getTextValue(detailsNode, "dealCode"))
				.paymentMethod(this.getTextValue(detailsNode, "paymentMethod"))
				.buyingParty(this.parsePartyInfo(detailsNode.get("buyingParty")))
				.sellingParty(this.parsePartyInfo(detailsNode.get("sellingParty")))
				.shipToParty(this.parsePartyInfo(detailsNode.get("shipToParty")))
				.billToParty(this.parsePartyInfo(detailsNode.get("billToParty")))
				.shipWindow(this.getTextValue(detailsNode, "shipWindow"))
				.deliveryWindow(this.getTextValue(detailsNode, "deliveryWindow"))
				.items(this.parseOrderItems(detailsNode.get("items")));

			// 解析进口详情
			if (detailsNode.has("importDetails")) {
				JsonNode importNode = detailsNode.get("importDetails");
				AmzVcImportDetails importDetails = AmzVcImportDetails.builder()
					.methodOfPayment(this.getTextValue(importNode, "methodOfPayment"))
					.internationalCommercialTerms(this.getTextValue(importNode, "internationalCommercialTerms"))
					.portOfDelivery(this.getTextValue(importNode, "portOfDelivery"))
					.importContainers(this.getTextValue(importNode, "importContainers"))
					.shippingInstructions(this.getTextValue(importNode, "shippingInstructions"))
					.build();
				detailsBuilder.importDetails(importDetails);
			}

			builder.orderDetails(detailsBuilder.build());
		}
	}

	/**
	 * 获取文本值
	 * @param node JSON节点
	 * @param fieldName 字段名
	 * @return String
	 */
	private String getTextValue(JsonNode node, String fieldName) {
		if (node != null && node.has(fieldName)) {
			JsonNode fieldNode = node.get(fieldName);
			return fieldNode.isNull() ? null : fieldNode.asText();
		}
		return null;
	}

	/**
	 * 获取布尔值
	 * @param node JSON节点
	 * @param fieldName 字段名
	 * @return Boolean
	 */
	private Boolean getBooleanValue(JsonNode node, String fieldName) {
		if (node != null && node.has(fieldName)) {
			JsonNode fieldNode = node.get(fieldName);
			return fieldNode.isNull() ? null : fieldNode.asBoolean();
		}
		return null;
	}

	/**
	 * 获取BigDecimal值
	 * @param node JSON节点
	 * @param fieldName 字段名
	 * @return BigDecimal
	 */
	private java.math.BigDecimal getBigDecimalValue(JsonNode node, String fieldName) {
		if (node != null && node.has(fieldName)) {
			JsonNode fieldNode = node.get(fieldName);
			if (!fieldNode.isNull()) {
				try {
					return new java.math.BigDecimal(fieldNode.asText());
				}
				catch (Exception e) {
					log.warn("Failed to parse BigDecimal for field {}: {}", fieldName, fieldNode.asText(), e);
				}
			}
		}
		return null;
	}

	/**
	 * 获取Integer值
	 * @param node JSON节点
	 * @param fieldName 字段名
	 * @return Integer
	 */
	private Integer getIntegerValue(JsonNode node, String fieldName) {
		if (node != null && node.has(fieldName)) {
			JsonNode fieldNode = node.get(fieldName);
			if (!fieldNode.isNull()) {
				try {
					return fieldNode.asInt();
				}
				catch (Exception e) {
					log.warn("Failed to parse Integer for field {}: {}", fieldName, fieldNode.asText(), e);
				}
			}
		}
		return null;
	}

	/**
	 * 解析订购数量信息
	 * @param quantityNode 订购数量JSON节点
	 * @return AmzVcOrderedQuantity
	 */
	private AmzVcOrderedQuantity parseOrderedQuantity(JsonNode quantityNode) {
		if (quantityNode == null) {
			return null;
		}

		// 检查是否包含orderedQuantity字段
		if (quantityNode.has("orderedQuantity")) {
			// 解析当前订购数量
			AmzVcQuantity orderedQuantity = this.parseQuantityDetail(quantityNode.get("orderedQuantity"));

			// 解析订购数量详情历史
			List<AmzVcOrderedQuantityDetails> details = null;
			if (quantityNode.has("orderedQuantityDetails")) {
				// 将JSON数组转换为详情对象列表
				details = StreamSupport.stream(quantityNode.get("orderedQuantityDetails").spliterator(), false)
					.map(this::parseOrderedQuantityEvent)
					.toList();
			}

			// 构建订购数量对象
			return AmzVcOrderedQuantity.builder().orderedQuantity(orderedQuantity).orderedQuantityDetails(details).build();
		}
		return null;
	}

	/**
	 * 解析订购数量详情事件
	 * @param eventNode 订购数量详情事件JSON节点
	 * @return AmzVcOrderedQuantityDetails
	 */
	private AmzVcOrderedQuantityDetails parseOrderedQuantityEvent(JsonNode eventNode) {
		if (eventNode == null) {
			return null;
		}

		// 构建订购数量详情事件对象
		return AmzVcOrderedQuantityDetails.builder()
			// 数量更新日期
			.updatedDate(this.getLocalDateTimeValue(eventNode, "updatedDate"))
			// 订购数量
			.orderedQuantity(this.parseQuantityDetail(eventNode.get("orderedQuantity")))
			// 取消数量
			.cancelledQuantity(this.parseQuantityDetail(eventNode.get("cancelledQuantity")))
			.build();
	}

	/**
	 * 解析订单商品行金额
	 * @param moneyNode 金额JSON节点
	 * @return AmzVcOrderItemMoney
	 */
	private AmzVcOrderItemMoney parseMoney(JsonNode moneyNode) {
		if (moneyNode == null) {
			return null;
		}

		return AmzVcOrderItemMoney.builder()
			.currencyCode(this.getTextValue(moneyNode, "currencyCode"))
			.amount(this.getBigDecimalValue(moneyNode, "amount"))
			.unitOfMeasure(this.getTextValue(moneyNode, "unitOfMeasure"))
			.build();
	}

	/**
	 * 解析订单商品行金额（用于状态）
	 * @param moneyNode 金额JSON节点
	 * @return AmzVcMoney
	 */
	private AmzVcMoney parseStatusMoney(JsonNode moneyNode) {
		if (moneyNode == null) {
			return null;
		}

		return AmzVcMoney.builder()
			.currencyCode(this.getTextValue(moneyNode, "currencyCode"))
			.amount(this.getBigDecimalValue(moneyNode, "amount"))
			.build();
	}

	/**
	 * 解析确认状态
	 * @param ackNode 确认状态JSON节点
	 * @return AmzVcAcknowledgementStatus
	 */
	private AmzVcAcknowledgementStatus parseAcknowledgementStatus(JsonNode ackNode) {
		if (ackNode == null) {
			return null;
		}

		return AmzVcAcknowledgementStatus.builder()
			.confirmationStatus(this.getTextValue(ackNode, "confirmationStatus"))
			.acceptedQuantity(this.parseQuantityDetail(ackNode.get("acceptedQuantity")))
			.rejectedQuantity(this.parseQuantityDetail(ackNode.get("rejectedQuantity")))
			.acknowledgementDate(this.getLocalDateTimeValue(ackNode, "acknowledgementDate"))
			.acknowledgementStatusDetails(this.parseAcknowledgementStatusDetails(ackNode.get("acknowledgementStatusDetails")))
			.build();
	}

	/**
	 * 解析确认状态详情列表
	 * @param detailsNode 确认状态详情JSON节点
	 * @return List<AmzVcAcknowledgementStatusDetails>
	 */
	private List<AmzVcAcknowledgementStatusDetails> parseAcknowledgementStatusDetails(JsonNode detailsNode) {
		if (detailsNode == null || !detailsNode.isArray()) {
			return null;
		}

		List<AmzVcAcknowledgementStatusDetails> detailsList = new ArrayList<>();
		for (JsonNode detailNode : detailsNode) {
			AmzVcAcknowledgementStatusDetails details = AmzVcAcknowledgementStatusDetails.builder()
				.acknowledgementDate(this.getLocalDateTimeValue(detailNode, "acknowledgementDate"))
				.acceptedQuantity(this.parseQuantityDetail(detailNode.get("acceptedQuantity")))
				.rejectedQuantity(this.parseQuantityDetail(detailNode.get("rejectedQuantity")))
				.build();
			detailsList.add(details);
		}
		return detailsList;
	}

	/**
	 * 解析数量详情
	 * @param quantityNode 数量详情JSON节点
	 * @return AmzVcQuantity
	 */
	private AmzVcQuantity parseQuantityDetail(JsonNode quantityNode) {
		if (quantityNode == null) {
			return null;
		}

		return AmzVcQuantity.builder()
			.amount(this.getIntegerValue(quantityNode, "amount"))
			.unitOfMeasure(this.getTextValue(quantityNode, "unitOfMeasure"))
			.unitSize(this.getIntegerValue(quantityNode, "unitSize"))
			.build();
	}

	/**
	 * 解析接收状态
	 * @param recvNode 接收状态JSON节点
	 * @return AmzVcReceivingStatus
	 */
	private AmzVcReceivingStatus parseReceivingStatus(JsonNode recvNode) {
		if (recvNode == null) {
			return null;
		}

		return AmzVcReceivingStatus.builder()
			.receiveStatus(this.getTextValue(recvNode, "receiveStatus"))
			.receivedQuantity(this.parseOrderedQuantity(recvNode.get("receivedQuantity")))
			.lastReceiveDate(this.getLocalDateTimeValue(recvNode, "lastReceiveDate"))
			.build();
	}

	/**
	 * 获取LocalDateTime值
	 * @param node JSON节点
	 * @param fieldName 字段名
	 * @return LocalDateTime
	 */
	private LocalDateTime getLocalDateTimeValue(JsonNode node, String fieldName) {
		if (node != null && node.has(fieldName)) {
			JsonNode fieldNode = node.get(fieldName);
			if (!fieldNode.isNull()) {
				try {
					return OffsetDateTime.parse(fieldNode.asText()).atZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();
				}
				catch (Exception e) {
					log.warn("Failed to parse LocalDateTime for field {}: {}", fieldName, fieldNode.asText(), e);
				}
			}
		}
		return null;
	}

}
