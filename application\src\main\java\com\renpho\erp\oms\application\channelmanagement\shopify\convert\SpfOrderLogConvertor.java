package com.renpho.erp.oms.application.channelmanagement.shopify.convert;

import com.renpho.erp.oms.application.channelmanagement.shopify.dto.SpfOrderLog;
import com.renpho.erp.oms.application.channelmanagement.shopify.vo.SpfOrderVO;
import com.renpho.erp.oms.domain.channelmanagement.shopify.model.SpfOrder;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, componentModel = "spring")
public interface SpfOrderLogConvertor {
	SpfOrderLog toLog(SpfOrder param);

	SpfOrderVO toVO(SpfOrder param);
}
