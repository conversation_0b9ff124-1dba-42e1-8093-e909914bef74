package com.renpho.erp.oms.application.ruleconfig.mulSkuMapping.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MulSkuMappingVO {
	@JsonSerialize(using = ToStringSerializer.class)
	private Long id;

	/**
	 * 店铺
	 */
	private String orderStore;

	/**
	 * 店铺id
	 */
	private Integer orderStoreId;

	/**
	 * 渠道
	 */
	private String orderChannel;

	/**
	 * 渠道id
	 */
	private Integer orderChannelId;


	/**
	 * 状态
	 */
	private String orderPsku;


	/**
	 * 映射行
	 */
	private List<MulSkuMappingLineVO> mulSkuMappingLineVOList;

	@Data
	public static class MulSkuMappingLineVO {

		/**
		 * 多渠道msku
		 */
		private String mulChannelMsku;

		/**
		 * 多渠道类型  7  FBA多渠道  8WFS多渠道
		 */
		private Integer mulChannelType;

		/**
		 * 多渠道店铺
		 */
		private String mulChannelStore;

		/**
		 * 多渠道店铺id
		 */
		private Integer mulChannelStoreId;
	}

}
