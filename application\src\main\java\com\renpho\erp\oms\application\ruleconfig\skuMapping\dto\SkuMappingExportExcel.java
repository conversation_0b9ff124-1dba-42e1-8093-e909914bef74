package com.renpho.erp.oms.application.ruleconfig.skuMapping.dto;

import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelProperty;

import lombok.Data;

@Data
public class SkuMappingExportExcel {
	/**
	 * 店铺
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.STORE")
	private String store;

	/**
	 * 销售渠道
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.CHANNEL")
	private String channel;

	/**
	 * 销售SKU
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.SELLERSKU")
	private String msku;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL.FNSKU")
	private String fnsku;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL.ASIN")
	private String asin;

	/**
	 * 采购SKU
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.PURCHASESKU")
	private String psku;

	/**
	 * 运营
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.OPERATOR")
	private String operator;

	/**
	 * 发货方式
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL_EXPORT.FULFILLMENTTYPE")
	private String fulfillmentType;

	/**
	 * 是否统计FBA库存
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL_EXPORT.COUNTFBAINVENTORY")
	private String fbaInventoryCounted;

	/**
	 * 版本
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL.VERSION")
	private String version;

	/**
	 * 状态
	 */
	@ExcelProperty(value = "SKU_MAPPING_EXCEL_EXPORT.STATUS")
	private String status;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL_EXPORT.UPDATER")
	private String updater;

	@ExcelProperty(value = "SKU_MAPPING_EXCEL_EXPORT.UPDATETIME")
	private LocalDateTime updateTime;
}
