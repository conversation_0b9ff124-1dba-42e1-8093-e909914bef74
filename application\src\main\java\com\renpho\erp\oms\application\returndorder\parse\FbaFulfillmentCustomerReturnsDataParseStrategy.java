package com.renpho.erp.oms.application.returndorder.parse;

import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.renpho.erp.amz.report.po.AmzReportRecordPo;
import com.renpho.erp.amz.report.strategy.parse.AbstractAmzReportParseStrategy;
import com.renpho.erp.oms.domain.returnorder.model.fba.AmzReportFbaCustomReturn;
import com.renpho.erp.oms.domain.returnorder.repository.fba.AmzReportFbaCustomReturnRepository;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 亚马逊GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA报告解析类
 *
 * <AUTHOR>
 * @since 2025/3/17
 */
@Slf4j
@Component
public class FbaFulfillmentCustomerReturnsDataParseStrategy extends AbstractAmzReportParseStrategy<AmzReportFbaCustomReturn> implements InitializingBean {
    @Autowired
    private AmzReportFbaCustomReturnRepository amzReportFbaCustomReturnRepository;

    @Override
    protected List<AmzReportFbaCustomReturn> parseData(List<CsvRow> csvRows, AmzReportRecordPo reportRecordPo) {
        List<AmzReportFbaCustomReturn> amzReportFbaCustomReturnList = new ArrayList<>(csvRows.size());
        for (CsvRow csvRow : csvRows) {
            AmzReportFbaCustomReturn amzReportFbaCustomReturn = new AmzReportFbaCustomReturn();
            amzReportFbaCustomReturn.setStoreId(reportRecordPo.getStoreId());
            amzReportFbaCustomReturn.setMarketplaceId(reportRecordPo.getMarketplaceId());

            // 退货时间
            String returnDate = csvRow.getByName("return-date");
            amzReportFbaCustomReturn.setReturnDate(DateUtil.parseISOStr(returnDate));

            // 亚马逊渠道订单id
            String orderId = csvRow.getByName("order-id");
            amzReportFbaCustomReturn.setOrderId(orderId);

            // 亚马逊销售sku
            String sku = csvRow.getByName("sku");
            amzReportFbaCustomReturn.setMsku(sku);

            // 亚马逊asin
            String asin = csvRow.getByName("asin");
            amzReportFbaCustomReturn.setAsin(asin);

            // 亚马逊fnsku
            String fnsku = csvRow.getByName("fnsku");
            amzReportFbaCustomReturn.setFnSku(fnsku);

            // 亚马逊商品名称
            String productName = csvRow.getByName("product-name");
            amzReportFbaCustomReturn.setProductName(productName);

            // 退货数量
            Integer quantity = NumberUtil.parseInt(csvRow.getByName("quantity"));
            amzReportFbaCustomReturn.setQuantity(quantity);

            // 亚马逊平台仓退货仓库编码
            String fulfillmentCenterId = csvRow.getByName("fulfillment-center-id");
            amzReportFbaCustomReturn.setFulfillmentCenterId(fulfillmentCenterId);

            // 处置信息
            String detailedDisposition = csvRow.getByName("detailed-disposition");
            amzReportFbaCustomReturn.setDetailedDisposition(detailedDisposition);

            // 退货原因
            String reason = csvRow.getByName("reason");
            amzReportFbaCustomReturn.setReason(reason);

            // 状态
            String status = csvRow.getByName("status");
            amzReportFbaCustomReturn.setStatus(status);

            // 退货唯一id,亚马逊提供
            String licensePlateNumber = csvRow.getByName("license-plate-number");
            amzReportFbaCustomReturn.setLicensePlateNumber(licensePlateNumber);

            // 备注
            String customerComments = csvRow.getByName("customer-comments\r");
            customerComments = StrUtil.removeSuffix(customerComments, "\r");
            amzReportFbaCustomReturn.setCustomerComments(customerComments);

            amzReportFbaCustomReturnList.add(amzReportFbaCustomReturn);
        }
        return amzReportFbaCustomReturnList;
    }

    @Override
    protected void saveReportData(List<AmzReportFbaCustomReturn> dataList) {
        dataList.forEach(amzReportFbaCustomReturnRepository::saveOrUpdate);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        this.register("GET_FBA_FULFILLMENT_CUSTOMER_RETURNS_DATA");
    }
}
