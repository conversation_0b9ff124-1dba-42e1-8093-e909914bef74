package com.renpho.erp.oms.application.ordershipaudit.chain.handler;

import java.util.Objects;

import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics.AllocateLogistics;
import com.renpho.erp.oms.application.ordershipaudit.chain.strategy.logistics.AllocateLogisticsFactory;
import com.renpho.erp.oms.application.ordershipaudit.dto.OrderAutoShipAuditDTO;
import com.renpho.erp.oms.domain.SaleChannelType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.ServiceProvider;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.WarehouseType;
import com.renpho.erp.oms.domain.ruleconfig.autoShipAudit.model.Warehouse;
import com.renpho.erp.oms.domain.salemanagement.FulfillmentServiceType;
import com.renpho.erp.oms.domain.salemanagement.SaleOrderAggRoot;

/**
 * 订单-分物流处理器
 * <AUTHOR>
 */
@Component
public class OrderAllocateLogisticsHandler implements OrderAutoShipAuditHandler {

	/**
	 * 处理链顺序：1-前置校验 -> 2-分仓 -> 3-分物流 -> 4-订单自动发货审核创建
	 * @return int
	 */
	@Override
	public int getOrder() {
		return 3;
	}

	/**
	 * 分物流
	 * @param orderAutoShipAudit 订单-自动发货审核DTO
	 * @return 分物流失败原因
	 */
	@Override
	public String handle(OrderAutoShipAuditDTO orderAutoShipAudit) {
		// 订单聚合根
		SaleOrderAggRoot orderAggRoot = orderAutoShipAudit.getSaleOrderAggRoot();
		// 仓库
		Warehouse warehouse = orderAutoShipAudit.getWarehouse();
		// 根据仓库类型，服务提供商，订单渠道，获取履约服务类型
		FulfillmentServiceType fulfillmentServiceType = this.getFulfillmentServiceType(orderAggRoot, warehouse);
		// 失败
		if (Objects.isNull(fulfillmentServiceType)) {
			return String.format("根据仓库【%s】，获取履约服务类型失败", warehouse.getWarehouseCode());
		}
		// 履约服务类型
		orderAutoShipAudit.setFulfillmentServiceType(fulfillmentServiceType);
		// 分物流策略
		AllocateLogistics strategy = AllocateLogisticsFactory.get(fulfillmentServiceType);
		return strategy.allocateLogistics(orderAutoShipAudit);
	}

	/**
	 * 获取履约服务类型
	 * @param orderAggRoot 订单聚合根
	 * @param warehouse 仓库
	 * @return FulfillmentServiceType
	 */
	private FulfillmentServiceType getFulfillmentServiceType(SaleOrderAggRoot orderAggRoot, Warehouse warehouse) {
		// 仓库类型
		WarehouseType warehouseType = warehouse.getWarehouseType();
		// 根据仓库类型，获取履约服务
		return switch (warehouseType) {
			// 三方仓，根据 服务提供商 获取
			case THIRD_PARTY_WAREHOUSE -> this.getFulfillmentServiceType(warehouse.getServiceProvider());
			// 自建仓，根据 销售渠道 获取
			case SELF_BUILT_WAREHOUSE -> this.getFulfillmentServiceType(orderAggRoot.getChannelType());
			default -> null;
		};
	}

	/**
	 * 根据 销售渠道 获取履约服务类型
	 * @param saleChannelType 销售渠道
	 * @return FulfillmentServiceType
	 */
	private FulfillmentServiceType getFulfillmentServiceType(SaleChannelType saleChannelType) {
		return switch (saleChannelType) {
			// Temu面单
			case TEMU -> FulfillmentServiceType.TEMU_ONLINE_LABEL;
			// EBAY面单
			case EBAY -> FulfillmentServiceType.EBAY_LABEL_SELF_WAREHOUSE;
			// 其他渠道：ECOM面单
			default -> FulfillmentServiceType.ECOM_LABEL_SELF_WAREHOUSE;
		};
	}

	/**
	 * 根据serviceProvider获取履约服务类型
	 * @param serviceProvider 服务提供商
	 * @return FulfillmentServiceType
	 */
	private FulfillmentServiceType getFulfillmentServiceType(ServiceProvider serviceProvider) {
		// 根据serviceProvider获取履约服务类型
		return switch (serviceProvider) {
			// 京东
			case JD -> FulfillmentServiceType.JD_WAREHOUSE;
			// 极智佳
			case JIZHIJIA -> FulfillmentServiceType.YPL_WAREHOUSE;
			// KingSpark
			case KINGSPARK -> FulfillmentServiceType.KING_SPARK_WAREHOUSE;
			default -> null;
		};
	}

}
