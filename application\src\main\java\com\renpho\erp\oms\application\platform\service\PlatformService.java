package com.renpho.erp.oms.application.platform.service;

import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderDto;
import com.renpho.erp.oms.application.platform.dto.MonitorPlatformOrderQuery;
import com.renpho.erp.oms.application.platform.vo.PlatformOrderVO;
import com.renpho.erp.oms.domain.platform.query.PlatformPageQuery;
import com.renpho.erp.oms.domain.platform.query.PlatformQuery;
import com.renpho.karma.dto.Paging;
import com.renpho.karma.dto.R;

import java.util.List;

public interface PlatformService {

	R<Paging<PlatformOrderVO>> page(PlatformPageQuery platformPageQuery);

	R get(PlatformQuery platformQuery);

	/**
	 * 查询监控的平台订单量
	 * @param monitorPlatformOrderQuery
	 * @return
	 */
	List<MonitorPlatformOrderDto> queryMonitorPlatformOrder(MonitorPlatformOrderQuery monitorPlatformOrderQuery);

}
