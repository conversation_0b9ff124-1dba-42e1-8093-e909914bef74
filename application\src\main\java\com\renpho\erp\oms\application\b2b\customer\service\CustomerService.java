package com.renpho.erp.oms.application.b2b.customer.service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.lock.annotation.Lock4j;
import com.fhs.core.trans.anno.TransMethodResult;
import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import com.renpho.erp.log.repository.annotation.LogRecord;
import com.renpho.erp.log.repository.support.LogRecordContextHolder;
import com.renpho.erp.mdm.client.company.vo.CompanyVo;
import com.renpho.erp.mdm.client.vo.LanguageNameVo;
import com.renpho.erp.oms.application.b2b.customer.command.CreateCustomerCommand;
import com.renpho.erp.oms.application.b2b.customer.command.UpdateCustomerCommand;
import com.renpho.erp.oms.application.b2b.customer.convert.CustomerConvert;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerAttachmentUploadVO;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerSalesCompanyTermsVO;
import com.renpho.erp.oms.application.b2b.customer.vo.CustomerVO;
import com.renpho.erp.oms.domain.b2b.customer.model.CustomerAggRoot;
import com.renpho.erp.oms.domain.b2b.customer.repository.CustomerRepository;
import com.renpho.erp.oms.infrastructure.common.constant.PermissionConstant;
import com.renpho.erp.oms.infrastructure.common.exception.BusinessException;
import com.renpho.erp.oms.infrastructure.common.vo.log.LogStatusVO;
import com.renpho.erp.oms.infrastructure.feign.ftm.FileClient;
import com.renpho.erp.oms.infrastructure.feign.mdm.CompanyClient;
import com.renpho.erp.oms.infrastructure.feign.pds.CountryClient;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.IncotermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.po.PaymentTermsConfigPO;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.IncotermsConfigService;
import com.renpho.erp.oms.infrastructure.persistence.customer.service.PaymentTermsConfigService;
import com.renpho.erp.security.util.SecurityUtils;
import com.renpho.karma.i18n.I18nMessageKit;

import lombok.RequiredArgsConstructor;

/**
 * 客户应用服务
 */
@Service
@RequiredArgsConstructor
public class CustomerService {
	// 最大允许 10MB
	private final long MAX_SIZE = 10L * 1024 * 1024;

	private final CustomerRepository customerRepository;
	private final CustomerOperateService customerOperateService;
	private final PaymentTermsConfigService paymentTermsConfigService;
	private final IncotermsConfigService incotermsConfigService;
	private final FileClient fileClient;
	private final CountryClient countryClient;
	private final CompanyClient companyClient;
	private final CustomerConvert customerConvert = CustomerConvert.INSTANCE;

	/**
	 * 创建客户
	 *
	 * @param command 创建命令
	 * @return 客户ID
	 */
	@Lock4j(name = "oms:customer:create", keys = { "#command.customerCompanyName" })
	public Long createCustomer(CreateCustomerCommand command) {
		// 构建客户聚合根
		CustomerAggRoot customerAggRoot = CustomerAggRoot.builder()
			.customerCompanyName(command.getCustomerCompanyName())
			.country(command.getCountry())
			.countryManagerUserId(command.getCountryManagerUserId())
			.salesAssistantUserId(command.getSalesAssistantUserId())
			.remark(command.getRemark())
			.contactInfos(customerConvert.toCustomerContactInfoList(command.getContactInfos()))
			.invoiceInfos(customerConvert.toCustomerInvoiceInfoList(command.getInvoiceInfos()))
			.shippingInfos(customerConvert.toCustomerShippingInfoList(command.getShippingInfos()))
			.attachmentInfos(customerConvert.toCustomerAttachmentInfoList(command.getAttachmentInfos()))
			.salesCompanyTermsInfo(customerConvert.toCustomerSalesCompanyTermsInfo(command.getSalesCompanyTermsInfo()))
			.insuranceCreditInfo(customerConvert.toCustomerInsuranceCreditInfo(command.getInsuranceCreditInfo()))
			.build();
		// 校验并填充信息
		customerOperateService.validateAndFillNewCustomer(customerAggRoot);

		// 生成ID
		customerAggRoot.generateId();

		// 保存客户信息
		customerRepository.save(customerAggRoot);

		return customerAggRoot.getId();
	}

	/**
	 * 更新客户
	 *
	 * @param command 更新命令
	 * @return 是否成功
	 */
	@Lock4j(name = "oms:customer:update", keys = "#command.id")
	public Boolean updateCustomer(UpdateCustomerCommand command) {
		// 查询客户是否存在
		CustomerAggRoot existingCustomer = customerRepository.findById(command.getId());

		// 构建客户聚合根
		CustomerAggRoot customerAggRoot = CustomerAggRoot.builder()
			.id(command.getId())
			.customerCompanyName(command.getCustomerCompanyName())
			.country(command.getCountry())
			.countryManagerUserId(command.getCountryManagerUserId())
			.salesAssistantUserId(command.getSalesAssistantUserId())
			.remark(command.getRemark())
			.contactInfos(customerConvert.toCustomerContactInfoList(command.getContactInfos()))
			.invoiceInfos(customerConvert.toCustomerInvoiceInfoList(command.getInvoiceInfos()))
			.shippingInfos(customerConvert.toCustomerShippingInfoList(command.getShippingInfos()))
			.attachmentInfos(customerConvert.toCustomerAttachmentInfoList(command.getAttachmentInfos()))
			.salesCompanyTermsInfo(customerConvert.toCustomerSalesCompanyTermsInfo(command.getSalesCompanyTermsInfo()))
			.insuranceCreditInfo(customerConvert.toCustomerInsuranceCreditInfo(command.getInsuranceCreditInfo()))
			.build();

		// 校验并填充信息
		customerOperateService.validateAndFillNewCustomer(customerAggRoot);

		// 更新客户信息，传递已查询到的客户聚合根，避免重复查询
		customerRepository.update(customerAggRoot, existingCustomer);

		return true;
	}

	/**
	 * 获取客户详情
	 *
	 * @param id 客户ID
	 * @return 客户详情VO
	 */
	@TransMethodResult
	public CustomerVO getCustomerDetail(Long id) {
		// 查询客户聚合根
		CustomerAggRoot customerAggRoot = customerRepository.findById(id);

		// 转换为VO对象
		CustomerVO customerVO = customerConvert.toCustomerVO(customerAggRoot);

		// 判断是否有保险权限
		Authentication authentication = SecurityUtils.getAuthentication();
		if (!SecurityUtils.isAdmin() && (authentication == null || !authentication.getAuthorities()
			.stream()
			.anyMatch(authority -> authority.getAuthority().equals(PermissionConstant.Customer.INSURANCE_GET)))) {
			// 如果没有权限，将insuranceCreditInfo设置为null
			customerVO.setInsuranceCreditInfo(null);
		}
		// 国家多语言
		customerVO.setLanguageConutryName(countryClient.getLanguageCountryName(customerVO.getCountry()));
		// 贸易条款、付款条款转换
		if (Objects.nonNull(customerVO.getSalesCompanyTermsInfo())) {
			CustomerSalesCompanyTermsVO salesCompanyTermsInfo = customerVO.getSalesCompanyTermsInfo();
			if (Objects.nonNull(salesCompanyTermsInfo.getSalesCompanyId())) {
				// 调用MDM服务获取销售公司名称
				List<CompanyVo> companyVos = companyClient.getByIds(List.of(salesCompanyTermsInfo.getSalesCompanyId()));
				if (CollectionUtils.isNotEmpty(companyVos)) {
					CompanyVo companyVo = companyVos.get(0);
					String language = Optional.of(LocaleContextHolder.getLocale()).map(locale -> locale.toLanguageTag()).orElse("zh-CN");
					salesCompanyTermsInfo.setSalesCompany(Optional.ofNullable(companyVo.getCompanyName())
						.orElse(Collections.emptyList())
						.stream()
						.filter(ln -> language.equals(ln.getLanguage()))
						.findFirst()
						.map(LanguageNameVo::getName)
						.orElse(""));
					salesCompanyTermsInfo.setSalesCompanyCountry(companyVo.getRegisteredCountry());
				}
			}
			if (StringUtils.isNotEmpty(salesCompanyTermsInfo.getIncotermsCode())) {
				IncotermsConfigPO incotermsConfigPO = incotermsConfigService.getByCode(salesCompanyTermsInfo.getIncotermsCode());
				if (Objects.nonNull(incotermsConfigPO)) {
					salesCompanyTermsInfo.setIncotermsName(incotermsConfigPO.getName());
				}
			}
			if (StringUtils.isNotEmpty(salesCompanyTermsInfo.getPaymentTermsCode())) {
				PaymentTermsConfigPO paymentTermsConfigPO = paymentTermsConfigService
					.getByCode(salesCompanyTermsInfo.getPaymentTermsCode());
				if (Objects.nonNull(paymentTermsConfigPO)) {
					salesCompanyTermsInfo.setPaymentTermsName(paymentTermsConfigPO.getName());
				}
			}
		}
		return customerVO;
	}

	/**
	 * 启用客户
	 *
	 * @param id 客户ID
	 * @return 是否成功
	 */
	@Lock4j(name = "oms:customer:status", keys = "#id")
	@LogRecord(module = "CUSTOMER", type = "Enable", bsId = "#id")
	public Boolean enableCustomer(Long id) {
		// 查询客户聚合根
		CustomerAggRoot customerAggRoot = customerRepository.findById(id);

		// 如果已经是启用状态，则直接返回成功
		if (customerAggRoot.getStatus() != null && customerAggRoot.getStatus() == 1) {
			return true;
		}

		// 更新状态为启用(1)
		customerAggRoot.updateStatus(1);

		// 保存状态变更
		customerRepository.updateStatus(customerAggRoot);
		LogRecordContextHolder.putRecordData(id.toString(), new LogStatusVO("禁用"), new LogStatusVO("启用"));
		return true;
	}

	/**
	 * 禁用客户
	 *
	 * @param id 客户ID
	 * @return 是否成功
	 */
	@Lock4j(name = "oms:customer:status", keys = "#id")
	@LogRecord(module = "CUSTOMER", type = "Disable", bsId = "#id")
	public Boolean disableCustomer(Long id) {
		// 查询客户聚合根
		CustomerAggRoot customerAggRoot = customerRepository.findById(id);

		// 如果已经是禁用状态，则直接返回成功
		if (customerAggRoot.getStatus() != null && customerAggRoot.getStatus() == 0) {
			return true;
		}

		// 更新状态为禁用(0)
		customerAggRoot.updateStatus(0);

		// 保存状态变更
		customerRepository.updateStatus(customerAggRoot);
		LogRecordContextHolder.putRecordData(id.toString(), new LogStatusVO("启用"), new LogStatusVO("禁用"));
		return true;
	}

	/**
	 * 上传客户附件
	 *
	 * @param file 附件文件
	 * @return 附件上传结果
	 */
	public CustomerAttachmentUploadVO uploadAttachment(MultipartFile file) {
		// 2. 大小校验
		long size = file.getSize(); // 返回文件大小，单位：字节 :contentReference[oaicite:1]{index=1}
		if (size > MAX_SIZE) {
			// 根据业务需要，抛出异常或返回特定错误对象
			throw new BusinessException(I18nMessageKit.getMessage("CUSTOMER_FILE_LARGER_THAN_10MB"));
		}

		// 调用FileClient获取上传文件地址
		String fileUrl = fileClient.uploadSingleMultipleFile(file);

		// 获取原始文件名
		String originalFileName = file.getOriginalFilename();

		// 获取文件格式（扩展名）
		String format = "";
		if (StringUtils.isNotEmpty(originalFileName)) {
			int lastDotIndex = originalFileName.lastIndexOf(".");
			if (lastDotIndex > 0 && lastDotIndex < originalFileName.length() - 1) {
				format = originalFileName.substring(lastDotIndex + 1);
			}
		}

		// 组装返回结果
		return CustomerAttachmentUploadVO.builder().fileUrl(fileUrl).originalFileName(originalFileName).format(format).build();
	}

}