package com.renpho.erp.oms.application.ordershipaudit.job;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import com.renpho.erp.oms.application.salemanagement.service.OrderShipAuditInventoryService;
import com.renpho.erp.oms.application.salemanagement.service.OrderShippedInventoryDeductService;
import com.renpho.erp.oms.domain.ordershipaudit.query.InventoryQuery;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import com.renpho.karma.json.JSONKit;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @desc: 发货审核库存锁定解锁定时任务补偿
 * @time: 2025-03-05 12:24:43
 * @author: Alina
 */
@Slf4j
@Component
@AllArgsConstructor
public class OrderShipAuditInventoryJob {

	private final OrderShipAuditInventoryService orderShipAuditInventoryService;
	private final OrderShippedInventoryDeductService orderShippedInventoryDeductService;

	/**
	 * 库存解锁-定时任务补偿
	 */
	@XxlJob("inventoryUnlockCompensationJob")
	public void inventoryUnlockCompensationJob() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		OrderShipAuditInventoryJob.Param param = JSONKit.parseObject(jobParam, OrderShipAuditInventoryJob.Param.class);
		InventoryQuery query = buildInventoryQuery(param);
		// 分批查询发货审核表数据，库存状态为5（解锁失败） 且 当前重试次数小于最大重试次数,并调用库存解锁接口
		orderShipAuditInventoryService.inventoryUnlockCompensation(query);
	}

	/**
	 * 库存解锁-数据巡检-告警通知定时任务
	 */
	@XxlJob("inventoryUnlockAlarmJob")
	public void inventoryUnlockAlarmJob() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		OrderShipAuditInventoryJob.Param param = JSONKit.parseObject(jobParam, OrderShipAuditInventoryJob.Param.class);
		InventoryQuery query = buildInventoryUnlockAlarmQuery(param);

		if (query.getAlarmThreshold() == null) {
			throw new RuntimeException("alarmThreshold不能为空");
		}
		// 分批查询发货审核表数据，库存状态为5（解锁失败） 且 当前重试次数大于xxl-job中设置的阈值次数，发到钉钉
		orderShipAuditInventoryService.inventoryUnlockAlarm(query);
	}

	/**
	 * 库存扣减-定时任务补偿
	 */
	@XxlJob("inventoryDeductCompensationJob")
	public void inventoryDeductCompensationJob() {
		String jobParam = StringUtils.isEmpty(XxlJobHelper.getJobParam()) ? "{}" : XxlJobHelper.getJobParam();
		OrderShipAuditInventoryJob.Param param = JSONKit.parseObject(jobParam, OrderShipAuditInventoryJob.Param.class);
		InventoryQuery query = buildInventoryQuery(param);
		// 分批查询销售订单行履约表数据，库存扣减状态为 1-待扣减， 3-扣减失败的数据，并调用库存解锁接口
		orderShippedInventoryDeductService.inventoryDeductCompensation(query);
	}

	/**
	 * 构建查询参数
	 */
	private InventoryQuery buildInventoryUnlockAlarmQuery(Param param) {
		InventoryQuery query = new InventoryQuery();
		query.setStartTime(DateUtil.parseTime(param.getStartTime(), LocalDateTime.now().minusHours(24)));
		query.setEndTime(DateUtil.parseTime(param.getEndTime(), LocalDateTime.now()));
		query.setAlarmThreshold(param.getAlarmThreshold());
		return query;
	}

	/**
	 * 构建库存解锁-定时任务补偿查询参数
	 */
	private InventoryQuery buildInventoryQuery(Param param) {
		InventoryQuery query = new InventoryQuery();
		query.setStartTime(DateUtil.parseTime(param.getStartTime(), null));
		query.setEndTime(DateUtil.parseTime(param.getEndTime(), null));
		if (CollectionUtils.isNotEmpty(param.getIds())) {
			query.setIds(param.getIds());
		}
		return query;
	}

	@Data
	public static class Param {
		/**
		 * 开始时间(yyyy-MM-dd HH:mm:ss)
		 */
		private String startTime;

		/**
		 * 结束时间（yyyy-MM-dd HH:mm:ss）
		 */
		private String endTime;

		/**
		 * ids
		 */
		private List<Long> ids;

		/**
		 * 告警阈值
		 */
		private Integer alarmThreshold;

	}
}
