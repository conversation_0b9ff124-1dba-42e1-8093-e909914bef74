package com.renpho.erp.oms.application.listingmanagement.strategy.pusher;

import com.renpho.erp.oms.application.listingmanagement.strategy.pusher.message.ListingSourceMessage;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.listingmanagement.repository.SalesChannelListingSourceRepository;
import com.renpho.erp.oms.infrastructure.common.constant.MQConstant;
import com.renpho.erp.oms.infrastructure.common.enums.ListingSourcePushMqStatusEnum;
import com.renpho.karma.json.JSONKit;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageConst;
import org.springframework.cloud.stream.function.StreamBridge;
import org.springframework.messaging.support.MessageBuilder;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * 抽象Listing推送器模板
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractListingPusherTemplate<T, R> implements ListingPusher {

	@Resource
	private StreamBridge streamBridge;
	@Resource
	private SalesChannelListingSourceRepository salesChannelListingSourceRepository;

	@Override
	public void pushListing(SalesChannelListingSource listingSource) {
		// 1.反序列化
		T t = JSONKit.parseObject(listingSource.getContent(), this.getClazz());
		// 2.解析
		R r = this.parseListingSource(t);
		// 3.构建Listing MQ消息体
		ListingSourceMessage<R> message = this.buildListingSourceMessage(r, listingSource);
		// 4.推送MQ
		this.pushListingSourceMq(message);
	}

	/**
	 * 解析原始listing数据
	 * @param t 原始listing数据
	 * @return R
	 */
	protected abstract R parseListingSource(T t);

	/**
	 * 构建Listing MQ消息体
	 * @param r 具体渠道对应的报文
	 * @param listingSource 原始Listing
	 * @return ListingSourceMessage
	 */
	protected ListingSourceMessage<R> buildListingSourceMessage(R r, SalesChannelListingSource listingSource) {
		ListingSourceMessage<R> message = new ListingSourceMessage<>();
		message.setId(listingSource.getId());
		message.setListingId(listingSource.getListingId());
		message.setSalesChannelId(listingSource.getSalesChannelId());
		message.setSalesChannelCode(listingSource.getSalesChannelCode());
		message.setStoreId(listingSource.getStoreId());
		message.setT(r);
		return message;
	}

	/**
	 * 推送MQ
	 * @param message 消息体
	 */
	protected void pushListingSourceMq(ListingSourceMessage<R> message) {
		String msg = JSONKit.toJSONString(message);
		MessageBuilder<String> messageBuilder = MessageBuilder.withPayload(msg);
		messageBuilder.setHeader(MessageConst.PROPERTY_TAGS, message.getSalesChannelCode());
		SalesChannelListingSource salesChannelListingSource = new SalesChannelListingSource();
		salesChannelListingSource.setId(message.getId());
		log.info("推送MQ-ListingSource消息：{}", msg);
		try {
			// 推送MQ
			streamBridge.send(MQConstant.TOPIC.LISTING_SOURCE, messageBuilder.build());
			// 实际不一定推送成功，后续看看是否需要事务消息
			salesChannelListingSource.setPushImsStatus(ListingSourcePushMqStatusEnum.PUSH_SUCCESS.getStatus());
		}
		catch (Exception e) {
			log.warn("Listing推送MQ失败，原因：", e);
			// 推送失败及原因
			salesChannelListingSource.setPushImsStatus(ListingSourcePushMqStatusEnum.PUSH_FAIL.getStatus());
			// 长度如果超长，截取前128个字符
			Optional.ofNullable(e.getMessage()).ifPresent(m -> salesChannelListingSource.setPushImsMsg(m.length() > 128 ? m.substring(0, 128) : m));
		}
		salesChannelListingSource.setPushImsTime(LocalDateTime.now());
		// 修改推送状态，推送时间
		salesChannelListingSourceRepository.updateById(salesChannelListingSource);
	}

	/**
	 * 获取泛型的class对象
	 * @return Class
	 */
	@SuppressWarnings({ "unchecked" })
	private Class<T> getClazz() {
		Type superclass = this.getClass().getGenericSuperclass();
		if (superclass instanceof ParameterizedType parameterizedtype) {
			Type type = parameterizedtype.getActualTypeArguments()[0];
			return (Class<T>) type;
		}
		throw new IllegalArgumentException("Unsupported Type: " + superclass);
	}
}
