package com.renpho.erp.oms.application.listingmanagement.strategy.parser.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.renpho.erp.apiproxy.ebay.model.listing.GetSellerListResponse;
import com.renpho.erp.oms.application.listingmanagement.strategy.parser.AbstractListingParserTemplate;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListing;
import com.renpho.erp.oms.domain.listingmanagement.model.SalesChannelListingSource;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.command.query.SkuMappingQuery;
import com.renpho.erp.oms.domain.ruleconfig.skuMapping.vo.SkuMappingFulfillmentVO;
import com.renpho.erp.oms.infrastructure.common.constant.ChannelCode;
import com.renpho.erp.oms.infrastructure.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class EbayListingParser extends AbstractListingParserTemplate<GetSellerListResponse.Item> {


    @Override
    protected List<SalesChannelListing> parseSourceListing(SalesChannelListingSource source, GetSellerListResponse.Item item) {
        log.info("解析ebay listing开始, 解析SalesChannelListingSource id 是{}, 平台内容是{}", source.getId(), source.getContent());
        String msku = item.getSKU();
        String itemID = item.getItemID();

        Map<String, List<SkuMappingFulfillmentVO>> skuMappingListMap = this.getSkuMappingListMap(source.getStoreId(), CollectionUtil.newHashSet(itemID));
        Map<String, String> purchaseSkuImageMap = super.getPurchaseSkuImageMap(skuMappingListMap.values().stream().flatMap(Collection::stream).toList());
        SalesChannelListing salesChannelListing = super.buildSalesChannelListing(source, skuMappingListMap, purchaseSkuImageMap, itemID, source.getStoreId());
        // 销售sku
        salesChannelListing.setMsku(msku);
        // item编码
        salesChannelListing.setItem(itemID);
        // 标题
        salesChannelListing.setTitle(item.getTitle());
        // 链接
        salesChannelListing.setLink(Optional.ofNullable(item.getListingDetail())
                .map(GetSellerListResponse.ListingDetail::getViewItemURL)
                .orElse(null));
        // 状态
        salesChannelListing.setStatus(Optional.ofNullable(item.getSellingStatus())
                .map(GetSellerListResponse.SellingStatus::getListingStatus)
                .orElse(null));
        // 币种
        salesChannelListing.setCurrency(Optional.ofNullable(item.getSellingStatus())
                .map(GetSellerListResponse.SellingStatus::getCurrentPrice)
                .map(GetSellerListResponse.Amount::getCurrencyID)
                .orElse(null));
        // 价格
        salesChannelListing.setPrice(Optional.ofNullable(item.getSellingStatus())
                .map(GetSellerListResponse.SellingStatus::getCurrentPrice)
                .map(GetSellerListResponse.Amount::getValue)
                .orElse(null));
        // 上架时间
        salesChannelListing.setPublishedTime(Optional.ofNullable(item.getListingDetail())
                .map(GetSellerListResponse.ListingDetail::getStartTime)
                .map(DateUtil::parse)
                .orElse(null));

        return List.of(salesChannelListing);
    }

    /**
     * 获取sku映射Map
     * @param storeId 店铺id
     * @param itemSet 销售item集合
     * @return Map key为 店铺id+销售SKU，value为List
     */
    @NotNull
    @Override
    protected Map<String, List<SkuMappingFulfillmentVO>> getSkuMappingListMap(Integer storeId, Set<String> itemSet) {
        // sku映射查询条件
        SkuMappingQuery skuMappingQuery = new SkuMappingQuery();
        // 店铺id
        skuMappingQuery.setStoreIdList(List.of(storeId));
        // 销售SKU列表
        skuMappingQuery.setAsin(new ArrayList<>(itemSet));
        // 查询 sku映射列表
        List<SkuMappingFulfillmentVO> skuMappingList = skuMappingRepository.list(skuMappingQuery);
        // sku映射Map，key为 店铺id+销售SKU，value为List<SkuMapping>
        return skuMappingList.stream().collect(Collectors.groupingBy(s -> s.getStoreId() + "-" + s.getAsin()));
    }

    @Override
    public String getSalesChannelCode() {
        return ChannelCode.EBY_CHANNEL_CODE;
    }
}
