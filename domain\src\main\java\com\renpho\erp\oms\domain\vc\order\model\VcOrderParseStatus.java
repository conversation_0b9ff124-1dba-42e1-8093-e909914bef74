package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VC订单解析SKU状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VcOrderParseStatus {

	/**
	 * 解析成功
	 */
	SUCCESS(1, "解析成功"),

	/**
	 * SKU映射异常
	 */
	SKU_MAPPING_EXCEPTION(2, "SKU映射异常");

	private final Integer value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return VcOrderParseStatus
	 */
	public static VcOrderParseStatus getByValue(Integer value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
