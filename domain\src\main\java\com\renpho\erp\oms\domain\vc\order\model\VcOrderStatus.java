package com.renpho.erp.oms.domain.vc.order.model;

import java.util.Arrays;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * VC订单状态枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum VcOrderStatus {

	/**
	 * 待接单
	 */
	PENDING_ACCEPT(1, "待接单"),

	/**
	 * 接单异常
	 */
	ACCEPT_EXCEPTION(2, "接单异常"),

	/**
	 * 待上传箱唛
	 */
	PENDING_UPLOAD_LABEL(3, "待上传箱唛"),

	/**
	 * 备货中
	 */
	PREPARING(4, "备货中"),

	/**
	 * 待开票
	 */
	PENDING_INVOICE(5, "待开票"),

	/**
	 * 已完成
	 */
	COMPLETED(6, "已完成"),

	/**
	 * 已取消
	 */
	CANCELLED(7, "已取消");

	private final Integer value;
	private final String description;

	/**
	 * 根据值获取枚举
	 * @param value 值
	 * @return VcOrderStatus
	 */
	public static VcOrderStatus getByValue(Integer value) {
		return Arrays.stream(values()).filter(e -> e.getValue().equals(value)).findFirst().orElse(null);
	}

}
